﻿@model AppTech.MSMS.Domain.Models.Client
@using Obout.Mvc.ComboBox

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<input type="hidden" name="ParentID" value="@ViewBag.ParentID" />

<div class="form-group">
    @Html.Label("اختر النقطة", new { @class = "control-label col-md-2" })
    <div class="col-md-10">

        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })

        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>
<script>
    hideLoading();
</script>
<script>

    $(function () {
        try {
            formHelper.onSuccess = function (data) {
                log('user party form');
                //           hideFormLoading();
                i('data> ' + data);
                // ar('تم سحب الحوالة بنجاح');
                $("#modal").modal('hide');
                $("#list").html(data);
                //  var pager = Patterns.Art.Pager;
                // pager.activateList();

            }
            formHelper.onBegin = function (context) {
                return true;
            };
        } catch (e) {
        }
    });


</script>