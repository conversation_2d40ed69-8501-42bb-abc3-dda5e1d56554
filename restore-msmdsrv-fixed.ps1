# Script to restore msmdsrv configuration from backup

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Restore msmdsrv Configuration" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# File paths
$backupConfigPath = "E:\inetpub\OLAP\Config"
$backupIniFile = "$backupConfigPath\msmdsrv.ini"
$backupBakFile = "$backupConfigPath\msmdsrv.bak"

# SQL Server Analysis Services paths
$sqlServerPaths = @(
    "C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Config",
    "C:\Program Files\Microsoft SQL Server\MSAS15.MSSQLSERVER\OLAP\Config",
    "C:\Program Files\Microsoft SQL Server\MSAS13.MSSQLSERVER\OLAP\Config",
    "C:\Program Files\Microsoft SQL Server\MSAS12.MSSQLSERVER\OLAP\Config"
)

Write-Host "`n1. Checking backup files..." -ForegroundColor Yellow

if (Test-Path $backupIniFile) {
    $iniSize = (Get-Item $backupIniFile).Length / 1KB
    Write-Host "Found config file: msmdsrv.ini ($([math]::Round($iniSize, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "Config file not found: $backupIniFile" -ForegroundColor Red
    exit 1
}

if (Test-Path $backupBakFile) {
    $bakSize = (Get-Item $backupBakFile).Length / 1KB
    Write-Host "Found backup file: msmdsrv.bak ($([math]::Round($bakSize, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "Backup file not found: $backupBakFile" -ForegroundColor Yellow
}

Write-Host "`n2. Searching for SQL Server Analysis Services..." -ForegroundColor Yellow

$targetConfigPath = $null
foreach ($path in $sqlServerPaths) {
    if (Test-Path $path) {
        $targetConfigPath = $path
        Write-Host "Found SSAS folder: $path" -ForegroundColor Green
        break
    } else {
        Write-Host "Not found: $path" -ForegroundColor Red
    }
}

if (-not $targetConfigPath) {
    Write-Host "Warning: SQL Server Analysis Services not found" -ForegroundColor Yellow
    Write-Host "Please install SSAS first or specify path manually" -ForegroundColor Gray
    
    $manualPath = Read-Host "Enter SSAS Config folder path (or press Enter to skip)"
    if ($manualPath -and (Test-Path $manualPath)) {
        $targetConfigPath = $manualPath
        Write-Host "Path set: $targetConfigPath" -ForegroundColor Green
    } else {
        Write-Host "Operation cancelled" -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "`n3. Checking current files..." -ForegroundColor Yellow

$currentIniFile = "$targetConfigPath\msmdsrv.ini"
$currentBakFile = "$targetConfigPath\msmdsrv.bak"

if (Test-Path $currentIniFile) {
    $currentSize = (Get-Item $currentIniFile).Length / 1KB
    Write-Host "Current config file exists: msmdsrv.ini ($([math]::Round($currentSize, 2)) KB)" -ForegroundColor Yellow
    
    $overwrite = Read-Host "Do you want to replace it with backup? (y/n)"
    if ($overwrite -ne "y") {
        Write-Host "Operation cancelled" -ForegroundColor Yellow
        exit 0
    }
    
    # Create backup of current file
    $backupCurrent = "$targetConfigPath\msmdsrv.ini.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $currentIniFile $backupCurrent -Force
    Write-Host "Current file backed up: $backupCurrent" -ForegroundColor Green
}

Write-Host "`n4. Copying configuration files..." -ForegroundColor Yellow

try {
    # Copy main config file
    Copy-Item $backupIniFile $currentIniFile -Force
    Write-Host "Copied: msmdsrv.ini" -ForegroundColor Green
    
    # Copy backup file if exists
    if (Test-Path $backupBakFile) {
        Copy-Item $backupBakFile $currentBakFile -Force
        Write-Host "Copied: msmdsrv.bak" -ForegroundColor Green
    }
    
} catch {
    Write-Host "Error copying files: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n5. Analyzing msmdsrv configuration..." -ForegroundColor Yellow

try {
    [xml]$configXml = Get-Content $currentIniFile
    
    $dataDir = $configXml.ConfigurationSettings.DataDir
    $logDir = $configXml.ConfigurationSettings.LogDir
    $backupDir = $configXml.ConfigurationSettings.BackupDir
    $tempDir = $configXml.ConfigurationSettings.TempDir
    
    Write-Host "Configuration info:" -ForegroundColor Cyan
    Write-Host "  Data folder: $dataDir" -ForegroundColor Gray
    Write-Host "  Log folder: $logDir" -ForegroundColor Gray
    Write-Host "  Backup folder: $backupDir" -ForegroundColor Gray
    Write-Host "  Temp folder: $tempDir" -ForegroundColor Gray
    
    # Check folder existence
    $directories = @($dataDir, $logDir, $backupDir, $tempDir)
    foreach ($dir in $directories) {
        if (Test-Path $dir) {
            Write-Host "  Exists: $dir" -ForegroundColor Green
        } else {
            Write-Host "  Missing: $dir" -ForegroundColor Yellow
            Write-Host "    Will be created when SSAS starts" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "Error analyzing config file: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n6. Checking SQL Server Analysis Services..." -ForegroundColor Yellow

$ssasServices = @(
    "MSSQLServerOLAPService",
    "MSOLAP`$MSSQLSERVER17",
    "MSOLAP`$MSSQLSERVER"
)

$foundService = $null
foreach ($serviceName in $ssasServices) {
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            $foundService = $service
            Write-Host "Found SSAS service: $serviceName ($($service.Status))" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "Service not found: $serviceName" -ForegroundColor Red
    }
}

if ($foundService) {
    if ($foundService.Status -eq "Running") {
        Write-Host "Service is running - recommend restart to apply new settings" -ForegroundColor Yellow
        
        $restart = Read-Host "Do you want to restart SSAS service? (y/n)"
        if ($restart -eq "y") {
            try {
                Write-Host "Stopping service..." -ForegroundColor Gray
                Stop-Service -Name $foundService.Name -Force
                Start-Sleep -Seconds 5
                
                Write-Host "Starting service..." -ForegroundColor Gray
                Start-Service -Name $foundService.Name
                
                Write-Host "SSAS service restarted successfully" -ForegroundColor Green
            } catch {
                Write-Host "Error restarting service: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "Service is stopped - can be started now" -ForegroundColor Gray
    }
} else {
    Write-Host "SSAS service not found" -ForegroundColor Yellow
    Write-Host "Please install SQL Server Analysis Services" -ForegroundColor Gray
}

Write-Host "`n7. Creating restore report..." -ForegroundColor Yellow

$report = @"
# msmdsrv Configuration Restore Report
Restore Date: $(Get-Date)

## Restored Files:
- Source: $backupIniFile
- Target: $currentIniFile
- Size: $([math]::Round((Get-Item $currentIniFile).Length / 1KB, 2)) KB

## SSAS Settings:
- Data folder: $dataDir
- Log folder: $logDir  
- Backup folder: $backupDir
- Temp folder: $tempDir

## Service Status:
- Service name: $($foundService.Name)
- Status: $($foundService.Status)

## Next Steps:
1. Start SQL Server Analysis Services
2. Test SSAS connection
3. Restore analytical databases if needed
4. Update connection strings in applications

## Notes:
- Previous settings backed up
- Settings restored from original backup
- May need to update folder paths for new environment
"@

$report | Out-File -FilePath "$targetConfigPath\restore-report.txt" -Encoding UTF8

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "msmdsrv configuration restore completed!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nRestore report saved: $targetConfigPath\restore-report.txt" -ForegroundColor Cyan

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Run database restore script" -ForegroundColor Cyan
Write-Host "2. Update web.config files" -ForegroundColor Cyan
Write-Host "3. Test system functionality" -ForegroundColor Cyan

pause
