﻿@model AppTech.MSMS.Domain.Settings.CashTransSetting

<div class="row">
    <div class="col-xs-12 col-sm-6">
        
      

            @using (Ajax.BeginForm(new AjaxOptions
            {
                OnBegin = "return OnFormBegin()",
                OnSuccess = "onCrudSuccess",
                OnFailure = "onCrudFailure"
            }))
            {
                <table id="search-filter-table" class="table table-responsive borderless">
                    @foreach (var property in ViewData.ModelMetadata.Properties)
                    {

                        if (property.PropertyName.Contains("AccountID"))
                        {

                            <tr>
                                <td><span class="lbl">  @Html.Label(property.DisplayName, new { @class = "control-label col-md-3" })</span></td>
                                <td>  @Html.DropDownList(property.PropertyName, (SelectList)ViewBag.CreditorAccounts, new { @class = "select2" })</td>
                            </tr>
               
                        }
                        else
                        {
                            <tr>
                                <td>        <span class="lbl">  @Html.Label(property.DisplayName) </span></td>
                                <td> @Html.Editor(property.PropertyName) </td>
                            </tr>
                       
                        }
                    }

             
            
                </table>

                <div class="space-10"></div>

                <div class="space-32"></div>
                <div class="space-32"></div>
                <div class="hr hr32 hr-dotted"></div>
            @Html.Partial("_FormAction")
            }

      
    </div>
</div>

<script>
      $(function () {
    $('.select2').css('width', '200px').select2({ allowClear: false });
    $('#select2-multiple-style .btn').on('click',
        function (e) {
            var target = $(this).find('input[type=radio]');
            var which = parseInt(target.val());
            if (which == 2) $('.select2').addClass('tag-input-style');
            else $('.select2').removeClass('tag-input-style');
        });
    });
    function loadFirstList() {
        i('loadDataList');
        fillDataList('PropertyName', '/DirectPayment/CashTransSetting/GetAccounts');
    }
        //loadFirstList();
</script>