-- سكريبت تفريغ قاعدة بيانات نوافذ (nawafd) من البيانات
-- AppTech MSMS - Nawafd Database Cleanup

-- تحذير: هذا السكريبت سيحذف جميع البيانات!
-- تأكد من إنشاء نسخة احتياطية أولاً

USE [nawafd];

PRINT '========================================';
PRINT 'بدء عملية تفريغ قاعدة بيانات نوافذ من البيانات';
PRINT '========================================';

-- إنشاء نسخة احتياطية أولاً (اختياري)
DECLARE @BackupPath NVARCHAR(500) = 'C:\Backup\nawafd_backup_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss') + '.bak';
PRINT 'إنشاء نسخة احتياطية: ' + @BackupPath;

-- تأكد من وجود مجلد النسخ الاحتياطية
EXEC xp_create_subdir 'C:\Backup';

BACKUP DATABASE [nawafd] 
TO DISK = @BackupPath
WITH FORMAT, INIT, NAME = 'nawafd-تفريغ البيانات-نسخة احتياطية';

PRINT 'تم إنشاء النسخة الاحتياطية بنجاح';

-- 1. عرض إحصائيات قبل التفريغ
PRINT '';
PRINT 'إحصائيات قبل التفريغ:';
PRINT '------------------------';

DECLARE @TableName NVARCHAR(128);
DECLARE @SQL NVARCHAR(MAX);
DECLARE @Count INT;

DECLARE table_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
AND TABLE_NAME NOT LIKE 'sys%'
ORDER BY TABLE_NAME;

OPEN table_cursor;
FETCH NEXT FROM table_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'SELECT @Count = COUNT(*) FROM [' + @TableName + ']';
    EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count OUTPUT;
    PRINT @TableName + ': ' + CAST(@Count AS VARCHAR(10)) + ' سجل';
    FETCH NEXT FROM table_cursor INTO @TableName;
END

CLOSE table_cursor;
DEALLOCATE table_cursor;

-- 2. تعطيل جميع القيود المرجعية (Foreign Keys)
PRINT '';
PRINT 'تعطيل القيود المرجعية...';

DECLARE @DisableFK NVARCHAR(MAX) = '';
SELECT @DisableFK = @DisableFK + 'ALTER TABLE [' + SCHEMA_NAME(schema_id) + '].[' + OBJECT_NAME(parent_object_id) + '] NOCHECK CONSTRAINT [' + name + '];' + CHAR(13)
FROM sys.foreign_keys;

IF LEN(@DisableFK) > 0
    EXEC sp_executesql @DisableFK;

PRINT 'تم تعطيل القيود المرجعية';

-- 3. حذف البيانات من الجداول (مع تجنب جداول النظام المهمة)
PRINT '';
PRINT 'حذف البيانات من الجداول...';

-- جداول يجب تجنب حذف بياناتها (إعدادات النظام)
DECLARE @SystemTables TABLE (TableName NVARCHAR(128));
INSERT INTO @SystemTables VALUES 
('Users'), ('Roles'), ('Permissions'), ('Settings'), ('Configuration'),
('SystemSettings'), ('ApplicationSettings'), ('LookupTables');

DECLARE delete_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
AND TABLE_NAME NOT LIKE 'sys%'
AND TABLE_NAME NOT IN (SELECT TableName FROM @SystemTables)
ORDER BY TABLE_NAME;

OPEN delete_cursor;
FETCH NEXT FROM delete_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- حذف البيانات
    SET @SQL = 'DELETE FROM [' + @TableName + ']';
    EXEC sp_executesql @SQL;
    
    -- إعادة تعيين Identity إذا وجد
    IF EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(@TableName) AND is_identity = 1)
    BEGIN
        DBCC CHECKIDENT(@TableName, RESEED, 0);
    END
    
    PRINT 'تم تفريغ جدول: ' + @TableName;
    FETCH NEXT FROM delete_cursor INTO @TableName;
END

CLOSE delete_cursor;
DEALLOCATE delete_cursor;

-- 4. إعادة تفعيل القيود المرجعية
PRINT '';
PRINT 'إعادة تفعيل القيود المرجعية...';

DECLARE @EnableFK NVARCHAR(MAX) = '';
SELECT @EnableFK = @EnableFK + 'ALTER TABLE [' + SCHEMA_NAME(schema_id) + '].[' + OBJECT_NAME(parent_object_id) + '] WITH CHECK CHECK CONSTRAINT [' + name + '];' + CHAR(13)
FROM sys.foreign_keys;

IF LEN(@EnableFK) > 0
    EXEC sp_executesql @EnableFK;

PRINT 'تم إعادة تفعيل القيود المرجعية';

-- 5. تحديث الإحصائيات
PRINT '';
PRINT 'تحديث الإحصائيات...';
EXEC sp_updatestats;

-- 6. تقليص حجم قاعدة البيانات
PRINT '';
PRINT 'تقليص حجم قاعدة البيانات...';
DBCC SHRINKDATABASE([nawafd], 10);

-- 7. عرض إحصائيات بعد التفريغ
PRINT '';
PRINT 'إحصائيات بعد التفريغ:';
PRINT '------------------------';

DECLARE final_cursor CURSOR FOR
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
AND TABLE_NAME NOT LIKE 'sys%'
ORDER BY TABLE_NAME;

OPEN final_cursor;
FETCH NEXT FROM final_cursor INTO @TableName;

WHILE @@FETCH_STATUS = 0
BEGIN
    SET @SQL = 'SELECT @Count = COUNT(*) FROM [' + @TableName + ']';
    EXEC sp_executesql @SQL, N'@Count INT OUTPUT', @Count OUTPUT;
    PRINT @TableName + ': ' + CAST(@Count AS VARCHAR(10)) + ' سجل';
    FETCH NEXT FROM final_cursor INTO @TableName;
END

CLOSE final_cursor;
DEALLOCATE final_cursor;

PRINT '';
PRINT '========================================';
PRINT 'انتهت عملية تفريغ قاعدة البيانات بنجاح!';
PRINT 'النسخة الاحتياطية محفوظة في: ' + @BackupPath;
PRINT '========================================';
