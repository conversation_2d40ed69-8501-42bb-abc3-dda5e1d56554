# سكريبت إعداد الصلاحيات والأمان لنظام AppTech MSMS

Write-Host "إعداد الصلاحيات والأمان..." -ForegroundColor Green

# المجلدات التي تحتاج صلاحيات خاصة
$folders = @(
    "wwwroot\api",
    "wwwroot\client", 
    "wwwroot\portal",
    "wwwroot\api\App_Data",
    "wwwroot\api\Log",
    "wwwroot\api\Photos",
    "wwwroot\api\Updates",
    "wwwroot\client\Photos",
    "wwwroot\portal\Updates"
)

# المستخدمين الذين يحتاجون صلاحيات
$users = @(
    "IIS_IUSRS",
    "IUSR",
    "IIS AppPool\AppTechAPI",
    "IIS AppPool\AppTechClient", 
    "IIS AppPool\AppTechPortal"
)

Write-Host "إعداد صلاحيات المجلدات..." -ForegroundColor Yellow

foreach ($folder in $folders) {
    if (Test-Path $folder) {
        Write-Host "إعداد صلاحيات: $folder" -ForegroundColor Gray
        
        foreach ($user in $users) {
            try {
                # إعطاء صلاحيات كاملة للمجلدات الأساسية
                if ($folder -like "*\api" -or $folder -like "*\client" -or $folder -like "*\portal") {
                    icacls $folder /grant "${user}:(OI)(CI)F" /T /Q 2>$null
                    Write-Host "  ✓ صلاحيات كاملة لـ $user" -ForegroundColor Green
                }
                # صلاحيات قراءة وكتابة للمجلدات الفرعية
                else {
                    icacls $folder /grant "${user}:(OI)(CI)M" /T /Q 2>$null
                    Write-Host "  ✓ صلاحيات تعديل لـ $user" -ForegroundColor Green
                }
            } catch {
                Write-Host "  ✗ فشل في إعطاء صلاحيات لـ $user" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "✗ المجلد غير موجود: $folder" -ForegroundColor Red
        # إنشاء المجلد إذا لم يكن موجوداً
        try {
            New-Item -ItemType Directory -Path $folder -Force | Out-Null
            Write-Host "  ✓ تم إنشاء المجلد: $folder" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ فشل في إنشاء المجلد: $folder" -ForegroundColor Red
        }
    }
}

# إعداد صلاحيات خاصة لملفات معينة
Write-Host "`nإعداد صلاحيات الملفات الخاصة..." -ForegroundColor Yellow

$specialFiles = @(
    "wwwroot\api\maincs.erp",
    "wwwroot\api\license.lic",
    "wwwroot\client\maincs.erp",
    "wwwroot\portal\license.lic"
)

foreach ($file in $specialFiles) {
    if (Test-Path $file) {
        Write-Host "حماية ملف: $file" -ForegroundColor Gray
        # إزالة الصلاحيات العامة وإعطاء صلاحيات محددة فقط
        icacls $file /inheritance:r /Q 2>$null
        icacls $file /grant "SYSTEM:(F)" /Q 2>$null
        icacls $file /grant "Administrators:(F)" /Q 2>$null
        icacls $file /grant "IIS_IUSRS:(R)" /Q 2>$null
        Write-Host "  ✓ تم تأمين الملف" -ForegroundColor Green
    }
}

# إعداد صلاحيات Temporary ASP.NET Files
Write-Host "`nإعداد صلاحيات Temporary ASP.NET Files..." -ForegroundColor Yellow
$tempAspNetPath = "$env:WINDIR\Microsoft.NET\Framework64\v4.0.30319\Temporary ASP.NET Files"
if (Test-Path $tempAspNetPath) {
    foreach ($user in $users) {
        try {
            icacls $tempAspNetPath /grant "${user}:(OI)(CI)F" /T /Q 2>$null
            Write-Host "  ✓ صلاحيات Temporary ASP.NET Files لـ $user" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ فشل في إعطاء صلاحيات Temporary ASP.NET Files" -ForegroundColor Red
        }
    }
}

# تفعيل ميزات الأمان في IIS
Write-Host "`nتكوين أمان IIS..." -ForegroundColor Yellow

$sites = @("AppTechAPI", "AppTechClient", "AppTechPortal")
foreach ($site in $sites) {
    if (Get-Website -Name $site -ErrorAction SilentlyContinue) {
        Write-Host "تكوين أمان الموقع: $site" -ForegroundColor Gray
        
        # تفعيل Request Filtering
        Set-WebConfiguration -Filter "system.webServer/security/requestFiltering" -Value @{allowDoubleEscaping=$false; allowHighBitCharacters=$false} -PSPath "IIS:\Sites\$site"
        
        # تحديد حد أقصى لحجم الطلبات
        Set-WebConfiguration -Filter "system.webServer/security/requestFiltering/requestLimits" -Value @{maxAllowedContentLength=52428800} -PSPath "IIS:\Sites\$site"  # 50MB
        
        Write-Host "  ✓ تم تكوين أمان الموقع" -ForegroundColor Green
    }
}

Write-Host "`nانتهى إعداد الصلاحيات والأمان" -ForegroundColor Green
Write-Host "ملاحظة: قد تحتاج إلى إعادة تشغيل IIS لتطبيق جميع التغييرات" -ForegroundColor Yellow
