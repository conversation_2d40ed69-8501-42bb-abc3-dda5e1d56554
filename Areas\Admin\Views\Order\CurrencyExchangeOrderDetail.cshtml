﻿@model AppTech.MSMS.Domain.Models.CurrencyExchange

<div>


    <div class="space-6"></div>
    <span class="label label-info arrowed-in-right arrowed"> بيانات الطلب</span>
    <div class="space-6"></div>
    @Html.HiddenFor(x => x.Type)


    @if (Model.Type)
    {
        <div class="profile-info-row">
            <div class="profile-info-name"> بيع عملة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.Currency.Name) </span>
            </div>
        </div>

    }
    else
    {
        <div class="profile-info-row">
            <div class="profile-info-name"> شراء عملة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.Currency.Name) </span>
            </div>
        </div>

    }

    <div class="profile-info-row">
        <div class="profile-info-name"> بمبلغ </div>

        <div class="profile-info-value">
            <span class="editable" id="Amount"> @Html.DisplayFor(model => model.Amount)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> سعر التحويل </div>

        <div class="profile-info-value">
            <span class="editable" id="Amount"> @Html.DisplayFor(model => model.ExchangePrice)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> المبلغ المقابل </div>

        <div class="profile-info-value">
            <span class="editable" id="Amount"> @Html.DisplayFor(model => model.ExchangeAmount)</span>
        </div>
    </div>



    <div class="profile-info-row">
        <div class="profile-info-name"> الملاحظات </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.Note)</span>
        </div>
    </div>


</div>


<script>
    $('#bootbox-modify-amount').hide();
</script>
@*<script>
    //  $('#bootbox-relay').hide();
    function saveTransNumber() {

        var id = $("#mr_id").val();
        var num = $("#TransferNumber").val();
        $.ajax({
            url: '@Url.Action("SaveTransNumber", "Order")',
            data: { id: id, num: num },
            success: function(data) {

                if (data.Success) {
                    alert("تم حفظ رقم الحوالة بنجاح");
                    $('.trans-num').val(num);
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }

    function changeCommission() {
        i('changerExchanger');
        var id = $("#mr_id").val();
        var commission = $("#Commission").val();
        var num = $("#CommissionCurrencyID").children("option:selected").val();
        $.ajax({
            url: '@Url.Action("ChangeCommission", "Order")',
            data: { id: id, curId: num, amount: commission },
            success: function(data) {

                if (data.Success) {
                    alert("تم تعديل العمولة بنجاح");
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }

    function changerExchanger() {

        i('changerExchanger');
        var id = $("#mr_id").val();
        var num = $("#ExchangerID").children("option:selected").val();
        $.ajax({
            url: '@Url.Action("ChangeExchanger", "Order")',
            data: { id: id, exchnagerId: num },
            success: function(data) {

                if (data.Success) {
                    alert("تم تعديل الشركة بنجاح");
                    //  $('.trans-num').val(num);
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }
</script>*@
