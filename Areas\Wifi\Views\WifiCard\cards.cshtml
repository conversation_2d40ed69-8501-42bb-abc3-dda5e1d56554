﻿@model IEnumerable<AppTech.MSMS.Domain.Models.WifiCard>
@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}

<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.FactionID','@ViewBag.ProviderID')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span>إضافة كرت جديد</span>
    </a>
</p>
<div id="list">
    @Html.Partial("_cards")
</div>
    @Html.Partial("_Modal")

    <script>
        function openModal(id,ProviderID) {
            i('open modal id' + id);
            openViewAsModal('Wifi/WifiCard/AddOrEditCards?ID=' + id +'&ProviderID='+ProviderID);
        }
    </script>
