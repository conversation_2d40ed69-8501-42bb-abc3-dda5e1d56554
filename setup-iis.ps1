# سكريبت تفعيل وإعداد IIS لنظام AppTech MSMS
# يجب تشغيله كمدير

Write-Host "بدء تفعيل IIS..." -ForegroundColor Green

# تفعيل IIS والميزات المطلوبة
try {
    Write-Host "تفعيل IIS Web Server Role..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All -NoRestart
    
    Write-Host "تفعيل ASP.NET 4.6..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-ASPNET45 -All -NoRestart
    
    Write-Host "تفعيل IIS Management Console..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-ManagementConsole -All -NoRestart
    
    Write-Host "تفعيل Default Document..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-DefaultDocument -All -NoRestart
    
    Write-Host "تفعيل Directory Browsing..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-DirectoryBrowsing -All -NoRestart
    
    Write-Host "تفعيل HTTP Errors..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpErrors -All -NoRestart
    
    Write-Host "تفعيل Static Content..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-StaticContent -All -NoRestart
    
    Write-Host "تفعيل HTTP Logging..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpLogging -All -NoRestart
    
    Write-Host "تفعيل Request Filtering..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-RequestFiltering -All -NoRestart
    
    Write-Host "تفعيل Windows Authentication..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WindowsAuthentication -All -NoRestart
    
    Write-Host "تم تفعيل IIS بنجاح!" -ForegroundColor Green
    
    # بدء خدمة IIS
    Write-Host "بدء خدمة IIS..." -ForegroundColor Yellow
    Start-Service W3SVC -ErrorAction SilentlyContinue
    Set-Service W3SVC -StartupType Automatic
    
    Write-Host "تم بدء خدمة IIS بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "خطأ في تفعيل IIS: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# التحقق من حالة IIS
Write-Host "التحقق من حالة IIS..." -ForegroundColor Yellow
$iisService = Get-Service W3SVC -ErrorAction SilentlyContinue
if ($iisService -and $iisService.Status -eq "Running") {
    Write-Host "IIS يعمل بنجاح!" -ForegroundColor Green
} else {
    Write-Host "IIS غير مُفعل أو لا يعمل" -ForegroundColor Red
}

Write-Host "انتهى إعداد IIS" -ForegroundColor Green
