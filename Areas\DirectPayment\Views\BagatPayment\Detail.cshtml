﻿@model AppTech.MSMS.Domain.Models.BagatPayment

<div>
    <h4>تفاصيل العملية</h4>
    <hr/>
    <dl class="dl-horizontal">
        <dt>
            @Html.DisplayNameFor(model => model.Number)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Number)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ServiceID)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ServiceID)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SubscriberNumber)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SubscriberNumber)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Amount)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Amount)
        </dd>


        <dt>
            @Html.DisplayNameFor(model => model.LineType)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.LineType)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Date)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Date)
        </dd>


        <dt>
            @Html.DisplayNameFor(model => model.Status)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Status)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.Note)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Note)
        </dd>


        <dt>
            @Html.DisplayNameFor(model => model.AccountID)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Account.Name)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.RefNumber)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.RefNumber)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.TransactionID)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TransactionID)
        </dd>

        @*<dt>
            @Html.DisplayNameFor(model => model.ProviderID)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.TopupProvider.Name)
        </dd>*@


        <dt>
            @Html.DisplayNameFor(model => model.Channel)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.Channel)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CreatedBy)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CreatedBy)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.CreatedTime)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.CreatedTime)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ProviderRM)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ProviderRM)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.ProviderPrice)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.ProviderPrice)
        </dd>

        <dt>
            @Html.DisplayNameFor(model => model.SubNote)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.SubNote)
        </dd>


        <dt>
            @Html.DisplayNameFor(model => model.UniqueNo)
        </dt>

        <dd>
            @Html.DisplayFor(model => model.UniqueNo)
        </dd>

    </dl>
</div>
@*<p>
    @Html.ActionLink("Edit", "Edit", new { id = Model.ID }) |
    @Html.ActionLink("Back to List", "Index")
</p>*@