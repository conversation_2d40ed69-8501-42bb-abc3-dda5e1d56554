﻿@model AppTech.MSMS.Domain.Reports.Models.InvoiceModel

@{
    ViewBag.Title = "تقرير المشتركين";
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}

<select id="AccountID" name="AccountID" class="select2" placeholder="كافة المشتركين"></select>

<div class="space-6"></div>
<span class="lbl">الحالة السداد</span>
@Html.EnumDropDownListFor(model => model.PaidState)

<div class="space-6"></div>
<span class="lbl"> نوع التقرير</span>
@Html.EnumDropDownListFor(m => m.Type)
<div class="space-6"></div>
<script>
    $(function() {
        console.log('topupreport load');
        AjaxCall('/Print/GetSubscribers').done(function(response) {
            console.log('get parties');
            if (response.length > 0) {
                $('#AccountID').html('');
                var options = '<option value="0">كافة المشتركين</option>';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].AccountID + '">' + response[i].Name + '</option>';
                }
                $('#AccountID').append(options);

            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });

        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function(e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>