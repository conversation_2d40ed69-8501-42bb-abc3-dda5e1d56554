﻿@model AppTech.MSMS.Domain.Models.Product

@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}
<div>
    <div class="form-group">
        @Html.LabelFor(model => model.CategoryID, new { @class = "control-label col-md-2" })
        <div class="col-md-3">
            @Html.DropDownListFor(model => model.CategoryID, (SelectList)ViewBag.ProductCategorys, new { htmlAttributes = new { @class = "form-control" } })
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.Title, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Title, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Title)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Description)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
        <div class="col-md-3">
            @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
        </div>

        @Html.LabelFor(model => model.Active, new { @class = "control-label col-md-2" })
        <div class="col-md-3">
            @Html.EditorFor(model => model.Active, new { @style = "margin: 12px -17px 0 0;" })
            @Html.ValidationMessageFor(model => model.Active)
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.CostPrice, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.CostPrice, new { htmlAttributes = new { @class = "form-control" } })

            @Html.ValidationMessageFor(model => model.CostPrice)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.SoldOut, new { @class = "control-label col-md-2" })
        <div class="col-md-1">
            @Html.EditorFor(model => model.SoldOut)
            @Html.ValidationMessageFor(model => model.SoldOut)
        </div>

        @Html.LabelFor(model => model.IsNew, new { @class = "control-label col-md-2" })
        <div class="col-md-1">
            @Html.EditorFor(model => model.IsNew)
            @Html.ValidationMessageFor(model => model.IsNew)
        </div>

        @Html.LabelFor(model => model.Limited, new { @class = "control-label col-md-2" })
        <div class="col-md-1">
            @Html.EditorFor(model => model.Limited)
            @Html.ValidationMessageFor(model => model.Limited)
        </div>

        @Html.LabelFor(model => model.IsSpecail, new { @class = "control-label col-md-2" })
        <div class="col-md-1">
            @Html.EditorFor(model => model.IsSpecail)
            @Html.ValidationMessageFor(model => model.IsSpecail)
        </div>

    </div>


    <div class="form-group">
        @*<label class="col-sm-2 control-label">صوره الشعار</label>*@
        @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
        <div style="position: relative;">
            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
        </div>

        <img class="img-thumbnail" width="150" height="150" id="preview"
             src="@Url.Action("GetImage", "Product",
                  new {Model.ID})" />

    </div>

</div>


<script>

    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });
</script>