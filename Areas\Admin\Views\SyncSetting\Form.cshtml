﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Sync.SyncSetting

@using (Ajax.BeginForm(new AjaxOptions
{
    OnBegin = "return OnFormBegin()",
    OnSuccess = "onCrudSuccess",
    OnFailure = "onCrudFailure",
    LoadingElementId = "formloader"
}))
{
    <div class="form-group">
        <div class="col-md-12">
            @Html.Label("حساب عمولات الخدمات", new { @class = "control-label col-md-2" })
            @Html.Obout(new ComboBox("TopupAccountID")
            {
                Width = 300,
                SelectedValue = Model.TopupAccountID == 0 ? null : Model.TopupAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains
            })

            @Html.ValidationMessageFor(model => model.TopupAccountID)
        </div>
    </div>

    <div class="form-group">
        <div class="col-md-12">
            @Html.Label("حساب العمولات الحوالات", new { @class = "control-label col-md-2" })
            @Html.Obout(new ComboBox("CommissionAccountID")
            {
                Width = 300,
                SelectedValue = Model.CommissionAccountID == 0 ? null : Model.CommissionAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains
            })

            @Html.ValidationMessageFor(model => model.CommissionAccountID)
        </div>
    </div>

    <div class="form-group">
        <div class="col-md-12">
            @Html.Label("حساب الأيداع النقدي", new { @class = "control-label col-md-2" })
            @Html.Obout(new ComboBox("CashDepositAccountID")
            {
                Width = 300,
                SelectedValue = Model.CashDepositAccountID == 0 ? null : Model.CashDepositAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains
            })

            @Html.ValidationMessageFor(model => model.CashDepositAccountID)
        </div>
    </div>




    <div class="space-10"></div>

    <div class="space-32"></div>
    <div class="space-32"></div>
    <div class="hr hr32 hr-dotted"></div>
    @Html.Partial("_FormAction")
}