-- سكريبت تفريغ قاعدة البيانات من البيانات مع الاحتفاظ بالهيكل
-- AppTech MSMS Database Data Cleanup

-- تحذير: هذا السكريبت سيحذف جميع البيانات!
-- تأكد من إنشاء نسخة احتياطية أولاً

USE [MSDBData]; -- غير اسم قاعدة البيانات حسب الحاجة

PRINT 'بدء عملية تفريغ قاعدة البيانات من البيانات...';

-- 1. تعطيل جميع القيود المرجعية (Foreign Keys)
PRINT 'تعطيل القيود المرجعية...';
EXEC sp_msforeachtable "ALTER TABLE ? NOCHECK CONSTRAINT all";

-- 2. حذف البيانات من جميع الجداول (عدا جداول النظام)
PRINT 'حذف البيانات من الجداول...';

-- حذف البيانات مع الاحتفاظ بـ Identity
EXEC sp_msforeachtable "
    IF OBJECTPROPERTY(object_id('?'), 'TableHasIdentity') = 1
        DBCC CHECKIDENT ('?', RESEED, 0)
    DELETE FROM ?
";

-- 3. إعادة تفعيل القيود المرجعية
PRINT 'إعادة تفعيل القيود المرجعية...';
EXEC sp_msforeachtable "ALTER TABLE ? WITH CHECK CHECK CONSTRAINT all";

-- 4. تحديث الإحصائيات
PRINT 'تحديث الإحصائيات...';
EXEC sp_updatestats;

-- 5. تقليص حجم قاعدة البيانات
PRINT 'تقليص حجم قاعدة البيانات...';
DBCC SHRINKDATABASE([MSDBData], 10);

PRINT 'انتهت عملية تفريغ قاعدة البيانات بنجاح!';

-- عرض عدد السجلات في كل جدول للتأكد
PRINT 'فحص عدد السجلات في الجداول:';
EXEC sp_msforeachtable "
    DECLARE @count INT;
    SELECT @count = COUNT(*) FROM ?;
    PRINT '? : ' + CAST(@count AS VARCHAR(10)) + ' سجل';
";
