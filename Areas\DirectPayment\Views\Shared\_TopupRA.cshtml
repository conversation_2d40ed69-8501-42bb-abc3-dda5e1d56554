﻿@using AppTech.MSMS.Web.Code.HtmlHelpers
@model AppTech.MSMS.Web.Models.ActionModel
    <div class="inline pos-rel">
        <div class="btn-group">
            @Html.ActionButton((string)ViewBag.PageName, "Show", "", "eye", "detail", "التفاصيل", false)
            @Html.ActionButton((string)ViewBag.PageName, "CHECK", "", "refresh", "check", "التحقق", false)
            @{
                if (Model.Row["الحالة"].ToString().Equals("مرحلة"))
                {
                    @Html.ActionButton((string)ViewBag.PageName, "CANCEL", "", "remove red", "cancel", "إلغاء", true)
                    @Html.ActionButton((string)ViewBag.PageName, "DEPEND", "", "check", "depend", "أعتماد", true)
                }

                else if (Model.Row["الحالة"].ToString().Equals("ملغية") || Model.Row["الحالة"].ToString().Equals("معكوسة"))
                {
                    @Html.ActionButton((string)ViewBag.PageName, "RELAY", "", "check-circle green", "relay", "ترحيل", true)
                    @Html.ActionButton((string)ViewBag.PageName, "DELETE", "", "trash-o", "delete-record", "حذف ")
                }
                else
                {
                    @Html.ActionButton((string)ViewBag.PageName, "RELAY", "", "check-circle green", "relay", "ترحيل", true)
                    @Html.ActionButton((string)ViewBag.PageName, "CANCEL", "", "remove red", "cancel", "إلغاء", true)
                }
            }

        </div>
        </div>
