USE [2022]
GO

SELECT 
dbo.Client.ID, 
CAST(dbo.Client.RowVersion AS Bigint) AS RowVersion ,
dbo.Client.Number AS [??? ??????], 
dbo.Client.Name AS [??? ??????], 
CASE WHEN Client.Status = 1 THEN '???' 
	 WHEN Client.Status = 0 THEN '??? ???????' 
	 WHEN Client.Status = 2 THEN '????' 
END AS ??????, 
dbo.Client.AgentID,
dbo.Agent.Name AS ??????,
dbo.Client.BranchID,
Branch_BranchID.Name AS ?????, 
dbo.Client.CreatedTime AS [??? ???????], 
UserInfo_CreatedBy.UserName, 
dbo.Client.Status, 
dbo.Client.Type, 
dbo.Client.AccountID, 
dbo.Client.IsActive,
FROM dbo.Client WITH (nolock) INNER JOIN
dbo.UserInfo AS UserInfo_CreatedBy ON dbo.Client.CreatedBy = UserInfo_CreatedBy.ID LEFT OUTER JOIN
dbo.Agent ON dbo.Client.AgentID = dbo.Agent.ID INNER JOIN
dbo.Branch AS Branch_BranchID ON dbo.Client.BranchID = Branch_BranchID.ID

GO


