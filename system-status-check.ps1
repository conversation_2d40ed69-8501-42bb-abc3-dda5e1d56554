# System Status Check - AppTech MSMS

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    AppTech MSMS System Status Check" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n1. Checking copied files..." -ForegroundColor Yellow

# Check TopupProcessor
$topupPath = "E:\inetpub\TopupProcessor"
if (Test-Path $topupPath) {
    $topupFiles = Get-ChildItem $topupPath -File
    Write-Host "TopupProcessor: $($topupFiles.Count) files found" -ForegroundColor Green
    
    $requiredFiles = @("TopupInspector.exe", "AppTech.BusinessLogic.dll", "AppTech.Common.dll", "AppTech.Data.dll")
    foreach ($file in $requiredFiles) {
        if (Test-Path "$topupPath\$file") {
            Write-Host "  ✓ $file" -ForegroundColor Green
        } else {
            Write-Host "  ✗ $file (missing)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "TopupProcessor: Not found" -ForegroundColor Red
}

# Check Data files
$dataPath = "E:\inetpub\Data"
if (Test-Path $dataPath) {
    $csvFiles = Get-ChildItem $dataPath -Filter "*.csv"
    Write-Host "Data files: $($csvFiles.Count) CSV files found" -ForegroundColor Green
    
    # Check important files
    $importantFiles = @("Account.csv", "Agent.csv", "Branch.csv", "UserInfo.csv")
    foreach ($file in $importantFiles) {
        $filePath = "$dataPath\$file"
        if (Test-Path $filePath) {
            $size = (Get-Item $filePath).Length / 1KB
            Write-Host "  ✓ $file ($([math]::Round($size, 2)) KB)" -ForegroundColor Green
        } else {
            Write-Host "  ✗ $file (missing)" -ForegroundColor Red
        }
    }
} else {
    Write-Host "Data folder: Not found" -ForegroundColor Red
}

# Check SQL files
$sqlPath = "E:\inetpub\SQL"
if (Test-Path $sqlPath) {
    $sqlFiles = Get-ChildItem $sqlPath -Filter "*.sql"
    Write-Host "SQL files: $($sqlFiles.Count) files found" -ForegroundColor Green
} else {
    Write-Host "SQL folder: Not found" -ForegroundColor Red
}

# Check OLAP
$olapPath = "E:\inetpub\OLAP"
if (Test-Path $olapPath) {
    Write-Host "OLAP folder: Found" -ForegroundColor Green
    
    $msmdsrvIni = "$olapPath\Config\msmdsrv.ini"
    $msmdsrvBak = "$olapPath\Config\msmdsrv.bak"
    
    if (Test-Path $msmdsrvIni) {
        $iniSize = (Get-Item $msmdsrvIni).Length / 1KB
        Write-Host "  ✓ msmdsrv.ini ($([math]::Round($iniSize, 2)) KB)" -ForegroundColor Green
    } else {
        Write-Host "  ✗ msmdsrv.ini (missing)" -ForegroundColor Red
    }
    
    if (Test-Path $msmdsrvBak) {
        Write-Host "  ✓ msmdsrv.bak" -ForegroundColor Green
    } else {
        Write-Host "  ✗ msmdsrv.bak (missing)" -ForegroundColor Red
    }
} else {
    Write-Host "OLAP folder: Not found" -ForegroundColor Red
}

Write-Host "`n2. Checking database files..." -ForegroundColor Yellow

$mdfFile = "E:\MSSQLDATA\nawafd.mdf"
$ldfFile = "E:\MSSQLDATA\nawafd_0.ldf"

if (Test-Path $mdfFile) {
    $mdfSize = (Get-Item $mdfFile).Length / 1MB
    Write-Host "nawafd.mdf: Found ($([math]::Round($mdfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "nawafd.mdf: Not found" -ForegroundColor Red
}

if (Test-Path $ldfFile) {
    $ldfSize = (Get-Item $ldfFile).Length / 1MB
    Write-Host "nawafd_0.ldf: Found ($([math]::Round($ldfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "nawafd_0.ldf: Not found" -ForegroundColor Yellow
}

Write-Host "`n3. Checking SQL Server..." -ForegroundColor Yellow

$sqlInstances = @("localhost", ".\SQLEXPRESS", "localhost\SQLEXPRESS", ".\MSSQLSERVER17")
$workingInstance = $null

foreach ($instance in $sqlInstances) {
    try {
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION" -ErrorAction Stop -Timeout 5
        if ($result) {
            $workingInstance = $instance
            Write-Host "SQL Server: $instance (Working)" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "SQL Server: $instance (Not responding)" -ForegroundColor Red
    }
}

if ($workingInstance) {
    # Check nawafd database
    try {
        $dbCheck = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = 'nawafd'" -ErrorAction Stop
        if ($dbCheck) {
            Write-Host "nawafd database: Attached and accessible" -ForegroundColor Green
            
            # Check table count
            $tableCount = Invoke-Sqlcmd -ServerInstance $workingInstance -Database "nawafd" -Query "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'" -ErrorAction SilentlyContinue
            if ($tableCount) {
                Write-Host "  Tables: $($tableCount.TableCount) found" -ForegroundColor Green
            }
        } else {
            Write-Host "nawafd database: Not found" -ForegroundColor Red
        }
    } catch {
        Write-Host "nawafd database: Error checking - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n4. Checking web applications..." -ForegroundColor Yellow

$webConfigFiles = Get-ChildItem -Path "E:\inetpub" -Name "web.config" -Recurse -ErrorAction SilentlyContinue
Write-Host "Web.config files: $($webConfigFiles.Count) found" -ForegroundColor Green

# Check IIS
try {
    $iisFeature = Get-WindowsOptionalFeature -Online -FeatureName "IIS-WebServerRole" -ErrorAction SilentlyContinue
    if ($iisFeature -and $iisFeature.State -eq "Enabled") {
        Write-Host "IIS: Installed and enabled" -ForegroundColor Green
    } else {
        Write-Host "IIS: Not installed or disabled" -ForegroundColor Yellow
    }
} catch {
    Write-Host "IIS: Unable to check status" -ForegroundColor Yellow
}

Write-Host "`n5. Checking services..." -ForegroundColor Yellow

# Check SQL Server services
$sqlServices = @("MSSQLSERVER", "SQLSERVERAGENT", "MSSQL`$SQLEXPRESS", "SQLAgent`$SQLEXPRESS")
foreach ($serviceName in $sqlServices) {
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            $status = if ($service.Status -eq "Running") { "Running" } else { "Stopped" }
            $color = if ($service.Status -eq "Running") { "Green" } else { "Yellow" }
            Write-Host "$serviceName: $status" -ForegroundColor $color
        }
    } catch {
        # Service not found, skip
    }
}

# Check SSAS services
$ssasServices = @("MSSQLServerOLAPService", "MSOLAP`$MSSQLSERVER17", "MSOLAP`$MSSQLSERVER")
foreach ($serviceName in $ssasServices) {
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            $status = if ($service.Status -eq "Running") { "Running" } else { "Stopped" }
            $color = if ($service.Status -eq "Running") { "Green" } else { "Yellow" }
            Write-Host "$serviceName: $status" -ForegroundColor $color
        }
    } catch {
        # Service not found, skip
    }
}

Write-Host "`n6. System readiness assessment..." -ForegroundColor Yellow

$readinessScore = 0
$maxScore = 10

# Check criteria
if (Test-Path "E:\inetpub\TopupProcessor\TopupInspector.exe") { $readinessScore++ }
if (Test-Path "E:\inetpub\Data\Account.csv") { $readinessScore++ }
if (Test-Path "E:\inetpub\OLAP\Config\msmdsrv.ini") { $readinessScore++ }
if (Test-Path $mdfFile) { $readinessScore++ }
if ($workingInstance) { $readinessScore += 2 }
if ($dbCheck) { $readinessScore += 2 }
if ($webConfigFiles.Count -gt 0) { $readinessScore++ }
if ($iisFeature -and $iisFeature.State -eq "Enabled") { $readinessScore++ }

$percentage = ($readinessScore / $maxScore) * 100
$color = if ($percentage -ge 80) { "Green" } elseif ($percentage -ge 60) { "Yellow" } else { "Red" }

Write-Host "`nSystem Readiness: $readinessScore/$maxScore ($percentage%)" -ForegroundColor $color

Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "System Status Check Completed" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan

Write-Host "`nNext steps based on readiness:" -ForegroundColor Yellow
if ($percentage -ge 80) {
    Write-Host "✅ System is ready for testing" -ForegroundColor Green
    Write-Host "1. Test TopupProcessor functionality" -ForegroundColor Cyan
    Write-Host "2. Test web applications" -ForegroundColor Cyan
    Write-Host "3. Verify data integrity" -ForegroundColor Cyan
} elseif ($percentage -ge 60) {
    Write-Host "⚠️ System needs minor fixes" -ForegroundColor Yellow
    Write-Host "1. Run missing restore scripts" -ForegroundColor Cyan
    Write-Host "2. Update web.config files" -ForegroundColor Cyan
    Write-Host "3. Start required services" -ForegroundColor Cyan
} else {
    Write-Host "❌ System needs major work" -ForegroundColor Red
    Write-Host "1. Complete file copying" -ForegroundColor Cyan
    Write-Host "2. Restore database" -ForegroundColor Cyan
    Write-Host "3. Configure services" -ForegroundColor Cyan
}

pause
