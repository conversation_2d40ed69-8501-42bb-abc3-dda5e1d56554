# سكريبت إنشاء المواقع لنظام AppTech MSMS
Import-Module WebAdministration

Write-Host "إنشاء المواقع..." -ForegroundColor Green

$basePath = "E:\inetpub\wwwroot"

# حذف المواقع الموجودة إن وجدت
$sitesToRemove = @("AppTechAPI", "AppTechClient", "AppTechPortal")
foreach ($site in $sitesToRemove) {
    if (Get-Website -Name $site -ErrorAction SilentlyContinue) {
        Write-Host "حذف الموقع الموجود: $site" -ForegroundColor Yellow
        Remove-Website -Name $site
    }
}

# إنشاء موقع API
$apiSiteName = "AppTechAPI"
$apiPath = "$basePath\api"
Write-Host "إنشاء موقع: $apiSiteName" -ForegroundColor Yellow
Write-Host "المسار: $apiPath" -ForegroundColor Gray

if (Test-Path $apiPath) {
    New-Website -Name $apiSiteName -Port 80 -PhysicalPath $apiPath -ApplicationPool "AppTechAPI"
    Write-Host "✓ تم إنشاء موقع API على المنفذ 80" -ForegroundColor Green
} else {
    Write-Host "✗ مجلد API غير موجود: $apiPath" -ForegroundColor Red
}

# إنشاء موقع Client
$clientSiteName = "AppTechClient"
$clientPath = "$basePath\client"
Write-Host "إنشاء موقع: $clientSiteName" -ForegroundColor Yellow
Write-Host "المسار: $clientPath" -ForegroundColor Gray

if (Test-Path $clientPath) {
    New-Website -Name $clientSiteName -Port 8080 -PhysicalPath $clientPath -ApplicationPool "AppTechClient"
    Write-Host "✓ تم إنشاء موقع Client على المنفذ 8080" -ForegroundColor Green
} else {
    Write-Host "✗ مجلد Client غير موجود: $clientPath" -ForegroundColor Red
}

# إنشاء موقع Portal
$portalSiteName = "AppTechPortal"
$portalPath = "$basePath\portal"
Write-Host "إنشاء موقع: $portalSiteName" -ForegroundColor Yellow
Write-Host "المسار: $portalPath" -ForegroundColor Gray

if (Test-Path $portalPath) {
    New-Website -Name $portalSiteName -Port 8081 -PhysicalPath $portalPath -ApplicationPool "AppTechPortal"
    Write-Host "✓ تم إنشاء موقع Portal على المنفذ 8081" -ForegroundColor Green
} else {
    Write-Host "✗ مجلد Portal غير موجود: $portalPath" -ForegroundColor Red
}

# إعداد Default Document لجميع المواقع
$defaultDocs = @("Default.aspx", "default.htm", "index.html", "Global.asax")
foreach ($site in @($apiSiteName, $clientSiteName, $portalSiteName)) {
    if (Get-Website -Name $site -ErrorAction SilentlyContinue) {
        Write-Host "إعداد Default Documents للموقع: $site" -ForegroundColor Yellow
        Clear-WebConfiguration -Filter "system.webServer/defaultDocument/files" -PSPath "IIS:\Sites\$site"
        foreach ($doc in $defaultDocs) {
            Add-WebConfiguration -Filter "system.webServer/defaultDocument/files" -Value @{value=$doc} -PSPath "IIS:\Sites\$site"
        }
    }
}

Write-Host "`nتم إنشاء جميع المواقع بنجاح!" -ForegroundColor Green

# عرض المواقع المُنشأة
Write-Host "`nالمواقع المُنشأة:" -ForegroundColor Cyan
Get-Website | Where-Object {$_.Name -like "AppTech*"} | Format-Table Name, State, PhysicalPath, @{Name="Bindings";Expression={$_.Bindings.Collection.bindingInformation}}

Write-Host "`nروابط الوصول:" -ForegroundColor Cyan
Write-Host "API: http://localhost/" -ForegroundColor White
Write-Host "Client: http://localhost:8080/" -ForegroundColor White
Write-Host "Portal: http://localhost:8081/" -ForegroundColor White
