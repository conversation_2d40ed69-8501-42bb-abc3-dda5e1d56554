-- سكريبت إعادة إرفاق قاعدة بيانات nawafd بعد النسخ
-- للسيرفر القديم بعد انتهاء النسخ

USE master;

PRINT '========================================';
PRINT 'إعادة إرفاق قاعدة بيانات nawafd';
PRINT '========================================';

-- مسارات الملفات الأصلية
DECLARE @MdfPath NVARCHAR(500) = 'E:\MSSQLDATA\nawafd.mdf';
DECLARE @LdfPath NVARCHAR(500) = 'E:\MSSQLDATA\nawafd_0.ldf';

-- 1. التحقق من وجود الملفات
PRINT '';
PRINT '1. فحص وجود الملفات:';
PRINT '---------------------';

DECLARE @MdfExists BIT = 0;
DECLARE @LdfExists BIT = 0;

-- فحص ملف MDF
BEGIN TRY
    EXEC xp_fileexist @MdfPath, @MdfExists OUTPUT;
    IF @MdfExists = 1
        PRINT '✓ ملف MDF موجود: ' + @MdfPath;
    ELSE
        PRINT '✗ ملف MDF غير موجود: ' + @MdfPath;
END TRY
BEGIN CATCH
    PRINT '⚠ خطأ في فحص ملف MDF';
END CATCH

-- فحص ملف LDF
BEGIN TRY
    EXEC xp_fileexist @LdfPath, @LdfExists OUTPUT;
    IF @LdfExists = 1
        PRINT '✓ ملف LDF موجود: ' + @LdfPath;
    ELSE
        PRINT '⚠ ملف LDF غير موجود: ' + @LdfPath;
END TRY
BEGIN CATCH
    PRINT '⚠ خطأ في فحص ملف LDF';
END CATCH

-- 2. إرفاق قاعدة البيانات
PRINT '';
PRINT '2. إرفاق قاعدة البيانات:';
PRINT '-------------------------';

IF @MdfExists = 1
BEGIN
    BEGIN TRY
        IF @LdfExists = 1
        BEGIN
            -- إرفاق مع ملف السجل
            PRINT 'إرفاق مع ملف السجل...';
            CREATE DATABASE [nawafd] ON 
            (FILENAME = @MdfPath),
            (FILENAME = @LdfPath)
            FOR ATTACH;
            
            PRINT '✓ تم إرفاق قاعدة البيانات مع ملف السجل';
        END
        ELSE
        BEGIN
            -- إرفاق بدون ملف السجل (إعادة إنشاء السجل)
            PRINT 'إرفاق بدون ملف السجل (إعادة إنشاء)...';
            CREATE DATABASE [nawafd] ON 
            (FILENAME = @MdfPath)
            FOR ATTACH_REBUILD_LOG;
            
            PRINT '✓ تم إرفاق قاعدة البيانات مع إعادة إنشاء ملف السجل';
        END
    END TRY
    BEGIN CATCH
        PRINT '✗ خطأ في إرفاق قاعدة البيانات:';
        PRINT ERROR_MESSAGE();
    END CATCH
END
ELSE
BEGIN
    PRINT '✗ لا يمكن إرفاق قاعدة البيانات - ملف MDF غير موجود';
END

-- 3. التحقق من نجاح الإرفاق
PRINT '';
PRINT '3. التحقق من الإرفاق:';
PRINT '--------------------';

IF EXISTS (SELECT 1 FROM sys.databases WHERE name = 'nawafd')
BEGIN
    PRINT '✓ تم إرفاق قاعدة البيانات بنجاح';
    
    -- عرض معلومات قاعدة البيانات
    SELECT 
        db.name AS DatabaseName,
        db.state_desc AS State,
        mf.name AS LogicalName,
        mf.physical_name AS PhysicalPath,
        mf.type_desc AS FileType,
        CAST(mf.size * 8.0 / 1024 AS DECIMAL(10,2)) AS SizeMB
    FROM sys.master_files mf
    INNER JOIN sys.databases db ON mf.database_id = db.database_id
    WHERE db.name = 'nawafd';
    
    -- اختبار قاعدة البيانات
    USE [nawafd];
    SELECT COUNT(*) AS TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE';
    
END
ELSE
BEGIN
    PRINT '✗ فشل في إرفاق قاعدة البيانات';
END

PRINT '';
PRINT '========================================';
PRINT 'انتهت عملية إعادة الإرفاق';
PRINT '========================================';
