﻿@model AppTech.MSMS.Web.Areas.Remittance.Models.RemittanceReceiptModel
@*@Styles.Render("~/Content/print")*@


<div class=" col-sm-12 " style="border: 1px solid black;">
    <div class="col-sm-6 pull-right" style="margin-top: 10px">
        رقم الحوالة : @Html.DisplayFor(model => model.RemittanceNumber)
    </div>


    @*<div class="col-sm-6 align-center" style="margin-top: 10px">
        @Html.DisplayFor(model => model.Type)
    </div>*@

    <div class="col-sm-6 align-left" style="margin-top: 10px">
        التاريخ : @Html.DisplayFor(model => model.Date)
    </div>

    <div class="col-sm-12 align-center" style="border-bottom: 1px solid black; margin-top: 1px; margin-bottom: 20px; width: 100%; padding: 8px 0px; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
        @Html.DisplayFor(model => model.Title)
    </div>


    <hr style="font-weight: 200"/>


    <table id="simple-table" class="table  " style="direction: rtl;">
        <tr >
            <td >
                مبلغ الحوالة
                @Html.DisplayFor(model => model.Amount)
            </td>
            <td>
                عملة الحوالة
                @Html.DisplayFor(model => model.CurrencyName)
            </td>


        </tr>
        <tr>

            <td>
                مبلغ وقدرة
                @Html.DisplayFor(model => model.AmountInText)
            </td>


        </tr>

    </table>
    <p class="text-right">جهة الحوالة: @Html.DisplayFor(model => model.TargetName)</p>

    <table id="simple-table " class="table x" style="direction: rtl">
        <tr>
            <td>
                المستفيد
                @Html.DisplayFor(model => model.BenficiaryName)
            </td>
            <td>رقم الهاتف @Html.DisplayFor(model => model.BenficiaryPhone)</td>

        </tr>

        <tr>
            <td>
                المرسل @Html.DisplayFor(model => model.SenderName)
            </td>
            <td>رقم الهاتف @Html.DisplayFor(model => model.SenderPhone)</td>

        </tr>
    </table>
    <div class="" style="text-align: right; border: 1px solid #000; padding: 20px 0; margin-bottom: 30px">
        ملاحظات
        @Html.DisplayFor(model => model.Note)
    </div>


</div>