﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<configSections>

		<sectionGroup name="elmah">
			<section name="security" requirePermission="false" type="Elmah.SecuritySection<PERSON><PERSON><PERSON>, Elmah" />
			<section name="errorLog" requirePermission="false" type="Elmah.ErrorLogS<PERSON><PERSON><PERSON><PERSON><PERSON>, Elmah" />
			<section name="errorMail" requirePermission="false" type="Elmah.ErrorMailSectionHandler, Elmah" />
			<section name="errorFilter" requirePermission="false" type="Elmah.ErrorFilterSection<PERSON>and<PERSON>, Elmah" />
		</sectionGroup>

		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>
	<connectionStrings>
	</connectionStrings>
	<elmah>

		<errorLog type="AppTech.MSMS.Web.Code.ElmahErrorLog, AppTech.MSMS.Web" connectionStringName="elmah-sql">
		</errorLog>
	
    <security allowRemoteAccess="false" /></elmah>

  <appSettings file="AppSettings.config">
		<add key="webpages:Version" value="*******" />
		<add key="webpages:Enabled" value="false" />
		<add key="ClientValidationEnabled" value="true" />
		<add key="UnobtrusiveJavaScriptEnabled" value="true" />
		<add key="vs:EnableBrowserLink" value="true" />
    <add key="PreserveLoginUrl" value="true" />
    <add key="sm:ShowCustomErrors" value="true" />
		<add key="RolesConfigKey" value="Admin" />
		<add key="UsersConfigKey" value="Client" />

	</appSettings>

	<system.web>
		<machineKey validationKey="FBD17EE6B7786E3CC14F8C97E716322ADD454F11DF9DC02A7D2050FD0C8445070C6A685106371980E09FC8EE81FF07C8264314820AE1AF0A7045A7D5B9C24805" decryptionKey="2F8EE5D282A6D0366CAD230214DE93B57E394AE4398F2D5F53924BA8AF4569E8" validation="SHA1" decryption="AES" />
		<httpModules>
			<add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" />
			<add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" />
			<add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" />
		</httpModules>

		<httpHandlers>
			<add verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
		</httpHandlers>

		<authentication mode="Forms">
			<forms name="Frenchi.AUTH" loginUrl="Account/Admin" timeout="43200" requireSSL="false" protection="All" slidingExpiration="false" enableCrossAppRedirects="false" />
			
		</authentication>
    <sessionState timeout="300" />

    <roleManager enabled="true" />
		<compilation debug="true" targetFramework="4.6.1" />
		<customErrors mode="RemoteOnly" />
    <pages buffer="true" enableViewState="false">
			<namespaces>
				<add namespace="System.Web.Helpers" />
				<add namespace="System.Web.Mvc" />
				<add namespace="System.Web.Mvc.Ajax" />
				<add namespace="System.Web.Mvc.Html" />
				<add namespace="System.Web.Optimization" />
				<add namespace="System.Web.Routing" />
				<add namespace="System.Web.WebPages" />
			</namespaces>
		</pages>
    <!--<httpRuntime targetFramework="4.5" maxRequestLength="2097152" executionTimeout="14400" maxQueryStringLength="16384" fcnMode="Single" />-->
		<httpRuntime targetFramework="4.5" executionTimeout="300" />

	</system.web>


	<location path="~/Scripts">
		<system.web>
			<authorization>
				<allow users="*" />
			</authorization>
		</system.web>
	</location>

	<system.webServer>
    <httpErrors errorMode="Detailed" defaultResponseMode="File">
      <remove statusCode="404" />
      <remove statusCode="500" />
      <error statusCode="404" path="404.html" responseMode="File" />
      <error statusCode="500" path="500.html" responseMode="File" />
    </httpErrors>
		<asp scriptErrorSentToBrowser="true" />

		<httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files">
			<scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" staticCompressionLevel="9" />
			<dynamicTypes>
				<add mimeType="text/*" enabled="true" />
				<add mimeType="message/*" enabled="true" />
				<add mimeType="application/x-javascript" enabled="true" />
				<add mimeType="application/json" enabled="true" />
				<add mimeType="*/*" enabled="false" />
			</dynamicTypes>
			<staticTypes>
				<add mimeType="text/*" enabled="true" />
				<add mimeType="message/*" enabled="true" />
				<add mimeType="application/x-javascript" enabled="true" />
				<add mimeType="application/atom+xml" enabled="true" />
				<add mimeType="application/xaml+xml" enabled="true" />
				<add mimeType="*/*" enabled="false" />
			</staticTypes>
		</httpCompression>

		<urlCompression doStaticCompression="true" doDynamicCompression="true" />

		<validation validateIntegratedModeConfiguration="false" />

		<modules runAllManagedModulesForAllRequests="true">
			<add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" preCondition="managedHandler" />
			<add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" preCondition="managedHandler" />
			<add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" preCondition="managedHandler" />
		</modules>
		<handlers>
			<remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" />
			<remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" />
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0" />
			<add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
			<add name="Elmah" verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
		
		</handlers>
 
		<httpProtocol>
			<customHeaders>
				<remove name="X-Powered-By" />
			</customHeaders>
		</httpProtocol>
	
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Spatial" publicKeyToken="31BF3856AD364E35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-2.2.1.0" newVersion="2.2.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
			</dependentAssembly>

			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>

			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
			</dependentAssembly>

			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Data.OData" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Data.Edm" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Spatial" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.1.1.3" newVersion="4.1.1.3" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Google.Apis" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.40.0.0" newVersion="1.40.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Google.Apis.Auth" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.40.0.0" newVersion="1.40.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Security.Principal.Windows" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.IdentityModel.JsonWebTokens" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
			<parameters>
				<parameter value="v13.0" />
			</parameters>
		</defaultConnectionFactory>
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
		</providers>
	</entityFramework>
</configuration>
