﻿@using AppTech.MSMS.Domain
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.TopupProvider
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number)
        @Html.ValidationMessageFor(model => model.Number)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name)
        @Html.ValidationMessageFor(model => model.Name)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BaseUrl, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.BaseUrl, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.BaseUrl)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Username, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Username)
        @Html.ValidationMessageFor(model => model.Username)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Password, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Password)
        @Html.ValidationMessageFor(model => model.Password)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Token, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Token, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Token)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.UserId, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.UserId)
        @Html.ValidationMessageFor(model => model.UserId)
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.ApiName, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ApiName)
        @Html.ValidationMessageFor(model => model.ApiName)
    </div>
</div>


<div class="form-group">
    @Html.Label("تفعيل الطفاية", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.External)
            @Html.ValidationMessageFor(model => model.External, "", new { @class = "text-danger" })
        </div>
    </div>
</div>


@if (DomainManager.AutoTopupInspect)
{

    <div class="form-group">
        @Html.LabelFor(model => model.AutoBalance, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.AutoBalance, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
            @Html.ValidationMessageFor(model => model.AutoBalance)
        </div>
    </div>

}


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>



