<!DOCTYPE html>
<html>
<head>
    <title>API - Test Page - AppTech MSMS</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header { 
            background: #007acc; 
            color: white; 
            padding: 20px; 
            border-radius: 5px;
            text-align: center;
        }
        .content { 
            margin: 20px 0; 
        }
        .status { 
            padding: 15px; 
            border-radius: 5px; 
            margin: 10px 0; 
            border-left: 4px solid;
        }
        .success { 
            background: #d4edda; 
            color: #155724; 
            border-left-color: #28a745;
        }
        .info { 
            background: #d1ecf1; 
            color: #0c5460; 
            border-left-color: #17a2b8;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            background: #007acc;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #005a9e;
        }
        ul {
            line-height: 1.6;
        }
        .code {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API Application - AppTech MSMS</h1>
            <p>Test Page - Ready for Testing</p>
            <p>Generated: <span id="datetime"></span></p>
        </div>
        
        <div class="content">
            <div class="status success">
                ✅ <strong>Application Status: READY</strong><br>
                Web.config has been configured with connection strings
            </div>
            
            <div class="status info">
                📁 <strong>Application Path:</strong> E:\inetpub\wwwroot\api<br>
                🔗 <strong>URL:</strong> http://localhost/api/
            </div>
            
            <div class="test-section">
                <h3>🧪 Quick Tests</h3>
                
                <button class="btn" onclick="testConnection()">Test Database Connection</button>
                <button class="btn" onclick="testAPI()">Test API Endpoints</button>
                <button class="btn" onclick="checkFiles()">Check Application Files</button>
                
                <div id="testResults" style="margin-top: 20px;"></div>
            </div>
            
            <div class="status warning">
                ⚠️ <strong>Database Status:</strong> Needs to be restored<br>
                Run the database restoration script to complete setup
            </div>
            
            <h3>📋 Configuration Details</h3>
            <div class="code">
                <strong>Connection Strings:</strong><br>
                • DefaultConnection: localhost → nawafd database<br>
                • AppTechEntities: Entity Framework connection<br>
                • elmah-sql: Error logging connection
            </div>
            
            <h3>🔧 Next Steps</h3>
            <ul>
                <li>✅ Web.config configured</li>
                <li>✅ Connection strings updated</li>
                <li>⏳ Database needs to be restored</li>
                <li>⏳ IIS configuration needed</li>
                <li>⏳ Application testing required</li>
            </ul>
            
            <h3>📊 Application Files Status</h3>
            <ul>
                <li><strong>Web.config:</strong> ✅ Configured</li>
                <li><strong>Bin folder:</strong> ✅ Available with DLL files</li>
                <li><strong>Views folder:</strong> ✅ Available</li>
                <li><strong>Global.asax:</strong> ✅ Available</li>
                <li><strong>License files:</strong> ✅ Available</li>
            </ul>
            
            <h3>🌐 Access URLs</h3>
            <div class="code">
                Main Application: <a href="http://localhost/api/" target="_blank">http://localhost/api/</a><br>
                Test Page: <a href="http://localhost/api/test.html" target="_blank">http://localhost/api/test.html</a><br>
                Error Logs: http://localhost/api/elmah.axd
            </div>
            
            <div class="status info">
                💡 <strong>Tip:</strong> If you see this page, the web application files are accessible and ready for testing!
            </div>
        </div>
    </div>

    <script>
        // Display current date/time
        document.getElementById('datetime').textContent = new Date().toLocaleString();
        
        function testConnection() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="status info">🔄 Testing database connection...</div>';
            
            // Simulate connection test
            setTimeout(() => {
                results.innerHTML = '<div class="status warning">⚠️ Database connection test requires actual database. Please restore nawafd database first.</div>';
            }, 1000);
        }
        
        function testAPI() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="status info">🔄 Testing API endpoints...</div>';
            
            // Test if we can access the application
            fetch(window.location.origin + '/api/')
                .then(response => {
                    if (response.ok) {
                        results.innerHTML = '<div class="status success">✅ API application is accessible!</div>';
                    } else {
                        results.innerHTML = '<div class="status warning">⚠️ API returned status: ' + response.status + '</div>';
                    }
                })
                .catch(error => {
                    results.innerHTML = '<div class="status warning">⚠️ API test failed. This is normal if IIS is not configured yet.</div>';
                });
        }
        
        function checkFiles() {
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <div class="status success">
                    ✅ <strong>File Check Results:</strong><br>
                    • Web.config: Present and configured<br>
                    • Application files: Available<br>
                    • Test page: You're viewing it now!<br>
                    • Ready for IIS deployment
                </div>
            `;
        }
    </script>
</body>
</html>
