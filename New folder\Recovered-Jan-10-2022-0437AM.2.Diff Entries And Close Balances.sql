


---------------------------------------- Currency Exchange Differentail Adjustment   ---------------------------------------

begin tran Currency_Exch_Diff_Adjust
declare @HDCAmount money
declare @CDCAmount money
declare @Diff money
declare @CurrencyID bigint
declare @AccountID bigint
declare @BranchID bigint
declare @ParentID bigint
declare @TotalAmount money
declare cur  cursor for

Select Sum(DCAmount) as HDCAmount,Sum(Amount*ExchangeRate) as CDCAmount,Round(Sum(Amount*ExchangeRate)-Sum(DCAmount),6) Diff
,CurrencyID,AccountID--,JournalEntries.BranchID 
            From JournalEntries Inner join ( 
SELECT [ID],[SourceCurrencyID],[TargetCurrencyID],[ExchangeRate],[BuyPrice],[SellPrice]
  From CurrencyRate Where TargetCurrencyID =1 AND (BranchID=1   OR (BranchID=1 and SourceCurrencyID Not in(Select SourceCurrencyID from CurrencyRate where TargetCurrencyID =1 AND BranchID= 1  ))) 
Union All 
SELECT 0 as  [ID],1 as [SourceCurrencyID],1 as [TargetCurrencyID],1 as [ExchangePrice],1 as [BuyPrice],1 as [SellPrice]
                                     
                                     ) CurrencyRate on CurrencyRate.SourceCurrencyID=JournalEntries.CurrencyID 
                                    Where CurrencyID<>1   
									--and JournalEntries.BranchID= 1 
									 AND datestamb<= '********'
                                    Group By CurrencyID,AccountID ,AccountNumber --,JournalEntries.BranchID 
                                    Having Round(Sum(Amount * ExchangeRate) - Sum(DCAmount), 3) <> 0 
                                    Order By AccountNumber,CurrencyID 
                    
                               
open cur
set @TotalAmount=0.0

 fetch next from cur into @HDCAmount,@CDCAmount,@Diff,@CurrencyID,@AccountID--,@BranchID

insert into Journal 
 (VoucherID,Number,EntryID,Date,CreatedBy,BranchID,Debited,Status,datestamb,EntrySource) 
 values
   ((4) 
,(select isnull(MAX(Number),0)+1 from Journal where VoucherID in (4))
,(select isnull(MAX(EntryID),0)+1 from Journal where VoucherID in (4))
,'2022-01-01',1,1,1,1,'********',9);--'قيد فوارق لإعادة تقييم العملات الأجنبية' + ' لفرع '  + 'الأدارة'

set  @ParentID= scope_identity();

 --if @ParentID>1
 --begin 

while @@FETCH_STATUS=0
 begin
 
if @Diff>0
begin
  insert into JournalEntry 
 (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp) 
values
 (@ParentID,0,@CurrencyID,@Diff,@AccountID,'قيد فوارق',1,'********')
 end
 if @Diff<0
 begin
   insert into JournalEntry 
 (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp) 
 values
 (@ParentID,0,@CurrencyID,-abs(@Diff),@AccountID,'قيد فوارق',1,'********')
 end
 set @TotalAmount +=@Diff
  
 fetch next from cur into @HDCAmount,@CDCAmount,@Diff,@CurrencyID,@AccountID--,@BranchID
 end--end while

begin 

if @TotalAmount>0
begin 
  insert into JournalEntry 
 (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp) 
values
 (@ParentID,-@TotalAmount,1,-@TotalAmount,(select id from Account where 
Name='فوارق صرف عملة'),'قيد فوارق ',1,'********')
end 

if @TotalAmount<0
begin
  insert into JournalEntry 
 (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp) 
values
 (@ParentID,abs(@TotalAmount),1,abs(@TotalAmount),(select id from Account where 
Name='فوارق صرف عملة'),'قيد فوارق ',1,'********')
end
 end
 --end 
close cur
deallocate cur
commit tran Currency_Exch_Diff_Adjust

GO





----------------------------------------Closing Revenue & Expenses Entries To Loss and Profits Account  ---------------------------------------

   begin tran Close_Entries    
   declare @AccountID bigint      
   declare @CurrencyID bigint      
   declare @Amount money      
   declare @DCAmount money      
   declare @ParentID bigint      
   declare @TotalAmount money      
   declare @MCTotalAmount money     
   declare @DiffAmount money     
   declare @BranchID bigint      


   declare cur  cursor for  
   select SUM(Amount) as Balance,CurrencyID,SUM(DCAmount) as McBalance,AccountID from JournalEntries    
   where  datestamb<='********' and AccountID in (select id from Account where ( ParentNumber like '3%' or ParentNumber like '4%' ) and type='فرعي')      
   group by AccountID,CurrencyID 
  having SUM(Amount)<>0 order by AccountID,CurrencyID
   open cur       
   set @TotalAmount=0.0
   set @MCTotalAmount=0.0 
   set @DiffAmount=0.0 
   set @ParentID=0 

   fetch next from cur into    
   @Amount,@CurrencyID,@DCAmount,@AccountId--,@BranchID  
   while @@FETCH_STATUS=0
    begin           
  if not Exists( select * from Journal  where  EntrySource=10)-- and Note='قيد إقفال'
   begin
	   insert into    

   Journal(Number,VoucherID,EntryID,Date,CreatedBy,BranchID,Debited,Status,datestamb,EntrySource)         
		 values                    
		(	
		 (select isnull(MAX(Number),0)+1 from Journal where  VoucherID in (4))
		 ,(4) 
		,(select isnull(MAX(EntryID),0)+1 from Journal where VoucherID in (4))
		
	 
		,'2022-01-01',1,1,1,1,'********',10);--,'قيد إقفال'
		set     @ParentID= scope_identity();         
   end
   
   
     if @Amount>0          
     begin            
		 insert into JournalEntry              
		 (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp)         values       
		 (@ParentID,-@Amount,@CurrencyID,-@DCAmount,@AccountID,'قيد إقفال'+' ' + (select name from    
	   Account where ID=@AccountID),1,'********')           
     end           
    
	
	 if @Amount<0           
     begin             
		 insert into JournalEntry        (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp)           
		values       
		 (@ParentID,abs(@Amount),@CurrencyID,abs(@DCAmount),@AccountID,'قيد إقفال'+' ' + (select    
	   name from Account where ID=@AccountID),1,'********')           
    end        
  

     set @TotalAmount +=@Amount  
	 set @MCTotalAmount +=@DCAmount   
	   
	   fetch next from cur into    
   @Amount,@CurrencyID,@DCAmount,@AccountId--,@BranchID 
  
   end --while end      

   if (@ParentID>0)
   begin       
   insert into JournalEntry      (ParentID,Amount,CurrencyID,DCAmount,AccountID,Note,ExchangeRate,Datestamp)            
    values (@ParentID,@MCTotalAmount,1,@MCTotalAmount,(select id from Account where    
   name='حساب الأرباح والخسائر'),'قيد إقفال '+' ' + 'حساب الأرباح والخسائر',1,'********')                

    end      
  
			   
    close cur       
    deallocate cur      
   commit tran  Close_Entries  




   GO





---------------------------------------- Transfer Assets & Liabilities Accounts'  Current Balances to [BalanceClosure] For New Year Opening Balance Entries   ---------------------------------------


insert into [BalanceClosure] (Balance,CurrencyID,DCBalance,AccountID) ( 
SELECT SUM(Amount) TotalAmount,CurrencyID,SUM(isnull(DCAmount,0)) TotalDCAmount ,AccountID FROM JournalEntries 
GROUP BY AccountID,CurrencyID
HAVING SUM(Amount)<>0
)