﻿@model AppTech.MSMS.Domain.Models.Supplier
@using Obout.Mvc.ComboBox
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
@if (AppTech.MSMS.Domain.DomainManager.SyncWithOtherSystem)
{
    <div class="form-group">
        <div class="col-md-12">
            @Html.Label("الحساب", new { @class = "control-label col-md-2" })
            @Html.Obout(new ComboBox("SyncAccountID")
            {
                Width = 300,
                SelectedValue = Model.SyncAccountID == null ? null : Model.SyncAccountID.ToString(),
                FilterType = ComboBoxFilterType.Contains
            })
            @Html.ValidationMessageFor(model => model.SyncAccountID)
        </div>
    </div>
}
else
{
    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
            @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
        </div>
    </div>
}
<div class="form-group">
    @Html.LabelFor(model => model.PhoneNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.PhoneNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.PhoneNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ContactNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ContactNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.ContactNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Address, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Address, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>