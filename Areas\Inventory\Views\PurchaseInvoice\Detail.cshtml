﻿@model AppTech.MSMS.Domain.Models.PurchaseInvoice

<h2 class="center"> فاتورة شراء</h2>
<div class="table-bordered" style="padding: 20px 20px;">
    <div class=" " style="margin-bottom: 25px;">
        <div class="table-responsive">
            <table class="table no-border table-condensed table-sm">
                <tr>
                    <td class="col-sm-6">
                        <label class="bolder">رقم الفاتورة: </label>
                        @Html.DisplayFor(m => m.Number)
                    </td>
                    <td class="col-sm-6">
                        <label class="bolder">العملة: </label>
                        @Html.DisplayFor(m => m.Currency.Name)
                    </td>
                </tr>
                <tr>
                    <td class="col-sm-6">
                        <label class="bolder">التاريخ: </label>
                        @Html.DisplayFor(m => m.Date)
                    </td>
                    <td class="col-sm-6">
                        <label class="bolder">الحساب: </label>
                        @Html.DisplayFor(m => m.Account.Name)
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-hover table-bordered table-striped table-condensed table-sm">
            <thead>
                <tr>
                    <th style="padding-right: 15px;background:lightgray">
                        @Html.DisplayName("الصنف")
                    </th>
                    <th style="background:lightgray">
                        @Html.DisplayName("الكميه")
                    </th>
                    <th style="background:lightgray">
                        @Html.DisplayName("الاجمالي")
                    </th>
                </tr>
            </thead>

            @foreach (var item in Model.PurchaseInvoiceLines)
            {
                <tr>
                    <td style="padding-right: 15px;">
                        @Html.DisplayFor(modelItem => item.ProductID) @* TODO:add Products to SaleInvoiceLine to view Product Name *@
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Quantity)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.TotalAmount)
                    </td>
                </tr>
            }
        </table>

        <table class="table no-border table-condensed table-sm">
            <tr>
                <td class="col-sm-8">
                    <label class="bolder"> المبلغ: </label>
                    @Html.DisplayFor(m => m.Amount)
                </td>
                <td rowspan="1" class="col-sm-4">
                    <label class="bolder"> ملاحظات: </label>
                    @Html.DisplayFor(m => m.Note)
                </td>
            </tr>
            <tr>
                <td>
                    <label class="bolder"> الإجمالي: </label>
                    @Html.DisplayFor(m => m.Amount)
                </td>
            </tr>
        </table>
    </div>
</div>

