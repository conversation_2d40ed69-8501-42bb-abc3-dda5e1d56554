﻿@model AppTech.MSMS.Domain.Models.UserDevice

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.LabelFor(model => model.UserID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.UserID, (SelectList)ViewBag.Users, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.UserID, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.Identifier, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Identifier, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Identifier, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Permitted, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Permitted)
            @Html.ValidationMessageFor(model => model.Permitted, "", new { @class = "text-danger" })
        </div>
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>