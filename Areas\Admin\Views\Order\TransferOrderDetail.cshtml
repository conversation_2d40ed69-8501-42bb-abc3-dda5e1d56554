﻿@model AppTech.MSMS.Domain.Models.TransferOrder

    <div>

        <div class="space-6"></div>
        <span class="label label-info arrowed-in-right arrowed"> بيانات الحوالة</span>
        <div class="space-6"></div>
        @Html.HiddenFor(x => x.IsIncoming)
        @if (Model.IsIncoming == true)
        {
            <div class="profile-info-row">
                <div class="profile-info-name">@Html.LabelFor(model => model.TransferNumber) </div>
                <div class="profile-info-value">
                    <span class="editable"> @Html.EditorFor(model => model.TransferNumber, new { @id = "TransferNumber" })</span>

                    <button class="btn btn-white loading" onclick="saveTransNumber();" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" id="bootbox-save-num">
                        <i class="ace-icon fa fa-save"></i>
                        حفظ
                    </button>

                </div>
            </div>
        }
        else
        {
            <div class="profile-info-row">
                <div class="profile-info-name">@Html.LabelFor(model => model.TransferNumber) </div>

                <div class="profile-info-value">
                    <span class="editable" id="trans_num"> @Html.DisplayFor(model => model.TransferNumber, new { @class = "trans-num" })</span>

                    <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.TransferNumber)">
                        <i class="ace-icon fa fa-copy"></i> نسخ
                    </button>

                </div>
            </div>
        }

        <div class="profile-info-row">
            <div class="profile-info-name">@Html.LabelFor(model => model.ExchangerID) </div>
            <div class="profile-info-value">
                <span class="editable"> @Html.DropDownListFor(model => model.ExchangerID, (SelectList)ViewBag.Exchangers)</span>
                <button class="btn btn-white loading" onclick="changerExchanger();" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" id="bootbox-changer-exchanger">
                    <i class="ace-icon fa fa-save"></i>
                    تعديل
                </button>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> المبلغ </div>
            <div class="profile-info-value">
                <span class="editable">@Html.DisplayFor(model => model.Amount)</span>
                <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Amount)">
                    <i class="ace-icon fa fa-copy"></i> نسخ
                </button>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> العملة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.Currency1.Name) </span>
                <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.Currency1.Name)">
                    <i class="ace-icon fa fa-copy"></i> نسخ
                </button>

            </div>
        </div>

        @if (Model.IsIncoming == true)
        {
            <div class="profile-info-row">
                <div class="profile-info-name">مبلغ العمولة</div>
                <div class="profile-info-value">
                    <span class="editable"> @Html.EditorFor(model => model.Commission)</span>
                    <span class="editable"> @Html.DropDownListFor(model => model.CommissionCurrencyID, (SelectList)ViewBag.Currencies)</span>
                    <button class="btn btn-white loading" onclick="changeCommission();" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار">
                        <i class="ace-icon fa fa-save"></i>
                        تعديل العمولة
                    </button>
                </div>
            </div>
        }
        <div class="profile-info-row">
            <div class="profile-info-name">@Html.LabelFor(model => model.SenderName) </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.SenderName)</span>
                <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.SenderName)">
                    <i class="ace-icon fa fa-copy"></i> نسخ
                </button>

            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name">@Html.LabelFor(model => model.SenderMobile) </div>
            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.SenderMobile)</span>
                <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.SenderMobile)">
                    <i class="ace-icon fa fa-copy"></i> نسخ
                </button>

            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name">@Html.LabelFor(model => model.ReceiverName) </div>
            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.ReceiverName)</span>
                <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.ReceiverName)">
                    <i class="ace-icon fa fa-copy"></i> نسخ
                </button>

            </div>

        </div>


        <div class="profile-info-row">
            <div class="profile-info-name">@Html.LabelFor(model => model.ReceiverMobile) </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.ReceiverMobile)</span>

                <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.ReceiverMobile)">
                    <i class="ace-icon fa fa-copy"></i> نسخ
                </button>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> الملاحظات </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.Note)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> Extra </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.Extra)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> بطاقة 1 </div>
            <div class="profile-info-value">
                @if (!string.IsNullOrEmpty(Model.ReceiverImage))
                {
                    <img id="avatar" class="editable img-responsive" alt="Image 1" src="@Url.Content(Model.ReceiverImage)" />
                }
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> بطاقة 2 </div>
            <div class="profile-info-value">
                @if (!string.IsNullOrEmpty(Model.SenderImage))
                {
                    <img id="avatar" class="editable img-responsive" alt="Image 2" src="@Url.Content(Model.SenderImage)" />
                }
            </div>
        </div>


    </div>
