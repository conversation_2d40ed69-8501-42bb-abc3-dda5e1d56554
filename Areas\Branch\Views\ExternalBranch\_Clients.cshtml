﻿@model IEnumerable<AppTech.MSMS.Domain.Models.Party1>

<div id="clist">
    <table class="table table-hover">
        <thead>
            <tr>

                <th>
                   رقم الحساب
                </th>
                <th>
                   اسم الحساب
                </th>
               

                <th>
                   نوع الحساب
                </th>

                <th></th>
            </tr>
        </thead>
        @foreach (var item in Model)
        {
            <tbody>
                <tr>

                    <td>
                        @Html.DisplayFor(modelItem => item.Number)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Name)
                    </td>

               
                    <td>
                        @Html.DisplayFor(modelItem => item.Type)
                    </td>
                    <td>
                        <Button class="btn btn-link" onclick="unbind('@item.AccountID','@ViewBag.ParentID')">
                            <i class="ace-icon fa fa-flag bigger-110"></i>
                            فك الربط
                        </Button>

                    </td>
                </tr>
            </tbody>
        }

    </table>

    </div>
    <script>
    function unbind(cid,aid) {
        i('open modal id' + cid +" aid: "+aid);
        var agId =@ViewBag.ParentID;
        if (confirm('سوف يتم فك الربط , هل انت متأكد')) {
        var url = 'Branch/ExternalBranch/RemoveClient?parentId=' + aid + '&accountId=' + cid;

        AjaxCall(url)
            .done(function (response) {
              //  ar(response.Message);
                $("#clist").replaceWith(response);
            }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
    }
    }
    </script>
