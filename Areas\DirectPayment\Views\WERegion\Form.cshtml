﻿@using AppTech.MSMS.Domain.Topuping
@model AppTech.MSMS.Domain.Models.WERegion
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Code, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Code, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Code, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        <div>
            @Html.RadioButtonFor(x => x.ServiceID, 0, new {htmlAttributes = new {@checked = "checked"}}) النقل
        </div>

        <div>
            @Html.RadioButtonFor(x => x.ServiceID, (long) Services.Water) المياه
        </div>
        <div>
            @Html.RadioButtonFor(x => x.ServiceID, (long) Services.Electricity) الكهرباء
        </div>

    </div>
</div>