USE [2021]
GO

/****** Object:  Table [dbo].[Journal]    Script Date: 26/02/2023 03:52:44 ص ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Journal](
	[ID] [bigint] IDENTITY(1,1) NOT FOR REPLICATION NOT NULL,
	[VoucherID] [bigint] NOT NULL,
	[Number] [bigint] NOT NULL,
	[EntryID] [bigint] NOT NULL,
	[Date] [datetime] NOT NULL,
	[CreatedBy] [bigint] NOT NULL,
	[Debited] [bit] NOT NULL,
	[CreatedTime] [datetime] NOT NULL,
	[BranchID] [bigint] NOT NULL,
	[Year]  AS (datepart(year,[date])),
	[Status] [tinyint] NOT NULL,
	[TotalAmount] [money] NULL,
	[SyncJournalID] [bigint] NULL,
	[datestamb] [bigint] NULL,
	[EntrySource] [tinyint] NULL,
	[Depended] [bit] NOT NULL,
	[RefNumber] [bigint] NULL,
	[CurrencyID] [bigint] NULL,
 CONSTRAINT [PK_JournalEntry] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Journal] ADD  CONSTRAINT [DF_JournalEntry_Debited]  DEFAULT ((1)) FOR [Debited]
GO

ALTER TABLE [dbo].[Journal] ADD  CONSTRAINT [DF_JournalEntry_CreatedTime]  DEFAULT (getdate()) FOR [CreatedTime]
GO

ALTER TABLE [dbo].[Journal] ADD  DEFAULT ((1)) FOR [Status]
GO

ALTER TABLE [dbo].[Journal] ADD  DEFAULT ((0)) FOR [Depended]
GO

ALTER TABLE [dbo].[Journal]  WITH CHECK ADD  CONSTRAINT [FK_JournalEntry_Branch] FOREIGN KEY([BranchID])
REFERENCES [dbo].[Branch] ([ID])
GO

ALTER TABLE [dbo].[Journal] CHECK CONSTRAINT [FK_JournalEntry_Branch]
GO

ALTER TABLE [dbo].[Journal]  WITH CHECK ADD  CONSTRAINT [FK_JournalEntry_UserInfo] FOREIGN KEY([CreatedBy])
REFERENCES [dbo].[UserInfo] ([ID])
GO

ALTER TABLE [dbo].[Journal] CHECK CONSTRAINT [FK_JournalEntry_UserInfo]
GO

ALTER TABLE [dbo].[Journal]  WITH CHECK ADD  CONSTRAINT [FK_JournalEntry_Voucher] FOREIGN KEY([VoucherID])
REFERENCES [dbo].[Voucher] ([ID])
GO

ALTER TABLE [dbo].[Journal] CHECK CONSTRAINT [FK_JournalEntry_Voucher]
GO

