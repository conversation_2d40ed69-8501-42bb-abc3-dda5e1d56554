# سكريبت إصلاح سلاسل الاتصال لنظام AppTech MSMS

Write-Host "إصلاح سلاسل الاتصال..." -ForegroundColor Green

# البحث عن SQL Server المتاح
Write-Host "`n1. البحث عن SQL Server..." -ForegroundColor Yellow
$sqlInstances = @(
    "localhost",
    ".\SQLEXPRESS", 
    "localhost\SQLEXPRESS",
    "(localdb)\MSSQLLocalDB",
    ".\MSSQLSERVER",
    "localhost\MSSQLSERVER"
)

$workingInstance = $null
$serverVersion = $null

foreach ($instance in $sqlInstances) {
    try {
        Write-Host "اختبار: $instance" -ForegroundColor Gray
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION, @@SERVERNAME" -ErrorAction Stop -Timeout 5
        if ($result) {
            $workingInstance = $instance
            $serverVersion = $result[0].Column1
            Write-Host "✓ تم العثور على SQL Server: $instance" -ForegroundColor Green
            Write-Host "الإصدار: $($serverVersion.Substring(0, [Math]::Min(80, $serverVersion.Length)))..." -ForegroundColor Gray
            break
        }
    } catch {
        Write-Host "✗ فشل الاتصال بـ: $instance" -ForegroundColor Red
    }
}

if (-not $workingInstance) {
    Write-Host "✗ لم يتم العثور على SQL Server" -ForegroundColor Red
    Write-Host "يرجى تثبيت SQL Server Express من:" -ForegroundColor Yellow
    Write-Host "https://www.microsoft.com/en-us/sql-server/sql-server-downloads" -ForegroundColor Cyan
    return
}

# إنشاء قاعدة البيانات
Write-Host "`n2. إعداد قاعدة البيانات..." -ForegroundColor Yellow
$databaseName = "AppTechMSMS"

try {
    # فحص وجود قاعدة البيانات
    $dbExists = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = '$databaseName'" -ErrorAction Stop
    
    if ($dbExists) {
        Write-Host "✓ قاعدة البيانات موجودة: $databaseName" -ForegroundColor Green
    } else {
        Write-Host "إنشاء قاعدة البيانات: $databaseName" -ForegroundColor Gray
        
        # إنشاء قاعدة البيانات مع إعدادات أساسية
        $createDbQuery = @"
CREATE DATABASE [$databaseName]
COLLATE Arabic_CI_AS
"@
        
        Invoke-Sqlcmd -ServerInstance $workingInstance -Query $createDbQuery -ErrorAction Stop
        Write-Host "✓ تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ خطأ في إعداد قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    return
}

# إنشاء سلاسل الاتصال
Write-Host "`n3. إنشاء سلاسل الاتصال..." -ForegroundColor Yellow

$connectionString = "Data Source=$workingInstance;Initial Catalog=$databaseName;Integrated Security=True;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"
$entityConnectionString = "metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=`"$connectionString`""

Write-Host "سلسلة الاتصال: $connectionString" -ForegroundColor Cyan

# تحديث ملفات web.config
Write-Host "`n4. تحديث ملفات web.config..." -ForegroundColor Yellow

$webConfigPaths = @(
    "wwwroot\api\Web.config",
    "wwwroot\client\Web.config", 
    "wwwroot\portal\Web.config"
)

foreach ($configPath in $webConfigPaths) {
    if (Test-Path $configPath) {
        Write-Host "تحديث: $configPath" -ForegroundColor Gray
        
        try {
            # إنشاء نسخة احتياطية
            $backupPath = "$configPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
            Copy-Item $configPath $backupPath
            Write-Host "  ✓ تم إنشاء نسخة احتياطية: $backupPath" -ForegroundColor Gray
            
            # قراءة وتحديث ملف XML
            [xml]$webConfig = Get-Content $configPath
            
            # البحث عن أو إنشاء عقدة connectionStrings
            $connectionStringsNode = $webConfig.configuration.connectionStrings
            if ($connectionStringsNode -eq $null) {
                $connectionStringsNode = $webConfig.CreateElement("connectionStrings")
                $webConfig.configuration.AppendChild($connectionStringsNode) | Out-Null
            } else {
                $connectionStringsNode.RemoveAll()
            }
            
            # إضافة سلاسل الاتصال
            $connections = @(
                @{name="DefaultConnection"; connectionString=$connectionString; providerName="System.Data.SqlClient"},
                @{name="AppTechMSMSEntities"; connectionString=$entityConnectionString; providerName="System.Data.EntityClient"},
                @{name="elmah-sql"; connectionString=$connectionString; providerName="System.Data.SqlClient"}
            )
            
            foreach ($conn in $connections) {
                $addElement = $webConfig.CreateElement("add")
                $addElement.SetAttribute("name", $conn.name)
                $addElement.SetAttribute("connectionString", $conn.connectionString)
                $addElement.SetAttribute("providerName", $conn.providerName)
                $connectionStringsNode.AppendChild($addElement) | Out-Null
            }
            
            # تحديث إعداد ConnectionStringSource
            $appSettings = $webConfig.configuration.appSettings
            if ($appSettings) {
                $connectionSourceSetting = $appSettings.add | Where-Object { $_.key -eq "ConnectionStringSource" }
                if ($connectionSourceSetting) {
                    $connectionSourceSetting.value = "ConfigFile"
                    Write-Host "  ✓ تم تحديث ConnectionStringSource إلى ConfigFile" -ForegroundColor Gray
                }
            }
            
            # حفظ الملف
            $webConfig.Save($configPath)
            Write-Host "  ✓ تم تحديث $configPath بنجاح" -ForegroundColor Green
            
        } catch {
            Write-Host "  ✗ خطأ في تحديث $configPath : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ الملف غير موجود: $configPath" -ForegroundColor Red
    }
}

# إنشاء جداول أساسية
Write-Host "`n5. إنشاء جداول أساسية..." -ForegroundColor Yellow

try {
    # جدول المستخدمين الأساسي
    $createUsersTable = @"
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
CREATE TABLE Users (
    Id int IDENTITY(1,1) PRIMARY KEY,
    Username nvarchar(50) NOT NULL UNIQUE,
    Password nvarchar(255) NOT NULL,
    FirstName nvarchar(50),
    LastName nvarchar(50),
    Email nvarchar(100),
    UserType int DEFAULT 1,
    IsActive bit DEFAULT 1,
    CreatedDate datetime DEFAULT GETDATE(),
    LastLoginDate datetime
)
"@

    Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $createUsersTable -ErrorAction Stop
    Write-Host "✓ تم إنشاء جدول المستخدمين" -ForegroundColor Green
    
    # إنشاء مستخدم افتراضي
    $defaultUserQuery = @"
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
INSERT INTO Users (Username, Password, FirstName, LastName, Email, UserType)
VALUES ('admin', 'admin123', 'مدير', 'النظام', '<EMAIL>', 1)
"@

    Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $defaultUserQuery -ErrorAction Stop
    Write-Host "✓ تم إنشاء المستخدم الافتراضي (admin/admin123)" -ForegroundColor Green
    
} catch {
    Write-Host "⚠ خطأ في إنشاء الجداول: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "سيتم إنشاء الجداول تلقائياً عند أول تشغيل للتطبيق" -ForegroundColor Gray
}

Write-Host "`n" + "="*50 -ForegroundColor Green
Write-Host "انتهى إصلاح سلاسل الاتصال" -ForegroundColor Green
Write-Host "="*50 -ForegroundColor Green

Write-Host "`nمعلومات الاتصال:" -ForegroundColor White
Write-Host "SQL Server: $workingInstance" -ForegroundColor Cyan
Write-Host "قاعدة البيانات: $databaseName" -ForegroundColor Cyan
Write-Host "المستخدم الافتراضي: admin / admin123" -ForegroundColor Yellow

Write-Host "`nتم تحديث ملفات web.config بسلاسل الاتصال الجديدة" -ForegroundColor Green
