﻿$(document).ajaxError(function (e, xhr, settings, exception) {
    console.log("ajax error: " + xhr.responseText);
    e.stopPropagation();
    if (xhr) {
       
        var msg = parseXhr(xhr);
        console.log(msg);
     //   showError(msg);
        //  showError(jqxhr.responseText.Message);
    }
    else {
        showError("an unknown error has occured while calling ajax");
    }
    hideFormLoading();
    hideLoading();

});
function parseXhr(xhr) {
    i("onError: "+xhr);
    var msg = "";
    try {
       // var jsonResponse = JSON.parse(xhr.responseText);
       // msg = jsonResponse["Message"];
        var err = eval("(" + xhr.responseText + ")");
        msg = err.Message;
        i("on Parse xhr msg: " + msg);
        return msg;
    } catch (e) {
        msg = xhr.statusText;
    } 

    return msg;
}

function ar(msg) {
    alert(msg);
}


function handleXhr(xhr) {
    try {
        hideLoading();
    } catch (e) {
        i("");
    }

var jsonResponse = JSON.parse(xhr.responseText);
    var msg = jsonResponse["Message"];
    showError(msg);
}



function fillListByClass(element, path, title) {
    AjaxCall(path).done(function (response) {

        $('.' + element).html('');
        var options = '';
        if (title !== '' && title !== undefined) {
            options += '<option value="Select">' + title + '  </option>';
        }
        for (var i = 0; i < response.length; i++) {
            options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
        }
        $('.' + element).append(options);
    }).fail(function (error) {
        alert(error.StatusText);
    });

}

function fillListOnChange(element, path, isAccount, title) { }

function fillListByElement(element,selectedVal, path, title,callback) {
    AjaxCall(path).done(function (response) {

        fillPlainListWithSelected(response, element, selectedVal, title, callback);

    }).fail(function (error) {
        alert(error.StatusText);
    });

}
function fillListWithSelected(element, selectedVal, path, isAccount, title) {
    i('selectedVal ' + selectedVal);

    AjaxCall(path).done(function (response) {
        if (isAccount)
            fillAccountListWithSelected(response, element, selectedVal, title);
        else
            fillPlainListWithSelected(response, $('#' + element), selectedVal, title);
    }).fail(function (error) {
        alert(error.StatusText);
    });

}
function fillPlainListWithSelected(response, element, selectedVal, title, callback) {

    element.html('');
    var options = '';
    if (title) {
        options += '<option value="Select">' + title + '  </option>';
    }
    for (var i = 0; i < response.length; i++) {
    //    console.log('selectedVal is ' + selectedVal + 'Current Val is' + Number(response[i].ID));
        if (Number(selectedVal) === Number(response[i].ID)) {
            console.log('selectedVal mateched');
            options += '<option value="' + response[i].ID + '" selected>' + response[i].Name + '</option>';
        }
        else {
            options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
        }
    }
    element.append(options);
    if (typeof callback === 'function') { 
        console.log('callback inside');
        callback();
    }
}
function fillAccountListWithSelected(response, element, selectedVal, title) {
   console.log('fillAccountListWithSelected ' + selectedVal);
    $('#' + element).html('');
    var options = '';
    if (title) {
        options += '<option value="Select">' + title + '  </option>';
    }

        console.log('response=' + response);
    for (var i = 0; i < response.length; i++) {
        if (selectedVal === response[i].AccountID) {
            console.log('selectedVal mateched');
            options += '<option value="' + response[i].AccountID + '" selected>' + response[i].Name + '</option>';
        }
        else
        {
            options += '<option value="' + response[i].AccountID + '">' + response[i].Name + '</option>';
        }
    }
    $('#' + element).append(options);
}
function fillDataList(element, path, isAccount, title) {
    AjaxCall(path).done(function (response) {
        if (isAccount)
            fillAccountList(response, element, title);
        else 
            fillPlainList(response, element, title);
    }).fail(function (error) {
        alert(error.StatusText);
    });
 
}
function fillAccountList(response, element, title) {

    $('#' + element).html('');
    var options = '';
    if (title) {
        options += '<option value="Select">' + title + '  </option>';
    }
    for (var i = 0; i < response.length; i++) {
       options += '<option value="' + response[i].AccountID + '">' + response[i].Name + '</option>';
    }
    $('#' + element).append(options);
}
function fillPlainList(response, element, title) {

    $('#' + element).html('');
    var options = '';
    if (title) {
        options += '<option value="Select">' + title + '  </option>';
    }
    for (var i = 0; i < response.length; i++) {
        options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
    }
    $('#' + element).append(options);
}
function AjaxCall(url, data, type) {
    return $.ajax({
        url: url,
        type: type ? type : "GET",
        data: data,
        cache: true,
        contentType: "application/json"
    });
}  
function downloan(url, params) {
    var xhr = new XMLHttpRequest();
    xhr.open('POST', url, true);
    xhr.responseType = 'arraybuffer';
    xhr.onload = function () {
        if (this.status === 200) {
            var filename = "";
            var disposition = xhr.getResponseHeader('Content-Disposition');
            if (disposition && disposition.indexOf('attachment') !== -1) {
                var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                var matches = filenameRegex.exec(disposition);
                if (matches !== null && matches[1]) filename = matches[1].replace(/['"]/g, '');
            }
            var type = xhr.getResponseHeader('Content-Type');

            var blob;
            if (typeof File === 'function') {
                try {
                    blob = new File([this.response], filename, { type: type });
                } catch (e) { /* Edge */ }
            }
            if (typeof blob === 'undefined') {
                blob = new Blob([this.response], { type: type });
            }

            if (typeof window.navigator.msSaveBlob !== 'undefined') {
                window.navigator.msSaveBlob(blob, filename);
            } else {
                var URL = window.URL || window.webkitURL;
                var downloadUrl = URL.createObjectURL(blob);

                if (filename) {
                    // use HTML5 a[download] attribute to specify filename
                    var a = document.createElement("a");
                    // safari doesn't support this yet
                    if (typeof a.download === 'undefined') {
                        window.location.href = downloadUrl;
                    } else {
                        a.href = downloadUrl;
                        a.download = filename;
                        document.body.appendChild(a);
                        a.click();
                    }
                } else {
                    window.location.href = downloadUrl;
                }

                setTimeout(function () { URL.revokeObjectURL(downloadUrl); }, 100); // cleanup
            }
        }
    };
    xhr.setRequestHeader('Content-type', 'application/x-www-form-urlencoded');
    xhr.send(JSON.stringify(params));
} 

function AjaxCallText(url, data, type) {
    return $.ajax({
        url: url,
        type: type ? type : "GET",
        data: data,
        contentType: "text"
    });
}  

function ajaxMultiForm(url, data) {
    i("ajaxForm");
    return $.ajax({
        type: "POST",
        url: url,
        data: new FormData(data),
        dataType:"json",
        contentType: false,
        processData: false
           });
}
function handleResult(result) {
    if (result.Success) {
        alert("تمت العملية بنجاح");
    } else {
        alert(result.Message);
    }
}
function openLargModal(url, title) {
    var $modal = $("#modal");
   //  $modal.modal({ backdrop: 'static', keyboard: false });
    showLoading();
    AjaxCall(url).done(function (response) {
        hideLoading();
        $modal.find("div.modal-body").html(response);
        $modal.find(".modal-title").text(title);
     //   $modal.find(".modal-header").css("background", "red");
        $modal.modal("show");
    }).fail(function (xhr, textStatus, errorThrown) {
        hideLoading();
        parseAndShowError(xhr, textStatus, errorThrown);
    });
}
function openViewAsModal(url,title) {
    var $modal = $("#modal");
    showLoading();
    AjaxCall(url).done(function (response) {
                        hideLoading();
                        $modal.find("div.modal-body").html(response);
                        $modal.find(".modal-title").text(title);
                        $modal.modal("show");
    }).fail(function (xhr, textStatus, errorThrown) {
                        hideLoading();

                        parseAndShowError(xhr, textStatus, errorThrown);
    });
   
}

function parseAndShowError(xhr, textStatus, errorThrown) {
    i('textStatus ' + textStatus);
    i('errorThrown ' + errorThrown);
    var msg = parseXhr(xhr);
    log(' details xhr msg:' + msg);
    alert(msg);
}

function hideModal() {
    $("#modal").modal("hide");
}

function openDetail(id, title) {
    var url = "/" + $("#Controller").val() + "/detail/"+id;
    openViewAsModal(url, title);
}
function log(msg) {
    console.log(msg);
}
function i(msg) {
    console.log('FMS: '+msg);
}

function setPageTitle(title) {
    try {
        //$("#page-title").val(title);
        $("#page-title").text(title);
    } catch (e) {
        i("cant set page title");
    } 
}



function showLoading() {
    try {
        $("#loader").show();
    } catch (e) {
        i("error on showLoading "+e);
    } 

}

function hideLoading() {

    try {
        if ($("#loader").is(":visible")) {
            $("#loader").hide();
        }    
    } catch (e) {
        i("");
    }     
    
}
function showFormLoading() {
   
    try {
        if ($("#formloader")[0]) {
            $("#formloader").show();
        }
    } catch (e) {
        i("");
    } 
}

function resetButton() {
    try {
        $(".loading").button("reset");
    } catch (e) {
        i("");
    } 
}
function hideFormLoading() {

    try {

        try {
            resetButton();
        } catch (e) {
            i("");
        } 



        if ($("#formloader")[0]) {
            if ($("#formloader").is(":visible")) {
                $("#formloader").hide();
            }
        }

        
    } catch (e) {
        i("");
    }
    try {
        $(".loading").button("reset");
    } catch (e) {
        i(e);
    } 
}

function showAlert(msg, type) {

    if ($(".alert")[0]) {
        var $alert = $(".alert");
        $alert.attr("class", "alert alert-absolute alert-" + type || "success")
            .html('<button type="button" class="close" data-dismiss="alert">' )
            .html('<i class="ace-icon fa fa-times"></i>' )
            .html("</button>")
            .html('<i class="ace-icon fa fa-times"></i>' + msg).show();
        setTimeout(function () {
                $alert.hide();
            },
            3000);
    } else {
        alert(title);
    }
 
}
function showSuccess(msg) {
    //showAlert(msg, "success");
    ar(msg);
}
function  showError(msg) {
    //showAlert(msg, "danger");
    ar(msg);
}

function getToday() {
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth() + 1; //January is 0!
    var yyyy = today.getFullYear();
    if (dd < 10) {
        dd = "0" + dd;
    }
    if (mm < 10) {
        mm = "0" + mm;
    }
    //today = dd + '/' + mm + '/' + yyyy;
    today = mm + "/" + dd + "/" + yyyy;
    return today;
}

function showImg(input) {
    i("show imag");
    $("#upload-file-info").html($(input).val());
    if (input.files && input.files[0]) {
    i("file is not null");
        var filerdr = new FileReader();
        filerdr.onload = function(e) {
            $("#preview").attr("src", e.target.result);
        };
        filerdr.readAsDataURL(input.files[0]);
    } else i("file in null");
}
function showImg2(input) {
    $("#upload-file-info").html($(input).val());
    if (input.files && input.files[0]) {
        var filerdr = new FileReader();
        filerdr.onload = function (e) {
            $("#preview2").attr("src", e.target.result);
        };
        filerdr.readAsDataURL(input.files[0]);
    } 
}

function isMobileScreen() {
    
    return true;
}


function closeDialog() {
    try {
        if (isMobileScreen())
            $("#modal").modal('hide');
        else {
            $("#dialog-message").dialog("close");
        }
    } catch (e) {
        i("");
    }
}
var CrudHelper = {
    stopEvent: function(event) {
        event.preventDefault();
        event.stopPropagation();
        return false;
    },

    printGrid: function () {
        i("print grid");
        var title = $("#Title").val();
        $(".none-print-element").hide();
        var listDiv = document.getElementById("list");

        localStorage["REPORT_title"] = title;
        localStorage["REPORT"] = listDiv.innerHTML;
        $(".none-print-element").show();
        //table.DataTable({ searching: true, info: true, paging:false, ordering: true });
        window.open("/Print/PrintDialog");
    },
    openFormUI: function (id, title) {
        showLoading();
        AjaxCall("/" + $("#Controller").val() + "/AddOrEdit/" + id).done(function (response) {
            hideLoading();
            $("#dialog-message").find("div.modal-body").html(response);
            if (title === undefined) {
                if (id) {
                    title = "تعديل السجل";
                } else {
                    title = "أضافة سجل جديد";
                }
            }
           $("#dialog-message").removeClass('hide').dialog({
                modal: true,
                width: '900',
             //   title: "<div class='widget-header widget-header-small'><h4 class='smaller'><i class='ace-icon fa fa-check'></i> " + title+"</h4></div>",
                title_html: true,
                resizable: true
                //,
                //buttons: [
                //    {
                //        text: "خروج",
                //        "class": "btn btn-danger",
                //        click: function () {
                //            $(this).dialog("close");
                //        }
                //    }
                //]
            });
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });

    },

    
    openForm: function (id, title) {

        if (isMobileScreen()) {
            showLoading();
            var $modal = $("#modal");
            $modal.find("div.modal-body").html("Loading ... ");
            $modal.modal({ backdrop: "static", keyboard: false });

            AjaxCall("/" + $("#Controller").val() + "/AddOrEdit/" + id).done(function (response) {
                hideLoading();
                $modal.find("div.modal-body").html(response);
                if (title === undefined) {
                    if (id) {
                        title = "تعديل السجل";
                    } else {
                        title = "أضافة سجل جديد";
                    }
                }
                $modal.find(".modal-title").text(title);
                $modal.modal("show");
            }).fail(function (xhr, textStatus, errorThrown) {
                parseAndShowError(xhr, textStatus, errorThrown);
            });
        } else {
            CrudHelper.openFormUI(id, title);
        }
       
       
    },
    openHelper: function () {
        var $modal = $("#modalHelp");
        showLoading();
        var controller = $("#Controller").val();
        var title = $("#Title").val();
        var url = "/" + controller + "/Help";

        $.get(url,
            function (data) {
                hideLoading();
                $modal.find(".modal-title").text(title);
                $modal.find("div.modal-body").html(data);
                $modal.modal("show");
            });
    },

    printReceipt: function (id) {

        var data = { id: id };
        var title = "";
        var controller = $("#Controller").val();
        i('print record controller: ' + controller);
        var url = "/" + controller + "/" + "Print";
        try {
            $.ajax({
                url: url,
                data: data,
                success: function (response) {
                    var result = response;
                    if (result !== undefined) {

                        localStorage["REPORT_title"] = title;
                        localStorage["REPORT"] = result;
                        window.open("/Print/PrintDialog");
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(xhr.responseText);
                }
            });

        } catch (err) {
            alert(err);
        }
    },
    showSearchBox: function () {
        if (document.getElementById("searchbox").style.display === "none") {
            document.getElementById("searchbox").style.display = "";

        }
        else {
            document.getElementById("searchbox").style.display = "none";

        }
    },
    hideSearchBox: function () {
        document.getElementById("searchbox").style.display = "none";
    },
    showSecondInput: function (self) {

        var selectedField = self.options[self.selectedIndex].value;
        if (selectedField === "4") {
            $("#value2").show();
        } else {

            $("#value2").hide();
        }
    },
    fillOperators: function (self) {
        $("#value").val("");
        $("#value2").val("");
        var sel = document.getElementById("operators");
        sel.options.length = 0;
        var arra = self.options[self.selectedIndex].value.split(",");
        var selectedValue = arra[0];

        $("#value2").hide();
        if (selectedValue === "Int64" || selectedValue === "bigint" || selectedValue === "decimal") {
            $("#value").removeClass("date-picker");
            $("#value2").hide();
            var opt = document.createElement("option");
            opt.innerHTML = "يساوي";
            //opt.value = 1;
            sel.appendChild(opt);

            var opt2 = document.createElement("option");
            opt2.innerHTML = "لا يساوي";
            //    opt2.value = 2;
            sel.appendChild(opt2);

            var opt3 = document.createElement("option");
            opt3.innerHTML = "اكبر من";
            //     opt3.value = 3;
            sel.appendChild(opt3);

            var opt4 = document.createElement("option");
            opt4.innerHTML = "اصغر من";
            //   opt4.value = 4;
            sel.appendChild(opt4);

            var opt5 = document.createElement("option");
            opt5.innerHTML = "اكبر او يساوي";
            // opt5.value = 5;
            sel.appendChild(opt5);

            var opt6 = document.createElement("option");
            opt6.innerHTML = "اصغر او يساوي";
            // opt6.value = 6;
            sel.appendChild(opt6);
        } else if (selectedValue === "Date_Time") {

            $("#value").addClass("date-picker");
            var dopt = document.createElement("option");
            dopt.innerHTML = "خلال يوم";
            dopt.value = 1;
            sel.appendChild(dopt);


            var dopt2 = document.createElement("option");
            dopt2.innerHTML = "خلال شهر";
            dopt2.value = 3;
            sel.appendChild(dopt2);


            var dopt4 = document.createElement("option");
            dopt4.innerHTML = "خلال الفترة";
            dopt4.value = 4;
            sel.appendChild(dopt4);



            //var opt3 = document.createElement("option");
            //opt3.innerHTML = "حتى يوم";
            //opt3.value = 2;
            //sel.appendChild(opt3);

        } else {
            $("#value").removeClass("date-picker");

            var ddopt3 = document.createElement("option");
            ddopt3.innerHTML = "يحتوي";
            sel.appendChild(ddopt3);

            var ddopt = document.createElement("option");
            ddopt.innerHTML = "يساوي";
            sel.appendChild(ddopt);


            var dpopt5 = document.createElement("option");
            dpopt5.innerHTML = "يبدأ ب";
            sel.appendChild(dpopt5);

            var dcopht6 = document.createElement("option");
            dcopht6.innerHTML = "ينتهي ب";
            sel.appendChild(dcopht6);

            var ddopt2 = document.createElement("option");
            ddopt2.innerHTML = "لا يساوي";
            sel.appendChild(ddopt2);


            var dpopt4 = document.createElement("option");
            dpopt4.innerHTML = "لا يحتوي";
            sel.appendChild(dpopt4);

        }

    }

};

//enums
var Services = {
    YemenMobile: 1,
    MTN: 2,
    Sabafon: 3,
    Y: 10013,
    Line: 7,
    ADSL: 6,
    Water: 8,
    Electricity: 9,
    YMOffers: 11,
    SPOpen: 13,
    MTNOffers: 10012,
    TelYemen: 10
};
// helper for Namespace pattern
var ns;
var Patterns = {
    // ** namespace pattern
    namespace: function(name) {

        // ** single var pattern
        var parts = name.split(".");
        ns = this;

        // ** iterator pattern
        for (var i = 0, len = parts.length; i < len; i++) {
            // ** || idiom
            ns[parts[i]] = ns[parts[i]] || {};
            ns = ns[parts[i]];
        }

        return ns;
    }
};
Patterns.namespace("Art").Pager = (function () {

    ////////////////Crud-List//////////////////////

    var activateCustomeActions = function() {
        var controller = $("#Controller").val();

        if (controller === undefined) {
            return;       
        }

        if (controller.includes("Client/Group") || controller.includes("DirectPayment/CommissionGroup")) {

            $("#simple-table").on("click",
                ".items",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = controller+"/Items/" + id;
                    openViewAsModal(url, "العناصر ");
                });

            $("#simple-table").on("click",
                ".permissions",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Client/Group/Permissions/" + id;
                    openViewAsModal(url, "صلاحيات ");
                });
        }
        if (controller.includes("Remittance/TargetGroup")) {

            $("#simple-table").on("click",
                ".items",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Remittance/TargetGroup/Items/" + id;
                    openViewAsModal(url, "العناصر ");
                });
        }
        if (controller.includes("Security/DbBackup")) {
            i("activateCustomeActions for DbBackup");
            $("#simple-table").on("click",
                ".download",
                function() {
                    showLoading();
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    $.ajax({
                        url: "Security/DbBackup/Download/" + id,
                        data: { id: id },
                        success: function(data) {
                            hideLoading();
                            //if (!data.Success) {
                            //    ar(data.Message);
                            //}

                        },
                        error: function(xhr, ajaxOptions, thrownError) {
                            hideLoading();
                            alert(xhr.responseText);
                        }
                    });
                });
        }
        if (controller.includes("Admin/SuspendRemittance")) {
            i("activateCustomeActions for SuspendRemittance");
            $("#simple-table").on("click",
                ".sync",
                function() {
                    if (confirm("سوف يتم مزامنة العملية , هل انت متأكد")) {

                    showLoading();
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    $.ajax({
                        url: "Admin/SuspendRemittance/Sync/" + id,
                        data: { id: id },
                        success: function(data) {
                            hideLoading();
                            ar(data.Message);
                            if (data.Success) {

                                table.parents("tr").fadeOut(1000,
                                    function() {
                                        $(this).remove();
                                    });
                            }

                        },
                        error: function(xhr, ajaxOptions, thrownError) {
                            hideLoading();
                            alert(xhr.responseText);
                        }
                    });
                }
                });


            $("#simple-table").on("click",
                ".cancel",
                function() {
                    if (confirm("سوف يتم أخفاء العملية , هل انت متأكد")) {

                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: "Remittance/SuspendRemittance/Hide/" + id,
                            data: { id: id },
                            success: function(data) {
                                hideLoading();
                                //  ar(data.Message);
                                if (data.Success) {

                                    table.parents("tr").fadeOut(1000,
                                        function() {
                                            $(this).remove();
                                        });
                                }
                                else
                                    ar(data.Message);
                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });

                    }
                });
        }
        if (controller.includes("Client/Register")) {
            i("activateCustomeActions for Register");

            $("#simple-table").on("click",
                ".activate",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    bootbox.confirm("سوف يتم تفعيل حساب العميل , هل انت متأكد?",
                        function (result) {
                            if (result) {
                                showLoading();
                                try {
                                    $.ajax({
                                        url: "/Client/Registeration/Activate",
                                        data: { id: id },
                                        success: function (data) {
                                            hideLoading();
                                            showSuccess(data);
                                            table.parents("tr").fadeOut(1000, function () {
                                                $(this).remove();
                                            });
                                         //   $("#activate").hide();
                                        },
                                        error: function (xhr, ajaxOptions, thrownError) {
                                            hideLoading();
                                            handleXhr(xhr);
                                        }
                                    });
                                } catch (e) {
                                    alert(e);
                                }
                            }
                        });
                });

        }
        if (controller.includes("Clients/DirectRemittance") || controller.includes("Remittance/SyncRemittanceIn") || controller.includes("Remittance/RemittanceIn")) {
            i("activateCustomeActions for Remittance");
            $("#simple-table").on("click",
                ".express",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    $.ajax({
                        url: controller+'/GetExpress',
                        data: { id: id },
                        success: function (data) {
                            alert(data);
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            alert(xhr.responseText);

                        }
                    });
                });

        }
        if (controller.includes("Security/AccountApi")) {
            i("activateCustomeActions for AccountApi");
            

        }
        if (controller.includes("Clients/SatelliteOrder")) { // aladdin
            i("TopupPayment for Topup");
            $("#simple-table").on("click",
                ".show",
                function () {
                    showLoading();
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    $.ajax({
                        url: controller + "/Show/" + id,
                        data: { id: id },
                        success: function (data) {
                            hideLoading();
                            ar(data);
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            hideLoading();
                            alert(xhr.responseText);
                        }
                    });
                });
        }
        if (controller.includes("Cards/CardType")) { // aladdin
            i("activateCustomeActions for WifiFaction");
            $("#simple-table").on("click",
                ".faction",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    window.location.href = "/#!/route/Cards/CardFaction/CFaction/" + id;
                });

        }
        if (controller.includes("Wifi/WifiProvider")) { // aladdin
            i("activateCustomeActions for WifiFaction");
            $("#simple-table").on("click",
                ".faction",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    window.location.href = "/#!/route/Wifi/WifiFaction/Faction/" + id;
                });

        }
        if (controller.includes("Satellite/SatelliteProvider")) { // aladdin
            i("activateCustomeActions for SatelliteFaction");
            $("#simple-table").on("click",
                ".faction",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    window.location.href = "/#!/route/Satellite/SatelliteFaction/Faction/" + id;
                });

        }
        if (controller.includes("Clients/TopupPayment")) {
            i("TopupPayment for Topup");
            $('#simple-table tr').each(function () {
                var status = $(this).find("td:eq(7)").html();
                i("status= " + status);
                if (status === 'مرحلة') {
                    $(this).addClass('success');
                }
                else if (status === 'معلقة' || status === 'غير معروف') {
                    $(this).addClass('warning');
                }
                else if (status === 'فشل') {
                    $(this).addClass('Danger');
                //    $(this).css('background-color', 'Danger');
                }
            });
    

            $("#simple-table").on("click",
                ".check",
                function () {
                    //var currentRow = $(this).closest("tr");

                   // var col1 = currentRow.find("td:eq(0)").text(); 
                    if (confirm("هل تريد التحقق من العملية")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: controller + "/CheckStatus/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar(data);
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });
        }
        if (controller.includes("MTNFaction") || controller.includes("MTNBagat") || controller.includes("SPFaction") || controller.includes("SPBagat") || controller.includes("YFaction") || controller.includes("SpBagatNorth") || controller.includes("SpNorth") ) {
            i("activateCustomeActions for Bond");
            $("#simple-table").on("click",
                ".BondDetail",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    window.location.href = "/#!/route/DirectPayment/Bundle/BundleDetail/" + id;
                });

        }
        if (controller.includes("LiveTopup") || controller.includes("Quotation") || controller.includes("ItemCost") || controller.includes("MTN")) {
            i("TopupPayment for Topup");
            try {
                $('#simple-table tr').each(function () {
                    var status = $(this).find("td:eq(2)").html();
                    i("status= " + status);
                    if (status.includes('يمن موبايل')) {
                        $(this).addClass('Danger');
                    }
                    else if (status.includes('أم تي ان')) {
                        $(this).addClass('warning');
                    }
                    else if (status.includes('سبافون')) {
                        $(this).addClass('info');
                    }
                    else if (status.includes('الانترنت') || status.includes('هاتف')) {
                        $(this).addClass('Success');
                    }

                });
            }
            catch (e) {
                i(e);
            }

        }
        if (controller.includes("DirectPayment/Topup") || controller.includes("DirectPayment/SuspendTopup") || controller.includes("Remittance/RemittanceIn") || controller.includes("Clients/DirectRemittanceIn") || controller.includes("DirectPayment/GomalaTopup")) {
            i("TopupPayment for Topup set status");
            try {
                //   $('#simple-table tr').each(function () {
                $("#simple-table tr").find("td").each(function () {
                    //   var status = $(this).find("td:eq(11)").html();
                    //   var status = $(this).find("td:eq('الحالة')").html();
                    var status = $(this).html();

                    i("status= " + status);
                    if (status === 'مرحلة') {
                        $(this).addClass('label label-success arrowed-in arrowed-in-right');
                    }
                    else if (status === 'معلقة') {
                        $(this).addClass('label label-warning arrowed-in arrowed-in-right');
                    }
                    else if (status === 'قيد التنفيذ') {
                        $(this).addClass('label label-purple arrowed-in arrowed-in-right');
                    }
                    else if (status === 'ملغية') {
                        $(this).addClass('label label-danger arrowed-in arrowed-in-right');
                    }
                    else if (status === 'معكوسة') {
                        $(this).addClass('label label-danger arrowed-in arrowed-in-right');
                    }
                    else {
                       // $(this).addClass('label label-grey arrowed-in arrowed-in-right');
                    }
                });
            }
            catch(e)
            {
                i('set status tag error: ' + e);
            }
        }
        if (controller.includes("DirectPayment/Topup") || controller.includes("DirectPayment/SuspendTopup") || controller.includes("DirectPayment/GomalaTopup") ) {
            i("activateCustomeActions for Topup");
            $("#simple-table").on("click",
                ".detail",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    //window.location.href = "/#!/route/DirectPayment/Topup/Detail/" + id;
                    var url = controller +"/Detail/" + id;
                    //openViewAsModal(url, "التفاصيل");
                    openLargModal(url, "التفاصيل");
                });
            $("#simple-table").on("click",
                ".depend",
                function () {
                    if (confirm("سوف يتم أعتماد العملية , هل انت متأكد")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: "DirectPayment/Topup/Depend/" + id,
                            data: { id: id },
                            success: function(data) {
                                hideLoading();
                                ar("تم اعتماد العملية بنجاح");
                                table.parents("tr").fadeOut(1000,
                                    function() {
                                        $(this).remove();
                                    });
                            },
                            error: function(xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });


            i("activateCustomeActions for Topup");
            $("#simple-table").on("click",
                ".relay",
                function () {
                    if (confirm("سوف يتم ترحيل العملية , هل انت متأكد")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            //url: "DirectPayment/Topup/Relay/" + id,
                            url: controller+"/Relay/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar("تم ترحيل العملية بنجاح");
                                table.parents("tr").fadeOut(1000,
                                    function () {
                                        $(this).remove();
                                    });
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });

            $("#simple-table").on("click",
                ".cancel",
                function () {
                    if (confirm("سوف يتم إلغاء العملية , هل انت متأكد")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: controller+"/Cancel/" + id,
                        //    url: "DirectPayment/Topup/Cancel/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar("تم إلغاء العملية بنجاح");
                                table.parents("tr").fadeOut(1000,
                                    function () {
                                        $(this).remove();
                                    });
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });


            $("#simple-table").on("click",
                ".check",
                function () {
                    if (confirm("هل تريد التحقق من العملية")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: controller+"/CheckStatus/" + id,
                        //    url: "DirectPayment/Topup/CheckStatus/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar(data);
                              
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });
        }

        if (controller.includes("DirectPayment/BagatPayment")) {
            i("activateCustomeActions for BagatPayment");
            $("#simple-table").on("click",
                ".detail",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    window.location.href = "/#!/route/DirectPayment/BagatPayment/Detail/" + id;
                });
            $("#simple-table").on("click",
                ".depend",
                function () {
                    if (confirm("سوف يتم أعتماد العملية , هل انت متأكد")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: "DirectPayment/BagatPayment/Depend/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar("تم اعتماد العملية بنجاح");
                                table.parents("tr").fadeOut(1000,
                                    function () {
                                        $(this).remove();
                                    });
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });


            i("activateCustomeActions for Topup");
            $("#simple-table").on("click",
                ".relay",
                function () {
                    if (confirm("سوف يتم ترحيل العملية , هل انت متأكد")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: "DirectPayment/BagatPayment/Relay/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar("تم ترحيل العملية بنجاح");
                                table.parents("tr").fadeOut(1000,
                                    function () {
                                        $(this).remove();
                                    });
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });

            $("#simple-table").on("click",
                ".cancel",
                function () {
                    if (confirm("سوف يتم إلغاء العملية , هل انت متأكد")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: "DirectPayment/BagatPayment/Cancel/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar("تم إلغاء العملية بنجاح");
                                table.parents("tr").fadeOut(1000,
                                    function () {
                                        $(this).remove();
                                    });
                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });
            $("#simple-table").on("click",
                ".check",
                function () {
                    if (confirm("هل تريد التحقق من العملية")) {
                        showLoading();
                        var table = $(this);
                        var id = table.parents("tr").attr("data-id");
                        $.ajax({
                            url: "DirectPayment/BagatPayment/CheckStatus/" + id,
                            data: { id: id },
                            success: function (data) {
                                hideLoading();
                                ar(data);

                            },
                            error: function (xhr, ajaxOptions, thrownError) {
                                hideLoading();
                                alert(xhr.responseText);
                            }
                        });
                    }
                });
        }
        if (controller.includes("Client/PaymentSync")) {
            i("activateCustomeActions for PaymentSync");
            $("#simple-table").on("click",
                ".depend",
                function () {
                    showLoading();
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    $.ajax({
                        url: "Client/PaymentSync/Depend/"+id,
                        data: { id: id },
                        success: function (data) {
                            hideLoading();
                            ar("تم اعتماد العملية بنجاح");
                            table.parents("tr").fadeOut(1000, function () {
                                $(this).remove();
                            });
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            hideLoading();
                            alert(xhr.responseText);
                        }
                    });
                });
        }

        if (controller.includes("Security/User")) {
            i("activateCustomeActions for user");
      //  $(".edit-record").hide();

            $("#simple-table").on("click",
                ".permissions",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    var title = "أعداد صلاحيات المستخدم";
                    openViewAsModal("/Security/User/Permission/" + id, title);
                });

            $("#simple-table").on("click",
                ".reset-pass",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    bootbox.confirm("سوف يتم اعداد كلمة المرور لهذا المستخدم , هل انت متأكد?",
                        function (result) {
                            if (result) {
                                try {
                                    $.ajax({
                                        url: '/Security/User/ResetPassword',
                                        data: { id: id },
                                        success: function (data) {
                                            alert(data);
                                        },
                                        error: function (xhr, ajaxOptions, thrownError) {
                                            alert(xhr.responseText);
                                        }
                                    });
                                } catch (e) {
                                    alert(e);
                                }
                            }
                        });
                });
        }

        if (controller.includes("Branch/ExternalBranch")) {
            i("activateCustomeActions for Branch");
            $("#simple-table").on("click",
                ".clients",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Branch/ExternalBranch/Clients/" + id;
                    window.location.href = "/#!/route/Branch/ExternalBranch/clients/" + id;
                });

            $("#simple-table").on("click",
                ".users",
                function () {

                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    var roleId = 5;
                    window.location.href = "/#!/UsersDetails/" + roleId + "/" + id;
                });


            $("#simple-table").on("click",
                ".permissions",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Branch/ExternalBranch/Permissions/" + id;
                    openViewAsModal(url, "صلاحيات الفرع");
                });
        }

        if (controller.includes("AgentPoint")) {
            i("activateCustomeActions for AgentPoint");

            $("#simple-table").on("click",
                ".bindusers",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Agency/AgentPoint/Users/" + id;
                    openViewAsModal(url, "ربط المستخدمين");
                });
        }
        if (controller.includes("Agency/Agent")) {
            i("activateCustomeActions for Agent");

            $("#simple-table").on("click",
                ".detail",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    //window.location.href = "/#!/route/Agency/Agent/Detail/" + id;
                    var url = "/Agency/Agent/Detail/" + id;
                    openViewAsModal(url, "التفاصيل ");
                });

            $("#simple-table").on("click",
                ".users",
                function() {

                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    var roleId = 3;
                    window.location.href = "/#!/UsersDetails/" + roleId + "/" + id;
                });


            $("#simple-table").on("click",
                ".permissions",
                function() {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Agency/Agent/Permissions/" + id;
                    openViewAsModal(url, "صلاحيات ");
                });


            $("#simple-table").on("click",
                ".clients",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                 //   var url = "/Agency/Agent/Clients/" + id;
                    window.location.href = "/#!/route/agency/agent/clients/" + id;
                    //openViewAsModal(url, "ربط العملاء ");
                });
        }
        if (controller.includes("Agency/Distributor")) { // aladdin
            i("activateCustomeActions for Product");
            $("#simple-table").on("click",
                ".clients",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                 //   var url = "/Agency/Distributor/Clients/" + id;
                    window.location.href = "/#!/route/Agency/Distributor/clients/" + id;
                });


            $("#simple-table").on("click",
                ".detail",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    //window.location.href = "/#!/route/Agency/Agent/Detail/" + id;
                    var url = "/Agency/Distributor/Detail/" + id;
                    openViewAsModal(url, "التفاصيل ");
                });

            $("#simple-table").on("click",
                ".users",
                function () {

                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    var roleId = 6;
                    window.location.href = "/#!/UsersDetails/" + roleId + "/" + id;
                });


            $("#simple-table").on("click",
                ".permissions",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Agency/Distributor/Permissions/" + id;
                    openViewAsModal(url, "صلاحيات ");
                });



        }
        if (controller.includes("Admin/Order")) {
            i("activateCustomeActions for Order");
            $("#simple-table").on("click",
                ".proccess",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    openDetail(id, "معالجة الطلب");
                });
    $("#simple-table").on("click",
                ".show",
                function () {
                    var id = $(this).parents("tr").attr("data-id");
                    openDetail(id, "مشاهدة الطلب");
                });
        }
        if (controller.includes("Client/Client")) {
            i("activateCustomeActions for Client");
            $("#simple-table").on("click",
                ".detail",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    window.location.href = "/#!/route/Client/Client/Detail/" + id;
                });
            $("#simple-table").on("click",
                ".users",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    var roleId = 2;
                    window.location.href = "/#!/UsersDetails/" + roleId + "/" + id;
                });
        }
        if (controller.includes("Merchants/Merchant")) {
            i("activateCustomeActions form merchant");
            $("#simple-table").on("click",
                ".users",
                function () {
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                    var roleId = 4;
                    window.location.href = "/#!/UsersDetails/" + roleId + "/" + id;
                });
            i("init permission form merchant");
            $("#simple-table").on("click",
                ".permissions",
                function () {
                    i("on permission merchant clicked");
                    var id = $(this).parents("tr").attr("data-id");
                    var url = "/Merchants/Merchant/Permissions/" + id;
                    openViewAsModal(url, "صلاحيات ");
                });
        }
    };

    var activateRecordActions = function () {
        activateCustomeActions();
        $("#simple-table").on("click", ".edit-record",
            function () {
                i("edited clicked");
                var table = $(this);
                var id = table.parents("tr").attr("data-id");
                CrudHelper.openForm(id);

            });

        $("#simple-table").on("click", ".delete-record",
            function () {
                i("Art.Pager del clicked");
                var table = $(this);
                var id = table.parents("tr").attr("data-id");
                var controller = $("#Controller").val();
                var url = "/" + controller + "/Delete/" + id;
                if (confirm("هل انت متأكد تريد حذف السجل?")) {
                    showLoading();
                    $.ajax({
                        url: url,
                        type: "get",
                        success: function (data) {
                            hideLoading();
                            if (data.Success) {
                                console.log("del success msg:" + data.Message);
                                table.parents("tr").fadeOut(1000, function () {
                                    $(this).remove();
                                });
                                //    table.parents('tr').remove();
                                showSuccess(data.Message);
                            } else {
                                showError(data.Message);
                            }
                        },
                        error: function (xhr, textStatus, errorThrown) {
                            hideLoading();
                            i("textStatus " + textStatus);
                            i("errorThrown " + errorThrown);

                            var msg = parseXhr(xhr);
                            i("on delete xhr msg:" + msg);
                            alert(msg);
                            //ar("حدث مشكلة أثناء حذف السجل , قم بالمحاولة لاحقاً");
                        }
                    });
                }
            });


        $("#simple-table").on("click", ".print-record",
            function () {
                i("edited clicked");
                var table = $(this);
                var id = table.parents("tr").attr("data-id");
                CrudHelper.printReceipt(id);

            });

        try {
            $("#simple-table").on("click", ".depend-record",
                function() {
                    i("depend clicked");
                    if (confirm("سوف يتم أعتماد هذا السجل , هل انت متأكد?")) {
                    
                    var table = $(this);
                    var id = table.parents("tr").attr("data-id");
                        var controller = $("#Controller").val();
                        var url = "/" + controller + "/Depend/" + id;
                    $.ajax({
                        url: url,
                        data: { id: id },
                        success: function(data) {
                            hideLoading();
                            ar("تم اعتماد العملية بنجاح");
                            table.parents("tr").fadeOut(1000,
                                function() {
                                    $(this).remove();
                                });
                        },
                        error: function(xhr, ajaxOptions, thrownError) {
                            hideLoading();
                            alert(xhr.responseText);
                        }
                    });

                }
        });
        } catch (e) {
            i("error on load depend button"+e);
        } 

        $("#simple-table").on("click",
            ".detail",
            function () {
                var table = $(this);
                var id = table.parents("tr").attr("data-id");
                //var url = $('#Controller').val()+"/Detail/" + id;
               // openViewAsModal(url, "التفاصيل");

                if (!$('#Controller').val().includes('Client/Client'))
                openDetail(id,'التفاصيل');
            });
    };
    var activatePager = function () {
        $("a[name^='page']").on("click", onPageSubmit);
      
    };

    var onPageSubmit = function (event) {
        i('pager change');
        var parent = $(this).parent();
        if (parent.hasClass("disabled")) return CrudHelper.stopEvent(event);
        if (parent.hasClass("active")) return CrudHelper.stopEvent(event);

        var page = $(this).attr("aria-valuemax");
        $("#page").val(page);
        var pageSize = $('#pageSize').val();
        i('pageSize ' + pageSize);
        var condition = $("#QryCondition").val();
        var controller = $("#Controller").val();

        i('pager condition:' + condition);
        var da = { condition: condition, page: page, pageSize: pageSize };
        var options = {
            url: "/" + controller + "/Index",
            data: da
        };
        $.ajax(options).done(function (data) {
            try {
                hideLoading();

                if (!data.startsWith("error:")) {
                    $("#list").replaceWith(data);
                }
                else {
                    alert(data);
                }
            } catch (e) {
                alert(e);
            }
            activatePager();
            activateRecordActions();
        });
        return CrudHelper.stopEvent(event);
    };
    var initForm = function () {
        function onSubmitSuccess(data) {
            showAlert("تم حفط السجل بنجاح");
            $("#modal").modal("hide");
            refreshData(data);
            activatePager();
            activateRecordActions();
        }

        $("#crud-form").submit(function (e) {

            i("pattern submit");
            var isvalid = $("#crud-form").valid();
            if (!isvalid) {
                showError("Form not valid");
                return;
            }
            var form = $("#crud-form");
            //      i('form.serialize: ' + form.serialize());
            var url = "/" + $("#Controller").val() + "/AddOrEdit";

            $.post(url, form.serialize())
                .done(function (data) {
                    onSubmitSuccess(data);
                }).fail(function (xhr, status, error) {
                    alert(parseXhr(xhr));
                });

            e.preventDefault(); // avoid to execute the actual submit of the form.
        });
    };
    ////////////////CrudLayout//////////////////////
    var ajaxSubmit = function (event) {

        i('pagesize change');
        var controller = $("#Controller").val();
        var $form = $(this);

        var input_name = "pagerSize";
       // var input = $("#search-records-form :input[name='" + input_name + "']"); 

       // var size = input.val();
        var pageSize = $('#pageSize').val();
        i('get input from search pageSize;' + pageSize);
        var $inputs = $('#search-records-form :input');
        i('get inputs from search');
        $inputs.each(function () {
            i('inputs.each name ' + this.name);
            if (this.name === input_name)
            {
                i('set pagersize val2');
                $(this).val(pageSize);
            }
        });
        i('go');
        var options = {
            url: "/" + controller + "/Search",
            data: $form.serialize()
        };
        showLoading();
        $.ajax(options).done(function (data) {
            hideLoading();
            $("#list").replaceWith(data);
            activatePager();
            activateRecordActions();
        });
        return CrudHelper.stopEvent(event);
    };   
    var initPage = function () {
        $("#page-title").text($("#Title").val());
        $(".alert").hide();
        hideLoading();

        $("#help")
            .on("click",
                function () {
                    //var controller = $("#Controller").val();
                    //var url = "/" + controller + "/Help";
                    //openViewAsModal(url, "Help Modal");
                    $("#modalHelp").modal("show");
            });

        $("#add-record").on("click",
            function () {
                i("trying to open modal for add new record");
                CrudHelper.openForm(0);
            });
    };    

    var fillSearchFields = function () {
        i("list len " + $("#field option").length);
        if ($("#field option").length > 1) {
            return;
        }
        i("load search box");
        var url = $("#Controller").val() + "/GetFeilds";
        AjaxCall(url).done(function (response) {
            i("get fields response: " + response);
            i("get fields stringify response: " + JSON.stringify(response));
            fillList(response);
        }).fail(function (xhr, textStatus, errorThrown) {
            //parseAndShowError(xhr, textStatus, errorThrown);
        });
    };
    function fillList(response) {
        var element = $("#field");
        if (response.length > 0) {
            element.html("");
            var options = '<option value="0">أختر الحقل</option>';
            for (var i = 0; i < response.length; i++) {
                options += '<option value="' + response[i].Type + "," + response[i].Name + '">' + response[i].Name + "</option>";
            }
            element.append(options);
        }
    }
    var activateSearch = function () {

        /*  
           $("input[name='checkbox']")
               .on("change", function () {
                   $("#page").val(1);
                   $("#ordersOnly").val($(this).is(':checked'));
                   $(this).closest("form").submit();
               });*/

        document.getElementById("searchbox").style.display = "none";
        $("#value2").addClass("date-picker");
        $("#value2").hide();

        $("#search")
            .on("click",
                function () {

                    CrudHelper.showSearchBox();

                    fillSearchFields();
                });


        $("select[name='field']")
            .on("change",
                function () {
                    CrudHelper.fillOperators(this);
                });
        $("select[name='operators']")
            .on("change",
                function () {
                    CrudHelper.showSecondInput(this);
                });
        $("#search-records")
            .on("click",
                function () {
                    $("#page").val(1);
                  //  $(this).closest("form").submit();
                });
        $("form").submit(ajaxSubmit);
        $("#pageSize").on("change", onPageSubmit);

    };
    var activatePrint = function () {
        $("#print-grid").on("click",
            function () {
                CrudHelper.printGrid();
            });
    };

    var start = function () {
        initPage();
        activatePager();
        activateRecordActions();
        activateSearch();
        activatePrint();

    };

    var activateList = function () {
        activateRecordActions();
        // activateCustomeActions();
        activatePager();
    };

    return {
        start: start,
        activateList: activateList
    };
})();

Patterns.namespace("Art").Report = (function() {


    var activateDate = function () {
        var endDate = $("#end_date");
        try {

            endDate.hide();
            $("#StartDate").val(getToday());
            $("#EndDate").val(getToday());

        } catch (e) {
            i("date error" + e);
        }

        $("select[name='PeriodType']")
            .on("change",
                function () {
                    if (this.options[self.selectedIndex] === undefined)
                        return;
                    var selectedField = this.options[self.selectedIndex].text;
                    if (selectedField === "خلال فترة") {
                        endDate.show();
                    } else {

                        endDate.hide();
                    }
                });
    };
    var activateReport = function () {

        $("#page-title").text($("#Title").val());
        $(".alert").hide();
    };
    var start = function () {
        activateReport();
        activateDate();
    };

    return { start: start };
})();

Patterns.namespace("Art").History = (function() {

    var pushingState = false;
    var History = window.History;
    var pop;

    // initialize history 
    var init = function(popCallback) {

        pop = popCallback;

        if (!History.enabled) return;

        // bind to statechange event (triggers at pushState call and when browser next/prev button is clicked)
        History.Adapter.bind(window,
            "statechange",
            function(event) { // Note: We are using statechange instead of popstate

                // statechange fires 'too often'. here we prevent loading following a pushState call.
                if (pushingState === true) {
                    pushingState = false;
                    return;
                }

                // load page without layout and activate pager and filters
                var state = History.getState();

                pop(state);
            });
    };

    // push state onto history stack
    var push = function(state, name) {
        pushingState = true;
        History.pushState(null, name || "Art shop", state);
    };

    // resplace current state on history stack
    var replace = function(state, name) {
        pushingState = true;
        History.replaceState(null, name || "Art shop", state);
    };

    // 'reveal' functions
    return {
        init: init,
        push: push,
        replace: replace
    };

})();

Patterns.namespace("Art").Utils = (function() {

    // stops default events 
    var stopEvent = function(event) {
        event.preventDefault();
        event.stopPropagation();
        return false;
    };

    // general money formatting function
    function toMoney(number, places, symbol, thousand, decimal) {
        number = number || 0;
        places = !isNaN(places = Math.abs(places)) ? places : 2;

        symbol = symbol !== undefined ? symbol : "$";
        thousand = thousand || ",";
        decimal = decimal || ".";

        var negative = number < 0 ? "-" : "";
        var i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "";
        var j = (j = i.length) > 3 ? j % 3 : 0;

        return symbol +
            negative +
            (j ? i.substr(0, j) + thousand : "") +
            i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) +
            (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
    }

    return {
        stopEvent: stopEvent,
        toMoney: toMoney
    };
})();

// application specific shared code

Patterns.namespace("Art").App = (function() {

    // first method called in newly rendered page    

    var openPage = function() {

       
        //try {

        //    $('.date-picker').datepicker({
        //        dateFormat: "dd/MM/yy",
        //        changeMonth: true,
        //        changeYear: true,
        //        yearRange: "-60:+0",
        //        autoclose: true,
        //        todayHighlight: true
        //    }).next().on(ace.click_event,
        //        function () {
        //            $(this).prev().focus();
        //        });
        //} catch (e) {
        //    alert("Couldnt set date-picker: " + e);
        //}
        i("on loading event");
        $(".loading").on("click", function () {
            i("on loading click");
            var $this = $(this);
            $this.button("loading");
        });

        // fades alert messages after 4 seconds
        $("#alert-success, #alert-failure").fadeIn(500).delay(3500).fadeOut(1000,
            function() {
                $(this).remove();
            });
    };
    var updateCartOnPage = function(delta) {

        //update cart display
        var count = parseInt($("#cartcount").val(), 10) + delta;
        if (count < 0) count = 0;

        $("#cartcount").val(count);

        if (count === 0) {
            $("#countshow").html("");
            $("#thecart").css("background-color", "");
        } else {
            $("#countshow").html("(" + count + ")");
            $("#thecart").css("background-color", "orange").css("color", "white");
        }
    };

    return {
        //updateCartOnPage: updateCartOnPage,
        openPage: openPage
    };

})();

// activate potential alerts when opening page

$(function() {
   // console.log("pattern load");

    var app = Patterns.Art.App;
    app.openPage();
   
});
