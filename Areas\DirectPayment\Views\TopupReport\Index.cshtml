﻿@model AppTech.MSMS.Domain.Reports.Models.TopupOperatorModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }
    <span class="lbl">اسم الحساب </span>
    <select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
    <div class="space-6"></div>

    @*<span class="lbl">اسم المزود </span>
        @Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.Providers)
        <div class="space-6"></div>*@

    <span class="lbl">المشغل</span>
    @Html.EnumDropDownListFor(model => model.Operator)
    <div class="space-6"></div>
    <span class="lbl">الصنف</span>
    @Html.DropDownListFor(model => model.ItemID, (SelectList)ViewBag.Items)
    <div class="space-6"></div>


    <span class="lbl">نوع الخط</span>
    @Html.DropDownListFor(model => model.LineType, new[]
       {
           new SelectListItem {Text = "الكل", Value = ""},
           new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق"},
           new SelectListItem {Text = "فوترة", Value = "فوترة"}
       }, new { onchange = "onLineType(this) " })

    <div class="space-6"></div>
    @*<span class="lbl">مرجع المزود</span>
        @Html.EditorFor(model => model.SNO)*@


    <span class="lbl">الحالة</span>
    @Html.EnumDropDownListFor(model => model.Status)
    <div class="space-6"></div>

    @Html.EditorFor(model => model.SNO, new { htmlAttributes = new { placeholder = "رقم المشترك" } })
    <div class="space-6"></div>
    <span class="lbl"> نوع التقرير</span>
    @Html.EnumDropDownListFor(m => m.Type)

    
   
</div>
<script>
    $(function () {
        $("select#Status").prop('selectedIndex', 2);  

           $('#Operator').on("change",
            function() {
                //var opId=@Model.Operator;
                var opId=$(this).val();
                i('change opId: '+opId);

                 fillDataList('ItemID','/DirectPayment/TopupReport/GetItems/'+opId,false,'كافة الأصناف')
               });

        AjaxCall('/Print/GetParties').done(function (response) {
            console.log('get parties');
            if (response.length > 0) {
                $('#AccountID').html('');
                var options = '<option value="0">كافة الحسابات</option>';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#AccountID').append(options);

            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });



        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>