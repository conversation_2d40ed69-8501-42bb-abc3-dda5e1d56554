﻿@{
    Layout = "~/Views/Shared/_CrudLayout.cshtml";
    ViewBag.Title = "دليل المجموعات";
}
<br />
@*<div class="row">
    <div class="col-md-2 ">
        <select id="Account" name="Accont" style="width:100%;"></select>
    </div>
    <div class="col-md-2 ">
        <button class="btn btn-white btn-info btn-bold btn-round" id="search1" style="width:30%;">بحث</button>
    </div>
</div>*@
<script>
    $(function () {
        loadDataAccontList();
        function loadDataAccontList() {
            fillDataList('Account', '/Print/GetParties', false, "")
        }
    });

    $(function () {
        $("#search1").on("click",
            function (e) {
                //   e.p
                var Account = $("#Account").val();
                var pageSize = $("#pageSize").val();


                i(' Account :' + Account);

                var data = {
                    pageSize: pageSize,
                   Account: Account
                };
                showLoading();
                AjaxCall('Client/Group/Searchs', data).done(function (response) {
                    //    resetButton();
                    //    i('search response' +response)
                    hideLoading();
                    $("#list").replaceWith(response);
                    var pager = Patterns.Art.Pager;
                    pager.activateList();
                    //  ar(response);
                }).fail(function (xhr, textStatus, errorThrown) {
                    hideLoading();
                    //   resetButton();
                    parseAndShowError(xhr, textStatus, errorThrown);

                });


            });

    });
</script>
