﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.OpeningBalance
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<div class="form-group">
    @Html.Label("اسم الحساب", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })
        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>


<div class="form-group">
    @Html.Label("الحساب المقابل", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.Obout(new ComboBox("ExchangeAccountID")
        {
            Width = 300,
            SelectedValue = Model.ExchangeAccountID == 0 ? null : Model.ExchangeAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })
        @Html.ValidationMessageFor(model => model.ExchangeAccountID)
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    <div class="col-md-12">
        @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
        @Html.DropDownListFor(m => m.CurrencyID, (SelectList) ViewBag.Currencies)
    </div>
</div>

<div class="form-group">
    @Html.Label("الحالة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownList("Credit", new[]
        {
            new SelectListItem {Text = "دائن-له", Value = bool.TrueString},
            new SelectListItem {Text = "مدين-علية", Value = bool.FalseString}
        })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>