﻿@model IEnumerable<AppTech.MSMS.Domain.Services.UserPermission>
@{
    Layout = "~/Views/Shared/_FormModal.cshtml";
}


@{
    if (Model.Any(x => x.PageActions.Any(y => !y.IsAllow)))
    {
        <input type="checkbox" onClick="toggle(this)" />
    }
    else
    {
        <input type="checkbox" checked="checked" onClick="toggle(this)" />
    }
}

<strong>منح كافة الصلاحيات لجميع الإدارات</strong>

<input type="hidden" name="userId" value="@ViewBag.UserID" />

<div id="accordion" class="accordion-style2">
   
    
    @foreach (var module in AppTech.MSMS.Domain.DomainManager.Modules)
    {
        <div class="group">
            <h3 class="accordion-header">   @module.Title</h3>
            <div>
                
                @{
                    if (Model.Any(x => x.Page.Assembly.Equals(module.Name) && x.PageActions.Any(y => !y.Is<PERSON>)))
                    {
                        <input type="checkbox" class="action" style="margin: 12px;" onClick="togglePageActions(this,'@module.Name')" />
                    }
                    else
                    {
                        <input type="checkbox" checked="checked" class="action" style="margin: 12px;" onClick="togglePageActions(this,'@module.Name')" />
                    }
                }
                <strong>منح كافة صلاحيات @module.Title</strong>

                <div class="table-responsive " style="margin-bottom: 12px;">
            <table id="items-table" class="table table-hover table-striped items-table">
                <thead class="">
                <tr style="background-color: gray; color: white">

                    <th style="text-align: center;">
                
                    </th>
                    <th style="text-align: center;">

                    </th>

                    <th style="text-align: center;">
                        النوع
                    </th>

                </tr>
                </thead>
                <tbody id="table-body">
                    @foreach (var item in Model.Where(x => x.Page.Assembly.Equals(module.Name)))
                    {
                        <tr>
                            <td class="align-center">
                                <strong>@Html.DisplayFor(modelItem => item.Page.Title)</strong>
                                <input type="checkbox" checked="@item.PageActions.Any(x=>x.IsAllow)" class="action @module.Name" style="margin: 12px;" onClick="togglePageActions(this,'@item.Page.ID')" />

                            </td>
                            <td style="text-align: right;">
                                @foreach (var op in item.PageActions)
                                {
                                    <input id="chk@(op.Value)"
                                           name="items"
                                           class="action @item.Page.ID @module.Name"
                                           type="checkbox"
                                           value="@op.Value"
                                           checked="@op.IsAllow"
                                           style="margin: 12px;" />

                                    <strong>@op.Text</strong>
                                    @*  <br/>*@
                                }
                            </td>


                            <td style="text-align: center;">
                                @{
                                    if (item.Page.Class == 0)
                                    {
                                        <strong>تهيئة</strong>
                                    }
                                    else if (item.Page.Class == 1)
                                    {
                                        <strong>عمليات</strong>
                                    }
                                    else if (item.Page.Class == 2)
                                    {
                                        <strong>تقارير</strong>
                                    }
                                    else
                                    {
                                        <strong>أخرى</strong>
                                    }
                                }

                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
            
        </div>
        </div>
            }

 </div>

    <script>


        jQuery(document).ready(function ($) {

            formHelper.onBegin = function () {

                if (confirm("سوف يتم حفظ الصلاحيات , هل انت متأكد?")) {
                    return true;
                }
                else {
                    return false;
                }
              
            }
            try {
              

            } catch (e) {
                i("data error: " + e);
            }
        });
    </script>
    <script>

        function togglePageActions(source, pageId) {
            var checkboxes = $("." + pageId);
            for (var i = 0, n = checkboxes.length; i < n; i++) {
                checkboxes[i].checked = source.checked;
            }
        }
        function toggle(source) {
            var checkboxes = $(".action");
            for (var i = 0, n = checkboxes.length; i < n; i++) {
                checkboxes[i].checked = source.checked;
            }
        }

        $(function () {
            //jquery accordion
            $("#accordion").accordion({
                collapsible: true,
                heightStyle: "content",
                animate: 250,
                header: ".accordion-header"
            }).sortable({
                axis: "y",
                handle: ".accordion-header",
                stop: function (event, ui) {
                    // IE doesn't register the blur when sorting
                    // so trigger focusout handlers to remove .ui-state-focus
                    ui.item.children(".accordion-header").triggerHandler("focusout");
                }
            });
        });

            //function OnFormBegin(context) {
            //}


            //function onCrudSuccess(data) {
            //    onPermSuccess(data);
            //}

            //function onCrudFailure(xhr, status) {
            //    hideFormLoading();
            //    var msg = parseXhr(xhr);
            //    log('onCrudFailure xhr msg:' + msg);
            //    alert(msg);
            //}

            //function onPermSuccess(data) {
            //    hideModal();
            //    showSuccess(data);
            //}


    </script>
