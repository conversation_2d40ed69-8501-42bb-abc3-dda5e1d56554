@echo off
echo ========================================
echo     نسخ الملفات المحدثة - AppTech MSMS
echo ========================================

echo.
echo 1. إنشاء المجلدات...
if not exist "E:\inetpub\TopupProcessor" mkdir "E:\inetpub\TopupProcessor"
if not exist "E:\inetpub\Data" mkdir "E:\inetpub\Data"
if not exist "E:\inetpub\SQL" mkdir "E:\inetpub\SQL"
if not exist "E:\inetpub\Documentation" mkdir "E:\inetpub\Documentation"
if not exist "E:\inetpub\OLAP" mkdir "E:\inetpub\OLAP"
echo تم إنشاء المجلدات

echo.
echo 2. نسخ TopupProcessor...
copy "E:\inetpub\New folder\TopupProcessor v1.2 - 4.2\*" "E:\inetpub\TopupProcessor\" /Y
echo تم نسخ TopupProcessor

echo.
echo 3. نسخ ملفات CSV الأساسية...
copy "E:\inetpub\New folder\Account.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\Agent.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\Agent2023.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\Branch.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\UserInfo.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\UserRole.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\AccountUser.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\AccountParent.csv" "E:\inetpub\Data\" /Y
echo تم نسخ ملفات CSV الأساسية

echo.
echo 4. نسخ ملفات CSV إضافية...
copy "E:\inetpub\New folder\acccount2021.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\acountusers.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\agg.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\balanc.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\balances 2021.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\cli.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\custm.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\groupitem.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\partiesACOUNTS.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\sim.csv" "E:\inetpub\Data\" /Y
copy "E:\inetpub\New folder\siminvoice.csv" "E:\inetpub\Data\" /Y
echo تم نسخ ملفات CSV الإضافية

echo.
echo 5. نسخ ملفات SQL...
copy "E:\inetpub\New folder\1111111.sql" "E:\inetpub\SQL\" /Y
copy "E:\inetpub\New folder\back.sql" "E:\inetpub\SQL\" /Y
copy "E:\inetpub\New folder\topup.sql" "E:\inetpub\SQL\" /Y
copy "E:\inetpub\New folder\dv.sql" "E:\inetpub\SQL\" /Y
echo تم نسخ ملفات SQL

echo.
echo 6. نسخ مجلد OLAP...
xcopy "E:\inetpub\New folder\OLAP" "E:\inetpub\OLAP" /E /I /Y
echo تم نسخ مجلد OLAP

echo.
echo 7. نسخ ملفات التوثيق...
copy "E:\inetpub\New folder\deployment-commands.md" "E:\inetpub\Documentation\" /Y
copy "E:\inetpub\New folder\nawafd missing indexed.xlsx" "E:\inetpub\Documentation\" /Y
echo تم نسخ ملفات التوثيق

echo.
echo ========================================
echo انتهت عملية نسخ الملفات بنجاح!
echo ========================================

echo.
echo الملفات المنسوخة:
echo - TopupProcessor v1.2 - 4.2
echo - ملفات CSV (البيانات)
echo - ملفات SQL
echo - مجلد OLAP مع إعدادات msmdsrv
echo - ملفات التوثيق

echo.
echo الخطوات التالية:
echo 1. فحص الملفات المنسوخة
echo 2. تشغيل سكريبت استعادة msmdsrv
echo 3. تشغيل سكريبت استعادة قاعدة البيانات

pause
