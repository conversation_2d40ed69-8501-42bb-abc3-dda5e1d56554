# Script to fix web applications and make them ready

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Fix Web Applications - AppTech MSMS" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Define applications
$applications = @(
    @{Name="Main Application"; Path="E:\inetpub"; HasWebConfig=$false},
    @{Name="API"; Path="E:\inetpub\wwwroot\api"; HasWebConfig=$true},
    @{Name="API Test"; Path="E:\inetpub\wwwroot\apiTEST"; HasWebConfig=$true},
    @{Name="API New"; Path="E:\inetpub\wwwroot\apinewAN"; HasWebConfig=$true},
    @{Name="API Old"; Path="E:\inetpub\wwwroot\apiold"; HasWebConfig=$true},
    @{Name="Client"; Path="E:\inetpub\wwwroot\client"; HasWebConfig=$true},
    @{Name="Portal"; Path="E:\inetpub\wwwroot\portal"; HasWebConfig=$true}
)

# Connection string template
$connectionString = "Data Source=localhost;Initial Catalog=nawafd;Integrated Security=True;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"
$entityConnectionString = "metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=`"$connectionString`""

Write-Host "`n1. Checking applications..." -ForegroundColor Yellow

foreach ($app in $applications) {
    Write-Host "`nChecking: $($app.Name)" -ForegroundColor Gray
    
    if (Test-Path $app.Path) {
        Write-Host "  ✓ Path exists: $($app.Path)" -ForegroundColor Green
        
        if ($app.HasWebConfig) {
            $webConfigPath = "$($app.Path)\Web.config"
            if (Test-Path $webConfigPath) {
                Write-Host "  ✓ Web.config found" -ForegroundColor Green
            } else {
                Write-Host "  ✗ Web.config missing" -ForegroundColor Red
            }
        }
        
        # Check bin folder
        $binPath = "$($app.Path)\bin"
        if (Test-Path $binPath) {
            $dllCount = (Get-ChildItem $binPath -Filter "*.dll" -ErrorAction SilentlyContinue).Count
            Write-Host "  ✓ Bin folder: $dllCount DLL files" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ Bin folder missing" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "  ✗ Path not found: $($app.Path)" -ForegroundColor Red
    }
}

Write-Host "`n2. Fixing connection strings..." -ForegroundColor Yellow

$fixedCount = 0

foreach ($app in $applications) {
    if ($app.HasWebConfig -and (Test-Path $app.Path)) {
        $webConfigPath = "$($app.Path)\Web.config"
        
        if (Test-Path $webConfigPath) {
            Write-Host "`nFixing: $($app.Name)" -ForegroundColor Gray
            
            try {
                # Read current content
                $content = Get-Content $webConfigPath -Raw
                
                # Create backup
                $backupPath = "$webConfigPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
                Copy-Item $webConfigPath $backupPath -Force
                Write-Host "  Backup created: $backupPath" -ForegroundColor Gray
                
                # Fix empty connectionStrings
                if ($content -match '<connectionStrings></connectionStrings>') {
                    $newConnectionStrings = @"
<connectionStrings>
    <add name="DefaultConnection" connectionString="$connectionString" providerName="System.Data.SqlClient" />
    <add name="AppTechEntities" connectionString="$entityConnectionString" providerName="System.Data.EntityClient" />
    <add name="elmah-sql" connectionString="$connectionString" providerName="System.Data.SqlClient" />
  </connectionStrings>
"@
                    $content = $content -replace '<connectionStrings></connectionStrings>', $newConnectionStrings
                    Write-Host "  ✓ Fixed empty connectionStrings" -ForegroundColor Green
                }
                
                # Fix existing connection strings
                if ($content -match 'connectionString="[^"]*"') {
                    $content = $content -replace 'connectionString="[^"]*nawafd[^"]*"', "connectionString=`"$connectionString`""
                    $content = $content -replace 'connectionString="[^"]*provider connection string[^"]*"', "connectionString=`"$entityConnectionString`""
                    Write-Host "  ✓ Updated existing connectionStrings" -ForegroundColor Green
                }
                
                # Save updated content
                $content | Out-File -FilePath $webConfigPath -Encoding UTF8
                Write-Host "  ✓ Web.config updated successfully" -ForegroundColor Green
                $fixedCount++
                
            } catch {
                Write-Host "  ✗ Error updating Web.config: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host "`n3. Checking IIS configuration..." -ForegroundColor Yellow

# Check if IIS is installed
try {
    $iisFeature = Get-WindowsOptionalFeature -Online -FeatureName "IIS-WebServerRole" -ErrorAction SilentlyContinue
    if ($iisFeature -and $iisFeature.State -eq "Enabled") {
        Write-Host "✓ IIS is installed and enabled" -ForegroundColor Green
        
        # Check if IIS Management is available
        try {
            Import-Module WebAdministration -ErrorAction SilentlyContinue
            Write-Host "✓ IIS Management module available" -ForegroundColor Green
            
            # Check default website
            $defaultSite = Get-Website -Name "Default Web Site" -ErrorAction SilentlyContinue
            if ($defaultSite) {
                Write-Host "✓ Default Web Site exists" -ForegroundColor Green
                Write-Host "  Physical Path: $($defaultSite.PhysicalPath)" -ForegroundColor Gray
                Write-Host "  State: $($defaultSite.State)" -ForegroundColor Gray
            } else {
                Write-Host "⚠ Default Web Site not found" -ForegroundColor Yellow
            }
            
        } catch {
            Write-Host "⚠ IIS Management module not available" -ForegroundColor Yellow
        }
        
    } else {
        Write-Host "⚠ IIS is not installed or disabled" -ForegroundColor Yellow
        Write-Host "  To install IIS, run: Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠ Unable to check IIS status" -ForegroundColor Yellow
}

Write-Host "`n4. Creating test pages..." -ForegroundColor Yellow

# Create simple test page for each application
foreach ($app in $applications) {
    if ($app.HasWebConfig -and (Test-Path $app.Path)) {
        $testPagePath = "$($app.Path)\test.html"
        
        $testPageContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>$($app.Name) - Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #007acc; color: white; padding: 20px; border-radius: 5px; }
        .content { margin: 20px 0; }
        .status { padding: 10px; border-radius: 3px; margin: 10px 0; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="header">
        <h1>$($app.Name) - AppTech MSMS</h1>
        <p>Test Page - $(Get-Date)</p>
    </div>
    
    <div class="content">
        <div class="status success">
            ✓ Application files are accessible
        </div>
        
        <div class="status info">
            📁 Application Path: $($app.Path)
        </div>
        
        <div class="status info">
            🔗 Connection strings have been configured
        </div>
        
        <h3>Next Steps:</h3>
        <ul>
            <li>Ensure SQL Server is running</li>
            <li>Restore nawafd database</li>
            <li>Test application functionality</li>
        </ul>
        
        <h3>Application Files:</h3>
        <ul>
            <li>Web.config: Configured ✓</li>
            <li>Bin folder: Available ✓</li>
            <li>Views folder: Available ✓</li>
        </ul>
    </div>
</body>
</html>
"@
        
        try {
            $testPageContent | Out-File -FilePath $testPagePath -Encoding UTF8
            Write-Host "  ✓ Test page created: $($app.Name)" -ForegroundColor Green
        } catch {
            Write-Host "  ✗ Failed to create test page: $($app.Name)" -ForegroundColor Red
        }
    }
}

Write-Host "`n5. Application URLs..." -ForegroundColor Yellow

Write-Host "If IIS is configured, applications should be accessible at:" -ForegroundColor Gray
Write-Host "  Main: http://localhost/" -ForegroundColor Cyan
Write-Host "  API: http://localhost/api/" -ForegroundColor Cyan
Write-Host "  API Test: http://localhost/apiTEST/" -ForegroundColor Cyan
Write-Host "  Client: http://localhost/client/" -ForegroundColor Cyan
Write-Host "  Portal: http://localhost/portal/" -ForegroundColor Cyan

Write-Host "`nTest pages:" -ForegroundColor Gray
Write-Host "  API Test: http://localhost/api/test.html" -ForegroundColor Cyan
Write-Host "  Client Test: http://localhost/client/test.html" -ForegroundColor Cyan

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "Web Applications Fix Completed!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "- Applications checked: $($applications.Count)" -ForegroundColor White
Write-Host "- Web.config files fixed: $fixedCount" -ForegroundColor White
Write-Host "- Test pages created: $fixedCount" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Start IIS (if not running)" -ForegroundColor Cyan
Write-Host "2. Configure IIS applications/virtual directories" -ForegroundColor Cyan
Write-Host "3. Restore database" -ForegroundColor Cyan
Write-Host "4. Test applications" -ForegroundColor Cyan

pause
