-- سكريبت فصل قاعدة البيانات لتمكين النسخ
-- يجب تشغيله في SQL Server Management Studio على السيرفر القديم

-- تحذير: هذا سيفصل قاعدة البيانات مؤقتاً من SQL Server
-- تأكد من عدم وجود مستخدمين متصلين

USE master;

PRINT 'بدء عملية فصل قاعدة البيانات nawafd...';

-- 1. إنهاء جميع الاتصالات النشطة بقاعدة البيانات
PRINT 'إنهاء الاتصالات النشطة...';
ALTER DATABASE [nawafd] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;

-- 2. فصل قاعدة البيانات
PRINT 'فصل قاعدة البيانات...';
EXEC sp_detach_db 'nawafd';

PRINT 'تم فصل قاعدة البيانات بنجاح!';
PRINT 'يمكنك الآن نسخ الملفات:';
PRINT '- nawafd.mdf';
PRINT '- nawafd_0.ldf';

PRINT '';
PRINT 'بعد النسخ، لإعادة إرفاق قاعدة البيانات استخدم:';
PRINT 'EXEC sp_attach_db ''nawafd'', ''E:\MSSQLDATA\nawafd.mdf'', ''E:\MSSQLDATA\nawafd_0.ldf'';';
