﻿@model AppTech.MSMS.Domain.Models.RemittanceCommission
@{
    Layout = "/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.Label("نوع الحوالة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.RemittanceType, new[]
        {
            new SelectListItem {Text = "توريد حوالة", Value = bool.FalseString},
            new SelectListItem {Text = "تصدير حوالة", Value = bool.TrueString}
        })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList) ViewBag.Currencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.TargetState, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetState, new[]
        {
            new SelectListItem {Text = "كافة الشركات", Value = "1"},
            new SelectListItem {Text = "مجموعة", Value = "2"},
            new SelectListItem {Text = "شركة محدد", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.TargetState, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group" id="specifc">
    @Html.LabelFor(model => model.TargetID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetID, (SelectList) ViewBag.Exchangers, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.TargetID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group" id="group">
    @Html.LabelFor(model => model.TargetGroupID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetGroupID, (SelectList) ViewBag.Groups, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.TargetGroupID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.StartAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.StartAmount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.StartAmount)
    </div>
</div>


<div class="form-group form-inline">
    @Html.LabelFor(model => model.EndAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.EndAmount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.EndAmount)
    </div>
</div>


<div class="form-group">
    @Html.Label("نوع العمولة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommmissionType, new[]
        {
            new SelectListItem {Text = "بالمبلغ", Value = "0"},
            new SelectListItem {Text = "بالنسبة", Value = "1"}
        })
    </div>
</div>


<div class="form-group form-inline">
    @Html.Label("مبلغ العمولة", new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.CenterCommission, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.CenterCommission)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.CommissionCurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommissionCurrencyID, (SelectList) ViewBag.CommissionCurrencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CommissionCurrencyID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.IsAgainst, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.IsAgainst, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.IsAgainst)
    </div>
</div>

<script>

    $(function() {
        $('#specifc').hide();
        $('#group').hide();

        $('#TargetState').on('change',
            function() {

                var num = Number($("#TargetState").children("option:selected").val());
                i('selected state: ' + num);
                if (num === 1) {
                    $('#specifc').hide();
                    $('#group').hide();

                } else if (num === 3) {
                    $('#specifc').show();
                    $('#group').hide();

                } else if (num === 2) {
                    $('#specifc').hide();
                    $('#group').show();
                }

            });
    })
</script>