﻿@model AppTech.MSMS.Domain.Models.Bagat

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.OrderNo, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.OrderNo)
        @Html.ValidationMessageFor(model => model.OrderNo)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number)
        @Html.ValidationMessageFor(model => model.Number)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
    </div>
</div>



<div class="form-group">
    <label class="col-sm-2 control-label">نوع الخط</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.LineType, new[]
        {
            new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق", Selected = true},
            new SelectListItem {Text = "فوترة", Value = "فوترة"}
        })
    </div>
    @Html.ValidationMessageFor(model => model.LineType)
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Mode, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Mode, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Mode, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.Description, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Quantity, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Quantity, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Quantity, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.ProviderPrice, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ProviderPrice, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.ProviderPrice, "", new { @class = "text-danger" })
    </div>
</div>
@if (!AppTech.MSMS.Domain.DomainManager.None_Clients_Types)
{
    <div class="form-group">
        @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.PersonnalPrice, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.PersonnalPrice)
            @Html.ValidationMessageFor(model => model.PersonnalPrice)
        </div>
    </div>
}
else
{
    <div class="form-group">
        @Html.Label("السعر الأفتراضي", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>

}


<div class="form-group">
    @Html.LabelFor(model => model.Code, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Code, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.Code, "", new { @class = "text-danger" })
    </div>
</div>



@if (AppTech.MSMS.Domain.DomainManager.BundleByProvider)
{

    <div class="form-group">
        @Html.LabelFor(model => model.ByProvider, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.ByProvider, new { htmlAttributes = new { @class = "" } })
            @Html.ValidationMessageFor(model => model.ByProvider, "", new { @class = "text-danger" })
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.ProviderID, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.Providers, new { htmlAttributes = new { @class = "" } })
            @Html.ValidationMessageFor(model => model.ProviderID, "", new { @class = "text-danger" })
        </div>
    </div>
}