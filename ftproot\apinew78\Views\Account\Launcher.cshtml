﻿@using AppTech.MSMS.Domain
@model AppTech.MSMS.Web.Models.LoginModel
@{
    var returnUrl = ViewBag.ReturnUrl != null ? "?ReturnUrl=" + ViewBag.ReturnUrl : "";
}
<!DOCTYPE html>
<html lang="en" dir="rtl">
<head>
<title>@ClientLicense.Customer.CompanyInfo.Name</title>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
@Styles.Render("~/Content/Launcher")


<style>
    body {
        /*font: 400 15px Lato, sans-serif;*/
        line-height: 1.8;
        color: #818181;
    }

    h2 {
        font-size: 24px;
        text-transform: uppercase;
        color: #303030;
        font-weight: 600;
        margin-bottom: 30px;
    }

    h4 {
        font-size: 19px;
        line-height: 1.375em;
        color: #303030;
        font-weight: 400;
        margin-bottom: 30px;
    }

    .jumbotron {
        /*background-color: #3d4bcb;
            color: #fff;*/
        padding: 0px 0px;
        font-family: Montserrat, sans-serif;
    }

    .container-fluid-nospace { padding: 0px 0px; }

    .container-fluid { padding: 60px 50px; }

    .bg-grey { background-color: #f6f6f6; }

    .logo-small {
        color: #3d4bcb;
        font-size: 50px;
    }

    .logo {
        color: #3d4bcb;
        font-size: 200px;
    }

    .thumbnail {
        padding: 0 0 15px 0;
        border: none;
        border-radius: 0;
    }

    .thumbnail img {
        width: 100%;
        height: 100%;
        margin-bottom: 10px;
    }

    .carousel-control.right, .carousel-control.left {
        background-image: none;
        color: #3d4bcb;
    }

    .carousel-indicators li { border-color: #3d4bcb; }

    .carousel-indicators li.active { background-color: #3d4bcb; }

    .item h4 {
        font-size: 19px;
        line-height: 1.375em;
        font-weight: 400;
        font-style: italic;
        margin: 70px 0;
    }

    .item span { font-style: normal; }

    .panel {
        border: 1px solid #3d4bcb;
        border-radius: 0 !important;
        transition: box-shadow 0.5s;
    }

    .panel:hover { box-shadow: 5px 0px 40px rgba(0, 0, 0, .2); }

    .panel-footer .btn:hover {
        border: 1px solid #3d4bcb;
        background-color: #fff !important;
        color: #3d4bcb;
    }

    .panel-heading {
        color: #fff !important;
        background-color: #3d4bcb !important;
        padding: 25px;
        border-bottom: 1px solid transparent;
        border-top-left-radius: 0px;
        border-top-right-radius: 0px;
        border-bottom-left-radius: 0px;
        border-bottom-right-radius: 0px;
    }

    .panel-footer { background-color: white !important; }

    .panel-footer h3 { font-size: 32px; }

    .panel-footer h4 {
        color: #aaa;
        font-size: 14px;
    }

    .panel-footer .btn {
        margin: 15px 0;
        background-color: #3d4bcb;
        color: #fff;
    }

    .navbar {
        margin-bottom: 0;
        background-color: #3d4bcb;
        z-index: 9999;
        border: 0;
        font-weight: bold;
        font-size: 12px !important;
        line-height: 1.42857143 !important;
        border-radius: 0;
        /*font-family: Montserrat, sans-serif;*/
    }

    .navbar li a, .navbar .navbar-brand { color: #fff !important; }

    .navbar-nav li a:hover, .navbar-nav li.active a {
        color: #3d4bcb !important;
        background-color: #fff !important;
    }

    .navbar-default .navbar-toggle {
        border-color: transparent;
        color: #fff !important;
    }

    footer .glyphicon {
        font-size: 20px;
        margin-bottom: 20px;
        color: #3d4bcb;
    }

    .slideanim { visibility: hidden; }

    .slide {
        animation-name: slide;
        -webkit-animation-name: slide;
        animation-duration: 1s;
        -webkit-animation-duration: 1s;
        visibility: visible;
    }

    @@keyframes slide {
        0% {
            opacity: 0;
            transform: translateY(70%);
        }

        100% {
            opacity: 1;
            transform: translateY(0%);
        }
    }

    @@-webkit-keyframes slide {
        0% {
            opacity: 0;
            -webkit-transform: translateY(70%);
        }

        100% {
            opacity: 1;
            -webkit-transform: translateY(0%);
        }
    }

    @@media screen and (max-width: 768px) {
        .col-sm-4 {
            text-align: center;
            margin: 25px 0;
        }

        .btn-lg {
            width: 100%;
            margin-bottom: 35px;
        }
    }

    @@media screen and (max-width: 480px) {
        .logo { font-size: 150px; }
    }

    .carousel-inner img {
        width: 100%; /* Set width to 100% */
        margin: auto;
        min-height: 200px;
    }

    @@media (max-width: 600px) {
        .carousel-caption { display: none; }
    }
</style>
</head>
<body id="myPage" data-spy="scroll" data-target=".navbar" data-offset="60">

<nav class="navbar navbar-default navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#myNavbar">
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="">@ClientLicense.Customer.CompanyInfo.Name</a>
        </div>
        <div class="collapse navbar-collapse" id="myNavbar">
            <ul class="nav navbar-nav navbar-right">
                >
                <li>
                    <a href="#contact">تواصل معنا</a>
                </li>
                <li>
                    <a href="#login">تسجيل الدخول</a>
                </li>
                <li>
                    <a href="#services">خدماتنا</a>
                </li>
                <li>
                    <a href="#terms">الشروط</a>
                </li>
                <li>
                    <a href="#about">حول عنا</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="jumbotron text-center">
    <div id="myCarousel" class="carousel slide" data-ride="carousel">
        <!-- Indicators -->
        <ol class="carousel-indicators">
            <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
            <li data-target="#myCarousel" data-slide-to="1"></li>
        </ol>

        <!-- Wrapper for slides -->
        <div class="carousel-inner" role="listbox">
            <div class="item active">
                <img src="https://png.pngtree.com/thumb_back/fw800/back_pic/05/12/55/775999859824ced.jpg" alt="Image">

                <div class="carousel-caption">
                    @*style="background: #3d4bcb; color: #ffffff; height: 400px"*@
                    <h2 style="color: #ffffff">@ClientLicense.Customer.CompanyInfo.Name</h2>
                    <p style="color: #ffffff">معنا تختصر المسافة الى مالانهاية</p>

                </div>
            </div>

            <div class="item">
                <img src="https://png.pngtree.com/thumb_back/fw800/back_pic/05/12/55/7359998584a1e4b.jpg" alt="Image">
                <div class="carousel-caption">
                    <h2> تطبيق اندريد في جوجل بلاي </h2>
                    <p>خاص بالعملاء فقط</p>
                    <button type="button" onclick="downloadApp();" class="btn btn-primary btn-round fa fa-android"> &nbsp; تحميل التطبيق &nbsp;</button>
                </div>
            </div>
        </div>

        <!-- Left and right controls -->
        <a class="left carousel-control" href="#myCarousel" role="button" data-slide="prev">
            <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
            <span class="sr-only">Previous</span>
        </a>
        <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">
            <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
            <span class="sr-only">Next</span>
        </a>
    </div>


</div>

<!-- Container (About Section) -->
<div id="about" class="container-fluid">
    <div class="row">
        <div class="col-sm-8">
            <h2>حول النظام</h2><br>
            <h4>@ClientLicense.Customer.CompanyInfo.Description </h4><br>

            <p></p>
            <p></p>
            @*<br><button class="btn btn-default btn-lg">Get in Touch</button>*@
        </div>
        <div class="col-sm-4">
            <span class="glyphicon glyphicon-signal logo slideanim"></span>
        </div>
    </div>
</div>

<div id="terms" class="container-fluid bg-grey">
    <div class="row">
        <div class="col-sm-4">
            <span class="glyphicon glyphicon-check logo slideanim"></span>
            @*<span class="glyphicon glyphicon-globe logo slideanim"></span>*@
        </div>
        <div class="col-sm-8">
            <h2>شروط الاشتراك :</h2><br>
            @*<h4><strong>MISSION:</strong></h4><br>*@
            <p>
                1- على المشترك انشاء حساب جديد بنفسة وتعبئة البيانات الخاصة به كما هو مطلوب .
                <br/>
                2- يجب على أي مشترك أن يدفع 500ريال رسوم اشتراك عند اول مرة فقط ومبلغ تأمين مسبقا وذلك عبر الحضورالمباشراوالتحويل او الايداع في احد حساباتنا الموضحة بالبرنامج تحت قائمة( معلومات التحويل ) اوالتواصل معنا عبرالارقام والعناوين الموضحة أدناه لإضافة المبلغ
                <br/>
                3- يجب ان يكون مبلغ التأمين كافيا لتغطية عمليات العميل وكل عميل حسب عمله اليومي وذلك تجنبا لعملية التقييد المحاسبي يوميا كما يفضل ان يتم ايداع المبلغ قبل نفاذ المبلغ السابق لكي لايتوقف العميل عن العمل في حال تأخرنا بإضافة مبلغ التأمين بوقته .
                <br/>
                4- يتحمل العميل مسئولية البيانات او العملية التي يرسلها من ناحية أي خطأ في رقم الهاتف الذي يقوم بتسديدة او اي نوع من الباقات .
                <br/>
                5- يتحمل العميل مسئولية صحة بيانات البطائق وذلك للعملاء الذين يتم التعامل معهم لربط ارقام جديدة او بدل فاقد لأجهزة البرمجة من خلال استلام صورة البطاقة من نفس الشخص ومطابقتها والالتزام بالأسعار الرسمية للشركة .
                <br/>
                6- اوقات الدوام الرسمي من الساعة ( 8 صباحا حتى 8:30 مساء ) ماعدا الجمعة من الساعة (4 عصرا حتى 8:30مساء)
                <br/>
                7- في الإجازات الوطنية والدينية سيتم إبلاغكم بطبيعة ووقت الدوام عبر رسالة قبل الإجازة بيوم .
                <br/>
                8- تتوفر إشعارات العمليات أولا بأول وكذلك خلاصة الحساب عبر التطبيق .
                <br/>
                9- يمكن استخدام البرنامج عبر الكمبيوتر او عبر المتصفح من أي هاتف يمكنكم استخدم العنوان WWW.bcmobily.com
                <br/>
                10- يستطيع العميل مشاهدة تفاصيل التقارير والحسابات الخاصة به عند دخوله عبر الموقع مباشرة بنفس اسم المستخدم وكلمة المرور للتطبيق .
                <br/>
                11- لا يتم إعادة أي مبالغ تم تسديدها بالخطأ من قبل أي عميل والشركة لاتتحمل أي أخطاء .
                <br/>
                تنبية هام جدا :
                <br/>
                يرجى التأكد من الرقم عند تفعيل اي باقة من الرصيد لانة في حالة تفعيل اي باقة لاي رقم بالخطاء فان العميل سيتحمل أي ضرر لدى الشركة


            </p>
        </div>
    </div>
</div>

<!-- Container (Services Section) -->
<div id="services" class="container-fluid text-center">
    <h2>خدماتنا</h2>
    <br>
    <div class="row slideanim">

        <div class="col-sm-4">
            <span class="blue fa fa-money logo-small"></span>
            <h4>بيع وشراء عملات</h4>
            <p>قم بالشراء او بيع عملات مثل الدولار والسعودي وغيرها</p>
        </div>

        <div class="col-sm-4">
            <span class="blue fa fa-mobile-phone logo-small"></span>
            <h4>باقات وشحن رصيد</h4>
            <p>اشحن رصيد الهواتف لكافة شركات الاتصالات في اليمن</p>
        </div>
        <div class="col-sm-4">
            <span class="blue fa fa-bolt logo-small"></span>
            <h4>تسديد الفواتير</h4>
            <p>قم بتسديد فواتير الماء ,الكهرباء, الانترنت المنزلي, والهاتف الثابت وغيرها من الفواتير</p>
        </div>

    </div>
    <br><br>
    <div class="row slideanim">
        <div class="col-sm-4">
            <span class="fa fa-users logo-small"></span>
            <h4>تحويل الى حساب عميل</h4>
            <p>يمكنك تحويل مبالغ مالية الى اي عميل من عملانا بشكل مباشر</p>
        </div>
        <div class="col-sm-4">
            <span class="fa fa-industry logo-small"></span>
            <h4>أشعار أيداع نقدي</h4>
            <p>قم بتعبئة رصيدك لدينا لتتمكن من الاستفادة من خدماتنا </p>
        </div>
        <div class="col-sm-4">
            <span class="fa fa-exchange logo-small"></span>
            <h4 style="color: #303030;">طلب أرسال/سحب حوالة</h4>
            <p>قم بالطلب بارسال او سحب حوالة عبر جميع شركات الصرافة في اليمن</p>
        </div>
    </div>

    @if (ClientLicense.Flavor != Flavors.BCMobile)
    {
        <br>
        <br>
        <div class="row slideanim">
            <div class="col-sm-4">
                <span class="fa fa-shopping-cart logo-small"></span>
                <h4>تسديد تاجر</h4>
                <p>يمكن العملاء من محاسبة او دفع فواتيرهم للتجار والشركات المتعاقدين معنا بكل بساطة</p>
            </div>
            <div class="col-sm-4">
                <span class="fa fa-industry logo-small"></span>
                <h4>سحب نقدي</h4>
                <p>يمكن العملاء من سحب اموال من حسابهم عبر اي وكيل من وكلائنا </p>
            </div>
            <div class="col-sm-4">
                <span class="fa fa-bar-chart logo-small"></span>
                <h4 style="color: #303030;">كشف حساب</h4>
                <p>كشف حساب تفصيلي لجميع المستخدمين عملاء , وكلاء و تجار</p>
            </div>
        </div>
    }
</div>

<!-- Container (Client Login Section) -->
<div id="login" class="container-fluid bg-grey">

    <div class="row">
        <div class="col-sm-7 slideanim">
            @using (Html.BeginForm())
            {
                @Html.AntiForgeryToken()
                @Html.ValidationSummary(true)
                <h2 class="text-center">تسجيل دخول </h2>
                <div class="wrap-input100 validate-input">

                    @Html.TextBoxFor(m => m.Email, new {placeholder = "اسم المستخدم", @class = "input100"})
                    @Html.ValidationMessageFor(m => m.Email, string.Empty, new {@class = "invalid"})
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
                        <i class="fa fa-user" aria-hidden="true" style="padding: 0px 10px"></i>
                    </span>
                </div>

                <div class="wrap-input100 validate-input" data-validate="Password is required">
                    @Html.PasswordFor(m => m.Password, new {placeholder = "كلمة المرور", @class = "input100"})
                    @Html.ValidationMessageFor(m => m.Password, string.Empty, new {@class = "invalid"})
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
                        <i class="fa fa-lock" aria-hidden="true" style="padding: 0px 10px"></i>
                    </span>
                </div>

                if (ClientLicense.Flavor == Flavors.BCMobile)
                {
                    <div class="wrap-input100">

                        <div class="col-md-10">
                            @Html.RadioButtonFor(x => x.UserType, 2, new {@checked = "checked", style = "margin-right:20px"}) عميل
                            @Html.RadioButtonFor(x => x.UserType, 3, new {style = "margin-right:20px"}) وكيل
                            @*@Html.RadioButtonFor(x => x.UserType, 4, new { style = "margin-right:20px" }) تاجر*@
                        </div>
                        @Html.Label("نوع المستخدم", new {@class = "control-label col-md-2"})

                    </div>
                }
                else
                {
                    <div class="wrap-input100">

                        <div class="col-md-10">
                            @Html.RadioButtonFor(x => x.UserType, 2, new {@checked = "checked", style = "margin-right:20px"}) عميل
                            @Html.RadioButtonFor(x => x.UserType, 3, new {style = "margin-right:20px"}) وكيل
                            @Html.RadioButtonFor(x => x.UserType, 4, new {style = "margin-right:20px"}) تاجر
                        </div>
                        @Html.Label("نوع المستخدم", new {@class = "control-label col-md-2"})

                    </div>
                }
                <div class="container-login100-form-btn">
                    <button class="login100-form-btn" type="submit">
                        الدخول
                    </button>
                </div>
            }

        </div>
        <div class="col-sm-5">
            <span class="glyphicon glyphicon-log-in logo slideanim"></span>
        </div>
    </div>
</div>
<!-- Container (Contact Section) -->
<div id="contact" class="container-fluid">
    <h2 class="text-center">تواصل معنا</h2>
    <div class="row">

        <div class="col-sm-7 slideanim">
            <div class="row">
                <div class="col-sm-6 form-group">
                    <input class="form-control" id="name" name="name" placeholder="الأسم" type="text" required>
                </div>
                <div class="col-sm-6 form-group">
                    <input class="form-control" id="email" name="email" placeholder="الأيميل" type="email" required>
                </div>
            </div>
            <textarea class="form-control" id="comments" name="comments" placeholder="الرسالة" rows="5"></textarea><br>
            <div class="row">
                <div class="col-sm-12 form-group">
                    <button class="btn btn-default pull-right" type="submit">أرسال</button>
                </div>
            </div>
        </div>

        <div class="col-sm-5">
            <p>
                <span class="glyphicon glyphicon-map-marker"></span> @ClientLicense.Customer.CompanyInfo.Address
            </p>
            <p>
                <span class="glyphicon glyphicon-phone"></span> @ClientLicense.Customer.CompanyInfo.Contacts
            </p>
            <p>
                <span class="glyphicon glyphicon-envelope"></span> @ClientLicense.Customer.CompanyInfo.Email
            </p>
            <p>
                <span class="glyphicon glyphicon-globe"></span> @ClientLicense.Customer.CompanyInfo.Email
            </p>
            <p>
                <span class="glyphicon glyphicon-question-sign"></span>@ClientLicense.Customer.CompanyInfo.More
            </p>
        </div>
    </div>
</div>

<!-- Add Google Maps -->
@*<div id="googleMap" style="height:400px;width:100%;"></div>*@
@*<script>
            function myMap() {
                var myCenter = new google.maps.LatLng(41.878114, -87.629798);
                var mapProp = {center:myCenter, zoom:12, scrollwheel:false, draggable:false, mapTypeId:google.maps.MapTypeId.ROADMAP};
                var map = new google.maps.Map(document.getElementById("googleMap"),mapProp);
                var marker = new google.maps.Marker({position:myCenter});
                marker.setMap(map);
            }
        </script>*@
@*<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBu-916DdpKAjTmJNIgngS6HL_kDIKU0aU&callback=myMap"></script>*@
<!--
To use this code on your website, get a free API key from Google.
Read more at: https://www.w3schools.com/graphics/google_maps_basic.asp
-->
<footer class="container-fluid text-center">
    <a href="#myPage" title="To Top">
        <span class="glyphicon glyphicon-chevron-up"></span>
    </a>
    <div class="footer-inner">
        <div class="footer-content">
            <span class="bigger-120">
                <span class="blue bolder">@AppTechInfo.Name</span>
                Inc. &copy; 2017-2018 Tel:@AppTechInfo.Contact
            </span>
        </div>
    </div>
</footer>
@Scripts.Render("~/scripts/home")


<script>

    $(document).ready(function() {
        i('onload');
        // Add smooth scrolling to all links in navbar + footer link
        $(".navbar a, footer a[href='#myPage']").on('click',
            function(event) {
                // Make sure this.hash has a value before overriding default behavior
                if (this.hash !== "") {
                    // Prevent default anchor click behavior
                    event.preventDefault();

                    // Store hash
                    var hash = this.hash;

                    // Using jQuery's animate() method to add smooth page scroll
                    // The optional number (900) specifies the number of milliseconds it takes to scroll to the specified area
                    $('html, body').animate({
                            scrollTop: $(hash).offset().top
                        },
                        900,
                        function() {

                            // Add hash (#) to URL when done scrolling (default click behavior)
                            window.location.hash = hash;
                        });
                } // End if
            });

        $(window).scroll(function() {
            $(".slideanim").each(function() {
                i('window scroll');
                var pos = $(this).offset().top;

                var winTop = $(window).scrollTop();
                if (pos < winTop + 600) {
                    $(this).addClass("slide");
                }
            });
        });
    })
</script>

</body>
</html>