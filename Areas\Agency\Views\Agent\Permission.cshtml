﻿@model IEnumerable<AppTech.MSMS.Domain.Services.UserPermission>

@using (Ajax.BeginForm(new AjaxOptions
{
    OnBegin = "OnFormBegin",
    LoadingElementId = "formloader",
    OnSuccess = "onCrudSuccess",
    OnFailure = "onCrudFailure"
}))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new {@class = "text-danger"})


    <input type="checkbox" onClick="toggle(this)"/>
    <strong>أختر الكل</strong>
    <input type="hidden" name="userId" value="@ViewBag.UserID"/>

    <table class="table">
        <tr style="background-color: gray; color: white">
            <th style="text-align: center;">
                السجل
            </th>
            <th style="text-align: center;">العمليات</th>
        </tr>

        @foreach (var item in Model)
        {
            <tr style="background-color: lightgray">
                <td class="align-center">
                    <strong>@Html.DisplayFor(modelItem => item.Page.Title)</strong>

                </td>
                <td style="text-align: right;">
                    @foreach (var op in item.PageActions)
                    {
                        <input id="chk@(op.Value)"
                               name="items"
                               class="action"
                               type="checkbox"
                               value="@op.Value"
                               checked="@op.IsAllow"
                               style="margin: 12px;"/>

                        <strong>@op.Text</strong>
                @*  <br/>*@
                    }
                </td>

            </tr>
        }

    </table>


    <div class="hr hr32 hr-dotted"></div>
    <img id="formloader" class="img-center" src="@Url.Content("~/Content/images/loader-64x/Preloader_4.gif")" alt="loading"/>
    @Html.Partial("_FormAction")
}
<script>

    function toggle(source) {
        var checkboxes = $(".action");
        for (var i = 0, n = checkboxes.length; i < n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }

    $(function() {
        $("#formloader").hide();
    });

    function OnFormBegin(context) {
    }


    function onCrudSuccess(data) {
        onPermSuccess(data);
    }

    function onCrudFailure(xhr, status) {
        hideFormLoading();
        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
    }

    function onPermSuccess(data) {
        //  hideLoading();
        alert(data);
        hideModal();

        showSuccess(data);
    }

    //$(function () {

    //    formHelper.onSuccess = function(data) {

    //        alert("formHelper.onSuccess");
    //        onPermSuccess(data);
    //    }
    //  //  hideLoading();
    //    //$("#crud-form").submit(function(event) {

    //    //    alert("perms submit");
    //    //    showLoading();
    //    //    var form = $("#crud-form");
    //    //    var url = "Admin/User/Permission";
    //    //    $.post(url,
    //    //        form.serialize(),
    //    //        function(data) {
    //    //            onPermSuccess(data);
    //    //        }
    //    //    );

    //    //});
    //});

    //$(function() {

    //    formHelper.onSubmit = function() {

    //        alert("perms submit")
    //        var form = $("#crud-form");
    //        var url = "/Client/Permissions";
    //        $.post(url, form.serialize(),
    //            function (data) {
    //                hideModal();
    //                showAlert(data);
    //            }
    //        );
    //    }
    //});
</script>