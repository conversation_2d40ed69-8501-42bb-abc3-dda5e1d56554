﻿@model AppTech.MSMS.Domain.Models.CardType
@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";

}
    <div class="form-horizontal">
        @Html.EditorFor(model => model.ID, new { htmlAttributes = new { @style = "display: none" } })
        @Html.ValidationSummary(true)

        <div class="form-group">
            @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Number)
                @Html.ValidationMessageFor(model => model.Number)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name)
                @Html.ValidationMessageFor(model => model.Name)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Image, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                <input type="file" name="ImageData" size="40" onchange="showImg(this)">
                @*@Html.EditorFor(model => model.Image)*@
                @Html.ValidationMessageFor(model => model.Image)
            </div>
            <img class="img-thumbnail" width="150" height="150" id="preview"
                 src="@Url.Action("GetImage", "CardType",
              new {Model.ID})" />
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Type, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @*@Html.EditorFor(model => model.Type)*@
                @Html.DropDownListFor(model => model.Type, new[]
                {
                     new SelectListItem {Text = "اختر النوع", Value = "", Selected = true},
                     new SelectListItem {Text = "كرت", Value = "1"},
                     new SelectListItem {Text = "شحن", Value = "2"}
                 })
                @Html.ValidationMessageFor(model => model.Type)
            </div>
        </div>
        <div class="form-group">
            @*@Html.LabelFor(model => model.AccountID, new { @class = "control-label col-md-2" })*@
            <label class="col-sm-2 control-label">اسم الحساب </label>
            <div class="col-md-10">
                @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts, new { @class = "select2" })
                @Html.ValidationMessageFor(model => model.AccountID)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Active, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Active)
                @Html.ValidationMessageFor(model => model.Active)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Note)
                @Html.ValidationMessageFor(model => model.Note)
            </div>
        </div>

    </div>

    <script>
        $(function () {
            $('.select2').css('width', '200px').select2({ allowClear: false });
            $('#select2-multiple-style .btn').on('click',
                function (e) {
                    var target = $(this).find('input[type=radio]');
                    var which = parseInt(target.val());
                    if (which == 2) $('.select2').addClass('tag-input-style');
                    else $('.select2').removeClass('tag-input-style');
                });
        });

    </script>