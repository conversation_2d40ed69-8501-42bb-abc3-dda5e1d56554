﻿@{
    ViewBag.Title = "تقرير التسقيفات";
    Layout = "~/Views/Shared/_Report.cshtml";
}

<span class="lbl">اسم العميل </span>
<select id="AccountID" name="AccountID" class="select2" placeholder=""></select>

<script>
    $(function() {
        console.log('topupreport load');
        AjaxCall('/Print/GetParties').done(function(response) {
            console.log('get parties');
            if (response.length > 0) {
                $('#AccountID').html('');
                var options = '<option value="0"></option>';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#AccountID').append(options);

            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });

        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function(e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>