﻿@model AppTech.MSMS.Domain.Models.GeneralInfo

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.Title, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Title, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.Title, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Description, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Description, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Description, "", new {@class = "text-danger"})
    </div>
</div>