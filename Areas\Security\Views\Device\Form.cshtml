﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.Device

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.AccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AccountID")
        {
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains
        })

        @Html.ValidationMessageFor(model => model.AccountID, "", new {@class = "text-danger"})
    </div>
</div>


@Html.ValidationSummary(true, "", new {@class = "text-danger"})
<div class="form-group">
    @Html.LabelFor(model => model.Number, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Number, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Number, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Active, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Active)
            @Html.ValidationMessageFor(model => model.Active, "", new {@class = "text-danger"})
        </div>
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>