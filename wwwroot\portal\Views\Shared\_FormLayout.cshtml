﻿<input type="hidden" id="Title" value="@ViewBag.Title">
<input type="hidden" id="FormTitle" value="@ViewBag.FormTitle">
@using (Ajax.BeginForm(
    "AddOrEdit",
    null,
    new AjaxOptions
    {
        OnBegin = "return OnFormBegin()",
        LoadingElementId = "formloader",
        OnSuccess = "onCrudSuccess",
        OnFailure = "onCrudFailure"
    },
    new { id = "crudform" }))
{
   

    <section>
        <div class="form-horizontal">
            @Html.AntiForgeryToken()
            @Html.ValidationSummary(true, "", new { @class = "text-danger" })
            @RenderBody()
        </div>

    </section>
    <div class="hr hr32 hr-dotted"></div>
    @Html.Partial("_FormAction")
}

<script>


    $(function () {
        $('#loader').hide();
        if ($("#Amount")[0]) {
            $('#Amount').on('input',
                function () {
                    var words = tafqeet($('#Amount').val());
                    $('#words').text(words);
                    $('#Amount').title(words);
                });
        }
        if ($("#Price")[0]) {
            $('#Price').on('input',
                function () {
                    var words = tafqeet($('#Price').val());
                    $('#Price').title(words);
                });
        }
        if ($("#Date")[0]) {
            $("#Date").val(getToday());
            $('#Date').prop('readonly', true);
        }

        var title = $('#Title').val();
        $("#page-title").text(title);
        if ($("#words")[0]) {
            $('#words').text('');
        }
    });

    var formHelper = {
        onSuccess: function (data) {
            onSubmitSuccess(data);
        },
        onBegin: function () {
            showLoading();
            return true;
        }
    }

    function onSubmitSuccess(data) {
        log('onCrudSuccess');
        resetButton();
        hideLoading();
        if (data.Success) {
            alert("تمت العملية بنجاح");
            $('#crudform')[0].reset();
        } else {
            log('onCrudSuccess not success, msg: ' + data.Message);
            alert(data.Message);
        }
       
    }

    
    function OnFormBegin(context) {
        return formHelper.onBegin(context);
    }

    function onCrudSuccess(data) {
        formHelper.onSuccess(data);

    }

    function onCrudFailure(xhr, textStatus, errorThrown) {
        hideLoading();
        resetButton();
        parseAndShowError(xhr, textStatus, errorThrown)
    }

    
</script>