# سكريبت إيقاف SQL Server مؤقتاً لنسخ ملفات قاعدة البيانات
# تحذير: هذا سيوقف SQL Server بالكامل

Write-Host @"
========================================
    إيقاف SQL Server لنسخ الملفات
========================================
"@ -ForegroundColor Yellow

Write-Host "تحذير: هذا سيوقف SQL Server بالكامل!" -ForegroundColor Red
$confirm = Read-Host "هل تريد المتابعة؟ (y/n)"

if ($confirm -ne "y") {
    Write-Host "تم إلغاء العملية" -ForegroundColor Yellow
    exit
}

# البحث عن خدمات SQL Server
Write-Host "`n1. البحث عن خدمات SQL Server..." -ForegroundColor Yellow

$sqlServices = Get-Service | Where-Object {$_.Name -like "*SQL*" -and $_.Status -eq "Running"}

if ($sqlServices) {
    Write-Host "الخدمات النشطة:" -ForegroundColor Gray
    foreach ($service in $sqlServices) {
        Write-Host "  - $($service.Name): $($service.Status)" -ForegroundColor Gray
    }
} else {
    Write-Host "لم يتم العثور على خدمات SQL Server نشطة" -ForegroundColor Green
    exit
}

# إيقاف خدمات SQL Server
Write-Host "`n2. إيقاف خدمات SQL Server..." -ForegroundColor Yellow

$servicesToStop = @("MSSQLSERVER", "SQLSERVERAGENT", "MSSQL`$SQLEXPRESS", "SQLAgent`$SQLEXPRESS")

foreach ($serviceName in $servicesToStop) {
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq "Running") {
            Write-Host "إيقاف خدمة: $serviceName" -ForegroundColor Gray
            Stop-Service -Name $serviceName -Force
            Write-Host "✓ تم إيقاف $serviceName" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠ لم يتم العثور على خدمة: $serviceName" -ForegroundColor Yellow
    }
}

Write-Host "`n3. التحقق من حالة الخدمات..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

$stoppedServices = Get-Service | Where-Object {$_.Name -like "*SQL*"}
foreach ($service in $stoppedServices) {
    $status = if ($service.Status -eq "Stopped") { "✓" } else { "✗" }
    Write-Host "$status $($service.Name): $($service.Status)" -ForegroundColor Gray
}

Write-Host "`n" + "="*50 -ForegroundColor Green
Write-Host "تم إيقاف خدمات SQL Server" -ForegroundColor Green
Write-Host "="*50 -ForegroundColor Green

Write-Host "`nيمكنك الآن نسخ ملفات قاعدة البيانات:" -ForegroundColor White
Write-Host "- المصدر: E:\MSSQLDATA\nawafd.mdf" -ForegroundColor Cyan
Write-Host "- المصدر: E:\MSSQLDATA\nawafd_0.ldf" -ForegroundColor Cyan

Write-Host "`nبعد انتهاء النسخ، اضغط أي مفتاح لإعادة تشغيل الخدمات..." -ForegroundColor Yellow
pause

Write-Host "`n4. إعادة تشغيل خدمات SQL Server..." -ForegroundColor Yellow

foreach ($serviceName in $servicesToStop) {
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq "Stopped") {
            Write-Host "تشغيل خدمة: $serviceName" -ForegroundColor Gray
            Start-Service -Name $serviceName
            Write-Host "✓ تم تشغيل $serviceName" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠ خطأ في تشغيل خدمة: $serviceName" -ForegroundColor Yellow
    }
}

Write-Host "`n✓ تم إعادة تشغيل خدمات SQL Server" -ForegroundColor Green
