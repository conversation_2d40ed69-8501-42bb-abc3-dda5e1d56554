-- سكريبت إنشاء نسخة احتياطية من قاعدة بيانات nawafd
-- هذا الحل أفضل لأنه لا يتطلب فصل قاعدة البيانات

USE master;

PRINT 'بدء عملية إنشاء نسخة احتياطية من قاعدة بيانات nawafd...';

-- إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
DECLARE @BackupPath NVARCHAR(500) = 'C:\Backup\';
EXEC xp_create_subdir @BackupPath;

-- مسار ملف النسخة الاحتياطية
DECLARE @BackupFile NVARCHAR(500) = @BackupPath + 'nawafd_' + FORMAT(GETDATE(), 'yyyyMMdd_HHmmss') + '.bak';

PRINT 'إنشاء نسخة احتياطية في: ' + @BackupFile;

-- إنشاء النسخة الاحتياطية
BACKUP DATABASE [nawafd] 
TO DISK = @BackupFile
WITH 
    FORMAT,
    INIT,
    NAME = 'nawafd-نسخة احتياطية كاملة',
    SKIP,
    NOREWIND,
    NOUNLOAD,
    COMPRESSION,
    STATS = 10;

PRINT 'تم إنشاء النسخة الاحتياطية بنجاح!';
PRINT 'مسار الملف: ' + @BackupFile;

-- عرض معلومات النسخة الاحتياطية
PRINT '';
PRINT 'معلومات النسخة الاحتياطية:';
RESTORE HEADERONLY FROM DISK = @BackupFile;

PRINT '';
PRINT 'لاستعادة قاعدة البيانات في السيرفر الجديد استخدم:';
PRINT 'RESTORE DATABASE [nawafd] FROM DISK = ''' + @BackupFile + ''' WITH REPLACE;';
