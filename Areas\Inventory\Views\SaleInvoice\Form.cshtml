﻿@model AppTech.MSMS.Domain.Models.SaleInvoice


<form id="form">
    <div class="form-horizontal">
        @Html.AntiForgeryToken()
        @Html.ValidationSummary(true, "", new { @class = "text-danger" })
        <div class="form-group">
            <label class="col-sm-2 control-label"> رقم الفاتورة</label>
            <div class="col-md-3">
                <input type="number" name="Number" value="@Model.Number" disabled/>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-6">
                <label class="col-md-4 control-label no-padding-right"> العملة</label>
                <div class="col-md-8">
                    @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies ,new{ width= "187px"})
                </div>
            </div>
            <div class="col-md-6">
                <label class="col-md-3 control-label no-padding-right" for="HeaderDate"> التاريخ</label>
                <div class="col-md-9">
                    <input type="text" name="Date" id="Date" value="@Model.Date.ToShortDateString()" />
                </div>
            </div>
        </div>
        <div class="form-group">
            <div class="col-md-6">
                <label class="col-md-4 control-label no-padding-right" for="CreditorAccountID"> الحساب</label>
                <div class="col-md-8">
                    @Html.DropDownListFor(m => m.CreditorAccountID, (SelectList)ViewBag.CreditorAccount)
                </div>
            </div>
            <div class="col-md-6">
                <label class="col-md-3 control-label no-padding-right" for="HeaderDate"> رقم المرجع</label>
                <div class="col-md-9">
                    <input type="text" name="RefNumber" value="@Model.RefNumber" placeholder="رقم المرجع" id="RefNumber" />
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-2 control-label" for="Note"> ملاحظات</label>
            <div class="col-md-9">
                <input type="text" name="Note" value="@Model.Note" placeholder="ملاحظات" id="Note" style="width:100%" />
            </div>
        </div>


        <button type='button' class="btn btn-white btn-round" id='add' onclick='myfunction()'>
            <i class="ace-icon fa fa-plus bigger-110"></i>
            اضافة سجل
        </button>

        <table class="table table-responsive table-hover" id="detailsTable">
            <thead>
                <tr style="background:gray">

                    <th>

                    </th>
                    <th>
                        الصنف
                    </th>
                    <th>
                        سعرالحبه
                    </th>
                    <th>
                        الكميه
                    </th>
                    <th>
                        الإجمالي
                    </th>
                </tr>
            </thead>
            @if (Model.ID == 0)
            {
                <tbody>
                    <tr>
                        <td>
                            <a class="fa fa-trash-o remove-row"></a>
                        </td>
                        <td>
                            <select id="ProductID" name="ProductID" class="select2" style="width: 110px;" placeholder="choose Product" required></select>
                        </td>
                        <td>
                            <input type="number" name="Amount" value="Amount" id="Amount" class="Amount" min="1" onchange="countTotal()" />
                        </td>
                        <td>
                            <input type="number" name="Quantity" value="Quantity" id="Quantity" class="Quantity" min="1" onchange="countTotal()" />
                        </td>
                        <td>
                            <input type='number' id='TotalRow' disabled />
                        </td>
                    </tr>
                </tbody>
                <tfoot>
                    <tr style="background:lightgray">
                        <td colspan="5">
                            الإجمالي
                            <input type='number' id='Total' disabled />
                        </td>
                    </tr>
                </tfoot>
            }
            else
            {
                @*<script>  </script>*@
                <tbody>
                    @foreach (var item in Model.SaleInvoiceLines)
                    {
                        <tr>
                            <td>
                                <script>
                                    i('accountid attach listener');
                                    $('#detailsTable').trigger('rowAddOrRemove');
                                </script>
                                <a class="fa fa-trash-o remove-row"></a>
                            </td>
                            <td>
                                <select class="select2" id="ProductID" placeholder="choose Product" name="ProductID" style="width: 110px;" required></select>
                                <input type="hidden" id="ProductIDHolder" value="@item.ProductID" />
                            </td>
                            <td>
                                <input type='number' value="@Math.Abs(item.UnitPrice)" name='Amount' onchange='countTotal()' id='Amount' min='0' required />
                            </td>
                            <td>
                                <input type='number' name='Quantity' value='@item.Quantity' onchange='countTotal()' id='Quantity' min='1' />
                            </td>
                            <td>
                                <input type='number' name="TotalAmount" value='@item.TotalAmount' id='TotalRow' disabled />
                            </td>
                        </tr>
                    }
                </tbody>
                <tfoot>
                    <tr style="background:lightgray">
                        <td colspan="5">
                            الإجمالي
                            <input type='number' name="Amount" id='Total' value='@Model.Amount' disabled />
                        </td>
                    </tr>
                </tfoot>
            }

        </table>
        <button class="btn btn-primary btn-info btn-bold btn-block loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" type="submit" id="submit" value="submit">
            <i class="ace-icon fa fa-save bigger-110"></i>
            حفظ
        </button>
    </div>
</form>

<script>

    $(function () {
        $('#setSelectedAccount').hide();
        if(Number(@Model.ID)>0)
        {
            countTotal();
            $.each($("#detailsTable tbody tr"), function () {
                i('each tb');
                var selectProduct = $(this).find("#ProductID");
                var ProductIDHolder = $(this).find("#ProductIDHolder").val();
                    i(' ProductIDHolder'+ProductIDHolder);
                    fillListByElement(selectProduct, ProductIDHolder, '/Inventory/SaleInvoice/GetAccounts','', function () {
                        initAutoSearch();
                    });
            });
        }
        else {
            $("#Date").val(getToday());
            loadFirstList();
        }
        $("#detailsTable").on("click",
            ".remove-row",
            function () {
                    var table = $(this);
                    table.parents("tr").fadeOut(1000,
                        function () {
                            $(this).remove();
                            countTotal();
                        });
            });

    });
    function loadFirstList() {
        i('loadDataList');
        fillDataList('ProductID', '/Inventory/SaleInvoice/GetAccounts');
        //     initAutoSearch();
    }
    function myfunction() {
        var table = $("#detailsTable tbody");
        var rowCount = $('#detailsTable tr').length-1;

        var innerHTML = "<tr>";
        innerHTML += "<td> <a class='fa fa-trash-o remove-row'></a></td>";
        innerHTML += "<td><select id='ProductID' name='ProductID' class='select2 ProductID' style='width: 110px;'></select></td>";
        innerHTML +='<td><input type="number" name="Amount" id="Amount" value="Amount" class="Amount"  min="1" onchange="countTotal()" /></td>';
        innerHTML +='<td><input type="number" name="Quantity"  id="Quantity" value="Quantity" class="Quantity"  min="1" onchange="countTotal()" /></td>';
        innerHTML += "<td><input type='number' class='TotalRow' id='TotalRow' disabled /></td>";
        innerHTML += "</tr>";
        table.append(innerHTML);
        var currentRow = $('#detailsTable').find('tr').eq(rowCount);

        var selectProduct = currentRow.find("#ProductID");
        fillListByElement(selectProduct, 0, '/Inventory/SaleInvoice/GetAccounts');
        initAutoSearch();
    };

    function countTotal() {
        i('countTotal');
        var totalRow = 0;
        var total = 0;
        var finelTotal = 0;
        var totalValues = 0;
        var toTotalValues = 0;

    $.each($("#detailsTable tbody tr"), function () {
        i('countTotal each row');

        var value = parseInt($(this).find('td:eq(2) input[type="number"]').val());
        i('countTotal value ' + value);

        var Quantity = parseInt($(this).find('td:eq(3) input[type="number"]').val());
        i('countTotal Quantity ' + Quantity);

        totalRow = value * Quantity;
        i('countTotal totalRow ' + totalRow);

        totalValues = parseInt($(this).find('td:eq(4) input[type="number"]').val(totalRow));

        toTotalValues = parseInt($(this).find('td:eq(4) input[type="number"]').val());

        i('countTotal toTotalValues ' + toTotalValues);
        finelTotal += toTotalValues;
        i('countTotal totalRow ' + totalRow);
    });

        $("#Total").val(finelTotal);
        i('countTotal total ' + total);
};

    $(function () {
        i('form load');
      $("#form").submit(function(event) {
            i(' multiform onsubmit');
            event.preventDefault();
                var Entry = [];
                Entry.length = 0;
                 $.each($("#detailsTable tbody tr"), function () {
                     Entry.push({
                        ProductID: 11, //$(this).find('td:eq(1) option:selected').val(),
                        UnitPrice: $(this).find('td:eq(2) input[type="number"]').val(),
                        Quantity: $(this).find('td:eq(3) input[type="number"]').val(),
                        TotalAmount: $(this).find('td:eq(4) input[type="number"]').val(),
                     });
                 });

                 var data = JSON.stringify({
                     ID:@Model.ID,
                    Date: $("#Date").val(),
                    CurrencyID: $("#CurrencyID").val(),
                    CreditorAccountID: $("#CreditorAccountID").val(),
                    Note: $("#Note").val(),
                    RefNumber: $("#RefNumber").val(),
                    Amount: $("#Total").val(),
                    SaleInvoiceLines: Entry
                });

                $.ajax({
                    method: "POST",
                    url: 'Inventory/SaleInvoice/AddOrEditSale',
                   contentType: "application/json",
                    data: data ,
                success: function (data) {
                    console.log(data);
            showSuccess(data.Message);
                     $("#modal").modal('hide');
            fetchData(false);
                },
                error: function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown)
                }
                });
        });

    });
  //  initAutoSearch();
    function initAutoSearch() {
        i('initAutoSearch');
        $('.select2').css('width', '200px').select2({ allowClear: false });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    }
</script>