﻿@model AppTech.MSMS.Domain.Reports.Models.AuditLogModel
@{
    ViewBag.Title = "مراقبة المستخدمين";
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}


<div class="form-group">
    @Html.LabelFor(model => model.UserID)
    @Html.DropDownListFor(m => m.UserID, (SelectList) ViewBag.Users)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.PageName)
    @Html.DropDownListFor(m => m.PageName, (SelectList) ViewBag.Pages)
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Action)
    @Html.DropDownListFor(m => m.Action, (SelectList) ViewBag.Actions)
</div>

<script>
    $("#export-pdf").hide();
    $("#print_grid").hide();
</script>