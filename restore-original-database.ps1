# سكريبت استعادة قاعدة البيانات الأصلية - AppTech MSMS

Write-Host @"
========================================
    استعادة قاعدة البيانات الأصلية
========================================
"@ -ForegroundColor Cyan

# معلومات قاعدة البيانات الأصلية
$originalPath = "C:\Program Files\Microsoft SQL Server\MSSQL14.MSSQLSERVER17\MSSQL\DATA"
$mdfFile = "$originalPath\MSDBData.mdf"
$ldfFile = "$originalPath\MSDBLog.ldf"
$databaseName = "MSDBData"

# مسار الوجهة الجديد
$newDataPath = "C:\Program Files\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA"
$newMdfFile = "$newDataPath\MSDBData.mdf"
$newLdfFile = "$newDataPath\MSDBLog.ldf"

Write-Host "`n1. فحص وجود الملفات الأصلية..." -ForegroundColor Yellow

if (Test-Path $mdfFile) {
    $mdfSize = (Get-Item $mdfFile).Length / 1MB
    Write-Host "✓ وُجد ملف البيانات: $mdfFile (حجم: $([math]::Round($mdfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "✗ ملف البيانات غير موجود: $mdfFile" -ForegroundColor Red
    Write-Host "يرجى نسخ الملف من السيرفر القديم أولاً" -ForegroundColor Yellow
    exit 1
}

if (Test-Path $ldfFile) {
    $ldfSize = (Get-Item $ldfFile).Length / 1MB
    Write-Host "✓ وُجد ملف السجل: $ldfFile (حجم: $([math]::Round($ldfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "⚠ ملف السجل غير موجود: $ldfFile" -ForegroundColor Yellow
    Write-Host "سيتم إنشاء ملف سجل جديد" -ForegroundColor Gray
}

Write-Host "`n2. البحث عن SQL Server المتاح..." -ForegroundColor Yellow

# قائمة SQL Server instances للاختبار
$sqlInstances = @(
    "localhost",
    ".\SQLEXPRESS", 
    "localhost\SQLEXPRESS",
    ".\MSSQLSERVER17",
    "localhost\MSSQLSERVER17",
    "(localdb)\MSSQLLocalDB"
)

$workingInstance = $null
foreach ($instance in $sqlInstances) {
    try {
        Write-Host "اختبار: $instance" -ForegroundColor Gray
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION, @@SERVERNAME" -ErrorAction Stop -Timeout 5
        if ($result) {
            $workingInstance = $instance
            Write-Host "✓ تم العثور على SQL Server: $instance" -ForegroundColor Green
            Write-Host "الإصدار: $($result[0].Column1.Substring(0, 60))..." -ForegroundColor Gray
            break
        }
    } catch {
        Write-Host "✗ فشل الاتصال بـ: $instance" -ForegroundColor Red
    }
}

if (-not $workingInstance) {
    Write-Host "✗ لم يتم العثور على SQL Server متاح" -ForegroundColor Red
    Write-Host "يرجى تثبيت SQL Server Express أو تفعيل SQL Server" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n3. فحص وجود قاعدة البيانات..." -ForegroundColor Yellow

try {
    # فحص إذا كانت قاعدة البيانات موجودة بالفعل
    $existingDb = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = '$databaseName'" -ErrorAction SilentlyContinue
    
    if ($existingDb) {
        Write-Host "⚠ قاعدة البيانات موجودة بالفعل: $databaseName" -ForegroundColor Yellow
        $overwrite = Read-Host "هل تريد استبدالها؟ (y/n)"
        if ($overwrite -eq "y") {
            # فصل قاعدة البيانات الموجودة
            Write-Host "فصل قاعدة البيانات الموجودة..." -ForegroundColor Gray
            Invoke-Sqlcmd -ServerInstance $workingInstance -Query "ALTER DATABASE [$databaseName] SET SINGLE_USER WITH ROLLBACK IMMEDIATE" -ErrorAction SilentlyContinue
            Invoke-Sqlcmd -ServerInstance $workingInstance -Query "DROP DATABASE [$databaseName]" -ErrorAction SilentlyContinue
        } else {
            Write-Host "تم إلغاء العملية" -ForegroundColor Yellow
            exit 0
        }
    }
} catch {
    Write-Host "خطأ في فحص قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. إرفاق قاعدة البيانات..." -ForegroundColor Yellow

try {
    # إنشاء استعلام الإرفاق
    if (Test-Path $ldfFile) {
        # إرفاق مع ملف السجل
        $attachQuery = @"
CREATE DATABASE [$databaseName] ON 
(FILENAME = '$mdfFile'),
(FILENAME = '$ldfFile')
FOR ATTACH
"@
    } else {
        # إرفاق بدون ملف السجل (سيتم إنشاؤه تلقائياً)
        $attachQuery = @"
CREATE DATABASE [$databaseName] ON 
(FILENAME = '$mdfFile')
FOR ATTACH_REBUILD_LOG
"@
    }
    
    Write-Host "تنفيذ استعلام الإرفاق..." -ForegroundColor Gray
    Invoke-Sqlcmd -ServerInstance $workingInstance -Query $attachQuery -ErrorAction Stop -Timeout 60
    
    Write-Host "✓ تم إرفاق قاعدة البيانات بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "✗ خطأ في إرفاق قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    
    # محاولة بديلة: استعادة من نسخة احتياطية
    Write-Host "`nمحاولة بديلة: إنشاء نسخة احتياطية واستعادتها..." -ForegroundColor Yellow
    
    try {
        # إنشاء مجلد للنسخ الاحتياطية
        $backupPath = "C:\Backup"
        if (-not (Test-Path $backupPath)) {
            New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
        }
        
        $backupFile = "$backupPath\MSDBData.bak"
        
        # إنشاء نسخة احتياطية (يتطلب الوصول للسيرفر الأصلي)
        Write-Host "يرجى إنشاء نسخة احتياطية من السيرفر الأصلي باستخدام:" -ForegroundColor Yellow
        Write-Host "BACKUP DATABASE [$databaseName] TO DISK = '$backupFile'" -ForegroundColor Cyan
        
    } catch {
        Write-Host "فشل في الحل البديل: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n5. اختبار قاعدة البيانات..." -ForegroundColor Yellow

try {
    # اختبار الاتصال بقاعدة البيانات
    $testQuery = "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
    $result = Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $testQuery -ErrorAction Stop
    
    Write-Host "✓ قاعدة البيانات تعمل بنجاح!" -ForegroundColor Green
    Write-Host "عدد الجداول: $($result.TableCount)" -ForegroundColor Gray
    
    # عرض بعض الجداول المهمة
    $tablesQuery = "SELECT TOP 10 TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
    $tables = Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $tablesQuery -ErrorAction SilentlyContinue
    
    if ($tables) {
        Write-Host "`nبعض الجداول الموجودة:" -ForegroundColor Gray
        foreach ($table in $tables) {
            Write-Host "  - $($table.TABLE_NAME)" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "⚠ خطأ في اختبار قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n6. إنشاء سلاسل الاتصال..." -ForegroundColor Yellow

# إنشاء سلاسل الاتصال الجديدة
$connectionString = "Data Source=$workingInstance;Initial Catalog=$databaseName;Integrated Security=True;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"
$entityConnectionString = "metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=`"$connectionString`""

Write-Host "سلسلة الاتصال الجديدة:" -ForegroundColor Cyan
Write-Host $connectionString -ForegroundColor White

# حفظ معلومات الاتصال في ملف
$connectionInfo = @"
# معلومات اتصال قاعدة البيانات المستعادة
SQL Server Instance: $workingInstance
Database Name: $databaseName
Connection String: $connectionString

Entity Framework Connection String:
$entityConnectionString
"@

$connectionInfo | Out-File -FilePath "database-connection-info.txt" -Encoding UTF8
Write-Host "`n✓ تم حفظ معلومات الاتصال في: database-connection-info.txt" -ForegroundColor Green

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "انتهت استعادة قاعدة البيانات بنجاح!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nالخطوة التالية: تحديث ملفات web.config بسلاسل الاتصال الجديدة" -ForegroundColor Yellow
Write-Host "استخدم السكريبت: .\update-web-configs-with-original-db.ps1" -ForegroundColor Cyan
