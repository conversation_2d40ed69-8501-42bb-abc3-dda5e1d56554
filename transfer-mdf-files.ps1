# سكريبت نقل ملفات MDF لقاعدة بيانات نوافذ

Write-Host @"
========================================
    نقل ملفات MDF - قاعدة بيانات نوافذ
========================================
"@ -ForegroundColor Cyan

# مسارات الملفات
$sourcePath = "E:\MSSQLDATA"
$sourceMDF = "$sourcePath\nawafd.mdf"
$sourceLDF = "$sourcePath\nawafd_0.ldf"

# مسار الوجهة (SQL Server Express)
$destPath = "C:\Program Files\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA"
$destMDF = "$destPath\nawafd.mdf"
$destLDF = "$destPath\nawafd_0.ldf"

$databaseName = "nawafd"

Write-Host "`n1. فحص الملفات المصدر..." -ForegroundColor Yellow

if (Test-Path $sourceMDF) {
    $mdfSize = (Get-Item $sourceMDF).Length / 1MB
    Write-Host "✓ ملف MDF موجود: $sourceMDF (حجم: $([math]::Round($mdfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "✗ ملف MDF غير موجود: $sourceMDF" -ForegroundColor Red
    exit 1
}

if (Test-Path $sourceLDF) {
    $ldfSize = (Get-Item $sourceLDF).Length / 1MB
    Write-Host "✓ ملف LDF موجود: $sourceLDF (حجم: $([math]::Round($ldfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "⚠ ملف LDF غير موجود: $sourceLDF" -ForegroundColor Yellow
}

Write-Host "`n2. فحص مجلد الوجهة..." -ForegroundColor Yellow

if (-not (Test-Path $destPath)) {
    Write-Host "إنشاء مجلد الوجهة: $destPath" -ForegroundColor Gray
    try {
        New-Item -ItemType Directory -Path $destPath -Force | Out-Null
        Write-Host "✓ تم إنشاء المجلد" -ForegroundColor Green
    } catch {
        Write-Host "✗ فشل في إنشاء المجلد: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✓ مجلد الوجهة موجود: $destPath" -ForegroundColor Green
}

Write-Host "`n3. البحث عن SQL Server..." -ForegroundColor Yellow

$sqlInstances = @(
    "localhost",
    ".\SQLEXPRESS", 
    "localhost\SQLEXPRESS",
    "(localdb)\MSSQLLocalDB"
)

$workingInstance = $null
foreach ($instance in $sqlInstances) {
    try {
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION" -ErrorAction Stop -Timeout 5
        if ($result) {
            $workingInstance = $instance
            Write-Host "✓ SQL Server متاح: $instance" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "✗ فشل الاتصال بـ: $instance" -ForegroundColor Red
    }
}

if (-not $workingInstance) {
    Write-Host "✗ لم يتم العثور على SQL Server" -ForegroundColor Red
    exit 1
}

Write-Host "`n4. فصل قاعدة البيانات إذا كانت موجودة..." -ForegroundColor Yellow

try {
    $existingDb = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = '$databaseName'" -ErrorAction SilentlyContinue
    
    if ($existingDb) {
        Write-Host "فصل قاعدة البيانات الموجودة..." -ForegroundColor Gray
        Invoke-Sqlcmd -ServerInstance $workingInstance -Query "ALTER DATABASE [$databaseName] SET SINGLE_USER WITH ROLLBACK IMMEDIATE" -ErrorAction SilentlyContinue
        Invoke-Sqlcmd -ServerInstance $workingInstance -Query "EXEC sp_detach_db '$databaseName'" -ErrorAction SilentlyContinue
        Write-Host "✓ تم فصل قاعدة البيانات" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ خطأ في فصل قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n5. نسخ ملفات MDF..." -ForegroundColor Yellow

try {
    # نسخ ملف MDF
    Write-Host "نسخ ملف MDF..." -ForegroundColor Gray
    Copy-Item $sourceMDF $destMDF -Force
    Write-Host "✓ تم نسخ ملف MDF" -ForegroundColor Green
    
    # نسخ ملف LDF إذا كان موجوداً
    if (Test-Path $sourceLDF) {
        Write-Host "نسخ ملف LDF..." -ForegroundColor Gray
        Copy-Item $sourceLDF $destLDF -Force
        Write-Host "✓ تم نسخ ملف LDF" -ForegroundColor Green
    }
    
} catch {
    Write-Host "✗ خطأ في نسخ الملفات: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n6. إرفاق قاعدة البيانات..." -ForegroundColor Yellow

try {
    if (Test-Path $destLDF) {
        # إرفاق مع ملف السجل
        $attachQuery = @"
CREATE DATABASE [$databaseName] ON 
(FILENAME = '$destMDF'),
(FILENAME = '$destLDF')
FOR ATTACH
"@
    } else {
        # إرفاق بدون ملف السجل
        $attachQuery = @"
CREATE DATABASE [$databaseName] ON 
(FILENAME = '$destMDF')
FOR ATTACH_REBUILD_LOG
"@
    }
    
    Invoke-Sqlcmd -ServerInstance $workingInstance -Query $attachQuery -ErrorAction Stop -Timeout 60
    Write-Host "✓ تم إرفاق قاعدة البيانات بنجاح!" -ForegroundColor Green
    
} catch {
    Write-Host "✗ خطأ في إرفاق قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n7. اختبار قاعدة البيانات..." -ForegroundColor Yellow

try {
    $testQuery = @"
SELECT 
    COUNT(*) as TableCount,
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.VIEWS) as ViewCount
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_TYPE = 'BASE TABLE'
"@
    
    $result = Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $testQuery -ErrorAction Stop
    
    Write-Host "✓ قاعدة البيانات تعمل بنجاح!" -ForegroundColor Green
    Write-Host "عدد الجداول: $($result.TableCount)" -ForegroundColor Gray
    Write-Host "عدد الـ Views: $($result.ViewCount)" -ForegroundColor Gray
    
} catch {
    Write-Host "⚠ خطأ في اختبار قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n8. إنشاء سلاسل الاتصال..." -ForegroundColor Yellow

$connectionString = "Data Source=$workingInstance;Initial Catalog=$databaseName;Integrated Security=True;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"

Write-Host "سلسلة الاتصال الجديدة:" -ForegroundColor Cyan
Write-Host $connectionString -ForegroundColor White

# حفظ معلومات الاتصال
$connectionInfo = @"
# معلومات اتصال قاعدة بيانات نوافذ بعد النقل
تاريخ النقل: $(Get-Date)
SQL Server Instance: $workingInstance
Database Name: $databaseName
MDF File: $destMDF
LDF File: $destLDF

Connection String:
$connectionString

Entity Framework Connection String:
metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string="$connectionString"
"@

$connectionInfo | Out-File -FilePath "nawafd-transfer-info.txt" -Encoding UTF8

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "انتهى نقل ملفات MDF بنجاح!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nتم حفظ معلومات الاتصال في: nawafd-transfer-info.txt" -ForegroundColor Cyan
Write-Host "`nالخطوة التالية: تحديث ملفات web.config بسلاسل الاتصال الجديدة" -ForegroundColor Yellow

pause
