﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
    <div class="center mainForImageRespons">
        <div class="col-md-12">
            <div class="col-md-6 col-xs-12">
                <h3 class="header blue bolder smaller">خدمات الشحن الفوري</h3> <br />
                <div class="ForImageRespons">
                    <div class="ForImage" onclick="openTopup(this, Services.YemenMobile)">
                        <img src="~/Photos/Image/ic_electronic_balan.png" />
                        <p class="center bolder"> يمن موبايل</p>
                    </div>
                    <div class="ForImage" onclick="openTopup(this, Services.MTN)">
                        <img src="~/Photos/Image/mtn.png" />
                        <p class="center bolder"> أم تي ان</p>
                    </div>
                    <div class="ForImage" onclick="openTopup(this, Services.Sabafon)">
                        <img src="~/Photos/Image/ic_saba_fon.png" />
                        <p class="center bolder"> سبأفون</p>
                    </div>

                    @if (DomainManager.LicensedServices.Gomala)
                    {
                        <div class="ForImage">
                            <a href="/#!/route/clients/TrailToupOrder/addoredit">
                                <img src="~/Photos/Image/ic_recharge.png" />
                                <p class="center bolder">شحن فوري بالجملة</p>
                            </a>
                        </div>
                    }
                    </div>
            </div>
            <div class="col-md-6 col-xs-12">
                <h3 class="header blue bolder smaller">خدمات الباقات</h3>
                <div class="ForImageRespons">
                    <div class="ForImage">
                        <a href="/#!/route/clients/ymofferpayment/addoredit">
                            <img src="~/Photos/Image/ic_electronic_balan.png" />
                            <p class="center bolder"> يمن موبايل </p>
                        </a>
                    </div>
                    <div class="ForImage">
                        <a href="/#!/route/clients/mtnofferpayment/addoredit">
                            <img src="~/Photos/Image/mtn.png" />
                            <p class="center bolder"> ام تي ان </p>
                        </a>
                    </div>
                    <div class="ForImage">
                        <a href="/#!/route/clients/spbagat/addoredit">
                            <img src="~/Photos/Image/ic_saba_fon.png" />
                            <p class="center bolder"> سبأفون </p>
                        </a>
                    </div>
                    
                    <div class="ForImage">
                        <a href="/#!/route/clients/spbagatNorth/addoredit">
                            <img src="~/Photos/Image/ic_saba_fon.png" />
                            <p class="center bolder"> سبأفون جنوب </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12 col-xs-12">
            <h3 class="header blue bolder smaller">خدمات السداد و الإشتراكات </h3>
            <div class="ForImageRespons mod">
                <div class="ForImage">
                    <a href="/#!/route/clients/SatellitePayment/addoredit">
                        <img src="~/Photos/Image/ic_Satellite.png" />
                        <p class="center bolder"> إشتراك قنوات </p>
                    </a>
                </div>

                <div class="ForImage" onclick="openTopup(this, Services.ADSL)">
                    <img src="~/Photos/Image/ic_internet.png" />
                    <p class="center bolder">     تسديد الأنترنت المنزلي</p>
                </div>

                <div class="ForImage" onclick="openTopup(this, Services.Line)">
                    <img src="~/Photos/Image/ic_phone_line.png" />
                    <p class="center bolder">       تسديد الهاتف الثابت</p>
                </div>

                <div class="ForImage" onclick="openTopup(this, Services.Electricity)">
                    <img src="~/Photos/Image/ic_electricity.png" />
                    <p class="center bolder"> تسديد الكهرباء</p>
                </div>

                <div class="ForImage" onclick="openTopup(this, Services.Water)">
                    <img src="~/Photos/Image/ic_water.png" />
                    <p class="center bolder">تسديد الماء</p>
                </div>


            </div>
        </div>

        @if (CurrentUser.Type != UserType.Admin)
        {
            <div class="col-md-12 ">

                <div class="col-md-6 col-xs-12">
                    <h3 class="header blue bolder smaller">خدمات مالية</h3>
                    <div class="ForImageRespons">

                        @*@if (DomainManager.LicensedServices.Transfers) {
        <div class="ForImage">
            <a href="/#!/route/Clients/transferorder/addoredit">
                <img src="~/Photos/Image/ic_send_money.png" />
                <p class="center bolder">ارسال/سحب حوالة شركات</p>
            </a>
        </div>
        }*@


                        <div class="ForImage">
                            <a href="/#!/route/Clients/transfer/addoredit">
                                <img src="~/Photos/Image/ic_peer_payment.png" />
                                <p class="center bolder">تحويل الى حساب </p>
                            </a>
                        </div>
                        <div class="ForImage">
                            <a href="/#!/route/Clients/DepositOrder/addoredit">
                                <img src="~/Photos/Image/ic_pay_merchant.png" />
                                <p class="center bolder">إيداع نقدي</p>
                            </a>
                        </div>

                        @*<div class="ForImage">
            <a href="/#!/route/Clients/MerchantPayment/addoredit">
                <img src="~/Photos/Image/ic_peer_payment.png" />
                <p class="center bolder">سداد تاجر</p>
            </a>
        </div>*@

                    </div>
                </div>
         
                @if (DomainManager.LicensedServices.Sim) { 
                    <div class="col-md-6 col-xs-12 forModFooter">
                    <h3 class="header blue bolder smaller">خدمات الشرائح</h3>
                    <div class="ForImageRespons">


                        
                       
                    <div class="ForImage">
                        <a href="/#!/route/Clients/SimCardOrderNew/addoredit">
                            <img src="~/Photos/Image/ic_sim.png" />
                            <p class="center bolder">الشرائح جديد</p>
                        </a>
                    </div>

                        <div class="ForImage">
                            <a href="/#!/route/Clients/SimCardOrderLost/addoredit">
                                <img src="~/Photos/Image/ic_sim.png" />
                                <p class="center bolder">الشرائح بدل</p>
                            </a>
                        </div>
                        

                    </div>
                </div>
                }

            </div>
        }
    </div>


    <div id="modal" class="modal fade" role="dialog" aria-labelledby="model-title">
        <div class="modal-dialog" role="form">
            <div class="modal-content">

                <div class="modal-header background-blue">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="modal-title"></h4>
                </div>

                <div class="modal-body" style="padding: 40px 50px;">
                    @Html.Partial("_Topup")
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-danger btn-default pull-left" data-dismiss="modal">
                        خروج
                    </button>

                    <button class="btn btn-white btn-default btn-round pull-right" type="reset" onclick="resetButton();">
                        <i class="ace-icon fa fa-undo bigger-110"></i>
                    </button>
                </div>

            </div>
        </div>
    </div>



    <script>
        function clear() {
            $('#crudform')[0].reset();
        }

        function init() {
            $("#SubscriberNumber").val('');
            $("#Amount").val('');
            $("#info").hide();
            $("#key").text("");
            $("#val").text('');
            $("#faction-row").hide();
            $("#region-row").hide();
            $("#amount-row").hide();
            $("#linetype-row").hide();
        }

        function fillList(response, element) {
            if (response.length > 0) {
                $('#' + element).html('');
                var options = '';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#' + element).append(options);
            }
        }

        function loadDataList(sid, element) {
            var data = { sid: sid };
            AjaxCall('/Clients/Charging/GetFactions', data).done(function (response) {
                fillList(response, element);
            }).fail(function (xhr, textStatus, errorThrown) {
                parseAndShowError(xhr, textStatus, errorThrown);
            });
        }

        $(function () {
            $("#page-title").text($('#Title').val());
            $("#submit-button").html('تسديد');
            init();
        });

        var $modal = $("#modal");

        function openTopup(self, sid) {
            init();
            $modal.find('input[name="ServiceID"]').val(sid);
            var title = self.innerText;
            $modal.find('.modal-title').text(title);
            if (sid === Services.YemenMobile ||
                sid === Services.Line ||
                sid === Services.ADSL ||
                sid === Services.TelYemen) {
                $("#amount-row").show();
            } else if (sid === Services.MTN || sid === Services.Sabafon) {
                $("#faction-row").show();
                $("#linetype-row").show();
                loadDataList(sid, 'FactionID');
            } else if (sid === Services.Water || sid === Services.Electricity) {
                $("#region-row").show();
                $("#amount-row").show();
                loadDataList(sid, 'RegionID');
            }
            $modal.modal("show");
        }


    </script>
    <script type="text/javascript">

    function showInfo(key, val) {
        $("#info").show();
        $("#key").text(key);
        $("#val").text(val);
    }

    function hideInfo() {
        $("#info").hide();
        $("#key").text('');
        $("#val").text('');
    }

    function queryBalance() {
        var sno = $("#SubscriberNumber").val();
        var sid = $("#ServiceID").val();
        i(sid);
        hideInfo();
        showLoading();
        $.ajax({
            url: '@Url.Action("QueryBalance", "Charging")',
            data: { sno: sno, sid: sid },
            success: function(data) {
                i('query arrived');
                hideLoading();
                var result = data;
                if (result.Success) {
                    i('success');
                    i('bal>' + result.Balance);
                    if (sid === "6" || sid === "7" || sid === "8" || sid === "9" || sid === "10") {
                        $("#info").show();
                        $("#key").text("");
                        $("#val").text(result.Balance);


                    } else {
                        $("#info").show();
                        $("#key").text("رصيد المشترك: ");
                        $("#val").text(result.Balance);

                        if (sid === "1") {
                            $("#val").text($("#val").text() + '   ' + result.LineType);
                        }
                    }
                } else {
                    i('not success');
                    alert(result.Message);
                }

            },
            error: function(xhr, ajaxOptions, thrownError) {
                hideLoading();
                alert(parseXhr(xhr));

            }
        });
    }

    function queryYM() {

        var sid = parseInt($("#ServiceID").val());

        var sno = $("#SubscriberNumber").val();
        if (sid === Services.YemenMobile) {

            if (sno.length === 9 && sno.match("^77")) {
                i('sno.length === 9 && sno.match("^77")');
                queryBalance();
            }
        }

    }

    $(function() {

        $('#SubscriberNumber').on('input',
            function() {
                i('key down sid:');
            });
    });

    function onLineType(self) {
        i('line');
        var sid = parseInt($("#ServiceID").val());
        if (sid === Services.Sabafon) {
            var type = $('#LineType').find(":selected").text();
            i(type);
            if (type === 'فوترة') {
                $("#faction-row").hide();
                $("#amount-row").show();
            } else {
                $("#faction-row").show();
                $("#amount-row").hide();
            }
        }

    }
    </script>