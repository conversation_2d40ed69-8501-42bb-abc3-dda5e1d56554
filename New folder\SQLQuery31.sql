﻿SELECT        
dbo.Account.ID, 
dbo.Account.Number AS Account_No, 
dbo.Account.Name AS Account_Name,
grp.ID Group_id,
grp.Name Group_name,

ISNULL(agg.ID,dbo.Agent.ID) As Agg_id ,
dbo.get_acc_no_1(ISNULL(agg.AccountID,dbo.Agent.AccountID)) As Agg_cli_acc_no ,
dbo.get_acc_name_1(ISNULL(agg.AccountID,dbo.Agent.AccountID)) As Agg_cli_acc_name ,

ISNULL(BRN.ID,dbo.Branch.ID) As Brn_id ,
dbo.get_acc_no_1(ISNULL(BRN.AccountID,Branch.AccountID)) As Brn_cli_acc_no ,
dbo.get_acc_name_1(ISNULL(BRN.AccountID,dbo.Branch.AccountID)) As Brn_cli_acc_name ,
dbo.Account.Type AS Account_Type, 

dbo.Account.CreatedTime AS [وقت الإنشاء], 
dbo.Account.CreatedBy, 
dbo.Account.BranchID
FROM 
dbo.Account LEFT JOIN
dbo.Client As cli ON cli.AccountID = Account.ID LEFT JOIN
dbo.GroupItem As itm ON (itm.Type = 'Accounts' and dbo.Account.ID = itm.ItemID) LEFT JOIN
dbo.PartyGroup As grp ON grp.ID = itm.GroupID LEFT JOIN
dbo.Agent As agg ON cli.AgentID = agg.ID LEFT JOIN
dbo.Agent ON dbo.Agent.AccountID = dbo.Account.ID LEFT JOIN
dbo.Branch ON dbo.Account.ID = dbo.Branch.AccountID LEFT JOIN
dbo.Branch As BRN ON cli.BranchID = BRN.ID


where dbo.Account.ParentNumber in ('1237','1221','1222')