﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.AccountApi

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

@Html.HiddenFor(model => model.ID)
<div class="">
    <div class="form-group">
        @Html.LabelFor(model => model.AccountId, new { @class = "control-label col-md-2" })
        <div class="col-md-10">

            @Html.DropDownListFor(model => model.AccountId, (SelectList)ViewBag.Accounts, new { @class = "control-label col-md-2" })
            @*@Html.Obout(new ComboBox("AccountId")
                {
                    SelectedValue = Model.AccountId == 0 ? null : Model.AccountId.ToString(),
                    FilterType = ComboBoxFilterType.Contains,
                    Class="accountIdList"
                })*@
            @*<select id="AccountId" name="AccountId" class="select2 form-control" placeholder="اختر الحساب"></select>*@
            @Html.ValidationMessageFor(model => model.AccountId, "", new { @class = "text-danger" })
        </div>
        <div class="space-6"></div>
    </div>
</div>

<br />

<div class="form-group">
    @Html.Label("المستخدم", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <select id="UserId" name="UserId"></select>
        @Html.ValidationMessageFor(model => model.UserId, "", new { @class = "text-danger" })
    </div>
</div>






<div class="form-group">
    @Html.LabelFor(model => model.Permitted, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Permitted)
            @Html.ValidationMessageFor(model => model.Permitted, "", new { @class = "text-danger" })
        </div>
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.IsAllowed, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.IsAllowed)
            @Html.ValidationMessageFor(model => model.IsAllowed, "", new { @class = "text-danger", onClick = "toggle(this)" })
        </div>
    </div>
</div>

<div class="form-group" id="ip-address">
    @Html.LabelFor(model => model.IpAddress, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.IpAddress, new { htmlAttributes = new { } })
        @Html.ValidationMessageFor(model => model.IpAddress, "", new { @class = "text-danger" })
    </div>
</div>




<div class="form-group">
    @Html.LabelFor(model => model.Binded, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Binded)
            @Html.ValidationMessageFor(model => model.Binded, "", new { @class = "text-danger" })
        </div>
    </div>
</div>


<div class="form-group" id="Binded">
    @Html.LabelFor(model => model.Binding, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Binding, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Binding, "", new { @class = "text-danger" })
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.Primary, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Primary)
            @Html.ValidationMessageFor(model => model.Primary, "", new { @class = "text-danger" })
        </div>
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.Synced, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Synced)
            @Html.ValidationMessageFor(model => model.Synced, "", new { @class = "text-danger" })
        </div>
    </div>
</div>






<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>

<script>
    $(function () {
    

        try {

            $('#AccountId').on("change",
                function () {
                    i('change');
                    var aid = $('#AccountId').val();
                    i('AccountId>' + aid);
                    fillDataList('UserId', '/Security/AccountApi/GetUsers/' + aid, false, "اختر المستخدم");

                });



            i('check id');
            if (Number($("#ID").val()) > 0) {
                i('id>0');
                var aid = $('#AccountId').val();
                var UserId = $('#UserId').val();
                fillListWithSelected('UserId', UserId, '/Security/AccountApi/GetUsers/' + aid);
            }
           
        }
        catch (e) {
            ar(e);
        }

    });



</script>
@*<script>
    $("#ip-address").hide();
    $('#IsAllowed').on('click',
        function () {
            $("#ip-address").toggle(!this.checked);
        });
    </script>*@