﻿@model AppTech.MSMS.Domain.Models.WifiPayment

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}

<div class="form-group">
    <label class="col-sm-2 control-label">الشبكة </label>
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ProviderID, (SelectList)ViewBag.Providers, new { })
        @Html.ValidationMessageFor(model => model.ProviderID)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">الفئة </label>
    <div class="col-md-10">
        <select id="FactionID" name="FactionID" class="select2" style="width: 110px;" placeholder="اختر فئه" required></select>
        @Html.ValidationMessageFor(model => model.FactionID)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">الكمية</label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.Quantity, new { htmlAttributes = new { min = 1, max = 20} })
        @Html.ValidationMessageFor(model => model.Quantity)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">رقم الهاتف </label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.WifiNumber)
        @Html.ValidationMessageFor(model => model.WifiNumber)
    </div>
</div>
<input type="number" id="Cost" hidden />

<script>
    $(function () {
        var Provider = $("#ProviderID").val();
        loadFactionList(Provider);
    });
    $("#ProviderID").on("change", function () {
        var id = $("#ProviderID").val();
        loadFactionList(id);
    });
    function loadFactionList(id) {
        i('loadDataList');
        fillDataList('FactionID', '/Clients/WifiPayment/GetWifiFactions?id=' + id, false, "اختر فئــه");
    };
    $("#FactionID").on("change", function () {
        var faction = $("#FactionID").val();

        $.post("Clients/WifiPayment/GetCost", { id: faction }, function (data) {
            $("#Cost").val(data);
        });
    });

    $(function () {
        $("#cancel-button").hide();
        $("#submit-button").text('طلب الكرت');

        $("#submit-button").on('click', function () {
        var quant = $("#Quantity").val();
            i('quant' + quant);
            if (quant == 0) {
            i('quant' + quant);
                alert("الرجاء تحديد الكمية");
                history.go(0);
            }
            else {
                 var phone = '';
                 var total = ($("#Cost").val()) * quant ;

                if ($("#Phone").val() > 700000000)
                    phone += ' لرقم ' + $("#Phone").val();

                var msg = "سوف يتم شراء كرت  " +
                    $("#ProviderID option:selected").text()
                    + ' فئة : ' + $("#FactionID option:selected").text()
                    + phone
                    + ' بمبلغ ' + total + ' ريال يمني  '
                    + ' هل انت متأكد؟';

                if (!confirm(msg)) {
                    i('not confirmed');
                    history.go(0);
                    return false;
                } else {
                    i('confirmed');
                    return true;
                }
            }
        })
    });

    //$(function () {
    //    $("#cancel-button").hide();
    //    $("#submit-button").text('طلب الكرت');

    //    $("#submit-button").on('click', function () {
    //        var msg = "سوف يتم شراء كرت " +
    //            $("#ProviderID option:selected").text()
    //            + ' فئة : ' + $("#FactionID option:selected").text()
    //        ' هل انت متأكد';
    //        if (!confirm(msg)) {
    //            i('not confirmed');
    //            history.go(0);
    //            return false;
    //        } else {
    //            i('confirmed');
    //            return true;
    //        }
    //    })
    //})

</script>

