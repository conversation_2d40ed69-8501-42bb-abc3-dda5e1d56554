﻿@model AppTech.MSMS.Domain.Models.SMSDispatch

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
@Html.ValidationSummary(true, "", new {@class = "text-danger"})
<div class="form-group">
    @Html.Label("اسم الخدمة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.ServiceID, (SelectList) ViewBag.Services)
        @Html.ValidationMessageFor(model => model.ServiceID, "", new {@class = "text-danger"})
    </div>
</div>

@*<div class="form-group">
    <div class="col-md-12">
        @Html.Label("اسم العميل", new {@class = "control-label col-md-2"})
        @Html.Obout(new ComboBox("ClientID")
        {
            Width = 300,
            SelectedValue = Model.ClientID == null ? null : Model.ClientID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
        })

        @Html.ValidationMessageFor(model => model.ClientID)
    </div>
</div>*@

<div class="form-group">
    @Html.LabelFor(model => model.Active, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Active)
            @Html.ValidationMessageFor(model => model.Active, "", new {@class = "text-danger"})
        </div>
    </div>
</div>