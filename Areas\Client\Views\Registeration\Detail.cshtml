﻿@using AppTech.Common.Extensions
@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@model AppTech.MSMS.Domain.Models.Client


<div class="hr dotted"></div>
<span class="alert"></span>

<div id="user-profile-1" class="user-profile row">
<div class="col-xs-12 col-sm-9">

    <div class="space-12"></div>


    <div class="profile-user-info profile-user-info-striped">

      

        <div class="profile-info-row">
            <div class="profile-info-name"> اسم العميل </div>

            <div class="profile-info-value">
                <span class="editable" id="username"> @Html.DisplayFor(model => model.Name)</span>
            </div>
        </div>



        <div class="profile-info-row">
            <div class="profile-info-name"> رقم الهاتف </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.PhoneNumber)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> الأيميل </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.Email)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> العنوان </div>

            <div class="profile-info-value">
                <i class="fa fa-map-marker light-orange bigger-110"></i>
                <span class="editable" id="country">اليمن</span>
                <span class="editable" id="city"> @Html.DisplayFor(model => model.Address)</span>
            </div>
        </div>
        <div class="profile-info-row">
            <div class="profile-info-name">الفرع </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.Branch.Name)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name">نوع النشاط </div>

            <div class="profile-info-value">
                <span class="editable" id="signup"> @Html.DisplayFor(model => model.ShopName)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> تاريخ التسجيل </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.CreatedTime)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> نوع البطاقة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardType)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم البطاقة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardNumber)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> مكان الأصدار </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardIssuePlace)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> تاريخ الأصدار </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardIssueDate)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> صورة البطاقة </div>

            <div class="profile-info-value">

                <img class="img-thumbnail editable img-responsive" alt="صورة البطاقة" id="preview"
                     src="@Url.Action("GetImage", "Client",
                                  new {Model.ID})" />
                @*@if (!string.IsNullOrEmpty(Model.ImageName) && Model.ImageName.StartsWith("~"))
                {
                    <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content(Model.ImageName)" />
                }*@

            </div>
        </div>


    </div>

</div>
</div>
