﻿@model AppTech.MSMS.Domain.Models.Account
@{
    ViewBag.Title = "الحسابات";
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<link href="~/Content/tree/jstree.min.css" rel="stylesheet"/>

<div class="row">

    <div class="col-xs-12 col-sm-6">
        <div class="widget-box widget-color-blue2">
            <div class="widget-header">
                <div class="pull-right">
                    <h4 class="widget-title lighter smaller">شجرة الحسابات</h4>
                </div>
                @*<div class="col-md-4 col-sm-8 col-xs-8">
                        <button type="button" class="btn btn-success btn-sm" onclick="demo_create();"><i class="glyphicon glyphicon-asterisk"></i> Create</button>
                        <button type="button" class="btn btn-warning btn-sm" onclick="demo_rename();"><i class="glyphicon glyphicon-pencil"></i> Rename</button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="demo_delete();"><i class="glyphicon glyphicon-remove"></i> Delete</button>
                    </div>*@

                <div class="pull-left" style="text-align: right;">
                    <input type="text" value="" id="demo_q" placeholder="البحث"/>
                </div>
            </div>
            <div class="widget-body">
                <div class="widget-main">
                    <div id="SimpleJSTree"></div>
                </div>
            </div>
        </div>
    </div>


    <div class="col-xs-12 col-sm-6">
        <div class="widget-box widget-color-blue2">
            <div class="widget-header">
                <h4 class="widget-title lighter smaller">بيانات الحساب</h4>
            </div>

            <div class="widget-body">
                <div class="widget-main padding-8">
                    @using (Ajax.BeginForm("Save", null, new AjaxOptions
                    {
                        OnBegin = "return OnFormBegin()",
                        OnSuccess = "onCrudSuccess",
                        OnFailure = "onCrudFailure",
                        LoadingElementId = "formloader"
                        //  UpdateTargetId = "list",
                        //InsertionMode = InsertionMode.ReplaceWith
                    },
                        new {id = "crudform"}
                        ))
                    {
                        @Html.AntiForgeryToken()
                        @Html.ValidationSummary(true, "", new {@class = "text-danger"})
                        <div id="account-form " class="form-horizontal">
                            @Html.HiddenFor(model => model.ID, new {htmlAttributes = new {@class = "form-control"}})
                            <div class="form-group">
                                @Html.LabelFor(model => model.ParentNumber, new {@class = "control-label col-md-2"})
                                <div class="col-md-10">
                                    @Html.DropDownListFor(model => model.ParentNumber, (SelectList) ViewBag.Accounts, new {htmlAttributes = new {@class = "form-control"}})
                                    @Html.ValidationMessageFor(model => model.ParentNumber, "", new {@class = "text-danger"})
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Number, new {@class = "control-label col-md-2"})
                                <div class="col-md-10">
                                    @Html.EditorFor(model => model.Number, new {htmlAttributes = new {@class = ""}})
                                    @Html.ValidationMessageFor(model => model.Number, "", new {@class = "text-danger"})
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
                                <div class="col-md-10">
                                    @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
                                    @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
                                </div>
                            </div>


                            <div class="form-group">
                                @Html.LabelFor(model => model.Type, new {@class = "control-label col-md-2"})
                                <div class="col-md-10">
                                    <select name="Type" id="Type">
                                        <option value="رئيسي">رئيسي</option>
                                        <option value="فرعي">فرعي</option>
                                    </select>
                                    @Html.ValidationMessageFor(model => model.Type, "", new {@class = "text-danger"})
                                </div>
                            </div>

                            <div class="form-group">
                                @Html.LabelFor(model => model.Description, new {@class = "control-label col-md-2"})
                                <div class="col-md-10">
                                    @Html.EditorFor(model => model.Description, new {htmlAttributes = new {@class = "form-control"}})
                                    @Html.ValidationMessageFor(model => model.Description, "", new {@class = "text-danger"})
                                </div>
                            </div>
                        </div>

                        <div class="hr hr32 hr-dotted"></div>
                        <img id="formloader" class="img-center" src="@Url.Content("~/Content/images/loader-64x/Preloader_4.gif")" alt="loading"/>
                        <button class="btn  btn-white btn-info btn-bold loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" type="submit" name="submitButton" value="save" id="submitButton">
                            <i class="ace-icon fa fa-save bigger-110"></i>
                            حفظ
                        </button>
                        <button class="btn btn-white btn-info btn-bold loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" type="submit" name="submitButton" value="edit" id="submitButton">
                            <i class="ace-icon fa fa-edit bigger-110"></i>
                            تعديل
                        </button>

                        <button class="btn btn-white btn-danger loading" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" type="submit" name="submitButton" value="delete" id="submitButton">
                            <i class="ace-icon fa fa-trash-o bigger-110"></i>
                            حذف
                        </button>
                    }
                    <button class="btn btn-white btn-info btn-bold  loading" style="margin-top: 20px" id="refresh-tree" data-loading-text="<i class='fa fa-spinner fa-spin '></i> جاري تحديث">
                        <i class="ace-icon fa fa-refresh bigger-110"></i>
                        تحديث
                    </button>
                </div>
            </div>
        </div>
    </div>

    @*<div class="col-md-12">
            <p style="text-align:center; padding-top:1em;">
                <button class="btn btn-info" onclick="$(this).blur().parent().next().slideToggle(); return false;">show the complete code</button>
            </p>
            <pre style="margin-top:1em; display:none;">
        </pre>
        </div>*@

</div>
<script type="text/javascript">

    $(function() {
        $("#formloader").hide();
        $.jstree.defaults.core.themes.variant = "large";
        var to = false;
        $('#demo_q').keyup(function() {
            if (to) {
                clearTimeout(to);
            }
            to = setTimeout(function() {
                    var v = $('#demo_q').val();
                    $('#SimpleJSTree').jstree(true).search(v);
                },
                250);
        });
        var loadTree = function() {
            i('loadTree');
            AjaxCall('/GeneralLedger/AccountTree/GetAccountTree').done(function(response) {
                i('tree res ' + JSON.stringify(response));
                try {
                    createJSTree(response);
                } catch (e) {
                    ar(e);
                }
            }).fail(function(error) {
                i(error);
            });
        };

        loadTree();
        $("#refresh-tree").on('click',
            function() {
                $('#SimpleJSTree').empty().jstree('destroy');
                loadTree();
                //$('#SimpleJSTree').jstree(true).refresh();

                //   var node=  $('#SimpleJSTree').jstree(true).select_node('a1');
                // $('#SimpleJSTree').jstree('select_node', 'a1');
                //  $.jstree.reference('#jstree').select_node('a1');
            });

    });

    function createJSTree(jsondata) {
        try {
            i('createJSTree');
            $('#SimpleJSTree').jstree({
                "core": {
                    "animation": 0,
                    //      "check_callback" : true,
                    //  "themes" : { "stripes" : true },
                    'data': jsondata
                },
                //"types" : {
                //    "#" : {
                //        "max_children" : 1000,
                //        "max_depth" : 7,
                //        "valid_children" : ["root"]
                //    },
                //    "root" : {
                //        // "icon" : "/static/3.3.8/assets/images/tree_icon.png",
                //        "valid_children" : ["default"]
                //    },
                //    "default" : {
                //        "valid_children" : ["default","file"]
                //    },
                //    "file" : {
                //        //  "icon" : "glyphicon glyphicon-file",
                //        "valid_children" : []
                //    }
                //},
                "plugins": [
                    "dnd", "search", "types", "wholerow"
                ]
            });

            $('#SimpleJSTree').on("changed.jstree",
                function(e, data) {
                    console.log("changed.jstree " + data.selected);
                    console.log("data.selected.lenght " + data.selected.length);


                    if (data.selected.length === undefined)
                        return;

                    if (data.selected.length === 0)
                        return;

                    if (data.selected.length > 1)
                        return;
                    //var i, j, r = [];
                    //for(i = 0, j = data.selected.length; i < j; i++) {
                    //    r.push(data.instance.get_node(data.selected[i]).text);
                    //}
                    //$('#event_result').html('Selected: ' + r.join(', '));
                    //   var selected_node = 'The selected node is: ' + data.instance.get_node(data.selected[0]).text;

                    var num = data.selected.toString().replace("a", "");
                    console.log("GetAccount num is " + num);
                    var url = "/GeneralLedger/AccountTree/GetAccount/" + num;
                    $.get(url,
                        function(data, status) {
                            i("status =" + data);
                            $("#ID").val(data.ID);
                            $("#Name").val(data.Name);
                            $("#Number").val(data.Number);
                            $("#Description").val(data.Description);
                            //$("#ParentNumber").val(data.ParentNumber);
                            document.getElementById("ParentNumber").value = data.ParentNumber;
                            document.getElementById("Type").value = data.Type;
                        });
                });
        } catch (e) {
            i('error on createJSTree' + e);
        }
    }


    var formHelper = {
        onSuccess: function(data) {
            log('on Form CrudSuccess');
            hideFormLoading();
            showSuccess(data.Message);

            if (data.Message.includes('حذف')) {
                $('#crudform')[0].reset();
                demo_delete();
            } else if (data.Message.includes('تعديل')) {
                renameNode($("#Name").val());
            } else {
                $('#crudform')[0].reset();
            }


        },
        onBegin: function(context) {
            showFormLoading();
            return true;
        }
    }

    function OnFormBegin(context) {
        formHelper.onBegin(context);
    }

    function onCrudSuccess(data) {
        formHelper.onSuccess(data);
    }

    function onCrudFailure(xhr, textStatus, errorThrown) {
        hideFormLoading();
        i('textStatus ' + textStatus);
        i('errorThrown ' + errorThrown);
        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
    }


</script>
<script>
    function demo_create() {
        var ref = $('#SimpleJSTree').jstree(true),
            sel = ref.get_selected();
        if (!sel.length) {
            return false;
        }
        sel = sel[0];
        sel = ref.create_node(sel, { "type": "file" });
        if (sel) {
            ref.edit(sel);
        }
    };

    function demo_rename() {
        var ref = $('#SimpleJSTree').jstree(true),
            sel = ref.get_selected();
        if (!sel.length) {
            return false;
        }
        sel = sel[0];
        ref.edit(sel);
    };

    function renameNode(name) {
        var ref = $('#SimpleJSTree').jstree(true),
            sel = ref.get_selected();
        if (!sel.length) {
            return false;
        }
        //sel = sel[0];
        sel[0].text = name;
        ref.edit(sel);
    };

    function demo_delete() {
        var ref = $('#SimpleJSTree').jstree(true),
            sel = ref.get_selected();
        if (!sel.length) {
            return false;
        }
        ref.delete_node(sel);
    };
    //$(function () {


    //    $('#SimpleJSTree')
    //        .jstree({
    //            "core" : {
    //                "animation" : 0,
    //                "check_callback" : true,
    //                'force_text' : true,
    //                "themes" : { "stripes" : true },
    //                'data' : {
    //                    'url' : function (node) {
    //                        return node.id === '#' ? '/static/3.3.8/assets/ajax_demo_roots.json' : '/static/3.3.8/assets/ajax_demo_children.json';
    //                    },
    //                    'data' : function (node) {
    //                        return { 'id' : node.id };
    //                    }
    //                }
    //            },
    //            "types" : {
    //                "#" : { "max_children" : 1, "max_depth" : 4, "valid_children" : ["root"] },
    //                "root" : { "icon" : "/static/3.3.8/assets/images/tree_icon.png", "valid_children" : ["default"] },
    //                "default" : { "valid_children" : ["default","file"] },
    //                "file" : { "icon" : "glyphicon glyphicon-file", "valid_children" : [] }
    //            },
    //            "plugins" : [ "contextmenu", "dnd", "search", "state", "types", "wholerow" ]
    //        });
    //});
</script>

@*<script>
        var jsondata = [
            { "id": "a1", "parent": "#", "text": "الأصول" },
            { "id": "a2", "parent": "a1", "text": "Root node 2" },
            { "id": "ajson3", "parent": "a2", "text": "Child 1" },
            { "id": "ajson4", "parent": "a2", "text": "Child 2" },
        ];

        var j = [
            { "id": "a1", "parent": "#", "text": "الأصول" },
            { "id": "a2", "parent": "#", "text": "الخصوم" },
            { "id": "a3", "parent": "#", "text": "المصروفات" },
            { "id": "a4", "parent": "#", "text": "الإيرادات" },
            { "id": "a11", "parent": "a1", "text": "الأصول الثابتة" },
            { "id": "a12", "parent": "a1", "text": "الأصول المتداولة" },
            { "id": "a21", "parent": "a2", "text": "حقوق الملكية" },
            { "id": "a22", "parent": "a2", "text": "المخصصات" },
            { "id": "a23", "parent": "a2", "text": "الدائنون" },
            { "id": "a24", "parent": "a2", "text": "حسابات دائنة أخرى" }, { "id": "a25", "parent": "a2", "text": "حساب النتائج" }, { "id": "a31", "parent": "a3", "text": "مصروفات إدارية وعمومية" }, { "id": "a41", "parent": "a4", "text": "إيرادات النشاط" }, { "id": "a42", "parent": "a4", "text": "إيرادات أخرى" }, { "id": "a111", "parent": "a11", "text": "الاراضي" }, { "id": "a112", "parent": "a11", "text": "المباني" }, { "id": "a113", "parent": "a11", "text": "السيارات" }, { "id": "a114", "parent": "a11", "text": "الاثاث والتجهيزات" }, { "id": "a115", "parent": "a11", "text": "نفقات التأسيس" }, { "id": "a116", "parent": "a11", "text": "مشاريع تحت التنفيذ" }, { "id": "a121", "parent": "a12", "text": "الأموال الجاهزة" }, { "id": "a122", "parent": "a12", "text": "الوكلاء والفروع" }, { "id": "a123", "parent": "a12", "text": "المدينون" }, { "id": "a221", "parent": "a22", "text": "مخصص اهلاك الاصول الثابتة" }, { "id": "a222", "parent": "a22", "text": "مخصص الديون المشكوك في تحصيلها" }, { "id": "a231", "parent": "a23", "text": "دائنون افراد" }, { "id": "a232", "parent": "a23", "text": "دائنون شركات" }, { "id": "a233", "parent": "a23", "text": "ذمم دائنة مختلفة" }, { "id": "a234", "parent": "a23", "text": "المزودين" }, { "id": "a235", "parent": "a23", "text": "التجار" }, { "id": "a236", "parent": "a23", "text": "الصرافين" }, { "id": "a311", "parent": "a31", "text": "مصروفات إدارية" }, { "id": "a312", "parent": "a31", "text": "مصروفات عمومية" }, { "id": "a411", "parent": "a41", "text": "إيرادات الحوالات" }, { "id": "a412", "parent": "a41", "text": "إيرادات فوارق الصرف" }, { "id": "a413", "parent": "a41", "text": "إيرادات الخدمات" }, { "id": "a1211", "parent": "a121", "text": "الصناديق" }, { "id": "a1212", "parent": "a121", "text": "البنوك" }, { "id": "a1213", "parent": "a121", "text": "الشيكات" }, { "id": "a1221", "parent": "a122", "text": "الفروع" }, { "id": "a1222", "parent": "a122", "text": "الوكلاء" }, { "id": "a1223", "parent": "a122", "text": "الوكلاء الخارجين" }, { "id": "a1224", "parent": "a122", "text": "وكلاء المستعجل" }, { "id": "a1226", "parent": "a122", "text": "وكلاء آخرون" }, { "id": "a1227", "parent": "a122", "text": "الفروع الخارجين" }, { "id": "a1231", "parent": "a123", "text": "مدينون افراد" }, { "id": "a1232", "parent": "a123", "text": "مدينون شركات" }, { "id": "a1233", "parent": "a123", "text": "العمال والموظفون" }, { "id": "a1234", "parent": "a123", "text": "مسحوبات شخصية" }, { "id": "a1235", "parent": "a123", "text": "ديون مشكوك في تحصيلها" }, { "id": "a1236", "parent": "a123", "text": "مدينون آخرون" }, { "id": "a1237", "parent": "a123", "text": "العملاء" }, { "id": "a12331", "parent": "a1233", "text": "جاري العمال والموظفين" }, { "id": "a12332", "parent": "a1233", "text": "سلف العمال والموظفين" }, { "id": "a12333", "parent": "a1233", "text": "عهد العمال والموظفين" }, { "id": "a21001", "parent": "a21", "text": "رأس المال" }, { "id": "a24001", "parent": "a24", "text": "الحوالات" }, { "id": "a25001", "parent": "a25", "text": "حساب الأرباح والخسائر" }, { "id": "a221001", "parent": "a221", "text": "مخصص اهلاك المباني" }, { "id": "a221002", "parent": "a221", "text": "مخصص اهلاك السيارات" }, { "id": "a221003", "parent": "a221", "text": "مخصص اهلاك الاثاث والتجهيزات" }, { "id": "a234001", "parent": "a234", "text": "الحاشدي" }, { "id": "a234002", "parent": "a234", "text": "تداول" }, { "id": "a235001", "parent": "a235", "text": "محلاات عبدالله سليمان داوود العلوي" }, { "id": "a235002", "parent": "a235", "text": "محلات السعادة التجارية" }, { "id": "a236001", "parent": "a236", "text": "شبكة النجم" }, { "id": "a236002", "parent": "a236", "text": "شركة النجم " }, { "id": "a236003", "parent": "a236", "text": "شركة الامتياز" }, { "id": "a236004", "parent": "a236", "text": "شركة الكريمي" }, { "id": "a236005", "parent": "a236", "text": "شركة المريسي-المحيط" }, { "id": "a236006", "parent": "a236", "text": "العيدروس -يمن اكسبرس" }, { "id": "a311001", "parent": "a311", "text": "مرتبات وأجور" }, { "id": "a311002", "parent": "a311", "text": "البدلات" }, { "id": "a311003", "parent": "a311", "text": "العمل الاضافي" }, { "id": "a311004", "parent": "a311", "text": "التأمينات" }, { "id": "a311005", "parent": "a311", "text": "ضريبة كسب العمل" }, { "id": "a311006", "parent": "a311", "text": "نقل وانتقالات" }, { "id": "a311007", "parent": "a311", "text": "قطع غيار وصيانة ووقود وزيوت" }, { "id": "a311008", "parent": "a311", "text": "ضيافات واستقبالات" }, { "id": "a311009", "parent": "a311", "text": "تبرعات واعانات ومساعدات" }, { "id": "a311010", "parent": "a311", "text": "الديون المعدومة" }, { "id": "a311011", "parent": "a311", "text": "الخسائر الراسمالية" }, { "id": "a311012", "parent": "a311", "text": "مصروفات ادارية اخرى" }, { "id": "a312001", "parent": "a312", "text": "عمولات الوكلاء المدينة" }, { "id": "a312002", "parent": "a312", "text": "كهرباء ومياه" }, { "id": "a312003", "parent": "a312", "text": "قرطاسية ومطبوعات" }, { "id": "a312004", "parent": "a312", "text": "هاتف وفاكس وبريد" }, { "id": "a312005", "parent": "a312", "text": "الايجارات" }, { "id": "a312006", "parent": "a312", "text": "الضرائب والرسوم والزكوات" }, { "id": "a312007", "parent": "a312", "text": "الاهلاكات" }, { "id": "a312008", "parent": "a312", "text": "عمولات بنكية مدينة" }, { "id": "a312009", "parent": "a312", "text": "مصروفات عمومية اخرى" }, { "id": "a312010", "parent": "a312", "text": "دعاية واعلان" }, { "id": "a312011", "parent": "a312", "text": "عمولات بيع شيكات" }, { "id": "a411001", "parent": "a411", "text": "عمولات الوكلاء الدائنة" }, { "id": "a411002", "parent": "a411", "text": "خدمات التحويل" }, { "id": "a411003", "parent": "a411", "text": "عمولات شراء شيكات" }, { "id": "a412001", "parent": "a412", "text": "فوارق صرف عملة" }, { "id": "a412002", "parent": "a412", "text": "فوارق توريد الحوالات" }, { "id": "a412003", "parent": "a412", "text": "فوارق تصدير الحوالات" }, { "id": "a412004", "parent": "a412", "text": "فوارق صرف الحوالات" }, { "id": "a412005", "parent": "a412", "text": "فوارق قبض الحوالات" }, { "id": "a412006", "parent": "a412", "text": "فوارق شراء العملات" }, { "id": "a412007", "parent": "a412", "text": "فوارق بيع العملات" }, { "id": "a412008", "parent": "a412", "text": "فوارق الصرف النقدي" }, { "id": "a412009", "parent": "a412", "text": "فوارق القبض النقدي" }, { "id": "a412010", "parent": "a412", "text": "فوارق شراء شيكات" }, { "id": "a412011", "parent": "a412", "text": "فوارق بيع شيكات" }, { "id": "a412012", "parent": "a412", "text": "فوارق ترجيع شيكات" }, { "id": "a412013", "parent": "a412", "text": "فوارق تبديل عملة" }, { "id": "a412014", "parent": "a412", "text": "فوارق استحقاق الشيكات" }, { "id": "a412015", "parent": "a412", "text": "فوارق تحصيل الشيكات" }, { "id": "a413001", "parent": "a413", "text": "عمولات الخدمات" }, { "id": "a413002", "parent": "a413", "text": "عمولات السداد الدائنة" }, { "id": "a1211001", "parent": "a1211", "text": "صندوق 1" }, { "id": "a1212001", "parent": "a1212", "text": "كاك بنك" }, { "id": "a1213001", "parent": "a1213", "text": "شيكات تحت التحصيل" }, { "id": "a1213002", "parent": "a1213", "text": "شيكات مؤجلة الدفع" }, { "id": "a1213003", "parent": "a1213", "text": "الشيكات الصادرة" }, { "id": "a1213004", "parent": "a1213", "text": "الشيكات الواردة" }, { "id": "a1221001", "parent": "a1221", "text": "المركز الرئيسي" }, { "id": "a1222001", "parent": "a1222", "text": "محمد علي علي عبدالباقي الرقيمي" }, { "id": "a1222002", "parent": "a1222", "text": "عبدالسلام محمد علي الرقيمي" }, { "id": "a1222003", "parent": "a1222", "text": "خليفه علي علي عبد الباقي الرقيمي" }, { "id": "a1222004", "parent": "a1222", "text": "بدر نصر راشد العلوي" }, { "id": "a1222005", "parent": "a1222", "text": "شهيد احمد احمد الرقيمي" }, { "id": "a1222006", "parent": "a1222", "text": "سام يوسف" }, { "id": "a1226001", "parent": "a1226", "text": "التزامات الوكلاء" }, { "id": "a1226002", "parent": "a1226", "text": "حقوق الوكلاء" }, { "id": "a1226003", "parent": "a1226", "text": "الحوالات المستعجلة" }, { "id": "a1227001", "parent": "a1227", "text": "الرقيمي تيليكوم " }, { "id": "a1227002", "parent": "a1227", "text": "الرقيمي موبايل" }, { "id": "a1227003", "parent": "a1227", "text": "بن عاطف" }, { "id": "a1236001", "parent": "a1236", "text": "الطلبات" }, { "id": "a1236002", "parent": "a1236", "text": "القروض المالية" }, { "id": "a1237001", "parent": "a1237", "text": "عبدالقادر محمد علي علي عبدالباقي الرقيمي" }, { "id": "a1237003", "parent": "a1237", "text": "عدنان الرقيمي" }, { "id": "a1237005", "parent": "a1237", "text": "عبدالودودخالدعبدالرقيب العريقي" }, { "id": "a1237006", "parent": "a1237", "text": "سامي عنان" }, { "id": "a3120012", "parent": "a312", "text": "عمولات السداد المدينة" }

        ];

    </script>*@