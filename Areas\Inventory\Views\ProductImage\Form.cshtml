﻿@model AppTech.MSMS.Domain.Models.ProductImage

@{
    Layout = "~/Views/Shared/_MultiForm.cshtml"; 
}
    <div>
        <div class="form-group">
            @Html.LabelFor(model => model.ProductID, new { @class = "control-label col-md-2" })
            <div class="col-md-3">
                @Html.DropDownListFor(model => model.ProductID, (SelectList)ViewBag.Products, new { htmlAttributes = new { @class = "form-control" } })
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
            <div style="position: relative;">
                <input type="file" name="ImageData" size="40" onchange="showImg(this)">
            </div>
            <img class="img-thumbnail" width="150" height="150" id="preview"
                 src="@Url.Action("GetImage", "ProductImage",new {Model.ID})" />
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Title, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Title, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Title)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Description)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Extainfo, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Extainfo, new { htmlAttributes = new { @class = "form-control" } })

                @Html.ValidationMessageFor(model => model.Extainfo)
            </div>
        </div>
    </div>


<script>

    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });
</script>