# Simple script to restore nawafd database

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Restore nawafd Database" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Database file paths
$originalPath = "E:\MSSQLDATA"
$mdfFile = "$originalPath\nawafd.mdf"
$ldfFile = "$originalPath\nawafd_0.ldf"
$databaseName = "nawafd"

Write-Host "`n1. Checking database files..." -ForegroundColor Yellow

if (Test-Path $mdfFile) {
    $mdfSize = (Get-Item $mdfFile).Length / 1MB
    Write-Host "Found data file: $mdfFile (Size: $([math]::Round($mdfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "Data file not found: $mdfFile" -ForegroundColor Red
    Write-Host "Please copy the file from old server first" -ForegroundColor Yellow
    exit 1
}

if (Test-Path $ldfFile) {
    $ldfSize = (Get-Item $ldfFile).Length / 1MB
    Write-Host "Found log file: $ldfFile (Size: $([math]::Round($ldfSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "Log file not found: $ldfFile" -ForegroundColor Yellow
    Write-Host "Will create new log file" -ForegroundColor Gray
}

Write-Host "`n2. Searching for SQL Server..." -ForegroundColor Yellow

$sqlInstances = @(
    "localhost",
    ".\SQLEXPRESS", 
    "localhost\SQLEXPRESS",
    ".\MSSQLSERVER17",
    "localhost\MSSQLSERVER17",
    "(localdb)\MSSQLLocalDB"
)

$workingInstance = $null
foreach ($instance in $sqlInstances) {
    try {
        Write-Host "Testing: $instance" -ForegroundColor Gray
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION" -ErrorAction Stop -Timeout 5
        if ($result) {
            $workingInstance = $instance
            Write-Host "Found SQL Server: $instance" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "Failed to connect: $instance" -ForegroundColor Red
    }
}

if (-not $workingInstance) {
    Write-Host "No SQL Server found" -ForegroundColor Red
    exit 1
}

Write-Host "`n3. Checking existing database..." -ForegroundColor Yellow

try {
    $existingDb = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = '$databaseName'" -ErrorAction SilentlyContinue
    
    if ($existingDb) {
        Write-Host "Database already exists: $databaseName" -ForegroundColor Yellow
        $overwrite = Read-Host "Do you want to replace it? (y/n)"
        if ($overwrite -eq "y") {
            Write-Host "Detaching existing database..." -ForegroundColor Gray
            Invoke-Sqlcmd -ServerInstance $workingInstance -Query "ALTER DATABASE [$databaseName] SET SINGLE_USER WITH ROLLBACK IMMEDIATE" -ErrorAction SilentlyContinue
            Invoke-Sqlcmd -ServerInstance $workingInstance -Query "DROP DATABASE [$databaseName]" -ErrorAction SilentlyContinue
        } else {
            Write-Host "Operation cancelled" -ForegroundColor Yellow
            exit 0
        }
    }
} catch {
    Write-Host "Error checking database: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. Attaching database..." -ForegroundColor Yellow

try {
    if (Test-Path $ldfFile) {
        $attachQuery = @"
CREATE DATABASE [$databaseName] ON 
(FILENAME = '$mdfFile'),
(FILENAME = '$ldfFile')
FOR ATTACH
"@
    } else {
        $attachQuery = @"
CREATE DATABASE [$databaseName] ON 
(FILENAME = '$mdfFile')
FOR ATTACH_REBUILD_LOG
"@
    }
    
    Write-Host "Executing attach query..." -ForegroundColor Gray
    Invoke-Sqlcmd -ServerInstance $workingInstance -Query $attachQuery -ErrorAction Stop -Timeout 60
    
    Write-Host "Database attached successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "Error attaching database: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n5. Testing database..." -ForegroundColor Yellow

try {
    $testQuery = "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
    $result = Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $testQuery -ErrorAction Stop
    
    Write-Host "Database is working!" -ForegroundColor Green
    Write-Host "Table count: $($result.TableCount)" -ForegroundColor Gray
    
    # Show some tables
    $tablesQuery = "SELECT TOP 10 TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME"
    $tables = Invoke-Sqlcmd -ServerInstance $workingInstance -Database $databaseName -Query $tablesQuery -ErrorAction SilentlyContinue
    
    if ($tables) {
        Write-Host "`nSome tables found:" -ForegroundColor Gray
        foreach ($table in $tables) {
            Write-Host "  - $($table.TABLE_NAME)" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "Error testing database: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n6. Creating connection strings..." -ForegroundColor Yellow

$connectionString = "Data Source=$workingInstance;Initial Catalog=$databaseName;Integrated Security=True;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"
$entityConnectionString = "metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=`"$connectionString`""

Write-Host "Connection string:" -ForegroundColor Cyan
Write-Host $connectionString -ForegroundColor White

# Save connection info
$connectionInfo = @"
# nawafd Database Connection Info
Restore Date: $(Get-Date)
SQL Server Instance: $workingInstance
Database Name: $databaseName
MDF File: $mdfFile
LDF File: $ldfFile

Connection String:
$connectionString

Entity Framework Connection String:
$entityConnectionString
"@

$connectionInfo | Out-File -FilePath "nawafd-connection-info.txt" -Encoding UTF8
Write-Host "`nConnection info saved: nawafd-connection-info.txt" -ForegroundColor Green

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "nawafd database restore completed!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Update web.config files with new connection strings" -ForegroundColor Cyan
Write-Host "2. Test system functionality" -ForegroundColor Cyan
Write-Host "3. Import CSV data if needed" -ForegroundColor Cyan

pause
