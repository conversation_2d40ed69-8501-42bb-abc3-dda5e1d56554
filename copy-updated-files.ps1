# سكريبت نسخ الملفات المحدثة من New folder

Write-Host @"
========================================
    نسخ الملفات المحدثة - AppTech MSMS
========================================
"@ -ForegroundColor Cyan

$sourcePath = "E:\inetpub\New folder"
$targetPath = "E:\inetpub"

Write-Host "`n1. نسخ TopupProcessor المحدث..." -ForegroundColor Yellow

$topupSource = "$sourcePath\TopupProcessor v1.2 - 4.2"
$topupTarget = "$targetPath\TopupProcessor"

if (Test-Path $topupSource) {
    # إنشاء نسخة احتياطية من النسخة القديمة
    if (Test-Path $topupTarget) {
        $backupPath = "$targetPath\TopupProcessor_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Host "إنشاء نسخة احتياطية: $backupPath" -ForegroundColor Gray
        Copy-Item $topupTarget $backupPath -Recurse -Force
    }
    
    # نسخ النسخة الجديدة
    Write-Host "نسخ TopupProcessor المحدث..." -ForegroundColor Gray
    Copy-Item $topupSource $topupTarget -Recurse -Force
    Write-Host "✓ تم نسخ TopupProcessor المحدث" -ForegroundColor Green
} else {
    Write-Host "✗ مجلد TopupProcessor غير موجود في المصدر" -ForegroundColor Red
}

Write-Host "`n2. نسخ ملفات CSV (البيانات)..." -ForegroundColor Yellow

$csvFiles = @(
    "Account.csv",
    "Agent.csv", 
    "Agent2023.csv",
    "Branch.csv",
    "UserInfo.csv",
    "UserRole.csv",
    "AccountUser.csv",
    "AccountParent.csv"
)

$dataPath = "$targetPath\Data"
if (-not (Test-Path $dataPath)) {
    New-Item -ItemType Directory -Path $dataPath -Force | Out-Null
}

foreach ($csvFile in $csvFiles) {
    $sourceFile = "$sourcePath\$csvFile"
    $targetFile = "$dataPath\$csvFile"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        $fileSize = (Get-Item $sourceFile).Length / 1KB
        Write-Host "✓ نُسخ: $csvFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $csvFile" -ForegroundColor Yellow
    }
}

Write-Host "`n3. نسخ ملفات SQL..." -ForegroundColor Yellow

$sqlFiles = @(
    "1111111.sql",
    "back.sql",
    "topup.sql",
    "dv.sql"
)

$sqlPath = "$targetPath\SQL"
if (-not (Test-Path $sqlPath)) {
    New-Item -ItemType Directory -Path $sqlPath -Force | Out-Null
}

foreach ($sqlFile in $sqlFiles) {
    $sourceFile = "$sourcePath\$sqlFile"
    $targetFile = "$sqlPath\$sqlFile"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        $fileSize = (Get-Item $sourceFile).Length / 1KB
        Write-Host "✓ نُسخ: $sqlFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $sqlFile" -ForegroundColor Yellow
    }
}

Write-Host "`n4. نسخ ملفات التوثيق..." -ForegroundColor Yellow

$docFiles = @(
    "deployment-commands.md",
    "nawafd missing indexed.xlsx"
)

$docsPath = "$targetPath\Documentation"
if (-not (Test-Path $docsPath)) {
    New-Item -ItemType Directory -Path $docsPath -Force | Out-Null
}

foreach ($docFile in $docFiles) {
    $sourceFile = "$sourcePath\$docFile"
    $targetFile = "$docsPath\$docFile"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "✓ نُسخ: $docFile" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $docFile" -ForegroundColor Yellow
    }
}

Write-Host "`n5. استخراج الأرشيف..." -ForegroundColor Yellow

$archiveFiles = @(
    "msms2021.rar",
    "portal.rar"
)

foreach ($archiveFile in $archiveFiles) {
    $sourceFile = "$sourcePath\$archiveFile"
    
    if (Test-Path $sourceFile) {
        $extractPath = "$targetPath\Extracted\$($archiveFile.Replace('.rar', ''))"
        
        if (-not (Test-Path $extractPath)) {
            New-Item -ItemType Directory -Path $extractPath -Force | Out-Null
        }
        
        Write-Host "استخراج: $archiveFile" -ForegroundColor Gray
        
        # محاولة استخراج باستخدام 7-Zip إذا كان متوفراً
        $sevenZip = "${env:ProgramFiles}\7-Zip\7z.exe"
        if (Test-Path $sevenZip) {
            & $sevenZip x $sourceFile -o$extractPath -y
            Write-Host "✓ تم استخراج: $archiveFile" -ForegroundColor Green
        } else {
            Write-Host "⚠ 7-Zip غير متوفر لاستخراج: $archiveFile" -ForegroundColor Yellow
            Write-Host "يرجى استخراج الملف يدوياً إلى: $extractPath" -ForegroundColor Gray
        }
    } else {
        Write-Host "⚠ غير موجود: $archiveFile" -ForegroundColor Yellow
    }
}

Write-Host "`n6. إنشاء تقرير الملفات المنسوخة..." -ForegroundColor Yellow

$report = @"
# تقرير نسخ الملفات المحدثة
تاريخ النسخ: $(Get-Date)

## الملفات المنسوخة:

### TopupProcessor المحدث:
- المسار: $targetPath\TopupProcessor
- النسخة: v1.2 - 4.2
- الملفات: AppTech.*.dll, TopupInspector.exe, license.lic

### ملفات البيانات (CSV):
- المسار: $targetPath\Data
- الملفات: Account.csv, Agent.csv, Branch.csv, UserInfo.csv, إلخ

### ملفات SQL:
- المسار: $targetPath\SQL  
- الملفات: 1111111.sql, back.sql, topup.sql, dv.sql

### التوثيق:
- المسار: $targetPath\Documentation
- الملفات: deployment-commands.md, nawafd missing indexed.xlsx

### الأرشيف المستخرج:
- المسار: $targetPath\Extracted
- الملفات: msms2021/, portal/

## الخطوات التالية:
1. فحص TopupProcessor المحدث وتحديث الإعدادات
2. مراجعة ملفات البيانات CSV لاستيراد البيانات المفقودة
3. فحص ملفات SQL للهياكل الإضافية
4. مراجعة دليل النشر في deployment-commands.md
5. فحص محتوى الأرشيف المستخرج
"@

$report | Out-File -FilePath "$targetPath\copy-report.txt" -Encoding UTF8

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "انتهت عملية نسخ الملفات المحدثة!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nتم حفظ تقرير العملية في: $targetPath\copy-report.txt" -ForegroundColor Cyan

Write-Host "`nالخطوات التالية:" -ForegroundColor Yellow
Write-Host "1. فحص TopupProcessor المحدث" -ForegroundColor White
Write-Host "2. مراجعة ملفات البيانات CSV" -ForegroundColor White  
Write-Host "3. فحص ملفات SQL الإضافية" -ForegroundColor White
Write-Host "4. مراجعة دليل النشر" -ForegroundColor White

pause
