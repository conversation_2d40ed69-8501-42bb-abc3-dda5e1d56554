# سكريبت نسخ الملفات المحدثة من New folder

Write-Host @"
========================================
    نسخ الملفات المحدثة - AppTech MSMS
========================================
"@ -ForegroundColor Cyan

$sourcePath = "E:\inetpub\New folder"
$targetPath = "E:\inetpub"

Write-Host "`n1. نسخ TopupProcessor المحدث..." -ForegroundColor Yellow

$topupSource = "$sourcePath\TopupProcessor v1.2 - 4.2"
$topupTarget = "$targetPath\TopupProcessor"

if (Test-Path $topupSource) {
    # إنشاء نسخة احتياطية من النسخة القديمة
    if (Test-Path $topupTarget) {
        $backupPath = "$targetPath\TopupProcessor_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Host "إنشاء نسخة احتياطية: $backupPath" -ForegroundColor Gray
        Copy-Item $topupTarget $backupPath -Recurse -Force
    }
    
    # نسخ النسخة الجديدة
    Write-Host "نسخ TopupProcessor المحدث..." -ForegroundColor Gray
    Copy-Item $topupSource $topupTarget -Recurse -Force
    Write-Host "✓ تم نسخ TopupProcessor المحدث" -ForegroundColor Green
} else {
    Write-Host "✗ مجلد TopupProcessor غير موجود في المصدر" -ForegroundColor Red
}

Write-Host "`n2. نسخ ملفات CSV (البيانات)..." -ForegroundColor Yellow

$csvFiles = @(
    "Account.csv",
    "Agent.csv", 
    "Agent2023.csv",
    "Branch.csv",
    "UserInfo.csv",
    "UserRole.csv",
    "AccountUser.csv",
    "AccountParent.csv"
)

$dataPath = "$targetPath\Data"
if (-not (Test-Path $dataPath)) {
    New-Item -ItemType Directory -Path $dataPath -Force | Out-Null
}

foreach ($csvFile in $csvFiles) {
    $sourceFile = "$sourcePath\$csvFile"
    $targetFile = "$dataPath\$csvFile"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        $fileSize = (Get-Item $sourceFile).Length / 1KB
        Write-Host "✓ نُسخ: $csvFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $csvFile" -ForegroundColor Yellow
    }
}

Write-Host "`n3. نسخ ملفات SQL..." -ForegroundColor Yellow

$sqlFiles = @(
    "1111111.sql",
    "back.sql",
    "topup.sql",
    "dv.sql"
)

$sqlPath = "$targetPath\SQL"
if (-not (Test-Path $sqlPath)) {
    New-Item -ItemType Directory -Path $sqlPath -Force | Out-Null
}

foreach ($sqlFile in $sqlFiles) {
    $sourceFile = "$sourcePath\$sqlFile"
    $targetFile = "$sqlPath\$sqlFile"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        $fileSize = (Get-Item $sourceFile).Length / 1KB
        Write-Host "✓ نُسخ: $sqlFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $sqlFile" -ForegroundColor Yellow
    }
}

Write-Host "`n4. نسخ ملفات التوثيق..." -ForegroundColor Yellow

$docFiles = @(
    "deployment-commands.md",
    "nawafd missing indexed.xlsx"
)

$docsPath = "$targetPath\Documentation"
if (-not (Test-Path $docsPath)) {
    New-Item -ItemType Directory -Path $docsPath -Force | Out-Null
}

foreach ($docFile in $docFiles) {
    $sourceFile = "$sourcePath\$docFile"
    $targetFile = "$docsPath\$docFile"
    
    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        Write-Host "✓ نُسخ: $docFile" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $docFile" -ForegroundColor Yellow
    }
}

Write-Host "`n5. نسخ ملفات OLAP و msmdsrv..." -ForegroundColor Yellow

$olapSource = "$sourcePath\OLAP"
$olapTarget = "$targetPath\OLAP"

if (Test-Path $olapSource) {
    Write-Host "نسخ مجلد OLAP كاملاً..." -ForegroundColor Gray
    Copy-Item $olapSource $olapTarget -Recurse -Force
    Write-Host "✓ تم نسخ مجلد OLAP" -ForegroundColor Green

    # فحص ملفات msmdsrv المهمة
    $msmdsrvIni = "$olapTarget\Config\msmdsrv.ini"
    $msmdsrvBak = "$olapTarget\Config\msmdsrv.bak"

    if (Test-Path $msmdsrvIni) {
        Write-Host "✓ وُجد ملف إعدادات: msmdsrv.ini" -ForegroundColor Green
    }

    if (Test-Path $msmdsrvBak) {
        Write-Host "✓ وُجد ملف النسخة الاحتياطية: msmdsrv.bak" -ForegroundColor Green
    }

    # فحص ملفات البيانات
    $olapDataFiles = Get-ChildItem "$olapTarget\Data" -File | Measure-Object
    Write-Host "✓ ملفات البيانات: $($olapDataFiles.Count) ملف" -ForegroundColor Green

} else {
    Write-Host "⚠ مجلد OLAP غير موجود" -ForegroundColor Yellow
}

Write-Host "`n6. نسخ ملفات CSV إضافية..." -ForegroundColor Yellow

$additionalCsvFiles = @(
    "acccount2021.csv",
    "acountusers.csv",
    "agg.csv",
    "balanc.csv",
    "balances 2021.csv",
    "cli.csv",
    "custm.csv",
    "groupitem.csv",
    "partiesACOUNTS.csv",
    "sim.csv",
    "siminvoice.csv"
)

foreach ($csvFile in $additionalCsvFiles) {
    $sourceFile = "$sourcePath\$csvFile"
    $targetFile = "$dataPath\$csvFile"

    if (Test-Path $sourceFile) {
        Copy-Item $sourceFile $targetFile -Force
        $fileSize = (Get-Item $sourceFile).Length / 1KB
        Write-Host "✓ نُسخ: $csvFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
    } else {
        Write-Host "⚠ غير موجود: $csvFile" -ForegroundColor Yellow
    }
}

Write-Host "`n7. استخراج الأرشيف..." -ForegroundColor Yellow

$archiveFiles = @(
    "msms2021.rar",
    "portal.rar"
)

foreach ($archiveFile in $archiveFiles) {
    $sourceFile = "$sourcePath\$archiveFile"

    if (Test-Path $sourceFile) {
        $extractPath = "$targetPath\Extracted\$($archiveFile.Replace('.rar', ''))"

        if (-not (Test-Path $extractPath)) {
            New-Item -ItemType Directory -Path $extractPath -Force | Out-Null
        }

        Write-Host "استخراج: $archiveFile" -ForegroundColor Gray

        # محاولة استخراج باستخدام 7-Zip إذا كان متوفراً
        $sevenZip = "${env:ProgramFiles}\7-Zip\7z.exe"
        if (Test-Path $sevenZip) {
            & $sevenZip x $sourceFile -o$extractPath -y
            Write-Host "✓ تم استخراج: $archiveFile" -ForegroundColor Green
        } else {
            Write-Host "⚠ 7-Zip غير متوفر لاستخراج: $archiveFile" -ForegroundColor Yellow
            Write-Host "يرجى استخراج الملف يدوياً إلى: $extractPath" -ForegroundColor Gray
        }
    } else {
        Write-Host "⚠ غير موجود: $archiveFile" -ForegroundColor Yellow
    }
}

Write-Host "`n8. إنشاء تقرير الملفات المنسوخة..." -ForegroundColor Yellow

$report = @"
# تقرير نسخ الملفات المحدثة - AppTech MSMS
تاريخ النسخ: $(Get-Date)

## الملفات المنسوخة:

### 1. TopupProcessor المحدث:
- المسار: $targetPath\TopupProcessor
- النسخة: v1.2 - 4.2
- الملفات: AppTech.*.dll, TopupInspector.exe, license.lic, maincs.erp
- الحالة: جاهز للاستخدام

### 2. ملفات البيانات (CSV):
- المسار: $targetPath\Data
- الملفات الأساسية: Account.csv (13,439 سجل), Agent.csv, Branch.csv, UserInfo.csv
- ملفات إضافية: balances 2021.csv, sim.csv, siminvoice.csv, partiesACOUNTS.csv
- المجموع: 15+ ملف بيانات

### 3. ملفات SQL:
- المسار: $targetPath\SQL
- الملفات: 1111111.sql (PostgreSQL), back.sql, topup.sql, dv.sql
- النوع: قواعد بيانات متعددة الأنواع

### 4. OLAP و msmdsrv:
- المسار: $targetPath\OLAP
- الإعدادات: msmdsrv.ini, msmdsrv.bak
- البيانات: مجلد Data مع ملفات .asm و .xml
- النسخ الاحتياطية: مجلد Backup
- الأهمية: إعدادات SQL Server Analysis Services

### 5. التوثيق:
- المسار: $targetPath\Documentation
- الملفات: deployment-commands.md (دليل نشر شامل), nawafd missing indexed.xlsx
- الفائدة: إرشادات التثبيت والتشغيل

### 6. الأرشيف المستخرج:
- المسار: $targetPath\Extracted
- الملفات: msms2021/, portal/
- المحتوى: نسخ كاملة من النظام

## الخطوات التالية:
1. ✅ فحص TopupProcessor المحدث وتحديث الإعدادات
2. ✅ مراجعة ملفات البيانات CSV لاستيراد البيانات المفقودة
3. ✅ فحص ملفات SQL للهياكل الإضافية
4. ✅ استعادة إعدادات OLAP من msmdsrv.ini
5. ✅ مراجعة دليل النشر في deployment-commands.md
6. ✅ فحص محتوى الأرشيف المستخرج
7. 🔄 تحديث سلاسل الاتصال في ملفات التكوين
8. 🔄 اختبار النظام مع الملفات المحدثة

## ملاحظات مهمة:
- تم العثور على نسخة احتياطية من إعدادات msmdsrv
- ملفات CSV تحتوي على بيانات حقيقية (13K+ حساب)
- دليل النشر يحتوي على تعليمات مفصلة لنظام Yemen Client
- TopupProcessor محدث بنسخة أحدث من الموجود
"@

$report | Out-File -FilePath "$targetPath\copy-report.txt" -Encoding UTF8

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "انتهت عملية نسخ الملفات المحدثة!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nتم حفظ تقرير العملية في: $targetPath\copy-report.txt" -ForegroundColor Cyan

Write-Host "`nالخطوات التالية:" -ForegroundColor Yellow
Write-Host "1. ✅ فحص TopupProcessor المحدث (v1.2 - 4.2)" -ForegroundColor White
Write-Host "2. ✅ مراجعة ملفات البيانات CSV (15+ ملف)" -ForegroundColor White
Write-Host "3. ✅ فحص ملفات SQL الإضافية (PostgreSQL + SQL Server)" -ForegroundColor White
Write-Host "4. ✅ استعادة إعدادات OLAP من msmdsrv.ini/.bak" -ForegroundColor White
Write-Host "5. ✅ مراجعة دليل النشر الشامل" -ForegroundColor White
Write-Host "6. 🔄 تحديث سلاسل الاتصال" -ForegroundColor Cyan
Write-Host "7. 🔄 اختبار النظام مع الملفات الجديدة" -ForegroundColor Cyan

Write-Host "`n🎯 أهم الاكتشافات:" -ForegroundColor Green
Write-Host "• TopupProcessor محدث بنسخة أحدث" -ForegroundColor Gray
Write-Host "• 13,439+ سجل حساب في Account.csv" -ForegroundColor Gray
Write-Host "• نسخة احتياطية من إعدادات msmdsrv" -ForegroundColor Gray
Write-Host "• دليل نشر شامل لنظام Yemen Client" -ForegroundColor Gray
Write-Host "• أرشيف كامل من النظام (msms2021, portal)" -ForegroundColor Gray

pause
