﻿@model AppTech.MSMS.Domain.Models.Card

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<input type="hidden" name="ID" value="@Model.ID" />
<input type="hidden" name="CardFactionID" value="@Model.CardFactionID" />
<input type="hidden" name="CardTypeID" value="@Model.CardTypeID" />

@*@if (Model.ID == 0)
{*@
    <div>
        <div class="form-group">
            @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Name)
                @Html.ValidationMessageFor(model => model.Name)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Password, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Password)
                @Html.ValidationMessageFor(model => model.Password)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Note)
                @Html.ValidationMessageFor(model => model.Note)
            </div>
        </div>
    </div>
@*}
else
{
    <p> else</p>
}*@



<script>
    hideLoading();
</script>
<script>

    $(function () {
        try {
            formHelper.onSuccess = function (data) {
                log('user party form');
                i('data> ' + data);
                history.go(0);

                $("#modal").modal('hide');
                $("#list").html(data);
            }
            formHelper.onBegin = function (context) {
                return true;
            };
        } catch (e) {
        }
    });
    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });

</script>