﻿@using AppTech.MSMS.Domain
@model AppTech.MSMS.Web.Models.HomeModel

    <div class="row">
        <div class="space-6"></div>


        <div class="col-sm-5">
            <div class="widget-box transparent">
                <div class="widget-header widget-header-flat">
                    <h4 class="widget-title lighter">
                        <i class="ace-icon fa fa-star orange"></i>
                        أرصدة مقاربة الأنتهاء
                    </h4>

                    <div class="widget-toolbar">
                        <a href="" data-action="collapse" target="_self">
                            <i class="ace-icon fa fa-chevron-up"></i>
                        </a>
                    </div>
                </div>

                <div class="widget-body">
                    <div class="widget-main no-padding">
                        <table class="table table-bordered table-striped">
                            <thead class="thin-border-bottom">
                                <tr>
                                    <th>
                                        <i class="ace-icon fa fa-caret-right blue"></i>اسم الحساب
                                    </th>

                                    <th>
                                        <i class="ace-icon fa fa-caret-right blue"></i>الرصيد
                                    </th>

                                    @*<th class="hidden-480">
                                        <i class="ace-icon fa fa-caret-right blue"></i>الحالة
                                    </th>*@

                                    <th>
                                        <i class="ace-icon fa fa-caret-right blue"></i>
                                    </th>

                                </tr>
                            </thead>

                            <tbody>
                            @{
                                foreach (var client in Model.ClientBalances)
                                {
                                <tr>
                                    <td>@client.Name</td>



                                        @if (client.Balance > 0)
                                        {
                                            <td class="">
                                                <span class="label label-success arrowed-in arrowed-in-right">@client.Balance</span>
                                            </td>
                                        }

                                        else
                                        {
                                            <td class="">
                                                <span class="label label-danger arrowed">@client.Balance</span>
                                            </td>
                                        }
                                    <td class="">
                                        @*<button class="btn btn-white btn-info btn-bold btn-round loading" id="refresh" data-loading-text="<i class='fa fa-spinner fa-spin '></i> جاري تحديث">
                                            <i class="ace-icon fa fa-refresh bigger-110"></i>
                                            الرصيد
                                        </button>*@

                                    </td>
               

                                </tr>
                                }
                            }

                            </tbody>
                        </table>
                    </div><!-- /.widget-main -->
                </div><!-- /.widget-body -->
            </div><!-- /.widget-box -->
        </div>

        <div class="col-sm-5">
            <div id="piechart" style="width: 600px; height: 400px;"></div>
        </div>
    </div>

    <div class="hr hr32 hr-dotted"></div>
    @*<div class="row">

        <div class="col-sm-5">
            <div class="widget-box">
                <div class="widget-header widget-header-flat widget-header-small">
                    <h5 class="widget-title">
                        <i class="ace-icon fa fa-signal"></i>
                        أسعار العملات
                    </h5>

                    <div class="widget-toolbar no-border">
                    </div>
                </div>

                <div class="widget-body">
                    <div class="widget-main">

                        <div class="clearfix">
                            <div class="grid3">
                                <span class="grey">
                                    &nbsp; الدولار
                                </span>
                                <h4 class="bigger pull-right">الشراء : @Model.CurrencyPrices.BuyUSD</h4>
                                <br/>
                                <h4 class="bigger pull-right">البيع : @Model.CurrencyPrices.SaleUSD</h4>
                            </div>

                            <div class="grid3">
                                <span class="grey">
                                    &nbsp; السعودي
                                </span>
                                <h4 class="bigger pull-right">@Model.CurrencyPrices.BuySR</h4>
                                <br/>
                                <h4 class="bigger pull-right">البيع : @Model.CurrencyPrices.SaleSR</h4>

                            </div>

                            @*     <div class="grid3">
                                <span class="grey">
                                    &nbsp; pins
                                </span>
                                <h4 class="bigger pull-right">1,050</h4>
                            </div
                        </div>
                    </div><!-- /.widget-main -->
                </div><!-- /.widget-body -->
            </div>
        </div>
        <div class="space-6"></div>

      <div class="col-sm-7 infobox-container">
        <div class="infobox infobox-green">
            <div class="infobox-icon">
                <i class="ace-icon fa fa-comments"></i>
            </div>

            <div class="infobox-data">
                <span class="infobox-data-number">32</span>
                <div class="infobox-content">comments + 2 reviews</div>
            </div>

            <div class="stat stat-success">8%</div>
        </div>

        <div class="infobox infobox-blue">
            <div class="infobox-icon">
                <i class="ace-icon fa fa-twitter"></i>
            </div>

            <div class="infobox-data">
                <span class="infobox-data-number">11</span>
                <div class="infobox-content">new followers</div>
            </div>

            <div class="badge badge-success">
                +32%
                <i class="ace-icon fa fa-arrow-up"></i>
            </div>
        </div>

        <div class="infobox infobox-pink">
            <div class="infobox-icon">
                <i class="ace-icon fa fa-shopping-cart"></i>
            </div>

            <div class="infobox-data">
                <span class="infobox-data-number">8</span>
                <div class="infobox-content">new orders</div>
            </div>
            <div class="stat stat-important">4%</div>
        </div>

        <div class="infobox infobox-red">
            <div class="infobox-icon">
                <i class="ace-icon fa fa-flask"></i>
            </div>

            <div class="infobox-data">
                <span class="infobox-data-number">7</span>
                <div class="infobox-content">experiments</div>
            </div>
        </div>

        <div class="infobox infobox-orange2">
            <div class="infobox-chart">
                <span class="sparkline" data-values="196,128,202,177,154,94,100,170,224"></span>
            </div>

            <div class="infobox-data">
                <span class="infobox-data-number">6,251</span>
                <div class="infobox-content">pageviews</div>
            </div>

            <div class="badge badge-success">
                7.2%
                <i class="ace-icon fa fa-arrow-up"></i>
            </div>
        </div>

        <div class="infobox infobox-blue2">
            <div class="infobox-progress">
                <div class="easy-pie-chart percentage" data-percent="42" data-size="46">
                    <span class="percent">42</span>%
                </div>
            </div>

            <div class="infobox-data">
                <span class="infobox-text">traffic used</span>

                <div class="infobox-content">
                    <span class="bigger-110">~</span>
                    58GB remaining
                </div>
            </div>
        </div>

        <div class="space-6"></div>

        <div class="infobox infobox-green infobox-small infobox-dark">
            <div class="infobox-progress">
                <div class="easy-pie-chart percentage" data-percent="61" data-size="39">
                    <span class="percent">61</span>%
                </div>
            </div>

            <div class="infobox-data">
                <div class="infobox-content">Task</div>
                <div class="infobox-content">Completion</div>
            </div>
        </div>

        <div class="infobox infobox-blue infobox-small infobox-dark">
            <div class="infobox-chart">
                <span class="sparkline" data-values="3,4,2,3,4,4,2,2"></span>
            </div>

            <div class="infobox-data">
                <div class="infobox-content">Earnings</div>
                <div class="infobox-content">$32,000</div>
            </div>
        </div>

        <div class="infobox infobox-grey infobox-small infobox-dark">
            <div class="infobox-icon">
                <i class="ace-icon fa fa-download"></i>
            </div>

            <div class="infobox-data">
                <div class="infobox-content">Downloads</div>
                <div class="infobox-content">1,205</div>
            </div>
        </div>
    </div>

        <!-- /.col -->
    </div>*@
