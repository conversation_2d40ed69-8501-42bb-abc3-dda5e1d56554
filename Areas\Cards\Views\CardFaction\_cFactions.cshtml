﻿@model IEnumerable<AppTech.MSMS.Domain.Models.CardFaction>
<table class="table table-hover" id="tableDetailPayed">
    <thead>
        <tr>
            <th>رقم الفئة</th>
            <th>اسم الفئة</th>
            <th>سعر التكلفة</th>
            <th>سعر البيع</th>
            <th>الكروت المتبقية</th>
            <th>الكروت المباعة</th>
            <th>إجمالي الكروت </th>
            <th>ملاحظات</th>
            <th></th>

        </tr>
    </thead>
    @foreach (var Faction in Model)
    {
        <tbody>
            <tr>
                @if (Faction != null)
                {
                    <td>@Html.DisplayFor(modelItem => Faction.Number)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Name)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.CostPrice)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.SelePrice)</td>
                    <td class="cardNotPayed">@Html.DisplayFor(modelItem => Faction.cardNotPayed)</td>
                    <td class="cardPayed">@Html.DisplayFor(modelItem => Faction.cardPayed)</td>
                    <td class="cardAll">@Html.DisplayFor(modelItem => Faction.cardAll)</td>
                    <td>@Html.DisplayFor(modelItem => Faction.Note)</td>
                    <td style="text-align:right">
                        @if (ViewBag.Type == 1)
                        {
                            <Button class="btn btn-link" onclick="openCards('@Faction.ID')">
                                <i class="ace-icon fa fa-eye bigger-110"></i>
                                عرض الكروت
                            </Button>
                        }
                        <Button class="btn btn-link" onclick="openEditModal('@Faction.ID','@Faction.CardTypeID')">
                            <i class="ace-icon fa fa-edit bigger-110"></i>
                            تعديل
                        </Button>
                    </td>
                }
            </tr>
        </tbody>
    }
</table>
<div class="center">
    <a href="/#!/route/Cards/CardType" class="btn btn-sm btn-primary btn-white btn-round">
        <i class="ace-icon fa fa-rss bigger-150 middle orange2"></i>
        <span class="bigger-110">رجوع</span>

        <i class="icon-on-right ace-icon fa fa-arrow-right"></i>
    </a>
</div>
<style>
    .cardNotPayed {
        color: #00bb00;
        font-size: 16px;
    }

    #tableDetailPayed thead tr th,
    #tableDetailPayed tbody tr td {
        text-align: center;
    }

    .cardPayed {
        color: #009cad;
        font-size: 16px;
    }

    .cardAll {
        color: #00009c;
        font-size: 16px;
    }
</style>
<script>
    function openModal(id) {
        i('open modal id' + id);
        openViewAsModal('Cards/CardFaction/AddOrEditFaction?ID=' + id, " جديد");
    }
    function openEditModal(id, CardTypeID) {
        i('open modal id' + id);
        openViewAsModal('Cards/CardFaction/AddOrEditFaction?ID=' + id + '&CardTypeID=' + CardTypeID);
    }
    function openCards(id) {
        i('open modal id' + id);

        window.location.href = "/#!/route/Cards/Card/Cards/" + id;
    }
    $(function () {
        $.each($("#tableDetailPayed tbody tr"), function () {
            var cardNotP = parseInt($(this).find("td:eq(3)").text());
            var cardN = parseInt($(this).find("td:eq(4)").text());
            var cardA = parseInt($(this).find("td:eq(5)").text());

            if (cardNotP == "0")
                $(this).find("td:eq(3)").css("color", "red");
            if (cardN == "0")
                $(this).find("td:eq(4)").css("color", "red");
            if (cardA == "0")
                $(this).find("td:eq(5)").css("color", "red");

        })
    });
</script>
