# تنفيذ نسخ الملفات خطوة بخطوة

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    نسخ الملفات المحدثة - AppTech MSMS" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$sourcePath = "E:\inetpub\New folder"
$targetPath = "E:\inetpub"

# التحقق من وجود المجلد المصدر
if (-not (Test-Path $sourcePath)) {
    Write-Host "خطأ: المجلد المصدر غير موجود: $sourcePath" -ForegroundColor Red
    exit 1
}

Write-Host "`nالمجلد المصدر: $sourcePath" -ForegroundColor Gray
Write-Host "المجلد الهدف: $targetPath" -ForegroundColor Gray

# إنشاء المجلدات الأساسية
Write-Host "`n1. إنشاء المجلدات الأساسية..." -ForegroundColor Yellow

$folders = @(
    "$targetPath\TopupProcessor",
    "$targetPath\Data", 
    "$targetPath\SQL",
    "$targetPath\Documentation",
    "$targetPath\OLAP",
    "$targetPath\Extracted"
)

foreach ($folder in $folders) {
    if (-not (Test-Path $folder)) {
        New-Item -ItemType Directory -Path $folder -Force | Out-Null
        Write-Host "✓ تم إنشاء: $folder" -ForegroundColor Green
    } else {
        Write-Host "✓ موجود: $folder" -ForegroundColor Gray
    }
}

Write-Host "`n2. نسخ TopupProcessor المحدث..." -ForegroundColor Yellow

$topupSource = "$sourcePath\TopupProcessor v1.2 - 4.2"
$topupTarget = "$targetPath\TopupProcessor"

if (Test-Path $topupSource) {
    # إنشاء نسخة احتياطية من النسخة القديمة
    if (Test-Path $topupTarget) {
        $backupPath = "$targetPath\TopupProcessor_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Host "إنشاء نسخة احتياطية: $backupPath" -ForegroundColor Gray
        
        try {
            Copy-Item $topupTarget $backupPath -Recurse -Force
            Write-Host "✓ تم إنشاء نسخة احتياطية" -ForegroundColor Green
        } catch {
            Write-Host "⚠ خطأ في النسخة الاحتياطية: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    # نسخ النسخة الجديدة
    Write-Host "نسخ TopupProcessor المحدث..." -ForegroundColor Gray
    try {
        Copy-Item "$topupSource\*" $topupTarget -Recurse -Force
        Write-Host "✓ تم نسخ TopupProcessor المحدث" -ForegroundColor Green
        
        # عرض الملفات المنسوخة
        $copiedFiles = Get-ChildItem $topupTarget -File
        Write-Host "الملفات المنسوخة:" -ForegroundColor Gray
        foreach ($file in $copiedFiles) {
            $size = [math]::Round($file.Length / 1KB, 2)
            Write-Host "  - $($file.Name) ($size KB)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "✗ خطأ في نسخ TopupProcessor: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "✗ مجلد TopupProcessor غير موجود في المصدر" -ForegroundColor Red
}

Write-Host "`n3. نسخ ملفات CSV الأساسية..." -ForegroundColor Yellow

$csvFiles = @(
    "Account.csv",
    "Agent.csv", 
    "Agent2023.csv",
    "Branch.csv",
    "UserInfo.csv",
    "UserRole.csv",
    "AccountUser.csv",
    "AccountParent.csv"
)

$dataPath = "$targetPath\Data"
$copiedCount = 0

foreach ($csvFile in $csvFiles) {
    $sourceFile = "$sourcePath\$csvFile"
    $targetFile = "$dataPath\$csvFile"
    
    if (Test-Path $sourceFile) {
        try {
            Copy-Item $sourceFile $targetFile -Force
            $fileSize = (Get-Item $sourceFile).Length / 1KB
            Write-Host "✓ نُسخ: $csvFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
            $copiedCount++
        } catch {
            Write-Host "✗ خطأ في نسخ: $csvFile - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ غير موجود: $csvFile" -ForegroundColor Yellow
    }
}

Write-Host "تم نسخ $copiedCount من $($csvFiles.Count) ملف CSV أساسي" -ForegroundColor Cyan

Write-Host "`n4. نسخ ملفات CSV إضافية..." -ForegroundColor Yellow

$additionalCsvFiles = @(
    "acccount2021.csv",
    "acountusers.csv", 
    "agg.csv",
    "balanc.csv",
    "balances 2021.csv",
    "cli.csv",
    "custm.csv",
    "groupitem.csv",
    "partiesACOUNTS.csv",
    "sim.csv",
    "siminvoice.csv"
)

$additionalCount = 0

foreach ($csvFile in $additionalCsvFiles) {
    $sourceFile = "$sourcePath\$csvFile"
    $targetFile = "$dataPath\$csvFile"
    
    if (Test-Path $sourceFile) {
        try {
            Copy-Item $sourceFile $targetFile -Force
            $fileSize = (Get-Item $sourceFile).Length / 1KB
            Write-Host "✓ نُسخ: $csvFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
            $additionalCount++
        } catch {
            Write-Host "✗ خطأ في نسخ: $csvFile - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ غير موجود: $csvFile" -ForegroundColor Yellow
    }
}

Write-Host "تم نسخ $additionalCount من $($additionalCsvFiles.Count) ملف CSV إضافي" -ForegroundColor Cyan

Write-Host "`n5. نسخ ملفات SQL..." -ForegroundColor Yellow

$sqlFiles = @(
    "1111111.sql",
    "back.sql",
    "topup.sql",
    "dv.sql"
)

$sqlPath = "$targetPath\SQL"
$sqlCount = 0

foreach ($sqlFile in $sqlFiles) {
    $sourceFile = "$sourcePath\$sqlFile"
    $targetFile = "$sqlPath\$sqlFile"
    
    if (Test-Path $sourceFile) {
        try {
            Copy-Item $sourceFile $targetFile -Force
            $fileSize = (Get-Item $sourceFile).Length / 1KB
            Write-Host "✓ نُسخ: $sqlFile ($([math]::Round($fileSize, 2)) KB)" -ForegroundColor Green
            $sqlCount++
        } catch {
            Write-Host "✗ خطأ في نسخ: $sqlFile - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ غير موجود: $sqlFile" -ForegroundColor Yellow
    }
}

Write-Host "تم نسخ $sqlCount من $($sqlFiles.Count) ملف SQL" -ForegroundColor Cyan

Write-Host "`n6. نسخ مجلد OLAP..." -ForegroundColor Yellow

$olapSource = "$sourcePath\OLAP"
$olapTarget = "$targetPath\OLAP"

if (Test-Path $olapSource) {
    try {
        Write-Host "نسخ مجلد OLAP كاملاً..." -ForegroundColor Gray
        Copy-Item "$olapSource\*" $olapTarget -Recurse -Force
        Write-Host "✓ تم نسخ مجلد OLAP" -ForegroundColor Green
        
        # فحص ملفات msmdsrv المهمة
        $msmdsrvIni = "$olapTarget\Config\msmdsrv.ini"
        $msmdsrvBak = "$olapTarget\Config\msmdsrv.bak"
        
        if (Test-Path $msmdsrvIni) {
            $iniSize = (Get-Item $msmdsrvIni).Length / 1KB
            Write-Host "✓ وُجد ملف إعدادات: msmdsrv.ini ($([math]::Round($iniSize, 2)) KB)" -ForegroundColor Green
        }
        
        if (Test-Path $msmdsrvBak) {
            $bakSize = (Get-Item $msmdsrvBak).Length / 1KB
            Write-Host "✓ وُجد ملف النسخة الاحتياطية: msmdsrv.bak ($([math]::Round($bakSize, 2)) KB)" -ForegroundColor Green
        }
        
        # فحص ملفات البيانات
        $olapDataFiles = Get-ChildItem "$olapTarget\Data" -File -ErrorAction SilentlyContinue
        if ($olapDataFiles) {
            Write-Host "✓ ملفات البيانات: $($olapDataFiles.Count) ملف" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "✗ خطأ في نسخ OLAP: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "⚠ مجلد OLAP غير موجود" -ForegroundColor Yellow
}

Write-Host "`n7. نسخ ملفات التوثيق..." -ForegroundColor Yellow

$docFiles = @(
    "deployment-commands.md",
    "nawafd missing indexed.xlsx"
)

$docsPath = "$targetPath\Documentation"
$docCount = 0

foreach ($docFile in $docFiles) {
    $sourceFile = "$sourcePath\$docFile"
    $targetFile = "$docsPath\$docFile"
    
    if (Test-Path $sourceFile) {
        try {
            Copy-Item $sourceFile $targetFile -Force
            Write-Host "✓ نُسخ: $docFile" -ForegroundColor Green
            $docCount++
        } catch {
            Write-Host "✗ خطأ في نسخ: $docFile - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠ غير موجود: $docFile" -ForegroundColor Yellow
    }
}

Write-Host "تم نسخ $docCount من $($docFiles.Count) ملف توثيق" -ForegroundColor Cyan

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "انتهت عملية نسخ الملفات المحدثة!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

# إنشاء تقرير سريع
$totalFiles = $copiedCount + $additionalCount + $sqlCount + $docCount
Write-Host "`n📊 ملخص العملية:" -ForegroundColor Cyan
Write-Host "• TopupProcessor: محدث إلى v1.2 - 4.2" -ForegroundColor White
Write-Host "• ملفات CSV: $($copiedCount + $additionalCount) ملف" -ForegroundColor White
Write-Host "• ملفات SQL: $sqlCount ملف" -ForegroundColor White
Write-Host "• OLAP: مجلد كامل مع إعدادات msmdsrv" -ForegroundColor White
Write-Host "• التوثيق: $docCount ملف" -ForegroundColor White
Write-Host "• المجموع: $totalFiles ملف + مجلدات" -ForegroundColor White

Write-Host "`n🎯 الخطوات التالية:" -ForegroundColor Yellow
Write-Host "1. تشغيل سكريبت استعادة msmdsrv" -ForegroundColor Cyan
Write-Host "2. تشغيل سكريبت استعادة قاعدة البيانات" -ForegroundColor Cyan
Write-Host "3. تحديث ملفات التكوين" -ForegroundColor Cyan

Write-Host "`nاضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
