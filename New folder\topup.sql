USE [2022]
GO

SELECT [ID]
      ,[Number]
      ,[ServiceID]
      ,[NetworkID]
      ,[SubscriberNumber]
      ,[Amount]
      ,[FactionID]
      ,[RegionID]
      ,[LineType]
      ,[Date]
      ,[Year]
      ,[Status]
      ,[Note]
      ,[CreditorAccountID]
      ,[CurrencyID]
      ,[DebitorAccountID]
      ,[AgentID]
      ,[RefNumber]
      ,[TransactionID]
      ,[ProviderID]
      ,[EntryID]
      ,[PaymentEntryID]
      ,[Channel]
      ,[CreatedBy]
      ,[BranchBy]
      ,[CreatedTime]
      ,[RowVersion]
      ,[BranchID]
      ,[ProviderRM]
      ,[ProviderPrice]
      ,[SubNote]
      ,[Datestamb]
      ,[UniqueNo]
      ,[Quantity]
      ,[UnitPrice]
      ,[UnitCost]
      ,[CostAmount]
      ,[DifferentialAmount]
      ,[CommissionAmount]
      ,[Discount]
      ,[TotalCost]
      ,[TotalAmount]
      ,[Profits]
      ,[Method]
      ,[Type]
      ,[Class]
      ,[LType]
      ,[OperatorID]
      ,[AppTechApi]
      ,[BillNumber]
      ,[BillState]
      ,[Debited]
      ,[ByChild]
      ,[IsDirect]
      ,[BundleName]
      ,[BundleCode]
      ,[ExCode]
      ,[TransNumber]
      ,[OperationID]
      ,[AccountID]
      ,[State]
      ,[StateClass]
      ,[Identifier]
      ,[AdminNote]
      ,[AccountNote]
      ,[Description]
      ,[Responded]
      ,[RequestInfo]
      ,[ResponseInfo]
      ,[ResponseTime]
      ,[ResponseStatus]
      ,[ResponseReference]
      ,[ExecutionPeroid]
      ,[FaildRequest]
      ,[FailedReason]
      ,[FailedType]
      ,[Cured]
      ,[CuredBy]
      ,[CuredInfo]
      ,[InspectInfo]
      ,[Flag]
      ,[Action]
      ,[QuotaionID]
      ,[SyncID]
  FROM [dbo].[Topup]

GO

