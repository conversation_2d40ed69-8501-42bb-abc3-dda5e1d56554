﻿@model AppTech.MSMS.Web.Areas.Merchants.Models.MerchantPaymentModel
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}

<input type="text" name="TransactionNumber" id="TransactionNumber" placeholder="رقم العملية (أختياري) "/>
@*<div class="hr hr-dotted hr-24"></div>
<input type="text" name="ClientNumber" id="ClientNumber" placeholder="رقم العميل (أختياري) " />*@
<div class="hr hr-dotted hr-24"></div>
<input type="text" name="InvoiceNumber" id="InvoiceNumber" placeholder="رقم الفاتورة (أختياري)"/>