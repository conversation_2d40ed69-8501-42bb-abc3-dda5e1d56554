﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>msmgdsrv</name>
  </assembly>
  <members>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand">
      <summary>Represents a command to run against a analytical data source.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> class with default values.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> class with the text of the command.</summary>
      <param name="">The command to be run by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.Cancel">
      <summary>Tries to cancel the command that the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> is currently running.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.Clone">
      <summary>Creates and returns an instance of an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> class based on the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> object.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CommandText">
      <summary>Gets or sets the command to run.</summary>
      <returns>The command for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />. The default value is an empty string.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CommandTimeout">
      <summary>Gets or sets the time to wait for a command to run before the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> stops trying to run the command and generates an error.</summary>
      <returns>The time, in seconds, to wait for the command to run.</returns>
      <exception cref="T:System.ArgumentException">The property is set to a number less than zero (0).</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CommandType">
      <summary>Gets or sets the <see cref="T:System.Data.CommandType" /> used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />.</summary>
      <returns>The <see cref="T:System.Data.CommandType" /> used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CreateParameter">
      <summary>Returns a newly created <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" />.</summary>
      <returns>The created <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.ExecuteNonQuery">
      <summary>Runs the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> without returning any results.</summary>
      <returns>A value of one (1).</returns>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CommandText" /> property was improperly set.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.ExecuteReader">
      <summary>Runs the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> and returns an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
      <returns>A data reader.</returns>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:System:InvalidOperationException">The <see cref="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CommandText" /> property was improperly set</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>Runs the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> using the specified CommandBehavior enumeration value and returns an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
      <returns>A data reader.</returns>
      <param name="">A CommandBehavior enumeration value that indicates the behavior of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> during or after running the command.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.ExecuteScalar">
      <summary>Executes the statement and returns the first object of the first column.</summary>
      <returns>The first object of the first column or null if no columns are found.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.Parameters">
      <summary>Gets an instance of an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> class that contains the parameters that the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> uses to run the command.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> that contains the parameters that the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> uses to run the command.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.Prepare">
      <summary>Verifies that the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> can run.</summary>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.CommandText" /> property was improperly set.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.System#Data#IDbCommand#CreateParameter">
      <summary>Creates and returns a new instance of an <see cref="T:System.Data.IDbDataParameter" /> object.</summary>
      <returns>An <see cref="T:System.Data.IDbDataParameter" /> object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.System#Data#IDbCommand#ExecuteReader">
      <summary>Executes the CommandText against the Connection and builds an IDataReader.</summary>
      <returns>An <see cref="T:System.Data.IDataReader" /> object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.System#Data#IDbCommand#ExecuteReader(System.Data.CommandBehavior)">
      <summary>Executes the CommandText against the Connection and builds an IDataReader using one of the CommandBehavior values.</summary>
      <returns>An <see cref="T:System.Data.IDataReader" /> object.</returns>
      <param name="b">One of the CommandBehavior values.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdCommand.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current instance of this object.</summary>
      <returns>A new <see cref="T:System.Object" /> that is a copy of the current instance of this object.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection">
      <summary>Represents the connection object to the running instance of Analysis Services.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdConnection.ClientCulture">
      <summary>Gets the <see cref="T:System.Globalization.CultureInfo" /> object that the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" /> is using with the client.</summary>
      <returns>A <see cref="T:System.Globalization.CultureInfo" /> object that has the culture information that the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" /> is using with the client.</returns>
    </member>
    <member name="E:Microsoft.AnalysisServices.AdomdServer.AdomdConnection.CubeClosing">
      <summary>Occurs when the cube starts to close, but before the cube is actually closed.</summary>
    </member>
    <member name="E:Microsoft.AnalysisServices.AdomdServer.AdomdConnection.CubeOpened">
      <summary>Occurs when the cube is opened.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdConnection.SessionID">
      <summary>Gets the session identifier for the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" />.</summary>
      <returns>A string with the session identifier.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdConnection.User">
      <summary>Gets the identification of the user who opened the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" />.</summary>
      <returns>A <see cref="T:System.Security.Principal.GenericIdentity" /> object that has the information about the user who opened the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader">
      <summary>Provides a means of reading a forward-only result set obtained by running a command, and can be used to access multidimensional data sources.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.{dtor}">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Close">
      <summary>Closes the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Depth">
      <summary>Gets a value indicating the depth of nesting for the current row.</summary>
      <returns>An Integer that contains the depth of nesting for the current row.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.FieldCount">
      <summary>Gets a value indicating the number of columns in the current row.</summary>
      <returns>An Integer that contains the number of columns in the current row.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetBoolean(System.Int32)">
      <summary>Returns a Boolean that contains the value from the specified column.</summary>
      <returns>A Boolean that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetByte(System.Int32)">
      <summary>Returns a Byte that contains the value from the specified column.</summary>
      <returns>A Byte that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>This member is reserved for future use.</summary>
      <returns>A Long that contains the number of bytes actually read from the column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
      <param name="fieldOffset">The zero-based index within the column from which to begin reading data.</param>
      <param name="buffer">The buffer into which to read the stream of bytes.</param>
      <param name="bufferoffset">The zero-based index within the buffer from which to begin writing data.</param>
      <param name="length">The number of bytes to read from the column.</param>
      <exception cref="NotSupportedException">The method is called. For more information about this exception, see NotSupportedException Class</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetChar(System.Int32)">
      <summary>Returns a Char that contains the value from the specified column.</summary>
      <returns>A Char that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Returns an array of Char objects that contain a stream of values from the specified column.</summary>
      <returns>A Long that contains the number of characters actually read from the column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
      <param name="fieldOffset">The zero-based index within the column from which to begin reading data.</param>
      <param name="buffer">The buffer into which to read the stream of characters.</param>
      <param name="bufferoffset">The zero-based index within the buffer from which to begin writing data.</param>
      <param name="length">The number of characters to read from the column.</param>
      <exception cref="IndexOutOfRangeException">The value of<paramref name="dataIndex" /> is greater than Int32.MaxValue.The value of <paramref name="bufferIndex" /> is less than 0 or greater than the total of <paramref name="length" /> and the length of <paramref name="buffer" />.For more information about this exception, see IndexOutOfRangeException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetData(System.Int32)">
      <summary>Returns a System.Data.IDataReader interface to be used when a column points to more remote structured data.</summary>
      <returns>A System.Data.IDataReader interface that points to more remote structured data referenced by the column. For more information, see the IDataReader Interface topic.</returns>
      <param name="i">The zero-based ordinal position of the column to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetDataReader(System.Int32)">
      <summary>Gets an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" /> object from the specified column.</summary>
      <returns>An AdomdDataReader object from the specified column.</returns>
      <param name="index">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetDataTypeName(System.Int32)">
      <summary>Returns a String that contains the full name of the data type for the specified column.</summary>
      <returns>A String that contains the full name of the data type for the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetDateTime(System.Int32)">
      <summary>Returns a DateTime that contains the value from the specified column.</summary>
      <returns>A DateTime that contains the value, converted to local system time, of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetDecimal(System.Int32)">
      <summary>Returns a Decimal that contains the value from the specified column.</summary>
      <returns>A Decimal that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetDouble(System.Int32)">
      <summary>Returns a Double that contains the value from the specified column.</summary>
      <returns>A Double that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetEnumerator">
      <summary>Gets an IEnumerator interface for iterating through the collection.</summary>
      <returns>An IEnumerator interface for iterating through the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetFieldType(System.Int32)">
      <summary>Returns a Type that represents the data type of the specified column.</summary>
      <returns>A Type that contains the type of the Object returned by the <see cref="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetValue(System.Int32)" /> method.</returns>
      <param name="i">The zero-based ordinal position of the column to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetFloat(System.Int32)">
      <summary>Returns a Float that contains the value from the specified column.</summary>
      <returns>A Single that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetGuid(System.Int32)">
      <summary>Returns a Guid that contains the value from the specified column.</summary>
      <returns>A Guid that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetInt16(System.Int32)">
      <summary>Returns an Int16 that contains the value from the specified column.</summary>
      <returns>An Int16 that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetInt32(System.Int32)">
      <summary>Returns an Int32 that contains the value from the specified column.</summary>
      <returns>An Int32 that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetInt64(System.Int32)">
      <summary>Returns an Int64 that contains the value from the specified column.</summary>
      <returns>An Int64 that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetName(System.Int32)">
      <summary>Returns the name of the specified column.</summary>
      <returns>A String that contains the name of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetOrdinal(System.String)">
      <summary>Returns the ordinal position of the specified column.</summary>
      <returns>An Integer that contains the ordinal position of the specified column.</returns>
      <param name="__unnamed0">The name of the column to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetSchemaTable">
      <summary>Returns a DataTable that describes the column metadata of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
      <returns>A DataTable that contains the schema information for the current result of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetString(System.Int32)">
      <summary>Returns a String that contains the value from the specified column.</summary>
      <returns>A String that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetTimeSpan(System.Int32)">
      <summary>Gets a TimeSpan that contains the value from the specified column.</summary>
      <returns>A TimeSpan that contains the value from the specified column.</returns>
      <param name="index">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetValue(System.Int32)">
      <summary>Returns the value for the specified column in its native format.</summary>
      <returns>An Object that contains the value of the specified column.</returns>
      <param name="i">The zero-based ordinal position of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.GetValues(System.Object[])">
      <summary>Gets an array of values for all of the columns in the current row.</summary>
      <returns>An Integer that contains the number of instances copied into the array of Object objects.</returns>
      <param name="__unnamed0">An array of Object objects into which to copy the column values.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.IsClosed">
      <summary>Gets a value indicating whether the data reader is closed.</summary>
      <returns>A Boolean that contains a value indicating whether the data reader is closed.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.IsDBNull(System.Int32)">
      <summary>Returns a Boolean that contains a value indicating whether the column is set to null.</summary>
      <returns>A Boolean containing true if the column is set to null; false otherwise.</returns>
      <param name="i">The zero-based ordinal position of the column to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Item(System.Int32)">
      <summary>Gets the value of a specified column in its native format, given the ordinal position of the column. In Microsoft Visual C#, this property is the indexer for the T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader class.</summary>
      <returns>An Object that contains the value of the specified column.</returns>
      <param name="__unnamed0">The zero-based index of the column to be retrieved.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Item(System.String)">
      <summary>Gets the value of a specified column in its native format, given the name of the column. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" /> class.</summary>
      <returns>An Object that contains the value of the specified column.</returns>
      <param name="__unnamed0">The name of the column to be retrieved.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.NextResult">
      <summary>This member is reserved for future use.</summary>
      <returns>Always false.</returns>
      <exception cref="NotSupportedException">The method is called. For more information about this exception, see NotSupportedException Class</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Read">
      <summary>Advances the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" /> to the next row.</summary>
      <returns>A Boolean that contains true if there are more rows; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.RecordsAffected">
      <summary>This member is reserved for future use.</summary>
      <returns>An Integer that contains the value -1.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Enumerator">
      <summary>Implements the IEnumeratorinterface to support iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" /> and reading its individual records.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Enumerator.Current">
      <summary>Gets the current IDataRecord interface in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
      <returns>The IDataRecord interface in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />, if the enumerator has not passed the end of the records.</returns>
      <exception cref="InvalidOperationException">The enumerator is either currently before the first or after the last IDataRecord interface in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Enumerator.MoveNext">
      <summary>Moves to the next IDataRecord interface in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader.Enumerator.Reset">
      <summary>This method is not supported on the forward-only <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdDataReader" />.</summary>
      <exception cref="NotSupportedException">The method is not supported. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdException">
      <summary>Represents an exception thrown by ADOMD.NET.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" /> class. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.#ctor(System.Int32!System.Runtime.CompilerServices.IsLong)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" /> class with a given HRESULT.</summary>
      <param name="hr">The HRESULT for the exception.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" /> class with a given message.</summary>
      <param name="message">A string containing the message for the exception.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" /> class with a given message and an inner exception.</summary>
      <param name="message">A string containing the message for the exception.</param>
      <param name="innerException">An instance of the Exception that caused the current exception.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.Dispose">
      <summary>Releases the resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" /> class.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdException.HR">
      <summary>Gets the HRESULT, a coded numerical value that is assigned to a specific exception.</summary>
      <returns>The HRESULT value.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdException.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the specified SerializationInfo object with the data needed to serialize the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdException" />.</summary>
      <param name="info">The SerializationInfo to populate with data.</param>
      <param name="context">The destination for this serialization.</param>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter">
      <summary>Represents a parameter to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.#ctor(System.String,System.Object)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> class with the name and value of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" />.</summary>
      <param name="parameterName">The name of the parameter.</param>
      <param name="value">The value of the parameter.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Clone">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> that is a copy of the current instance.</summary>
      <returns>A copy of the current instance.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.DbType">
      <summary>Gets or sets the DbType of the parameter. This property is reserved for future use.</summary>
      <returns>One of the DbType values. The default is String.</returns>
      <exception cref="NotSupportedException">The property is called. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Direction">
      <summary>Gets or sets a value indicating the direction of the parameter.</summary>
      <returns>Always returns ParameterDirection.Input.</returns>
      <exception cref="NotSupportedException">The property is set to an input other than ParameterDirection.Input. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.IsNullable">
      <summary>Gets a value indicating whether the parameter accepts null values. </summary>
      <returns>true if the parameter accepts null values; otherwise, false. Always returns false.</returns>
      <exception cref="NotSupportedException">The property is set. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.ParameterName">
      <summary>Gets or sets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" />.</summary>
      <returns>The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Precision">
      <summary>Gets or sets the precision of numeric parameters. This property is reserved for future use.</summary>
      <returns>The precision of numeric parameters. The default is 0.</returns>
      <exception cref="NotSupportedException">The property is called. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Scale">
      <summary>Gets or sets the scale of numeric parameters. This property is reserved for future use.</summary>
      <returns>The scale of numeric parameters. The default is 0.</returns>
      <exception cref="NotSupportedException">The property is called. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Size">
      <summary>Gets or sets the size of the parameter. This property is reserved for future use.</summary>
      <returns>The size of the parameter. The default is 0.</returns>
      <exception cref="NotSupportedException">The property is called. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.SourceColumn">
      <summary>Gets or sets the name of the source column that is mapped to the DataSet and used for loading or returning the Value. This property is reserved for future use.</summary>
      <returns>The name of the source column that is mapped to the DataSet and used for loading or returning the Value. The default is null.</returns>
      <exception cref="NotSupportedException">The property is called. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.SourceVersion">
      <summary>Gets or sets the DataRowVersion to use when loading <see cref="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Value" />.</summary>
      <returns>Always returns DataRowVersion.Current.</returns>
      <exception cref="NotSupportedException">The property is set to a value other than DataRowVersion.Current. For more information about this exception, see NotSupportedException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.System#ICloneable#Clone">
      <summary>Creates a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" />.</summary>
      <returns>A new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.ToString">
      <summary>Returns the <see cref="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.ParameterName" />.</summary>
      <returns>A string containing the name of the parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameter.Value">
      <summary>Gets or sets the value of the parameter.</summary>
      <returns>The value of the parameter.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection">
      <summary>This class gets a collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> objects contained in a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdCommand" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Add(Microsoft.AnalysisServices.AdomdServer.AdomdParameter)">
      <summary>Adds a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</summary>
      <returns>An instance of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object.</returns>
      <param name="__unnamed0">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to be added.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Add(System.String,System.Object)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object with the specified property name and value, and adds it to the collection.</summary>
      <returns>An instance of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object.</returns>
      <param name="parameterName">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> to add. </param>
      <param name="value">The value of the <see cref="T:Microsoft.AnalysisServices.AdomdCServer.AdomdParameter" /> object to be created.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Clear">
      <summary>Removes all parameters from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Contains(Microsoft.AnalysisServices.AdomdServer.AdomdParameter)">
      <summary>Determines whether a parameter is in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</summary>
      <returns>An instance of a Boolean object that contains true if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object exists in the collection. Otherwise, it is false.</returns>
      <param name="__unnamed0">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Contains(System.String)">
      <summary>Given a property name, this method determines whether a parameter is in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</summary>
      <returns>An instance of a Boolean object that contains true if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object exists in the collection. Otherwise, it is false.</returns>
      <param name="__unnamed0">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.AdomdParameter[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection to the specified array.</summary>
      <param name="destinationArray">An instance of a one-dimensional Array object in which to copy the elements of the collection. The Array object must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> objects in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> objects in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection by its name.</summary>
      <returns>An instance of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object.</returns>
      <param name="parameterName">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.IndexOf(Microsoft.AnalysisServices.AdomdServer.AdomdParameter)">
      <summary>Returns the zero-based index of the first occurrence of a parameter in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</summary>
      <returns>The index of <paramref name="value" /> if found in the list. Otherwise, it returns -1.</returns>
      <param name="__unnamed0">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to locate in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.IndexOf(System.String)">
      <summary>Given a property name, this method returns the zero-based index of the first occurrence of a parameter in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</summary>
      <returns>The index of <paramref name="value" /> if found in the list. Otherwise, it returns -1.</returns>
      <param name="__unnamed0">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to locate in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Insert(System.Int32,Microsoft.AnalysisServices.AdomdServer.AdomdParameter)">
      <summary>Inserts a parameter into the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection at the specified index.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> with the specified name.</returns>
      <param name="parameterName">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to find.</param>
      <exception cref="InvalidOperationException">The specified member did not exist in the collection. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.Remove(Microsoft.AnalysisServices.AdomdServer.AdomdParameter)">
      <summary>Removes the first occurrence of a specific parameter from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</summary>
      <param name="__unnamed0">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to remove from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.RemoveAt(System.Int32)">
      <summary>Removes the parameter at the specified index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</summary>
      <param name="__unnamed0">The index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to remove from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.RemoveAt(System.String)">
      <summary>Removes the parameter specified by name in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</summary>
      <param name="__unnamed0">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to remove from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> to the specified array, starting at a particular array index.</summary>
      <param name="destArray">The one-dimensional array to hold the elements to be copied from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" />.</param>
      <param name="index">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System::Collections::IEnumerable.GetEnumerator"></member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System#Collections#IList#Add(System.Object)">
      <summary>Adds an item to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" />.</summary>
      <returns>The position into which the new element was inserted, or -1 to indicate that the item was not inserted into the collection.</returns>
      <param name="pObj">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to add to the collection.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System#Collections#IList#Contains(System.Object)">
      <summary>Determines whether the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> contains a specific value.</summary>
      <returns>true if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object is found in the collection; otherwise, false.</returns>
      <param name="pObj">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to locate in the collection.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System#Collections#IList#IndexOf(System.Object)">
      <summary>Determines the index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" />.</summary>
      <returns>The index of the object if found in the collection; otherwise, -1.</returns>
      <param name="pObj">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to locate in the collection.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Inserts an item to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection" /> at the specified index.</summary>
      <param name="nIndex">The zero-based index at which the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object should be inserted.</param>
      <param name="pObj">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to insert in the collection.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.AdomdParameterCollection.System#Collections#IList#Remove(System.Object)">
      <summary>Removes the first occurrence of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object from the collection.</summary>
      <param name="pObj">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdParameter" /> object to remove from the collection.</param>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.AttributeStats">
      <summary>Represents the statistics for a single attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.Attribute">
      <summary>Represents the index for the associated attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.ExistingAdjustedProbability">
      <summary>Represents the existing adjusted probability for the attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.ExistingProbability">
      <summary>Represents the existing probability for the attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.ExistingSupport">
      <summary>Represents the existing support for the attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.Max">
      <summary>Represents the maximum for the attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.Min">
      <summary>Represents the minimum for the attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.States">
      <summary>Represents the number of state statistics for the attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.AttributeStats.StateStatistics">
      <summary>Represents an array of state statistics for each state of the attribute.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Context">
      <summary>Provides the execution context for the stored procedure.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Context.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Context" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Context.CheckCancelled">
      <summary>Throws an exception if the query has timed out or been canceled by the user.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.ClientCultureInfo">
      <summary>Gets the culture for the current client.</summary>
      <returns>The CultureInfo for the current client.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.Cubes">
      <summary>Gets a collection of cubes that are available in the current database or context.</summary>
      <returns>A collection of cubes that are available in the current database or context.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.CurrentConnection">
      <summary>Gets the current connection.</summary>
      <returns>The current connection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.CurrentCube">
      <summary>Gets the current cube.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that represents the current cube; null if not applicable.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.CurrentDatabaseName">
      <summary>Gets the current database name for the current session.</summary>
      <returns>A string that contains the current database name.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.CurrentMiningModel">
      <summary>Gets the current mining model.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> that represents the current mining model; null if not applicable.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.CurrentServerID">
      <summary>Gets the server identifier (server\instance) for the current session.</summary>
      <returns>A string that contains the server identifier.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.ExecuteForPrepare">
      <summary>Gets a value that indicates whether the stored procedure is being called for preparation purposes.</summary>
      <returns>true if the stored procedure is being called for preparation purposes; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.MiningModels">
      <summary>Gets the mining models in the current database.</summary>
      <returns>The mining models in the current database.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.MiningServices">
      <summary>Gets the mining services in the current database.</summary>
      <returns>The mining services in the current database.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.MiningStructures">
      <summary>Gets the mining structures in the current database.</summary>
      <returns>The mining structures in the current database.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.Pass">
      <summary>Gets the pass number that the user-defined function (UDF) or stored procedure is running under.</summary>
      <returns>An integer that represents the current pass.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Context.Server">
      <summary>Gets the server object.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Server" /> object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Context.TraceEvent(System.Int32,System.Int32,System.String)">
      <summary>Raises a trace event with the specified information.</summary>
      <param name="eventSubclass">The event subclass identifier for this event.</param>
      <param name="numberData">An integer containing the numeric data for this event.</param>
      <param name="textData">A string containing the text data for this event.</param>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection">
      <summary>Contains a read-only, on-demand list of <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.CubeDef[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> from the collection by its name.</summary>
      <returns>The <see cref="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Find(System.String)" /> method returns null if the object is not found, whereas the <see cref="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Item(System.String)" />property throws an exception if the object is not found.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Enumerator">
      <summary>Implements the IEnumerator interface to support iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> objects. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" />.</summary>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last cube in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" />. For more information about this exception, see InvalidOperationException Class. </exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeCollection" />.</summary>
      <returns>true if the Enumerator was successfully advanced to the next element; false if the Enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.CubeDef">
      <summary>Represents the metadata for a cube.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeDef.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>A String that contains the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>A String that contains the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Dimensions">
      <summary>Gets an instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" /> class that contains the dimensions for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> class.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" /> that contains dimensions for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeDef.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.CubeDef.GetSchemaObject(Microsoft.AnalysisServices.AdomdServer.SchemaObjectType,System.String)">
      <summary>Returns an object associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> from a specified schema rowset.</summary>
      <returns>An Object that contains an object associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
      <param name="schemaObjectType">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType" /> enumeration value that represents the type of object to find.</param>
      <param name="uniqueName">The unique name of the object to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Kpis">
      <summary>Gets the KPIs associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />. This member is not supported for versions of Analysis Services earlier than Microsoft SQL Server 2005 Analysis Services (SSAS).</summary>
      <returns>A collection of KPIs.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.LastProcessed">
      <summary>Gets the date and time on which the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> was last processed.</summary>
      <returns>A DateTime that contains the date on which the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> was last processed.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.LastUpdated">
      <summary>Gets the date and time on which the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> was last updated.</summary>
      <returns>A DateTime that contains the date on which the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> was last updated.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Measures">
      <summary>Gets an instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" /> class that contains the measures for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" /> that contains the measures for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>A String that contains the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.NamedSets">
      <summary>Gets an instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" /> class that contains the named sets for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" /> that contains the named sets for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Properties">
      <summary>Gets an instance of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> class that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the cube.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Type">
      <summary>Returns the <see cref="E:Microsoft.AnalysisServices.AdomdServer.CubeType" /> of the cube, if supported by the provider.</summary>
      <returns>One of the enumeration values of the <see cref="T:Microsoft.AnalysisServices.AdomdClient.CubeType" /> class.</returns>
      <exception cref="NotSupportedException">The provider does not support this property. For more information about this exception, see NotSupportedException Class</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.CubeType">
      <summary>Returned by <see cref="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Type" /> to determine if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> represents a dimension or a cube.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.CubeType.Cube">
      <summary>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> represents a cube.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.CubeType.Dimension">
      <summary>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> represents a dimension.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.CubeType.Unknown">
      <summary>It cannot be determined if the provider has specified that the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> represents a cube or a dimension.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Dimension">
      <summary>Represents a dimension within a cube.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Dimension.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.AttributeHierarchies">
      <summary>Gets the collection of attribute hierarchies. This member is not supported for versions of Analysis Services earlier than Microsoft SQL Server 2005 Analysis Services (SSAS).</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> collection containing the attribute hierarchies for this <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A String that contains the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A String that contains the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.DimensionType">
      <summary>Gets the dimension type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum" /> enumeration value that represents the dimension type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Dimension.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.Hierarchies">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> that contains the dimension hierarchies for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> that contains the dimension hierarchies for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A String that contains the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.ParentCube">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that represents the parent cube of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the dimension.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
      <returns>A String that contains the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Dimension.WriteEnabled">
      <summary>Gets a value that indicates whether the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> is write-enabled.</summary>
      <returns>true if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> is write-enabled; false if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> is not write-enabled.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection">
      <summary>Contains a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> objects contained in a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Dimension[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> from the collection by its name.</summary>
      <returns>If the object is not found, the <see cref="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Find(System.String)" /> method returns null, whereas the <see cref="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Item(System.String)" /> property throws an exception.</returns>
      <param name="name">The name of the <see cref="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Dimensions" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.DimensionCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.DimensionCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum">
      <summary>Represents the dimension type of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Accounts">
      <summary>Describes a dimension that contains an accounts structure with parent-child relationships.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.BillOfMaterials">
      <summary>Describes a dimension that represents a material/component breakdown. The parent-child relationship implies a parent composed of its children.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Channel">
      <summary>Describes a dimension that contains information about a distribution channel. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Currency">
      <summary>Describes a dimension that contains currency information.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Customers">
      <summary>Describes a dimension that contains customer information. The lowest level represents individual customers.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Geography">
      <summary>Describes a dimension that contains a geographic hierarchy. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Measure">
      <summary>Describes a dimension that contains measures.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Organization">
      <summary>Describes a dimension that represents the reporting structure of an organization.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Other">
      <summary>Describes a dimension of the default dimension type, which is used for dimensions that are not time dimensions or measure dimensions.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Products">
      <summary>Describes a dimension that contains product information. The lowest level represents individual products.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Promotion">
      <summary>Describes a dimension that contains information about marketing and advertising promotions.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Quantitative">
      <summary>Describes a dimension that contains quantitative elements (for example, income level, or number of children).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Rates">
      <summary>Describes a dimension that contains different types of rates (for example, buy, sell, or discounted).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Scenario">
      <summary>Describes a dimension that contains different business scenarios.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Time">
      <summary>Indicates that a dimension refers to time (for example, year, month, week, day, and so on).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Unknown">
      <summary>Describes a dimension with an unknown or unspecified type.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.DimensionTypeEnum.Utility">
      <summary>Describes a dimension that contains only calculated members. This type of dimension is usually used for data visualization techniques.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Expression">
      <summary>Provides the functionality to evaluate a Multidimensional Expressions (MDX) expression and return an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Expression.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Expression" /> class. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Expression.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Expression" /> class with the Multidimensional Expressions (MDX) expression to be evaluated. </summary>
      <param name="expressionText">An MDX expression.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Expression.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Expression" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Expression.Calculate(Microsoft.AnalysisServices.AdomdServer.Tuple)">
      <summary>Evaluates the Multidimensional Expressions (MDX) expression under a specified tuple and returns the scalar result.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> that contains the scalar result of the MDX expression. </returns>
      <param name="tuple">The tuple under which to evaluate the MDX expression.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Expression.CalculateMdxObject(Microsoft.AnalysisServices.AdomdServer.Tuple)">
      <summary>Evaluates the Multidimensional Expressions (MDX) expression under a specific tuple and returns the result.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> that contains the result of the MDX expression. </returns>
      <param name="tuple">The tuple under which to evaluate the MDX expression.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Expression.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Expression" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Expression.ExpressionText">
      <summary>Gets or sets the Multidimensional Expressions (MDX) expression to be evaluated.</summary>
      <returns>A string that contains the MDX expression.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy">
      <summary>Represents a dimension hierarchy contained by a dimension or set.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Hierarchy.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A String that contains the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.CurrentMember">
      <summary>Gets the current member for the hierarchy.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> for the hierarchy.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.DefaultMember">
      <summary>Gets the unique name of the default member for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A String that contains the unique name of the default member for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A String that contains the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.DisplayFolder">
      <summary>Gets the fully qualified name of a display folder. This member is reserved for future use.</summary>
      <returns>The fully qualified name of a display folder.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Hierarchy.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.HierarchyOrigin">
      <summary>Gets the hierarchy type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A <see cref="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.HierarchyOrigin" /> enumeration value that represents the hierarchy type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.Levels">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" /> that contains the levels of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" /> that contains the levels of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A String that contains the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.ParentDimension">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Hierarchy.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>A String that contains the key of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection">
      <summary>Contains a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> objects contained in a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" /> or a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Hierarchy[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> with the specified name if found; otherwise, null.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.GetEnumerator">
      <summary>Gets an enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>True if the access to the collection is synchronized; otherwise, false. Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> to find</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> to find.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnectionException">The connection was broken.</exception>
      <exception cref="InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.Hierarchy" /> was not found in the collection. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />.</summary>
      <returns>An IEnumerator for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> instance.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />.</summary>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, making the enumerator no longer valid. For more information on this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.HierarchyOrigin">
      <summary>Describes the overall structure of a hierarchy. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.HierarchyOrigin.AttributeHierarchy">
      <summary>Specifies that the hierarchy is an attribute hierarchy.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.HierarchyOrigin.ParentChildHierarchy">
      <summary>Specifies that the hierarchy is a parent-child hierarchy.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.HierarchyOrigin.UserHierarchy">
      <summary>Specifies that the hierarchy is a user-defined hierarchy.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.IMetadataObject">
      <summary>Defines an abstract class that provides the Name and UniqueName properties for metadata objects in <see cref="N:Microsoft.AnalysisServices.AdomdServer" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.IMetadataObject.Name">
      <summary>Gets the name for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.IMetadataObject" />.</summary>
      <returns>The name for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.IMetadataObject" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.IMetadataObject.UniqueName">
      <summary>Gets the unique name for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.IMetadataObject" />.</summary>
      <returns>The unique name for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.IMetadataObject" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Kpi">
      <summary>Key performance indicators (KPIs) are metadata wrappers around measures and MDX expressions. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Kpi.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.Caption">
      <summary>Gets the caption associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
      <returns>A string containing the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.Description">
      <summary>Gets a human-readable description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
      <returns>A String containing the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.DisplayFolder">
      <summary>Gets a forward-slash-delimited categorization structure, used by client applications to determine the hierarchical presentation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />. </summary>
      <returns>A String containing the hierarchical presentation information of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Kpi.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
      <returns>A String containing the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.ParentCube">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> representing the cube to which the Key performance indicator (KPI) belongs.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> representing the cube to which the KPI belongs.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.ParentKpi">
      <summary>Gets the parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> of the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
      <returns>Returns the parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> of the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.Properties">
      <summary>Gets a collection containing the properties of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
      <returns>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> containing the properties of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.StatusGraphic">
      <summary>Gets a String representing a graphic for displaying <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> status.</summary>
      <returns>A String representing a graphic for displaying <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> status.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.TrendGraphic">
      <summary>Gets a String representing a graphic for displaying <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> trend information.</summary>
      <returns>A String representing a graphic for displaying <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> trend information.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Kpi.UniqueName">
      <summary>Gets the unique name for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
      <returns>The unique name for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection">
      <summary>Contains a read-only, on-demand list of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Kpi[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.KpiCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>true if the access to the collection is synchronized; otherwise, false. Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" /> class.</summary>
      <returns>The specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" /> class.</summary>
      <returns>The specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.KpiCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" /> instance.</summary>
      <returns>An IEnumerator for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" /> instance.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.KpiCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.Exception typeCondition<see cref="InvalidOperationException" />The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.KpiCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Level">
      <summary>Represents a level within a hierarchy.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Level.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Level.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Level.GetMembers">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> that contains a collection of members for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.  Note   This method loads all members of the level. If there are a large number of members, this method may take a long time to execute.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> that contains a collection of members for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.LevelNumber">
      <summary>Gets the ordinal position of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> within the parent hierarchy.</summary>
      <returns>The ordinal position of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> within the parent hierarchy.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.LevelProperties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> that contains a collection of member properties for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> that contains a collection of level properties for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.LevelType">
      <summary>Gets the level type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum" /> enumeration value that represents the level type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.MemberCount">
      <summary>Gets the number of members contained by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The number of members contained by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.ParentHierarchy">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The level within the hierarchy.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the level.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Level.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection">
      <summary>Contains a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> objects contained in a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Level[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional array in which to copy the elements of the collection. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> objects in the collection.</summary>
      <returns>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> with the specified name if found; otherwise, null.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>True if the access to the collection is synchronized; otherwise, false. Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> to find.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnectionException">The connection was broken.</exception>
      <exception cref="InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> was not found in the collection. For more information about this exception, see the InvalidOperationException class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional array in which to copy the elements of the collection. The array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an enumerator that can iterate through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" /> instance.</summary>
      <returns>An IEnumerator for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" /> instance.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Enumerator">
      <summary>Contains a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> objects contained in a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty">
      <summary>Represents a member property available on members in the level.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelProperty.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelProperty.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</summary>
      <returns>A String that contains the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelProperty.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</summary>
      <returns>A String that contains the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelProperty.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelProperty.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</summary>
      <returns>A String that contains the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelProperty.ParentLevel">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelProperty.UniqueName">
      <summary>Gets the fully-qualified name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</summary>
      <returns>A String that contains the fully-qualified name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.LevelProperty[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> with the specified name if found; otherwise, null.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> to find</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> to find.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnectionException">The connection was broken.</exception>
      <exception cref="InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> was not found in the collection. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the element of <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> to an array, starting at a particular array index.</summary>
      <param name="array">The one dimensional array, that is the destination of the elements copied from <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />.</param>
      <param name="index">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns a reference to an enumerator object, which is used to iterate over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> object.</summary>
      <returns>A reference to an enumerator object, which is used to iterate over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> object.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />. For more information about this exception, see InvalidOperationException Class</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelProperty" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.LevelPropertyCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum">
      <summary>Represents the level type of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Account">
      <summary>Indicates that a level exists within an account dimension.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.All">
      <summary>Indicates the top (All) level of a dimension (the one that precalculates all the members of all lower levels).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.BomResource">
      <summary>Indicates that a level is part of a bill of materials dimension.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Calculated">
      <summary>Indicates that a level is calculated.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Channel">
      <summary>Indicates that a level exists within a distribution channel dimension.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Company">
      <summary>Indicates that a level contains information about a company. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.CurrencyDestination">
      <summary>Indicates that a level contains information about the resulting currency after a foreign exchange conversion. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.CurrencySource">
      <summary>Indicates that a level contains information about the starting currency before a foreign exchange conversion. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Customer">
      <summary>Indicates that a level contains information about an individual customer. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.CustomerGroup">
      <summary>Indicates that a level contains information about a customer group. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.CustomerHousehold">
      <summary>Indicates that a level contains information about an entire household.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoCity">
      <summary>Indicates that a level refers to a city name.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoContinent">
      <summary>Indicates that a level refers to a continent name.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoCountry">
      <summary>Indicates that a level refers to a country or region name.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoCounty">
      <summary>Indicates that a level refers to a county name.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoPoint">
      <summary>Indicates that a level refers to a location type that does not fit into the other geographic categories.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoPostalCode">
      <summary>Indicates that a level refers to a postal code.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoRegion">
      <summary>Indicates that a level refers to a custom-defined region.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.GeoStateOrProvince">
      <summary>Indicates that a level refers to a state or province name. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.OrgUnit">
      <summary>Indicates that a level refers to the name of a unit within a larger organization. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Person">
      <summary>Indicates that a level refers to an individual within a larger organization.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Product">
      <summary>Indicates that a level refers to an individual product.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.ProductGroup">
      <summary>Indicates that a level refers to a product group.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Promotion">
      <summary>Indicates that a level refers to a promotion.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Quantitative">
      <summary>Indicates that a level refers to a quantitative member within a quantitative dimension. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Regular">
      <summary>Indicates that the level is not related to time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Representative">
      <summary>Indicates that a level refers to a sales representative. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Reserved1">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Scenario">
      <summary>Indicates that a level refers to a scenario.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Time">
      <summary>Indicates that the level is related to time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeDays">
      <summary>Indicates that a level refers to days. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeHalfYears">
      <summary>Indicates that a level refers to half-years. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeHours">
      <summary>Indicates that a level refers to hours. It must be used in a dimension whose type is Time. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeMinutes">
      <summary>Indicates that a level refers to minutes. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeMonths">
      <summary>Indicates that a level refers to months. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeQuarters">
      <summary>Indicates that a level refers to (calendar) quarters. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeSeconds">
      <summary>Indicates that a level refers to seconds. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeUndefined">
      <summary>Indicates that a level refers to an indeterminate or nonstandard measurement of time. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeWeeks">
      <summary>Indicates that a level refers to weeks. It must be used in a dimension whose type is Time.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.TimeYears">
      <summary>Indicates that a level refers to years. It must be used in a dimension whose type is Time. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.LevelTypeEnum.Utility">
      <summary>Indicates that a level exists in a utility dimension. </summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MDX">
      <summary>Provides static methods for executing Multidimensional Expressions (MDX) functions without constructing the full MDX syntax.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.Aggregate(Microsoft.AnalysisServices.AdomdServer.Set,Microsoft.AnalysisServices.AdomdServer.Expression[])">
      <summary>Runs the Multidimensional Expressions (MDX) Aggregate function on a set and an expression.</summary>
      <returns>A System.ValueType that contains the results of the aggregation.</returns>
      <param name="set">The set to be aggregated.</param>
      <param name="expression">The expression to be aggregated on each member of the set.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.CrossJoin(Microsoft.AnalysisServices.AdomdServer.Set,Microsoft.AnalysisServices.AdomdServer.Set)">
      <summary>Runs the Multidimensional Expressions (MDX) Crossjoin function on two sets.</summary>
      <returns>A set that contains the cross, or Cartesian, product of <paramref name="set1" /> and <paramref name="set2" />.</returns>
      <param name="set1">The first set to be crossjoined.</param>
      <param name="set2">The second set to be crossjoined.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.DrilldownMember(Microsoft.AnalysisServices.AdomdServer.Set,Microsoft.AnalysisServices.AdomdServer.Set,System.Object[])">
      <summary>Runs the Multidimensional Expressions (MDX) DrilldownMember function on two sets.</summary>
      <returns>A set that contains the child members, ordered by hierarchy, of the parents that are contained in both <paramref name="set1" /> and <paramref name="set2" />.</returns>
      <param name="set1">The set that contains the members to be drilled down.</param>
      <param name="set2">The set that defines which members in <paramref name="set1" /> will be drilled down.</param>
      <param name="parameters">null or <see cref="F:Microsoft.AnalysisServices.AdomdServer.MDX.DrilldownMemberFlags.RECURSIVE" /></param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.Filter(Microsoft.AnalysisServices.AdomdServer.Set,Microsoft.AnalysisServices.AdomdServer.Expression)">
      <summary>Runs the Multidimensional Expressions (MDX) Filter function on a set.</summary>
      <returns>A set that contains the tuples that match the conditions that are defined by <paramref name="expression" />.</returns>
      <param name="set">The set that contains the members to be evaluated.</param>
      <param name="expression">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Expression" /> to be used to evaluate on the <paramref name="set" />.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.Generate(Microsoft.AnalysisServices.AdomdServer.Set,System.Object[])">
      <summary>Runs the Multidimensional Expressions (MDX) Generate function on a set.</summary>
      <returns>A set or an optional delimited string.</returns>
      <param name="set">The set to be evaluated.</param>
      <param name="parameters">The parameters to be used when performing the generation function.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.ParallelPeriod(System.Object[])">
      <summary>Runs the Multidimensional Expressions (MDX) ParallelPeriod function.</summary>
      <returns>A member that lies in a parallel period.</returns>
      <param name="parameters">An array that may contain the level, the lagging index, and the member from which to calculate the parallel period.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDX.StrToSet(System.String)">
      <summary>Runs the Multidimensional Expressions (MDX) StrToSet function.</summary>
      <returns>The set specified by the MDX-formatted string.</returns>
      <param name="string">The string to be converted to a set.</param>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MDX.DrilldownMemberFlags">
      <summary>Determines how members will be evaluated in <see cref="M:Microsoft.AnalysisServices.AdomdServer.MDX.DrilldownMember(Microsoft.AnalysisServices.AdomdServer.Set,Microsoft.AnalysisServices.AdomdServer.Set,System.Object[])" /> function.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MDX.DrilldownMemberFlags.INCLUDE_CALC_MEMBERS">
      <summary>Enables calculated members to be included in drilldown results.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MDX.DrilldownMemberFlags.RECURSIVE">
      <summary>Indicates recursive comparison of sets.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MDX.GenerateFlags">
      <summary>Determines how to evaluate the <see cref="M:Microsoft.AnalysisServices.AdomdServer.MDX.Generate(Microsoft.AnalysisServices.AdomdServer.Set,System.Object[])" /> method.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MDX.GenerateFlags.ALL">
      <summary>All duplicates will be retained in the final set.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Provides conversion capabilities between different <see cref="N:Microsoft.AnalysisServices.AdomdServer" /> and value objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromBool(System.Boolean)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a Boolean.</summary>
      <returns>A new value object from a Boolean object.</returns>
      <param name="parameter">A Boolean value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromByte(System.Byte)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from an 8-bit unsigned integer.</summary>
      <returns>A new object from an 8-bit unsigned integer.</returns>
      <param name="parameter">An 8-bit unsigned integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromChar(System.Char)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a Unicode character.</summary>
      <returns>A new object from a Unicode character.</returns>
      <param name="parameter">A Unicode character value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromDateTime(System.DateTime)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a DateTime.</summary>
      <returns>A new value from a datetime object.</returns>
      <param name="parameter">A DateTime object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromDecimal(System.Decimal)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a decimal.</summary>
      <returns>A new object from a decimal.</returns>
      <param name="parameter">A decimal value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromDouble(System.Double)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a double-precision floating-point number.</summary>
      <returns>A new object from a double-precision floating point number.</returns>
      <param name="parameter">A double-precision floating-point number value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromHierarchy(Microsoft.AnalysisServices.AdomdServer.Hierarchy)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> object.</summary>
      <returns>A new value from a hierarchy object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromInt16(System.Int16)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a 16-bit signed integer.</summary>
      <returns>A new object from a 16-bit signed integer.</returns>
      <param name="parameter">A 16-bit signed integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromInt32(System.Int32)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a 32-bit signed integer.</summary>
      <returns>A new object from a 32-bit signed integer.</returns>
      <param name="parameter">A 32-bit signed integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromInt64(System.Int64)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a 64-bit signed integer.</summary>
      <returns>A new object from a 64-bit signed integer.</returns>
      <param name="parameter">A 64-bit signed integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromLevel(Microsoft.AnalysisServices.AdomdServer.Level)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> object.</summary>
      <returns>A new value object from a level object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromMember(Microsoft.AnalysisServices.AdomdServer.Member)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> object.</summary>
      <returns>A new value object from a member object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromSByte(System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from an 8-bit signed integer.</summary>
      <returns>A new value object from an 8-bit signed integer.</returns>
      <param name="parameter">An 8-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromSet(Microsoft.AnalysisServices.AdomdServer.Set)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> object.</summary>
      <returns>A new value object from a set object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromSingle(System.Single)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a single-precision floating-point number.</summary>
      <returns>A new value object from a single-precision floating point object.</returns>
      <param name="parameter">A single-precision floating-point number value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromString(System.String)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a string.</summary>
      <returns>A new value object from a string object.</returns>
      <param name="parameter">A string value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromTuple(Microsoft.AnalysisServices.AdomdServer.Tuple)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> object.</summary>
      <returns>A new value object from a tuple object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromUInt16(System.UInt16)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a 16-bit unsigned integer.</summary>
      <returns>A new value object from a 16-bit unsigned integer.</returns>
      <param name="parameter">A 16-bit unsigned integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromUInt32(System.UInt32)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a 32-bit unsigned integer.</summary>
      <returns>A new value object from a 32-bit unsigned integer.</returns>
      <param name="parameter">A 32-bit unsigned integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.FromUInt64(System.UInt64)">
      <summary>Creates a new <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object from a 64-bit unsigned integer.</summary>
      <returns>A new value object from a 64-bit unsigned integer.</returns>
      <param name="parameter">A 64-bit unsigned integer value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.Hierarchy)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted hierarchy object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.Level)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> object to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted level object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~Microsoft.AnalysisServices.AdomdServer.Hierarchy">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
      <returns>The converted MDX value object into a hierarchy object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~Microsoft.AnalysisServices.AdomdServer.Tuple">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</summary>
      <returns>The converted MDX value object into a tuple object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~Microsoft.AnalysisServices.AdomdServer.Member">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> object.</summary>
      <returns>The converted MDX value object into a member object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~Microsoft.AnalysisServices.AdomdServer.Level">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
      <returns>The converted MDX value object into a level object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Char">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a Unicode character.</summary>
      <returns>The converted MDX value object into a Unicode character.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.DateTime">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a DateTime.</summary>
      <returns>The converted MDX value object into a DateTime object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.String">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a string.</summary>
      <returns>The converted MDX value object into a string.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Boolean">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a Boolean value.</summary>
      <returns>The converted MDX value object into a Boolean object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Decimal">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a decimal.</summary>
      <returns>The converted MDX value object into a decimal.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Int64">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a 64-bit signed integer.</summary>
      <returns>The converted MDX value object into a 64-bit signed integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.UInt64">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a 64-bit unsigned integer.</summary>
      <returns>The converted MDX value object into a 64-bit unsigned integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.UInt32">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a 32-bit unsigned integer.</summary>
      <returns>The converted MDX value object into a 32-bit unsigned integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.UInt16">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a 16-bit unsigned integer.</summary>
      <returns>The converted MDX value object into a 16-bit unsigned integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Int32">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a 32-bit signed integer.</summary>
      <returns>The converted MDX value object into a 32-bit signed integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Single">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a single-precision floating-point number.</summary>
      <returns>The converted MDX value object into a single-precision floating-point number.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~Microsoft.AnalysisServices.AdomdServer.Set">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />.</summary>
      <returns>The converted MDX value object into a set object.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to an 8-bit signed integer.</summary>
      <returns>The converted MDX value object into an 8-bit signed integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Byte">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to an 8-bit unsigned integer.</summary>
      <returns>The converted MDX value object into an 8-bit unsigned integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Double">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a double-precision floating-point number.</summary>
      <returns>The converted MDX value object into a double-precision floating-point number.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.MDXValue)~System.Int16">
      <summary>Explicitly converts an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object to a 16-bit signed integer.</summary>
      <returns>The converted MDX value object into a 16-bit signed integer.</returns>
      <param name="mpMDXValue">An <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.Member)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted member object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.Set)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted set object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(Microsoft.AnalysisServices.AdomdServer.Tuple)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted tuple object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts an 8-bit signed integer value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 8-bit signed integer into a MDX value object.</returns>
      <param name="parameter">An 8-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Boolean)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a Boolean value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted Boolean value into a MDX value object.</returns>
      <param name="parameter">A Boolean value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Byte)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts an 8-bit unsigned integer value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 8-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">An 8-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Char)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a Unicode character value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted Unicode character into a MDX value object.</returns>
      <param name="parameter">A Unicode character.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.DateTime)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a DateTime value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted DateTime object into a MDX value object.</returns>
      <param name="parameter">A DateTime object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Decimal)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a decimal value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted decimal object into a MDX value object.</returns>
      <param name="parameter">A decimal value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Double)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a double-precision floating-point number value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted double-precision floating-point number into a MDX value object.</returns>
      <param name="parameter">A double-precision floating-point number.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Int16)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a 16-bit signed integer value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 16-bit signed integer into a MDX value object.</returns>
      <param name="parameter">A 16-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Int32)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a 32-bit signed integer value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 32-bit signed integer into a MDX value object.</returns>
      <param name="parameter">A 32-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Int64)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a 64-bit signed integer value to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 64-bit signed integer into a MDX value object.</returns>
      <param name="parameter">A 64-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.Single)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a single-precision floating-point number to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted single-precision floating-point number into a MDX value object.</returns>
      <param name="parameter">A single-precision floating-point number.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.String)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a string to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted string object into a MDX value object.</returns>
      <param name="parameter">A string.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.UInt16)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a 16-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 16-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">A 16-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.UInt32)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a 32-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 32-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">A 32-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Explicit(System.UInt64)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Explicitly converts a 64-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 64-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">A 64-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(Microsoft.AnalysisServices.AdomdServer.Hierarchy)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted hierarchy object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(Microsoft.AnalysisServices.AdomdServer.Level)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted level object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(Microsoft.AnalysisServices.AdomdServer.Member)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted member object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(Microsoft.AnalysisServices.AdomdServer.Set)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted set object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(Microsoft.AnalysisServices.AdomdServer.Tuple)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted tuple object into a MDX value object.</returns>
      <param name="parameter">A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> object.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.SByte!System.Runtime.CompilerServices.IsSignUnspecifiedByte)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts an 8-bit signed integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 8-bit signed integer into a MDX value object.</returns>
      <param name="parameter">An 8-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Boolean)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a Boolean to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted Boolean value into a MDX value object.</returns>
      <param name="parameter">A Boolean.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Byte)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts an 8-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 8-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">An 8-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Char)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a Unicode character to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted Unicode character into a MDX value object.</returns>
      <param name="parameter">A Unicode character.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.DateTime)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a DateTime to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted DateTime object into a MDX value object.</returns>
      <param name="parameter">A DateTime.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Decimal)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a decimal to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted decimal object into a MDX value object.</returns>
      <param name="parameter">A decimal.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Double)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a double-precision floating-point number to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted double-precision floating-point number into a MDX value object.</returns>
      <param name="parameter">A double-precision floating-point number.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Int16)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a 16-bit signed integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 16-bit signed integer into a MDX value object.</returns>
      <param name="parameter">A 16-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Int32)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a 32-bit signed integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 32-bit signed integer into a MDX value object.</returns>
      <param name="parameter">A 32-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Int64)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a 64-bit signed integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 64-bit signed integer into a MDX value object.</returns>
      <param name="parameter">A 64-bit signed integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.Single)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a single-precision floating-point number to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted single-precision floating-point number into a MDX value object.</returns>
      <param name="parameter">A single-precision floating-point number.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.String)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a string to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted string object into a MDX value object.</returns>
      <param name="parameter">A string value.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.UInt16)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a 16-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 16-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">A 16-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.UInt32)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a 32-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 32-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">A 32-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.op_Implicit(System.UInt64)~Microsoft.AnalysisServices.AdomdServer.MDXValue">
      <summary>Implicitly converts a 64-bit unsigned integer to an <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>The converted 64-bit unsigned integer into a MDX value object.</returns>
      <param name="parameter">A 64-bit unsigned integer.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToBool">
      <summary>Returns a Boolean representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A Boolean.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToByte">
      <summary>Returns an 8-bit unsigned integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>An 8-bit unsigned integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToChar">
      <summary>Returns a Unicode character representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A Unicode character.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToDateTime">
      <summary>Returns a DateTime representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A DateTime.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToDecimal">
      <summary>Returns a decimal representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A decimal.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToDouble">
      <summary>Returns a double-precision floating-point number representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A double-precision floating-point number.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToHierarchy">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" /> representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A hierarchy object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToInt16">
      <summary>Returns a 16-bit signed integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A 16-bit signed integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToInt32">
      <summary>Returns a 32-bit signed integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A 32-bit signed integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToInt64">
      <summary>Returns a 64-bit signed integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A 64-bit signed integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToLevel">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A level object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToMember">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A member object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToSByte">
      <summary>Returns an 8-bit signed integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>An 8-bit signed integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToSet">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A set object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToSingle">
      <summary>Returns a single-precision floating-point number representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A single-precision floating-point number.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToString">
      <summary>Returns a string representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A string.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToTuple">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A tuple object.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToUInt16">
      <summary>Returns a 16-bit unsigned integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A 16-bit unsigned integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToUInt32">
      <summary>Returns a 32-bit unsigned integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A 32-bit unsigned integer.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MDXValue.ToUInt64">
      <summary>Returns a 64-bit unsigned integer representation of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MDXValue" /> object.</summary>
      <returns>A 64-bit unsigned integer.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Measure">
      <summary>Represents a measure within a cube or measure group.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Measure.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A String that contains the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A String that contains the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.DisplayFolder">
      <summary>Gets the display folder of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>The display folder.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Measure.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.Expression">
      <summary>Gets the MDX expression that is used to aggregate the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A String containing the MDX expression that is used to aggregate values for a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A String that contains the member name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.NumericPrecision">
      <summary>Gets the numeric precision value for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>An Integer that contains a value representing the numeric precision of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.NumericScale">
      <summary>Gets the numeric scale for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A Short that contains a value representing the numeric scale of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.An Int16 that contains a value representing the numeric scale of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.ParentCube">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdPropertyCollection" /> that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdPropertyCollection" /> that contains the properties associated with the measure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A String that contains the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Measure.Units">
      <summary>Gets a description of the unit of measure for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
      <returns>A String that contains a description of the unit of measure for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection">
      <summary>Gets a read-only, on-demand collection of <see cref="P:Microsoft.AnalysisServices.AdomdServer.CubeDef.Measures" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Measure[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> objects in the collection.</summary>
      <returns>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Find(System.String)">
      <summary>Returns the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> from the collection.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> with the specified name if found; otherwise, null.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>True if the access to the collection is synchronized; otherwise, false. Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />  to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />  to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />.</summary>
      <returns>An IEnumerator for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" /> instance.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MeasureCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MeasureCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Member">
      <summary>Represents a single member within a hierarchy, tuple, level, or member. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Member.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A String that contains the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.ChildCount">
      <summary>Gets the estimated count of child members contained in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A long that contains the estimated number of child members contained by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.Description">
      <summary>Gets the descriptive text of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A String that contains the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Member.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Member.GetChildren">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> that contains a collection of child members for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> that contains a collection of members for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.LevelDepth">
      <summary>Gets the ordinal position of the level that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>An int that contains the ordinal position within the parent hierarchy of the level that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.LevelName">
      <summary>Gets the name of the level that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A String that contains the name of the level that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A String that contains the member name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.Parent">
      <summary>Gets a reference to the parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />, if applicable, that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> that contains the parent member of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
      <exception cref="InvalidOperationException">The provider did not provide cube information for <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" /> members. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.ParentLevel">
      <summary>This property gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.Type">
      <summary>Gets the member type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum" /> enumeration value that represents the member type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
      <exception cref="InvalidOperationException">This property was not explicitly requested in the query, and was populated by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Member.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <returns>A String that contains the member key of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection">
      <summary>Gets a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Position" />, a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />, or a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Member[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects in the collection.</summary>
      <returns>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.GetEnumerator">
      <summary>Gets an enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MemberCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MemberCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array class in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MemberCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum">
      <summary>Represents the member type of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum.All">
      <summary>The member is an All member.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum.Formula">
      <summary>The member is a calculated member or calculated measure.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum.Measure">
      <summary>The member is a measure.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum.Regular">
      <summary>The member is a regular member.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MemberTypeEnum.Unknown">
      <summary>The member type is unknown.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute">
      <summary>Represents a mining attribute on a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> object and <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.AttributeID">
      <summary>Returns the identifier of the attribute.</summary>
      <returns>Returns an integer identifying the attribute.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.FeatureSelection">
      <summary>Determines if the mining attribute is feature-selected.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection" /> value.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.IsInput">
      <summary>Determines if this attribute is an input attribute.</summary>
      <returns>true if this is an input attribute; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.IsPredictable">
      <summary>Determines if this attribute is predictable.</summary>
      <returns>true if this is a predictable attribute; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.KeyColumn">
      <summary>Gets the key column for the attribute.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> representing the key column for the attribute.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.Name">
      <summary>Gets the fully qualified name of the attribute.</summary>
      <returns>A String containing the fully qualified name of the attribute.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.ShortName">
      <summary>Gets a shortened name of the attribute.</summary>
      <returns>A String that contains the shortened name of the attribute.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.UniqueName">
      <summary>Gets the unique name of the mining attribute,</summary>
      <returns>A string containing the name of the attribute.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttribute.ValueColumn">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> that represents the value column for the attribute.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> that represents the value column for the attribute.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningAttribute[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> if contained in the collection; otherwise, a null reference.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An Enumerator for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> to find.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> was not found in the collection.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.SyncRoot">
      <summary>Gets an Object that can be used to synchronize access to the collection.</summary>
      <returns>An Object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" /> to the specified array, starting at a particular array index.</summary>
      <param name="array">The one-dimensional array to hold the elements to be copied from the collection.</param>
      <param name="index">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an IEnumerator for iterating through the collection.</summary>
      <returns>An IEnumerator for iterating through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeParameter" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" />. </exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" />.</summary>
      <returns>true if the Enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection has changed, invalidating the enumerator. </exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="T:System.InvalidOperationException">The collection has changed, invalidating the enumerator. </exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution">
      <summary>Describes the distribution of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution.Custom">
      <summary>Represents a custom distribution, defined by the provider.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution.LogNormal">
      <summary>Represents a distribution in which the curve is elongated at the upper end and is skewed toward the lower end.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution.Missing">
      <summary>Represents an unspecified distribution.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution.Normal">
      <summary>Represents a distribution in which the curve has a normal (bell-curve) distribution.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution.Uniform">
      <summary>Represents a distribution in which all values are equally likely.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnType">
      <summary>Describes the type of the underlying data that a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> represents.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Boolean">
      <summary>Represents a Boolean data type (DBTYPE_BOOL).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Custom">
      <summary>Represents a Custom data type.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Date">
      <summary>Represents a Date data type (DBTYPE_DATE).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Double">
      <summary>Represents a Double data type (DBTYPE_R8).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Long">
      <summary>Represents a Long data type (DBTYPE_I8).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Missing">
      <summary>Represents an unspecified data type (DBTYPE_EMPTY).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Table">
      <summary>Represents a table data type (DBTYPE_HCHAPTER).</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningColumnType.Text">
      <summary>Represents a string data type (DBTYPE_WSTR).</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode">
      <summary>Represents the learned content of a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> in a hierarchical node format.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Ancestors">
      <summary>Gets a collection of all ancestor <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects.</summary>
      <returns>A collection of all ancestor <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Attribute">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> object that applies to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> object that applies to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Caption">
      <summary>Gets the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the current locale.</summary>
      <returns>A string containing the caption of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the current locale.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Children">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> containing the immediate children of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> containing the immediate children of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Descendants">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> containing all descendants of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> containing all descendants of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the current locale.</summary>
      <returns>A string containing the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the current locale.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Distribution">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" /> containing the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> objects that describe the distribution of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" /> containing the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> objects that describe the distribution of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.MarginalProbability">
      <summary>Gets the probability of reaching the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> from the parent of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A double representing the probability of reaching the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> from the parent of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.MarginalRule">
      <summary>Gets an XML description of the rule used when moving from the <see cref="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.ParentNode" /> to this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A String containing the XML description of the rule.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A String containing the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.NodeRule">
      <summary>Gets the rule associated with this node.</summary>
      <returns>A String containing the rule associated with this node.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.ParentMiningModel">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.ParentNode">
      <summary>Gets the immediate parent of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> representing the immediate parent of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.ParentUniqueName">
      <summary>Gets the unique name identifying the parent of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A string containing the unique name identifying the parent of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Probability">
      <summary>Gets the probability of reaching the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A double containing the probability of reaching the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> containing the properties of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> containing the properties of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Score">
      <summary>Gets the score of the node.</summary>
      <returns>A double representing the score of the node.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.ShortCaption">
      <summary>Gets a shortened caption for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A String containing the shortened caption.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Siblings">
      <summary>Gets a collection containing the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects on the same hierarchical level as the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> containing the sibling <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Support">
      <summary>Gets the number of cases that support the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>A double containing the number of cases that support the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.Type">
      <summary>Gets the type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />. </summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningNodeType" /> representing the type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>Returns a String representing the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> or <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningContentNode[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> if found in the collection; otherwise, null.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> to find.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnectionException">The connection was broken.</exception>
      <exception cref="InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> was not found in the collection. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.SyncRoot">
      <summary>Gets an Object that can be used to synchronize access to the collection.</summary>
      <returns>An Object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an enumerator for iterating through a collection.</summary>
      <returns>An IEnumerator used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution">
      <summary>Represents the distribution of a value in a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Attribute">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> object that applies to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> object that applies to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A string that contains the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.ParentMiningModel">
      <summary>Gets the mining model of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> representing the mining model of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.ParentNode">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> to which this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> applies.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> to which this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> applies.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Probability">
      <summary>Gets the probability of occurrence for the values represented by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A double that contains the probability of occurrence for the values represented by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Properties">
      <summary>Gets a collection of properties for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Support">
      <summary>Gets the number of cases that support the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A double that contains the number of cases that support the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Value">
      <summary>Gets the value represented by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> that contains the value.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.ValueType">
      <summary>Gets the type of the value represented by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueType" /> representing the type of the value.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistribution.Variance">
      <summary>Gets the variance of the value represented by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
      <returns>A double that contains the variance of the value.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningDistribution[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An Enumerator used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.SyncRoot">
      <summary>Gets an Object that can be used to synchronize access to the collection.</summary>
      <returns>An Object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an IEnumerator for iterating through a collection.</summary>
      <returns>An IEnumerator used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningDistributionCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection">
      <summary>Describes the type of attribute to return when calling <see cref="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.GetAttributes(Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection)" />. Alternatively describes a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection.All">
      <summary>Return all attributes.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection.Input">
      <summary>Return input-selected attributes. Also represents an attribute that is input-selected.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection.InputAndOutput">
      <summary>Return attributes that are both input-selected and output-selected. Also represents an attribute that is both input-selected and output-selected.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection.NotSelected">
      <summary>Return attributes that are not feature selected. Also represents an attribute that is not feature selected.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection.Output">
      <summary>Return output-selected attributes. Also represents an attribute that is output-selected.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection.Selected">
      <summary>Return attributes that are either input-selected or output-selected.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningModel">
      <summary>Represents a mining model of an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Algorithm">
      <summary>Gets a provider-specific name that describes the algorithm that is used to generate the model.</summary>
      <returns>The provider-specific name that describes the algorithm.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.AllowDrillThrough">
      <summary>Gets a value that indicates whether the mining model allows drillthrough.</summary>
      <returns>true if the mining model allows drillthrough; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Attributes">
      <summary>Gets the mining attributes associated with this mining model.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" /> that contains the attributes associated with this mining model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Columns">
      <summary>Gets a collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects for the model.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" /> that contains the columns of the mining model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Content">
      <summary>Gets a collection of top-level <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> objects for the model.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNodeCollection" /> that contains the top-level content nodes for the model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Created">
      <summary>Gets the date and time that the mining model was created.</summary>
      <returns>The date and time that the mining model was created.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Description">
      <summary>Gets the user-defined description of the mining model.</summary>
      <returns>A string that contains the description.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> class.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Filter">
      <summary>Gets the DMX filter which is used in selecting the structure cases used in training the mining model.</summary>
      <returns>The DMX filter.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.FindAttribute(System.Int32)">
      <summary>Returns the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> specified by an attribute ID.</summary>
      <returns>The mining attribute specified by the attribute ID.</returns>
      <param name="attributeIndex">The attribute ID associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttribute" /> to be returned.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.GetAttributeFullStatistics(System.Int32)">
      <summary>Returns the statistics for the specified attribute, including the statistics for each state of the attribute.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.AttributeStats" /> that contains the attribute statistics for the specified attribute.</returns>
      <param name="attributeIndex">The attribute identifier for which to retrieve statistics.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.GetAttributes(Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection)">
      <summary>Returns a collection of attributes based on the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningAttributeCollection" /> that contains the attributes with the specified feature selection.</returns>
      <param name="filter">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningFeatureSelection" /> on which to filter.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.GetAttributeSimpleStatistics(System.Int32)">
      <summary>Returns the statistics for the specified attribute, excluding the statistics for each state of the attribute.</summary>
      <returns>An <see cref="T:Microsoft.AnalysisServices.AdomdServer.AttributeStats" /> that contains the attribute statistics for the specified attribute.</returns>
      <param name="attributeIndex">The attribute identifier for which to retrieve statistics.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModel.GetNodeFromUniqueName(System.String)">
      <summary>Returns a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> from the <see cref="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Content" /> collection, using the <see cref="P:Microsoft.AnalysisServices.AdomdServer.MiningContentNode.UniqueName" /> property of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
      <returns>The requested <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> if found; otherwise, null.</returns>
      <param name="uniqueName">The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" /> to be retrieved.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.IsProcessed">
      <summary>Gets a value that indicates whether the mining model is populated.</summary>
      <returns>true if the mining model has been populated; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.LastProcessed">
      <summary>Gets the date and time that the mining model was last processed.</summary>
      <returns>A DateTime of the last date and time processed.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.LastUpdated">
      <summary>Gets the date and time that the mining model was last updated.</summary>
      <returns>A DateTime of the last date and time updated.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Name">
      <summary>Gets the name of the mining model.</summary>
      <returns>A string that contains the name of the mining model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Parameters">
      <summary>Gets a collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> objects that apply to the mining model.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> objects that apply to the mining model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Parent">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> to which this mining model belongs.</summary>
      <returns>The mining structure to which the mining model belongs.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.Properties">
      <summary>Gets a collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> objects that apply to the mining model.</summary>
      <returns>A collection of properties for the mining model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.TrainingSetSize">
      <summary>Gets the size of the training set used in training the mining model.</summary>
      <returns>The size of the training set.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModel.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</summary>
      <returns>The unique name of the mining model.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> objects that are contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> or <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningModel[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> from the collection by the model's name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> if contained in the collection; otherwise, a null reference (Nothing in Visual Basic).</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> to be found.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.GetEnumerator">
      <summary>Returns an enumerator for iterating through the collection.</summary>
      <returns>An enumerator for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> from the collection by using the model's index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> to be found.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> from the collection by using the model's name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> to be found.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> was not found in the collection.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Refresh">
      <summary>Refreshes the collection of mining models.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an IEnumerator for iterating through a collection.</summary>
      <returns>An IEnumerator that is used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn">
      <summary>Represents a column in a mining model.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.#ctor(ASMiningModel*)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> class with the specified mining model.</summary>
      <param name="in_pModel">The mining model.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Columns">
      <summary>Gets a collection containing the nested <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects.</summary>
      <returns>A collection containing the nested <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.ContainingColumn">
      <summary>Gets the name of the table column containing this column.</summary>
      <returns>The name of the table column containing this column, if this column is based on a table column; null otherwise.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Content">
      <summary>Gets the content type of the column.</summary>
      <returns>The content type of the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Description">
      <summary>Gets the user-description of the column.</summary>
      <returns>The user-description of the column.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Distribution">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution" /> for this column.</summary>
      <returns>The distribution of the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Filter">
      <summary>Gets a DMX filter which is used in selecting the nested table rows to be used in this table column.</summary>
      <returns>The DMX filter which is used in selecting the nested table rows.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Flags">
      <summary>Gets the modeling flag for the column.</summary>
      <returns>A comma-delimited string containing the modeling flags for the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.FullyQualifiedName">
      <summary>Gets the fully qualified name of the column.</summary>
      <returns>The fully qualified name of the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.IsInput">
      <summary>Gets a value that indicates whether the column is an input column.</summary>
      <returns>true if the column is an input column; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.IsPredictable">
      <summary>Gets a value that indicates whether the column is predictable.</summary>
      <returns>true if the column is predictable; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.IsProcessed">
      <summary>Gets a value that indicates whether the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> is processed.</summary>
      <returns>true if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> is processed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.IsRelatedToKey">
      <summary>Gets a value that indicates whether the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> is related to the key.</summary>
      <returns>true if the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> is related to the key; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.IsTable">
      <summary>Gets a value that indicates whether the column is a table datatype.</summary>
      <returns>true if the column contains a table datatype; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.LastProcessed">
      <summary>Gets the date and time the mining model column was last processed.</summary>
      <returns>The last date and time processed.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.LastUpdated">
      <summary>Gets the date and time the mining model column was last updated.</summary>
      <returns>The last date and time updated.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Name">
      <summary>Gets the name of the column.</summary>
      <returns>The name of the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Parent">
      <summary>Gets the parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</summary>
      <returns>The parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.ParentMiningModel">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> to which this column belongs.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> to which this column belongs.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.PredictionScore">
      <summary>Gets the prediction score for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
      <returns>The prediction score.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> containing the properties for the column.</summary>
      <returns>A collection of properties for the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.RelatedAttribute">
      <summary>Gets the name of the related column for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
      <returns>The name of the related column for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.StructureColumn">
      <summary>Gets the name of the structure column for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
      <returns>The name of the structure column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Type">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnType" /> of the column.</summary>
      <returns>The type of the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn.Values">
      <summary>Gets a collection of possible values associated with the column.</summary>
      <returns>A collection of possible values associated with the column.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningModelColumn[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> if contained in the collection; otherwise, a null reference (Nothing in Visual Basic).</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An Enumerator used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> to find.</param>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdErrorResponseException">The provider returned an error in response.</exception>
      <exception cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdUnknownResponseException">The provider sent an unrecognizable response.</exception>
      <exception cref="InvalidOperationException">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> was not found in the collection. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.SyncRoot">
      <summary>Gets an Object that can be used to synchronize access to the collection.</summary>
      <returns>An Object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an IEnumerator for iterating through a collection.</summary>
      <returns>An IEnumerator used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningModelColumnCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningNodeType">
      <summary>Represents the type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.ArimaAutoRegressive">
      <summary>The node that contains the autoregressive coefficient for a single term in an ARIMA model. (29)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.ArimaMovingAverage">
      <summary>The node that contains the moving average coefficient for a single term in an ARIMA model. (30)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.ArimaPeriodicStructure">
      <summary>The node that represents a periodic structure in an ARIMA model. (28)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.ArimaRoot">
      <summary>The root node of an ARIMA model. (27)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.AssociationRule">
      <summary>The node represents an association rule detected by the algorithm. (8)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Cluster">
      <summary>The node represents a cluster detected by the algorithm. (5)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.CustomBase">
      <summary>Represents the starting point for custom node types. Custom node types must be integers greater in value than this constant. This type is used by plug-in algorithms.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Distribution">
      <summary>The node represents the terminal node, or leaf node, of a classification tree. (4)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.InputAttribute">
      <summary>The node corresponds to a predictable attribute. (10)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.InputAttributeState">
      <summary>The node contains statistics about the states of an input attribute. (11)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Interior">
      <summary>The node represents an interior split node in a classification tree. (3)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.ItemSet">
      <summary>The node represents an itemset detected by the algorithm. (7)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Model">
      <summary>The root content node. This node applies to all algorithms. (1)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NaiveBayesMarginalStatNode">
      <summary>The node containing marginal statistics about the training set, stored in a format used by the Naïve Bayes algorithm. (26)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetHiddenLayer">
      <summary>The node that groups together the nodes that describe the hidden layer. This type is used with neural network algorithms. (19)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetHiddenNode">
      <summary>The node is a node of the hidden layer. This type is used with neural network algorithms. (22)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetInputLayer">
      <summary>The node that groups together the nodes of the input layer. This type is used with neural network algorithms. (18)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetInputNode">
      <summary>The node is a node of the input layer. This node will usually match an input attribute and the corresponding states. This type is used with neural network algorithms. (21)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetMarginalNode">
      <summary>The node containing marginal statistics about the training set, stored in a format used by the algorithm. This type is used with neural network algorithms. (24)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetOutputLayer">
      <summary>The node that groups together the nodes of the output layer. This type is used with neural network algorithms. (21)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetOutputNode">
      <summary>The node is a node of the output layer. This node will usually match an output attribute and the corresponding states. This type is used with neural network algorithms. (23)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.NNetSubnetwork">
      <summary>The node contains one sub-network. This type is used with neural network algorithms. (17)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.PredictableAttribute">
      <summary>The node corresponds to a predictable attribute. (9)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.RegressionTreeRoot">
      <summary>The node is the root of a regression tree. (25)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Sequence">
      <summary>The top node for a Markov model component of a sequence cluster. This node will have a node of type Cluster as a parent, and children of type Transition. (13)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.TimeSeries">
      <summary>The non-root node of a time series tree. (15)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Transition">
      <summary>The node representing a row of a Markov transition matrix. This node will have a node of type Sequence as a parent, and no children. (14)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Tree">
      <summary>The node is the root node of a classification tree. (2)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.TsTree">
      <summary>The root node of a time series tree that corresponds to a predictable time series. (16)</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningNodeType.Unknown">
      <summary>An unknown node type. (6)</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter">
      <summary>Represents an algorithm-specific parameter on the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameter.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameter.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameter.Name">
      <summary>Gets the name of the parameter.</summary>
      <returns>A string that contains the name of the parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameter.Properties">
      <summary>Gets the properties for the mining parameter.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties for the parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameter.UniqueName">
      <summary>Gets the unique name of the parameter.</summary>
      <returns>The unique name of the parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameter.Value">
      <summary>Gets the value of the mining parameter.</summary>
      <returns>A string that contains the value of the parameter.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningParameter[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> if contained in the collection; otherwise, a null reference.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>An Enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> from the collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> to return.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the collection.</summary>
      <returns>An object that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an IEnumerator for iterating through the collection.</summary>
      <returns>An IEnumerator for iterating through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningParameterCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningService">
      <summary>Represents a mining algorithm available from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Context" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningService.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.AllowsDuplicateKey">
      <summary>Gets a value that indicates whether the mining service allows cases to have duplicate keys.</summary>
      <returns>true if the mining service allows cases to have duplicate keys; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.AllowsIncrementalInsert">
      <summary>Gets a value that indicates whether the mining service allows additional INSERT INTO statements after the initial training.</summary>
      <returns>true if the mining service allows additional INSERT INTO statements after the initial training; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.AllowsPMMLInitialization">
      <summary>Gets a value that indicates whether the mining service allows the creation of a data mining model (including the structure and the content) based on an XML.</summary>
      <returns>true if the mining service allows the creation of a data mining model (including the structure and the content) based on an XML; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.AvailableParameters">
      <summary>Gets the mining service parameters available to this service.</summary>
      <returns>A collection of mining service parameters available to this service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.Control">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceControl" /> for the mining service model.</summary>
      <returns>The level of control for the mining service model.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.Description">
      <summary>Gets the description of this mining service.</summary>
      <returns>The description of this mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.DisplayName">
      <summary>Gets the localizable display name for the mining service.</summary>
      <returns>The localizable display name.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningService.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.ExpectedQuality">
      <summary>Gets the expected quality of the model produced with this mining service.</summary>
      <returns>The expected quality of the model produced with this mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.Guid">
      <summary>Gets the GUID for the mining service.</summary>
      <returns>A GUID uniquely identifying the mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.Name">
      <summary>Gets the name of the mining service.</summary>
      <returns>The name of the mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.PredictionComplexity">
      <summary>Gets the expected time for predicting with the mining service.</summary>
      <returns>The expected time for predicting with the mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.PredictionLimit">
      <summary>Gets the maximum number of predictions the mining service can provide.</summary>
      <returns>The maximum number of predictions the mining service can provide. 0 represents no limit.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.Properties">
      <summary>Gets the properties of this mining service.</summary>
      <returns>A collection of properties for this mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.Scaling">
      <summary>Gets the scalability of the mining service.</summary>
      <returns>The scalability of the mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.SupportedDistributionFlags">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution" /> flags that are supported by the mining service.</summary>
      <returns>An array of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution" /> flags.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.SupportedInputContentTypes">
      <summary>Gets the content types supported by the mining service.</summary>
      <returns>An array of String objects that contains the types supported by the mining service. Possible values include:KEYKEY SEQUENCEKEY TIMEDISCRETECONTINUOUSDISCRETIZED([args])ORDEREDCYCLICALPROBABILITYVARIANCESTDEVSUPPORTPROBABILITY_VARIANCEPROBABILITY_STDEV</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.SupportedModelingFlags">
      <summary>Gets the modeling flags supported by the mining service.</summary>
      <returns>An array of String objects that contains the modeling flags supported by the mining service. Possible values include:MODEL_EXISTENCE_ONLYNOT NULLREGRESSORAlgorithm-specific strings.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.SupportedPredictionContentTypes">
      <summary>Gets the supported prediction content types for the mining service.</summary>
      <returns>An array of String objects that contains the supported prediction content types for the mining service. Possible strings include:DISCRETECONTINUOUSDISCRETIZED([args])ORDEREDCYCLICALPROBABILITYVARIANCESTDEVSUPPORTPROBABILITY_VARIANCEPROBABILITY_STDEV</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.SupportsDMDimensions">
      <summary>Gets a value that indicates whether the mining service supports data mining dimensions.</summary>
      <returns>true if the mining service supports data mining dimensions; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.SupportsDrillThrough">
      <summary>Gets a value that indicates whether the mining service supports drillthrough.</summary>
      <returns>true if the mining service supports drillthrough; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.TrainingComplexity">
      <summary>Gets the expected time to train a model using this mining service.</summary>
      <returns>The expected time to train a model using this mining service.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningService.ViewerType">
      <summary>Gets the viewer recommended to view the results of this mining service.</summary>
      <returns>The type name of the viewer.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection">
      <summary>Represents a read-only collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningService[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> with the <paramref name="index" /> name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection.</summary>
      <returns>An Enumerator used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an IEnumerator for iterating through a collection.</summary>
      <returns>An IEnumerator used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection" />.</summary>
      <returns>true if the Enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceControl">
      <summary>Represents the level of control that the mining service algorithm exposes on the training operation .</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceControl.Cancel">
      <summary>The training of the algorithm can be canceled while still preserving content learned.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceControl.None">
      <summary>The training of the algorithm cannot be canceled without loosing content learned.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceControl.SuspendResume">
      <summary>The training operation can be resumed.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceControl.SuspendWithResult">
      <summary>The training operation can be suspended, and using the learned content will result in meaningful results.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceExpectedQuality">
      <summary>Describes the relative expected quality of the patterns found by the mining service algorithm.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceExpectedQuality.High">
      <summary>Represents a relatively high quality.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceExpectedQuality.Low">
      <summary>Represents a relatively low quality.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceExpectedQuality.Medium">
      <summary>Represents a relatively medium quality.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter">
      <summary>Represents an available parameter for the mining service.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.DefaultValue">
      <summary>Gets the default value for this parameter.</summary>
      <returns>A String containing the default value for this parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.Description">
      <summary>Gets the description of the parameter.</summary>
      <returns>A String containing the description of this parameter.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.IsRequired">
      <summary>Gets a Boolean indicating if this parameter is required.</summary>
      <returns>true if this parameter is required; false otherwise.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.Name">
      <summary>Gets the name of the parameter.</summary>
      <returns>A String containing the name of the parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.ParameterType">
      <summary>Gets the type of the parameter value.</summary>
      <returns>A String containing the type of the parameter value, in DBTYPE format.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.Properties">
      <summary>Gets the properties for the mining service parameter.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> containing the properties for the mining service parameter.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.ServiceName">
      <summary>Gets the service name for the mining service parameter.</summary>
      <returns>A String containing the service name.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter.ValueEnumeration">
      <summary>Gets a string that describes possible values for this parameter.</summary>
      <returns>A String that describes possible values for this parameter.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection">
      <summary>Represents a read-only collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.{dtor}">
      <summary>Releases the resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> with the <paramref name="index" /> name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection.</summary>
      <returns>An Enumerator used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> collection to the specified array and index.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an IEnumerator for iterating through a collection.</summary>
      <returns>An IEnumerator used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameterCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServicePredictionComplexity">
      <summary>Describes the relative expected complexity of executing a prediction with the mining service algorithm.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServicePredictionComplexity.High">
      <summary>Represents a relatively high complexity.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServicePredictionComplexity.Low">
      <summary>Represents a relatively low complexity.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServicePredictionComplexity.Medium">
      <summary>Represents a relatively medium complexity.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceScaling">
      <summary>Describes the relative expected scalability of the mining service algorithm.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceScaling.High">
      <summary>Represents a relatively high scalability.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceScaling.Low">
      <summary>Represents a relatively low scalability.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceScaling.Medium">
      <summary>Represents a relatively medium scalability.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceTrainingComplexity">
      <summary>Describes the relative expected complexity of training the mining service algorithm.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceTrainingComplexity.High">
      <summary>Represents a relatively high training complexity.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceTrainingComplexity.Low">
      <summary>Represents a relatively low training complexity.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningServiceTrainingComplexity.Medium">
      <summary>Represents a relatively medium training complexity.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure">
      <summary>Represents a mining structure on the server. This class cannot be inherited.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructure.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Caption">
      <summary>Gets the caption of the mining structure.</summary>
      <returns>The caption of the mining structure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Columns">
      <summary>Gets the columns within the mining structure.</summary>
      <returns>A collection of columns within the mining structure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Created">
      <summary>Gets the date and time the mining structure was created.</summary>
      <returns>The date and time the mining structure was created.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Description">
      <summary>Gets the description of the mining structure.</summary>
      <returns>The description of the mining structure.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> class.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.HoldoutActualSize">
      <summary>Gets the actual number of cases in the testing set.</summary>
      <returns>The actual number of cases in the testing set.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.HoldoutMaxCases">
      <summary>Gets the maximum number of cases to include in the testing set.</summary>
      <returns>The maximum number of cases to include in the testing set.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.HoldoutMaxPercent">
      <summary>Gets the number of cases to include in the testing set as a percentage of the complete data set.</summary>
      <returns>The maximum percentage of cases.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.HoldoutSeed">
      <summary>Gets the seed that is used to initialize random sampling.</summary>
      <returns>The seed that is used to initialize random sampling.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.IsProcessed">
      <summary>Gets a value that indicates whether the mining structure is processed.</summary>
      <returns>true if the mining structure is processed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.LastProcessed">
      <summary>Gets the date and time the mining structure was last processed.</summary>
      <returns>The date and time the mining structure was last processed.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.LastUpdated">
      <summary>Gets the date and time the mining structure was last updated.</summary>
      <returns>The date and time the mining structure was last updated.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.MiningModels">
      <summary>Gets the mining models in the mining structure.</summary>
      <returns>A collection of mining models in the mining structure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Name">
      <summary>Gets the name of the mining structure.</summary>
      <returns>The name of the mining structure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.Properties">
      <summary>Gets the properties of the mining structure.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties of the mining structure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructure.UniqueName">
      <summary>Gets the unique name that is associated with the mining structure.</summary>
      <returns>The unique name of the mining structure.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection">
      <summary>Represents a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> objects that are contained by an <see cref="T:Microsoft.AnalysisServices.AdomdServer.AdomdConnection" /> object.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.{dtor}">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningStructure[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Find(System.String)">
      <summary>Returns the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection by the structure's name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> with the given <paramref name="name" />.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> to be found.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.GetEnumerator">
      <summary>Returns an enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection.</summary>
      <returns>An enumerator that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection by using the structure's index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> to be found.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection by using the structure's name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> to be found.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Refresh">
      <summary>Refreshes the collection of mining structures.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that is used for iterating through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn">
      <summary>Represents the structure of a mining column.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" />.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Columns">
      <summary>Gets the nested columns for the mining structure column.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> containing the nested columns.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.ContainingColumn">
      <summary>Gets the name of the table column containing this mining structure column.</summary>
      <returns>The name of the table column containing this mining structure column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Content">
      <summary>Gets the content type of the mining structure column.</summary>
      <returns>The content type of the mining structure. Possible values include: KEYKEY SEQUENCEKEY TIMEDISCRETECONTINUOUSDISCRETIZED([args])ORDEREDSEQUENCE_TIMECYCLICALPROBABILITYVARIANCESTDEVSUPPORTPROBABILITY_VARIANCEPROBABILITY_STDEVORDERSEQUENCE</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Description">
      <summary>Gets the description of the mining structure column.</summary>
      <returns>The description of the mining structure column.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> class.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Distribution">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution" /> for this mining structure column.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnDistribution" /> for this mining structure column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Flags">
      <summary>Gets the modeling flags for the mining structure column.</summary>
      <returns>A comma-delimited string containing the modeling flags for the mining structure column. The possible value is NOT NULL.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.FullyQualifiedName">
      <summary>Gets the fully-qualified name for the mining structure column.</summary>
      <returns>The fully-qualified name for the mining structure column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.IsProcessed">
      <summary>Gets a value that indicates whether this column is processed.</summary>
      <returns>true if this column is processed; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.IsRelatedToKey">
      <summary>Gets a value that indicates whether this column is related to the key.</summary>
      <returns>true if this column is related to the key; otherwise, false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.LastProcessed">
      <summary>Gets the date and time the mining structure column was last processed.</summary>
      <returns>A DateTime of the last date and time processed.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.LastUpdated">
      <summary>Gets the date and time the mining structure column was last updated.</summary>
      <returns>A DateTime of the last date and time updated.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Name">
      <summary>Gets the name of the mining structure column.</summary>
      <returns>The name of the mining structure column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Parent">
      <summary>Gets the parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" />.</summary>
      <returns>The parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.ParentMiningStructure">
      <summary>Gets the parent <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> to which this mining structure column belongs.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" /> representing the parent mining structure.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> containing properties for the mining structure column.</summary>
      <returns>A collection of properties for the mining structure column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.RelatedAttribute">
      <summary>Gets the name of the target column that the current column either relates to, or is a special property of.</summary>
      <returns>A String containing the name of the target column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.Type">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningColumnType" /> representing the type of the column.</summary>
      <returns>The type of the column.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection">
      <summary>Represents a read-only collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" />. This class cannot be inherited.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Find(System.String)">
      <summary>Finds the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> with the <paramref name="index" /> name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection.</summary>
      <returns>An Enumerator for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.IsSynchronized">
      <summary>Gets a value that indicates whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always returns false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Returns an IEnumerator for iterating through a collection.</summary>
      <returns>An IEnumerator that is used to iterate through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumnCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningValue">
      <summary>Represents a value in the mining distribution or mining column. This class cannot be inherited. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValue.DiscretizedMax">
      <summary>Gets the maximum value of the discretized range.</summary>
      <returns>If the value is numeric, a numeric value that represents the maximum value of the range for the discretized value. If the value is a date, returns a DateTime object that represents the latest date in the range.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValue.DiscretizedMid">
      <summary>Gets the midpoint of the discretized range.</summary>
      <returns>If the range contains numeric values, returns a numeric value that represents the sum of the minimum and maximum values for the range, divided by two: that is,(DiscretizedMin+DiscretizedMax)/2. If the range contains dates, returns a date at the midpoint of the range. </returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValue.DiscretizedMin">
      <summary>Gets the minimum value of the discretized range.</summary>
      <returns>If the value is numeric, a numeric value that represents the minimum value of the range for the discretized value. If the value is a date, returns a DateTime object that represents the earliest date in the range.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValue.GetAsDouble">
      <summary>Returns the value that this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> represents, converted to a double.</summary>
      <returns>A double that represents the value of this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValue.Index">
      <summary>Gets the index of this mining value.</summary>
      <returns>An integer that represents the index of this mining value.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValue.ToString">
      <summary>Returns the value that this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> represents, converted to a string.</summary>
      <returns>A string that represents the value of this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValue.Value">
      <summary>Gets the value represented by this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" />.</summary>
      <returns>An object that contains the value represented by this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValue.ValueType">
      <summary>Gets the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueType" /> of this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueType" /> that represents the type of this <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection">
      <summary>Represents a read-only collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.MiningValue[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> collection to the specified array and index.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> collection.</summary>
      <returns>An Enumerator for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> collection to the specified array and index.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an instance of the IEnumerator for iterating through the collection.</summary>
      <returns>An IEnumerator for iterating through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" />, if the Enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The Enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.MiningValueCollection.Enumerator.Reset">
      <summary>Sets the Enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the Enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.MiningValueType">
      <summary>Represents the type of the value of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" /> object and the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningValue" /> object.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.AutoDetect">
      <summary>Represents a value of an inferred type, based on contextual information.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.AutoRegressiveOrder">
      <summary>Represents a value that contains the number of autoregressive series.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Boolean">
      <summary>Represents a Boolean type.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Coefficient">
      <summary>Represents a coefficient that is applied to the value of the associated attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Continuous">
      <summary>Represents a continuous value of the associated attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.DifferenceOrder">
      <summary>Represents a value that indicates how many times the series is differentiated.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Discrete">
      <summary>Represents a discrete value of the associated attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Discretized">
      <summary>Represents a discretized value of the associated attribute. The value will be a formatted string that describes the respective discretization buckets.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Existing">
      <summary>Represents a value that describes the existence of an attribute. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Intercept">
      <summary>Represents a value that contains the intercept in a regression formula.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Missing">
      <summary>Represents the Missing state of any attribute.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.MovingAverageOrder">
      <summary>Represents a value that represents the number of moving averages in a series.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.NodeUniqueName">
      <summary>Represents the unique identifier of another content node in a model.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Other">
      <summary>Represents a custom value that is defined by the algorithm.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.Periodicity">
      <summary>Represents a value that contains the number of periodic structures in a model.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.PreRenderedString">
      <summary>Represents a custom value that the algorithm renders as a string. No formatting was applied by the object model.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.RegressorStatistics">
      <summary>Represents a value that contains the algorithm-specific statistics concerning a single regressor.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.MiningValueType.ScoreGain">
      <summary>Represents a value that contains a score gain for an attribute.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.NamedSet">
      <summary>Represents a named set for a given cube.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSet.{dtor}">
      <summary>Releases the unmanaged resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSet.Description">
      <summary>Gets the description of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
      <returns>A String that contains the description for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSet.Dispose">
      <summary>Releases the unmanaged resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSet.Expression">
      <summary>Gets the MDX set expression that defines the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
      <returns>A String that contains the set expression that defines the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSet.Name">
      <summary>Gets the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
      <returns>A String that contains the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSet.ParentCube">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" /> that contains the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSet.Properties">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> that contains the properties associated with the named set.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSet.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection">
      <summary>Gets a read-only, on-demand collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.NamedSet[],System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> from the collection.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> if found in the collection; otherwise, null.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.GetEnumerator">
      <summary>Gets an enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> from the collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> to find.</param>
      <exception cref="InvalidOperationException">The specified member did not exist in the collection.</exception>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.SyncRoot">
      <summary>Gets an <see cref="T:System.Object" /> that can be used to synchronize access to the collection.</summary>
      <returns>An <see cref="T:System.Object" /> that can be used to synchronize access to the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.System::Collections::ICollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator for iterating through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.NamedSetCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.PlugInAttribute">
      <summary>Generates runtime information about the PlugInAttribute custom attribute.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PlugInAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PlugInAttribute" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PlugInAttribute.{dtor}">
      <summary>Finalizes an open instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PlugInAttribute" /> class.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Property">
      <summary>Represents a property of various ADOMD.NET objects. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Property.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Property.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Property.Name">
      <summary>Gets a String representing the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</summary>
      <returns>A String representing the name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Property.Type">
      <summary>Gets a Type representing the type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</summary>
      <returns>A Type representing the type of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Property.UniqueName">
      <summary>Gets the unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</summary>
      <returns>The unique name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Property.Value">
      <summary>Gets an Object representing the value of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</summary>
      <returns>An Object representing the value of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection">
      <summary>Contains a read-only collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.CellSet" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.CopyTo(Microsoft.AnalysisServices.AdomdServer.Property[],System.Int32)">
      <summary>Copies the elements of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing.</param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the elements of the collection to the specified array.</summary>
      <param name="array">An instance of a one-dimensional Array in which to copy the elements of the collection. The Array must have zero-based indexing. </param>
      <param name="index">The zero-based index at which to begin copying.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Find(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection by its name.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> with the <paramref name="name" /> name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection.</summary>
      <returns>An PropertyCollection.Enumerator for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.IsSynchronized">
      <summary>Gets a value indicating whether access to the collection is synchronized (thread safe).</summary>
      <returns>Always false.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection by its name. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> with the specified name.</returns>
      <param name="name">The name of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> to find.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.SyncRoot">
      <summary>Gets an object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection.</summary>
      <returns>An object that can be used to synchronize access to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator for iterating through the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for iterating through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Property" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.PropertyCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.PropertyCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.SafeToPrepareAttribute">
      <summary>Marks the methods in the assembly that are safe to run with the <see cref="P:Microsoft.AnalysisServices.AdomdServer.Context.ExecuteForPrepare" /> property set to true.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SafeToPrepareAttribute.#ctor(System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.SafeToPrepareAttribute" /> class. </summary>
      <param name="safeToPrepare">A value that indicates whether the associated method is safe to run with the <see cref="P:Microsoft.AnalysisServices.AdomdServer.Context.ExecuteForPrepare" /> property set to true.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SafeToPrepareAttribute.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.SafeToPrepareAttribute" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.SafeToPrepareAttribute.IsSafeToPrepare">
      <summary>Gets a value that indicates whether the associated method is safe to run with the <see cref="P:Microsoft.AnalysisServices.AdomdServer.Context.ExecuteForPrepare" /> property set to true.</summary>
      <returns>true if the associated method is safe to run with the <see cref="P:Microsoft.AnalysisServices.AdomdServer.Context.ExecuteForPrepare" /> property set to true; otherwise, false.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType">
      <summary>Represents the object type of an object retrieved by the <see cref="M:Microsoft.AnalysisServices.AdomdServer.CubeDef.GetSchemaObject(Microsoft.AnalysisServices.AdomdServer.SchemaObjectType,System.String)" /> method of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.CubeDef" />. </summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeDimension">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Dimension" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeHierarchy">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Hierarchy" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeKpi">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Kpi" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeLevel">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Level" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMeasure">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Measure" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMember">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningContentNode">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningContentNode" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningDistribution">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningDistribution" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningModel">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModel" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningModelColumn">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningModelColumn" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningService">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningService" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningServiceParameter">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningServiceParameter" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningStructure">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructure" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeMiningStructureColumn">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.MiningStructureColumn" />.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.SchemaObjectType.ObjectTypeNamedSet">
      <summary>Object is a <see cref="T:Microsoft.AnalysisServices.AdomdServer.NamedSet" />.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Server">
      <summary>Represents the server object for the running instance of Analysis Services.</summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Server.Culture">
      <summary>Gets the <see cref="T:System.Globalization.CultureInfo" /> object for the server.</summary>
      <returns>A <see cref="T:System.Globalization.CultureInfo" /> object that has the culture information for the server.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Server.Name">
      <summary>Gets the name of the server that has the running instance of Analysis Services.</summary>
      <returns>A string value with the name of the server.</returns>
    </member>
    <member name="E:Microsoft.AnalysisServices.AdomdServer.Server.SessionClosing">
      <summary>Occurs when the session between the server that has the running instance of Analysis Services and the client starts to close, but before the session is finally closed.</summary>
    </member>
    <member name="E:Microsoft.AnalysisServices.AdomdServer.Server.SessionOpened">
      <summary>Occurs when the server that has the running instance of Analysis Services starts a session with the client.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Set">
      <summary>Represents an ordered collection of zero or more tuples. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Set.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Set.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Set.GetEnumerator">
      <summary>Gets an instance of the Enumerator class for iterating through the collection.</summary>
      <returns>An IEnumerator used for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Set.Hierarchies">
      <summary>Gets a reference to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> collection referenced by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />.</summary>
      <returns>A reference to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.HierarchyCollection" /> collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Set.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> object that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Set.Tuples">
      <summary>Gets a reference to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" /> collection referenced by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />.</summary>
      <returns>A reference to the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" /> collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.SetBuilder">
      <summary>Provides the functionality to create immutable sets.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SetBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.SetBuilder" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SetBuilder.#ctor(Microsoft.AnalysisServices.AdomdServer.Tuple)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.SetBuilder" /> class, with a given <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</summary>
      <param name="tuple">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> on which to initially base the set.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SetBuilder.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.SetBuilder" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SetBuilder.Add(Microsoft.AnalysisServices.AdomdServer.Tuple)">
      <summary>Adds a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> to the set.</summary>
      <param name="tuple">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> to be appended to the set.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.SetBuilder.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects in the set.</summary>
      <returns>The count of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects in the set.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SetBuilder.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.SetBuilder" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.SetBuilder.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> object.</summary>
      <returns>The specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</returns>
      <param name="index">The index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> in the set.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.SetBuilder.ToSet">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" /> based on the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects.</summary>
      <returns>A set object based on the specified tuple objects.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.StateStats">
      <summary>Represents the statistics for a single attribute state.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.StateStats.AdjustedProbability">
      <summary>Represents the adjusted probability for the attribute state.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.StateStats.Probability">
      <summary>Represents the probability for the attribute state.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.StateStats.ProbabilityVariance">
      <summary>Represents the probability variance for the attribute state.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.StateStats.Support">
      <summary>Represents the support for the attribute state.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.StateStats.Value">
      <summary>Represents the value for the attribute state.</summary>
    </member>
    <member name="F:Microsoft.AnalysisServices.AdomdServer.StateStats.Variance">
      <summary>Represents the variance for the attribute state.</summary>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.Tuple">
      <summary>Represents an ordered collection of members from different hierarchies.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Tuple.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.Tuple.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Tuple.Members">
      <summary>Gets an instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> class from the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.MemberCollection" /> that contains the members of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.Tuple.TupleOrdinal">
      <summary>Gets the ordinal position of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</summary>
      <returns>An Integer that contains the ordinal position of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.TupleBuilder">
      <summary>Provides the functionality to create immutable tuples.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleBuilder" /> class.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.#ctor(Microsoft.AnalysisServices.AdomdServer.Member)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleBuilder" /> class with a given <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</summary>
      <param name="member">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> on which to initially base the tuple.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleBuilder" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.Add(Microsoft.AnalysisServices.AdomdServer.Member)">
      <summary>Adds a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> to the tuple.</summary>
      <param name="member">The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> to be appended to the tuple.</param>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects in the tuple.</summary>
      <returns>The count of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects in the tuple.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleBuilder" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> object.</summary>
      <returns>The specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" />.</returns>
      <param name="index">The index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> in the tuple.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleBuilder.ToTuple">
      <summary>Gets a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> based on the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Member" /> objects.</summary>
      <returns>A <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" />.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection">
      <summary>Gets a read-only collection of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects contained by a <see cref="T:Microsoft.AnalysisServices.AdomdServer.Set" />.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Count">
      <summary>Gets the number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects in the collection.</summary>
      <returns>The number of <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects in the collection.</returns>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.GetEnumerator">
      <summary>Gets an Enumerator for iterating through the collection.</summary>
      <returns>A TupleEnumerator for iterating through the collection.</returns>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Item(System.Int32)">
      <summary>Gets the specified <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> from the collection by its index. In Microsoft Visual C#, this property is the indexer for the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" /> class.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> at the specified index.</returns>
      <param name="index">The zero-based index of the <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> to find.</param>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.System::Collections::IEnumerable.GetEnumerator">
      <summary>Gets an enumerator that iterates through the collection.</summary>
      <returns>An enumerator that iterates through the collection.</returns>
    </member>
    <member name="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator">
      <summary>Supports iterating over a <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" /> and reading its individual <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> objects.</summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator.{dtor}">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator" />. </summary>
    </member>
    <member name="P:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator.Current">
      <summary>Gets the current <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" />.</summary>
      <returns>The <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" />, if the enumerator has not passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The enumerator is currently before the first or after the last <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" />. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator.Dispose">
      <summary>Releases all resources used by the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator" />. </summary>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator.MoveNext">
      <summary>Moves to the next <see cref="T:Microsoft.AnalysisServices.AdomdServer.Tuple" /> in the <see cref="T:Microsoft.AnalysisServices.AdomdServer.TupleCollection" />.</summary>
      <returns>true if the enumerator was successfully advanced to the next element; false if the enumerator has passed the end of the collection.</returns>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
    <member name="M:Microsoft.AnalysisServices.AdomdServer.TupleCollection.Enumerator.Reset">
      <summary>Sets the enumerator to its initial position, which is before the first element in the collection.</summary>
      <exception cref="InvalidOperationException">The collection has changed, invalidating the enumerator. For more information about this exception, see InvalidOperationException Class.</exception>
    </member>
  </members>
</doc>