﻿@model IEnumerable<AppTech.MSMS.Domain.Models.SatelliteFaction>
@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}

<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.SatelliteProviderID')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span>إضافة فئة جديدة</span>
    </a>
</p>
<div id="list">
    @Html.Partial("_factions")
</div>
    @Html.Partial("_Modal")

    <script>
        function openModal(id) {
            i('open modal id' + id);
            openViewAsModal('Satellite/SatelliteFaction/AddOrEditFaction?ID=' + id, " جديد");
        }
    </script>
