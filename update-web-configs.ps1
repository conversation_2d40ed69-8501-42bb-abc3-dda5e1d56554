# سكريبت تحديث ملفات web.config بسلاسل الاتصال

Write-Host "تحديث ملفات web.config..." -ForegroundColor Green

# سلسلة الاتصال (يجب تعديلها حسب إعدادات SQL Server)
$serverInstance = "localhost\SQLEXPRESS"  # أو localhost أو .\SQLEXPRESS
$databaseName = "AppTechMSMS"
$connectionString = "Data Source=$serverInstance;Initial Catalog=$databaseName;Integrated Security=True;MultipleActiveResultSets=True"
$entityConnectionString = "metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=`"$connectionString`""

Write-Host "سلسلة الاتصال: $connectionString" -ForegroundColor Cyan

# مسارات ملفات web.config
$webConfigPaths = @(
    "wwwroot\api\Web.config",
    "wwwroot\client\Web.config", 
    "wwwroot\portal\Web.config"
)

foreach ($configPath in $webConfigPaths) {
    if (Test-Path $configPath) {
        Write-Host "تحديث: $configPath" -ForegroundColor Yellow
        
        try {
            # قراءة ملف XML
            [xml]$webConfig = Get-Content $configPath
            
            # البحث عن عقدة connectionStrings
            $connectionStringsNode = $webConfig.configuration.connectionStrings
            
            if ($connectionStringsNode -eq $null) {
                Write-Host "إنشاء عقدة connectionStrings جديدة" -ForegroundColor Gray
                $connectionStringsNode = $webConfig.CreateElement("connectionStrings")
                $webConfig.configuration.AppendChild($connectionStringsNode)
            } else {
                # حذف سلاسل الاتصال الموجودة
                $connectionStringsNode.RemoveAll()
            }
            
            # إضافة سلسلة الاتصال الافتراضية
            $defaultConnection = $webConfig.CreateElement("add")
            $defaultConnection.SetAttribute("name", "DefaultConnection")
            $defaultConnection.SetAttribute("connectionString", $connectionString)
            $defaultConnection.SetAttribute("providerName", "System.Data.SqlClient")
            $connectionStringsNode.AppendChild($defaultConnection)
            
            # إضافة سلسلة اتصال Entity Framework
            $entityConnection = $webConfig.CreateElement("add")
            $entityConnection.SetAttribute("name", "AppTechMSMSEntities")
            $entityConnection.SetAttribute("connectionString", $entityConnectionString)
            $entityConnection.SetAttribute("providerName", "System.Data.EntityClient")
            $connectionStringsNode.AppendChild($entityConnection)
            
            # إضافة سلسلة اتصال ELMAH
            $elmahConnection = $webConfig.CreateElement("add")
            $elmahConnection.SetAttribute("name", "elmah-sql")
            $elmahConnection.SetAttribute("connectionString", $connectionString)
            $elmahConnection.SetAttribute("providerName", "System.Data.SqlClient")
            $connectionStringsNode.AppendChild($elmahConnection)
            
            # تحديث إعداد ConnectionStringSource
            $appSettings = $webConfig.configuration.appSettings
            if ($appSettings) {
                $connectionSourceSetting = $appSettings.add | Where-Object { $_.key -eq "ConnectionStringSource" }
                if ($connectionSourceSetting) {
                    $connectionSourceSetting.value = "ConfigFile"
                    Write-Host "تم تحديث ConnectionStringSource إلى ConfigFile" -ForegroundColor Gray
                }
            }
            
            # حفظ الملف
            $webConfig.Save($configPath)
            Write-Host "✓ تم تحديث $configPath بنجاح" -ForegroundColor Green
            
        } catch {
            Write-Host "✗ خطأ في تحديث $configPath : $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ الملف غير موجود: $configPath" -ForegroundColor Red
    }
}

Write-Host "`nانتهى تحديث ملفات web.config" -ForegroundColor Green
Write-Host "ملاحظة: تأكد من صحة اسم SQL Server Instance في السكريبت" -ForegroundColor Yellow
