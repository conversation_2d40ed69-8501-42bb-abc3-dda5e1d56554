# 🚀 أوامر النشر والتشغيل - Yemen Client Management System

## 📋 **الجزء الأول: إعداد الخادم الجديد**

### **1. تثبيت المتطلبات الأساسية:**
```powershell
# تثبيت Node.js (إذا لم يكن مثبت)
# تحميل من: https://nodejs.org/ (النسخة LTS)

# تثبيت PostgreSQL (إذا لم يكن مثبت)
# تحميل من: https://www.postgresql.org/download/windows/

# التحقق من التثبيت
node --version
npm --version
psql --version
```

### **2. إنشاء قاعدة البيانات:**
```sql
-- الاتصال بـ PostgreSQL كمدير
psql -U postgres

-- إنشاء قاعدة البيانات
CREATE DATABASE yemclient_db;

-- إنشاء مستخدم (اختياري)
CREATE USER yemclient_user WITH PASSWORD 'yemen123';
GRANT ALL PRIVILEGES ON DATABASE yemclient_db TO yemclient_user;

-- الخروج
\q
```

## 📁 **الجزء الثاني: نسخ ملفات النظام**

### **1. إنشاء هيكل المجلدات:**
```powershell
# إنشاء المجلد الرئيسي
New-Item -ItemType Directory -Path "C:\yemclinet" -Force

# إنشاء المجلدات الفرعية
New-Item -ItemType Directory -Path "C:\yemclinet\server" -Force
New-Item -ItemType Directory -Path "C:\yemclinet\client" -Force
New-Item -ItemType Directory -Path "C:\yemclinet\server\certs" -Force
```

### **2. نسخ الملفات الأساسية:**
```powershell
# نسخ ملفات الخادم (من الخادم الحالي)
# - working-server.js
# - package.json
# - .env
# - prisma/ (المجلد كاملاً)

# نسخ ملفات العميل
# - client/dist/ (المجلد كاملاً)

# نسخ ملفات التشغيل
# - start-system.bat
```

## 🔧 **الجزء الثالث: بناء وتشغيل النظام**

### **1. تثبيت المكتبات:**
```powershell
# الانتقال لمجلد الخادم
cd C:\yemclinet\server

# تثبيت المكتبات
npm install

# التحقق من التثبيت
npm list
```

### **2. إعداد قاعدة البيانات:**
```powershell
# في مجلد C:\yemclinet\server

# إنشاء جداول قاعدة البيانات
npx prisma generate
npx prisma migrate deploy

# إدخال البيانات الأساسية
npx prisma db seed
```

### **3. تشغيل النظام (HTTP فقط):**
```powershell
# الطريقة الأولى: تشغيل مباشر
cd C:\yemclinet\server
node working-server.js

# الطريقة الثانية: استخدام ملف التشغيل
cd C:\yemclinet
start-system.bat

# الطريقة الثالثة: تشغيل كخدمة (للإنتاج)
npm install -g pm2
pm2 start working-server.js --name "yemclient"
pm2 startup
pm2 save
```

## 🔒 **الجزء الرابع: تثبيت شهادة SSL**

### **1. تحضير ملفات الشهادة:**
```powershell
# نسخ ملفات الشهادة من C:\csr
Copy-Item "C:\csr\ymclient_com.crt" "C:\yemclinet\server\certs\ssl-cert.pem"
Copy-Item "C:\csr\extracted\SectigoPublicServerAuthenticationCADVR36.crt" "C:\yemclinet\server\certs\ca-bundle.pem"

# نسخ المفتاح الخاص (يجب الحصول عليه من مزود الشهادة)
# Copy-Item "المسار_للمفتاح_الخاص" "C:\yemclinet\server\certs\ssl-key.pem"
```

### **2. تحديث الخادم لدعم HTTPS:**
```powershell
# تشغيل سكريبت التثبيت
PowerShell -ExecutionPolicy Bypass -File "install-ssl-certificate.ps1"

# إضافة كود SSL للخادم (نسخ محتوى ssl-server-update.js)
# إلى نهاية ملف working-server.js
```

### **3. تشغيل النظام مع HTTPS:**
```powershell
# إعادة تشغيل الخادم
cd C:\yemclinet\server
node working-server.js

# يجب أن ترى رسائل مثل:
# ✅ SSL certificates found, starting HTTPS server...
# 🔒✅ HTTPS Server running on https://0.0.0.0:443
# 🌍 Domain HTTPS: https://ymclient.com
```

## 🧪 **الجزء الخامس: اختبار النظام**

### **1. اختبار HTTP:**
```powershell
# اختبار محلي
curl http://localhost:8080/health

# اختبار خارجي
curl http://***********:8080/health
```

### **2. اختبار HTTPS (بعد تثبيت الشهادة):**
```powershell
# اختبار النطاق
curl https://ymclient.com/health
curl https://www.ymclient.com/health

# اختبار API
curl https://ymclient.com/api/external/health
```

### **3. اختبار صفحة المطورين:**
```
# فتح في المتصفح:
http://localhost:8080/صفحة-اختبار-المطورين.html

# أو مع HTTPS:
https://ymclient.com/صفحة-اختبار-المطورين.html
```

## 🔧 **الجزء السادس: إعدادات الإنتاج**

### **1. إعدادات الجدار الناري:**
```powershell
# فتح المنافذ المطلوبة
New-NetFirewallRule -DisplayName "YemClient HTTP" -Direction Inbound -Protocol TCP -LocalPort 8080 -Action Allow
New-NetFirewallRule -DisplayName "YemClient HTTPS" -Direction Inbound -Protocol TCP -LocalPort 443 -Action Allow
New-NetFirewallRule -DisplayName "YemClient HTTP Redirect" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
```

### **2. إعدادات التشغيل التلقائي:**
```powershell
# إنشاء مهمة مجدولة للتشغيل التلقائي
$action = New-ScheduledTaskAction -Execute "node" -Argument "working-server.js" -WorkingDirectory "C:\yemclinet\server"
$trigger = New-ScheduledTaskTrigger -AtStartup
$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
Register-ScheduledTask -TaskName "YemClient" -Action $action -Trigger $trigger -Settings $settings -User "SYSTEM"
```

## 📋 **ملخص الروابط النهائية:**

### **HTTP (بدون شهادة):**
- http://localhost:8080
- http://***********:8080
- http://ymclient.com:8080

### **HTTPS (مع الشهادة):**
- https://ymclient.com
- https://www.ymclient.com
- https://***********

### **API Endpoints:**
- /api/external/verify-direct
- /api/external/health
- /api/dashboard/stats
