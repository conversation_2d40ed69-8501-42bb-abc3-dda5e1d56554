# سكريبت إعداد قاعدة البيانات لنظام AppTech MSMS

Write-Host "إعداد قاعدة البيانات..." -ForegroundColor Green

# فحص SQL Server
Write-Host "فحص SQL Server..." -ForegroundColor Yellow
$sqlInstances = @("localhost", ".\SQLEXPRESS", "localhost\SQLEXPRESS", "(localdb)\MSSQLLocalDB")
$workingInstance = $null

foreach ($instance in $sqlInstances) {
    try {
        Write-Host "اختبار الاتصال بـ: $instance" -ForegroundColor Gray
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION" -ErrorAction Stop
        if ($result) {
            $workingInstance = $instance
            Write-Host "✓ تم العثور على SQL Server: $instance" -ForegroundColor Green
            Write-Host "الإصدار: $($result.Column1.Substring(0,50))..." -ForegroundColor Gray
            break
        }
    } catch {
        Write-Host "✗ فشل الاتصال بـ: $instance" -ForegroundColor Red
    }
}

if (-not $workingInstance) {
    Write-Host "✗ لم يتم العثور على SQL Server" -ForegroundColor Red
    Write-Host "يرجى تثبيت SQL Server أو SQL Server Express" -ForegroundColor Yellow
    return
}

# إنشاء قاعدة البيانات
$databaseName = "AppTechMSMS"
Write-Host "إنشاء قاعدة البيانات: $databaseName" -ForegroundColor Yellow

try {
    # فحص وجود قاعدة البيانات
    $dbExists = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = '$databaseName'"
    
    if ($dbExists) {
        Write-Host "قاعدة البيانات موجودة بالفعل: $databaseName" -ForegroundColor Yellow
    } else {
        # إنشاء قاعدة البيانات
        $createDbQuery = @"
CREATE DATABASE [$databaseName]
ON (
    NAME = '${databaseName}_Data',
    FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA\${databaseName}.mdf',
    SIZE = 100MB,
    MAXSIZE = 1GB,
    FILEGROWTH = 10MB
)
LOG ON (
    NAME = '${databaseName}_Log',
    FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA\${databaseName}.ldf',
    SIZE = 10MB,
    MAXSIZE = 100MB,
    FILEGROWTH = 1MB
)
"@
        
        Invoke-Sqlcmd -ServerInstance $workingInstance -Query $createDbQuery
        Write-Host "✓ تم إنشاء قاعدة البيانات بنجاح" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ خطأ في إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
}

# إنشاء سلسلة الاتصال
$connectionString = "Data Source=$workingInstance;Initial Catalog=$databaseName;Integrated Security=True;MultipleActiveResultSets=True"
Write-Host "سلسلة الاتصال: $connectionString" -ForegroundColor Cyan

# إنشاء ملف تكوين سلاسل الاتصال
$connectionConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DefaultConnection" connectionString="$connectionString" providerName="System.Data.SqlClient" />
    <add name="AppTechMSMSEntities" connectionString="metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;$connectionString&quot;" providerName="System.Data.EntityClient" />
    <add name="elmah-sql" connectionString="$connectionString" providerName="System.Data.SqlClient" />
  </connectionStrings>
</configuration>
"@

# حفظ ملف تكوين سلاسل الاتصال
$configPath = "database-connections.config"
$connectionConfig | Out-File -FilePath $configPath -Encoding UTF8
Write-Host "✓ تم حفظ تكوين سلاسل الاتصال في: $configPath" -ForegroundColor Green

Write-Host "`nملاحظة: يجب تحديث ملفات web.config لتستخدم سلاسل الاتصال الجديدة" -ForegroundColor Yellow
Write-Host "أو فك تشفير ملف maincs.erp باستخدام مكتبة AppTech.Security" -ForegroundColor Yellow

Write-Host "`nانتهى إعداد قاعدة البيانات" -ForegroundColor Green
