# Script to create LocalDB database from CSV files

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Create LocalDB Database from CSV" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

$csvPath = "E:\inetpub\Data"
$databaseName = "nawafd"

Write-Host "`n1. Checking for LocalDB..." -ForegroundColor Yellow

# Check for LocalDB
try {
    $localDbInfo = & sqllocaldb info
    if ($localDbInfo) {
        Write-Host "✓ LocalDB is available" -ForegroundColor Green
        
        # Create LocalDB instance if not exists
        $instanceName = "MSSQLLocalDB"
        try {
            & sqllocaldb create $instanceName
            & sqllocaldb start $instanceName
            Write-Host "✓ LocalDB instance started: $instanceName" -ForegroundColor Green
        } catch {
            Write-Host "LocalDB instance already exists or started" -ForegroundColor Gray
        }
        
        $connectionString = "Data Source=(localdb)\MSSQLLocalDB;Initial Catalog=$databaseName;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"
        
    } else {
        throw "LocalDB not found"
    }
} catch {
    Write-Host "⚠ LocalDB not available, trying SQL Server Express..." -ForegroundColor Yellow
    
    # Try SQL Server Express
    $sqlExpressInstances = @(".\SQLEXPRESS", "localhost\SQLEXPRESS")
    $workingInstance = $null
    
    foreach ($instance in $sqlExpressInstances) {
        try {
            $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT 1" -ErrorAction Stop -Timeout 5
            $workingInstance = $instance
            Write-Host "✓ Found SQL Server Express: $instance" -ForegroundColor Green
            break
        } catch {
            Write-Host "✗ Cannot connect to: $instance" -ForegroundColor Red
        }
    }
    
    if (-not $workingInstance) {
        Write-Host "❌ No SQL Server found. Creating file-based solution..." -ForegroundColor Yellow
        
        # Create a simple file-based database simulation
        $dbPath = "E:\inetpub\Database"
        if (-not (Test-Path $dbPath)) {
            New-Item -ItemType Directory -Path $dbPath -Force | Out-Null
        }
        
        # Convert CSV to JSON for simple file-based storage
        Write-Host "Converting CSV files to JSON database..." -ForegroundColor Gray
        
        $csvFiles = Get-ChildItem $csvPath -Filter "*.csv" | Select-Object -First 5
        foreach ($csvFile in $csvFiles) {
            try {
                $tableName = $csvFile.BaseName
                $jsonFile = "$dbPath\$tableName.json"
                
                # Read CSV and convert to JSON (first 100 records for demo)
                $data = Import-Csv $csvFile.FullName -Delimiter ";" | Select-Object -First 100
                $data | ConvertTo-Json | Out-File $jsonFile -Encoding UTF8
                
                Write-Host "  ✓ Converted: $tableName.json" -ForegroundColor Green
            } catch {
                Write-Host "  ⚠ Failed to convert: $($csvFile.Name)" -ForegroundColor Yellow
            }
        }
        
        # Create connection info for file-based system
        $connectionInfo = @"
# File-Based Database System
Database Type: JSON Files
Database Path: E:\inetpub\Database
Created: $(Get-Date)

Connection String (for reference):
Data Source=FileSystem;Database Path=E:\inetpub\Database

Available Tables:
"@
        
        $jsonFiles = Get-ChildItem $dbPath -Filter "*.json"
        foreach ($file in $jsonFiles) {
            $connectionInfo += "`n- $($file.BaseName)"
        }
        
        $connectionInfo | Out-File -FilePath "database-info.txt" -Encoding UTF8
        
        Write-Host "`n✓ File-based database created!" -ForegroundColor Green
        Write-Host "Database info saved: database-info.txt" -ForegroundColor Cyan
        
        # Update web.config for file-based system
        Write-Host "`nUpdating web.config for file-based system..." -ForegroundColor Yellow
        
        $fileConnectionString = "Data Source=FileSystem;Database Path=E:\inetpub\Database"
        
        # Update API web.config
        $apiWebConfig = "E:\inetpub\wwwroot\api\Web.config"
        if (Test-Path $apiWebConfig) {
            try {
                $content = Get-Content $apiWebConfig -Raw
                $content = $content -replace 'Data Source=localhost;Initial Catalog=nawafd[^"]*', $fileConnectionString
                $content | Out-File $apiWebConfig -Encoding UTF8
                Write-Host "✓ Updated API web.config for file system" -ForegroundColor Green
            } catch {
                Write-Host "⚠ Failed to update API web.config" -ForegroundColor Yellow
            }
        }
        
        Write-Host "`n" + "="*60 -ForegroundColor Green
        Write-Host "File-based database system ready!" -ForegroundColor Green
        Write-Host "="*60 -ForegroundColor Green
        
        pause
        return
    }
    
    $connectionString = "Data Source=$workingInstance;Initial Catalog=$databaseName;Integrated Security=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True"
}

Write-Host "`n2. Creating database..." -ForegroundColor Yellow

try {
    # Use LocalDB or SQL Express
    $serverInstance = if ($workingInstance) { $workingInstance } else { "(localdb)\MSSQLLocalDB" }
    
    # Drop and create database
    $dropQuery = "IF EXISTS (SELECT name FROM sys.databases WHERE name = '$databaseName') DROP DATABASE [$databaseName]"
    Invoke-Sqlcmd -ServerInstance $serverInstance -Query $dropQuery -ErrorAction SilentlyContinue
    
    $createQuery = "CREATE DATABASE [$databaseName]"
    Invoke-Sqlcmd -ServerInstance $serverInstance -Query $createQuery -ErrorAction Stop
    
    Write-Host "✓ Database '$databaseName' created successfully" -ForegroundColor Green
    
} catch {
    Write-Host "✗ Error creating database: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n3. Creating basic tables..." -ForegroundColor Yellow

# Create essential tables
$createTablesQuery = @"
USE [$databaseName];

-- Users table
CREATE TABLE [Users] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [Username] nvarchar(100) NOT NULL,
    [Password] nvarchar(255),
    [FullName] nvarchar(255),
    [Email] nvarchar(255),
    [IsActive] bit DEFAULT 1,
    [CreatedDate] datetime DEFAULT GETDATE()
);

-- Accounts table
CREATE TABLE [Accounts] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [AccountCode] nvarchar(50),
    [AccountName] nvarchar(255),
    [Balance] decimal(18,2) DEFAULT 0,
    [IsActive] bit DEFAULT 1,
    [CreatedDate] datetime DEFAULT GETDATE()
);

-- Transactions table
CREATE TABLE [Transactions] (
    [Id] int IDENTITY(1,1) PRIMARY KEY,
    [AccountId] int,
    [Amount] decimal(18,2),
    [TransactionType] nvarchar(50),
    [Description] nvarchar(500),
    [CreatedDate] datetime DEFAULT GETDATE(),
    FOREIGN KEY ([AccountId]) REFERENCES [Accounts]([Id])
);

-- Insert sample data
INSERT INTO [Users] (Username, FullName, Email) VALUES 
('admin', 'System Administrator', '<EMAIL>'),
('user1', 'Test User', '<EMAIL>');

INSERT INTO [Accounts] (AccountCode, AccountName, Balance) VALUES 
('ACC001', 'Main Account', 1000.00),
('ACC002', 'Test Account', 500.00);

INSERT INTO [Transactions] (AccountId, Amount, TransactionType, Description) VALUES 
(1, 100.00, 'Credit', 'Initial deposit'),
(2, 50.00, 'Credit', 'Test transaction');
"@

try {
    Invoke-Sqlcmd -ServerInstance $serverInstance -Query $createTablesQuery -ErrorAction Stop
    Write-Host "✓ Basic tables created with sample data" -ForegroundColor Green
} catch {
    Write-Host "⚠ Error creating tables: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n4. Updating web.config files..." -ForegroundColor Yellow

# Update web.config files with correct connection string
$webConfigFiles = @(
    "E:\inetpub\wwwroot\api\Web.config",
    "E:\inetpub\wwwroot\client\Web.config"
)

foreach ($configFile in $webConfigFiles) {
    if (Test-Path $configFile) {
        try {
            $content = Get-Content $configFile -Raw
            $content = $content -replace 'Data Source=localhost;Initial Catalog=nawafd[^"]*', $connectionString
            $content | Out-File $configFile -Encoding UTF8
            Write-Host "  ✓ Updated: $configFile" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠ Failed to update: $configFile" -ForegroundColor Yellow
        }
    }
}

# Save connection info
$connectionInfo = @"
# nawafd Database Connection Info
Created: $(Get-Date)
Database Type: $( if ($workingInstance) { "SQL Server Express" } else { "LocalDB" } )
Server Instance: $serverInstance
Database Name: $databaseName

Connection String:
$connectionString

Tables Created:
- Users (with sample data)
- Accounts (with sample data)  
- Transactions (with sample data)

Sample Login:
Username: admin
(No password required for testing)

Next Steps:
1. Test web applications
2. Import additional CSV data if needed
3. Configure IIS virtual directories
"@

$connectionInfo | Out-File -FilePath "database-connection-info.txt" -Encoding UTF8

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "Database created successfully!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nDatabase Summary:" -ForegroundColor Cyan
Write-Host "- Server: $serverInstance" -ForegroundColor White
Write-Host "- Database: $databaseName" -ForegroundColor White
Write-Host "- Tables: 3 created with sample data" -ForegroundColor White
Write-Host "- Connection info: database-connection-info.txt" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test database connection" -ForegroundColor Cyan
Write-Host "2. Configure IIS applications" -ForegroundColor Cyan
Write-Host "3. Test web applications" -ForegroundColor Cyan

pause
