﻿@model AppTech.MSMS.Domain.Reports.Models.AccountBalanceModel
@using Obout.Mvc.ComboBox
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
}

<div class="form-horizontal">

    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>

    <span class="lbl"> نوع الحساب </span>
    <select name="Type" id="Type">
        <option value="0">كافة الحسابات</option>
        <option value="1">حساب محدد</option>
        <option value="2">مجموعة</option>
        <option value="3">الحسابات الجارية</option>
        <option value="4">حسابات رئيسية</option>
    </select>
    <div class="hr hr-dotted hr-24"></div>
    <div class="space-6"></div>

    <div id="Account" hidden>
        <span class="lbl">اسم الحساب </span>
        <div class="form-group">
            <div class="col-md-10">
                @Html.Obout(new ComboBox("AccountID")
                {
                    Width = 230,
                    FilterType = ComboBoxFilterType.Contains
                })
            </div>
        </div>
        <div class="hr hr-dotted hr-24"></div>
        <div class="space-6"></div>
    </div>

    <div id="Group" hidden>
        <span class="lbl">اسم المجموعة </span>
        <div class="form-group">
            <div class="col-md-10">
                <select id="GroupID" name="GroupID" class="select2" placeholder="كافة الحسابات"></select>
            </div>
        </div>
        <div class="hr hr-dotted hr-24"></div>
        <div class="space-6"></div>
    </div>

    <div id="ActiveAccounts" hidden>
        <span class="lbl">الحسابات الجارية </span>
        <div class="form-group">
            <div class="col-md-10">
                <select id="ActiveAccountID" name="ActiveAccountID" class="select2" placeholder="كافة الحسابات"></select>
            </div>
        </div>
        <div class="hr hr-dotted hr-24"></div>
        <div class="space-6"></div>
    </div>

    <div id="MainAccounts" hidden>
        <span class="lbl">الحسابات الرئيسية </span>
        <div class="form-group">
            <div class="col-md-10">
                <select id="MainAccountID" name="MainAccountID" class="select2" placeholder="كافة الحسابات"></select>
            </div>
        </div>
        <div class="hr hr-dotted hr-24"></div>
        <div class="space-6"></div>
    </div>

    <div class="form-group">
        @Html.Label("الحالة", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EnumDropDownListFor(x => x.Status)
        </div>
    </div>
</div>

<script>
    $("#Type").change(function () {
        i('Type');
        if (this.value == 0) {
            $("#Account").hide();
            $("#Group").hide();
            $("#ActiveAccounts").hide();
            $("#MainAccounts").hide();
        }
        if (this.value == 1) {
            $("#Account").show();
            $("#Group").hide();
            $("#ActiveAccounts").hide();
            $("#MainAccounts").hide();
        }
        if (this.value == 2) {
            $("#Account").hide();
            $("#ActiveAccounts").hide();
            $("#MainAccounts").hide();
            $("#Group").show();
            loadGroupList();

        }
        if (this.value == 3) {
            $("#ActiveAccounts").show();
            $("#Account").hide();
            $("#Group").hide();
            $("#MainAccounts").hide();
            loadActiveAccountList();
        }
        if (this.value == 4) {
            $("#MainAccounts").show();
            $("#Account").hide();
            $("#Group").hide();
            $("#ActiveAccounts").hide();
            loadMainAccountList();
        }
    });

    function loadGroupList() {
        i('loadGroupList');
        fillDataList('GroupID', 'GeneralLedger/AccountBalance/GetGroupAccounts');
    }
    function loadActiveAccountList() {
        i('loadActiveAccountList');
        fillDataList('ActiveAccountID', 'GeneralLedger/AccountBalance/GetActiveAccounts', false, 'كافة الحسابات');
    }
    function loadMainAccountList() {
        i('loadMainAccountList');
        fillDataList('MainAccountID', 'GeneralLedger/AccountBalance/GetMainAccounts');
    }

    //select2
    $('.select2').css('width', '200px').select2({ allowClear: true });
    $('#select2-multiple-style .btn').on('click',
        function (e) {
            var target = $(this).find('input[type=radio]');
            var which = parseInt(target.val());
            if (which == 2) $('.select2').addClass('tag-input-style');
            else $('.select2').removeClass('tag-input-style');
        });
</script>
