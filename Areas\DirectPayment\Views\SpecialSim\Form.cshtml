﻿@model AppTech.MSMS.Domain.Models.SpecialSim
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-horizontal">

    @Html.ValidationSummary(true)


    <div class="form-group">
        @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Number)
            @Html.ValidationMessageFor(model => model.Number)
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-2 control-label">نوع الخط</label>

        <div class="col-sm-10">
            @Html.DropDownListFor(model => model.Type, new[]
            {
                new SelectListItem {Text = "فضي", Value = "فضي", Selected = true},
                new SelectListItem {Text = "ذهبي", Value = "ذهبي"},
                new SelectListItem {Text = "برونزي", Value = "برونزي"},
                new SelectListItem {Text = "ماسي", Value = "ماسي"}
            })


        </div>
        @Html.ValidationMessageFor(model => model.Type)
    </div>
    <div class="form-group">
        @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>
</div>
