﻿@model IEnumerable < AppTech.MSMS.Domain.Models.Client>

@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}
<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.ParentID')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span> ربط عميل  </span>
    </a>
</p>
<div id="list">
    @Html.Partial("_Clients")
</div>
<p>

    <a href="/#!/route/agency/Distributor" class="btn btn-sm btn-primary btn-white btn-round">
        <i class="ace-icon fa fa-rss bigger-150 middle orange2"></i>
        <span class="bigger-110">الرجوع الى الخلف</span>

        <i class="icon-on-right ace-icon fa fa-arrow-right"></i>
    </a>
</p>
@Html.Partial("_Modal")

<script>


//var url = "~/Scripts/bootbox.js";
//$.getScript(url);
    function openModal(id, type) {
        i('open modal id' + id + ' type ' + type);
        openViewAsModal('Agency/Distributor/AddOrEditClient?id=' + id, "ربط عميل جديد");
    }


</script>