﻿@model AppTech.MSMS.Domain.Settings.AppNameSetting

<div class="row" style="margin: 30px">
    <div class="col-xs-12 col-sm-6">
        @using (Ajax.BeginForm(new AjaxOptions
        {
            OnBegin = "return OnFormBegin()",
            OnSuccess = "onCrudSuccess",
            OnFailure = "onCrudFailure",
            LoadingElementId = "formloader"
        }))
        {
        <table id="search-filter-table" class="table table-responsive borderless">
            @foreach (var property in ViewData.ModelMetadata.Properties)
            {
            <tr>
                <td> <span class="lbl">  @Html.Label(property.DisplayName) </span></td>
                <td style="width: 300px;background-color: #f7f7f7;"> @Html.Editor(property.PropertyName) </td>
            </tr>
            }
        </table>
            <div class="space-10"></div>

            <div class="space-32"></div>
            <div class="space-32"></div>
            <div class="hr hr32 hr-dotted"></div>
            @Html.Partial("_FormAction")
        }
    </div>
</div>
<style>
    input{
    width: 300px;
    background-color: #f7f7f7;
    }
</style>