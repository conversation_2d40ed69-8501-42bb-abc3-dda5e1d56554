﻿@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.Topup

<input type="hidden" id="Title" value="@ViewBag.Title">
<input type="hidden" id="FormTitle" value="@ViewBag.FormTitle">
@using (Ajax.BeginForm(
    "MakePayment",
    null,
    new AjaxOptions
    {
        OnBegin = "return Validate()",
        LoadingElementId = "loader",
        OnSuccess = "onCrudSuccess",
        OnFailure = "onCrudFailure"
    },
    new {id = "crudform"}))
{
    @Html.AntiForgeryToken()
    @Html.ValidationSummary(true, "", new {@class = "text-danger"})

<div class="form-horizontal">
    <input type="hidden" name="Device" value="Web" />
    <input type="hidden" name="ServiceID" id="ServiceID" />


    @if (CurrentUser.Type == UserType.Admin)
    {
        <div class="form-group">
            @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.Obout(new ComboBox("DebitorAccountID")
                {
                    Width = 300,
                    SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
                    FilterType = ComboBoxFilterType.Contains,
                    LoadingText = "Loading"
                })
                @Html.ValidationMessageFor(model => model.DebitorAccountID, "", new { @class = "text-danger" })
            </div>
        </div>
    }

    <div class="form-group">
        @Html.LabelFor(model => model.SubscriberNumber, new { @class = "control-label col-md-2" })
        <div class="col-md-10 form-inline">
            @Html.EditorFor(model => model.SubscriberNumber, new { id = "SubscriberNumber" })

            <a id="search" href="" onclick=" queryBalance() " class="btn btn-white btn-default btn-round">
                <i class="ace-icon fa fa-mobile-phone bigger-110"></i>
                الأستعلام
            </a>
            @Html.ValidationMessageFor(model => model.SubscriberNumber)
        </div>
    </div>

    <div class="alert alert-info" id="info">
        <button type="button" class="close" data-dismiss="alert">
            <i class="ace-icon fa fa-times"></i>
        </button>
        <p>
            <span id="key"></span>
            <strong id="val">
            </strong>
        </p>
    </div>

    <div class="form-group" id="linetype-row">
        @Html.LabelFor(model => model.LineType, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.LineType, new[]
            {
                new SelectListItem {Text = "دفع مسبق", Value = "دفع مسبق"},
                new SelectListItem {Text = "فوترة", Value = "فوترة"}
            }, new { onchange = "onLineType(this) " })
        </div>
    </div>

    <div class="form-group" id="faction-row">

        @Html.Label("الفئة", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <select id="FactionID" name="FactionID"></select>
        </div>
    </div>

    <div class="form-group" id="region-row">
        @Html.Label("المنطقة", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            <select id="RegionID" name="RegionID"></select>
        </div>
    </div>

    <div class="form-group form-inline" id="amount-row">
        @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
        <div class="col-md-10 form-inline">
            @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
            <label id="words" class="red"></label>
            @Html.ValidationMessageFor(model => model.Amount)
        </div>
    </div>



</div>
    <div class="hr hr32 hr-dotted"></div>
    @Html.Partial("_FormAction")
}


<script>

    $('#loader').hide();
    var lastSNO;
    var lastAmount;

    function Validate() {

        if (lastSNO === $("#SubscriberNumber").val() && lastAmount === $("#Amount").val()) {
            if (!confirm('تم تسديد هذا الرقم بنفس المبلغ من سابق , هل تريد الأستمرار')) {
                i('not confirmed');
                return false;
            } else {
                return true;
            }
        }

        var amount =' بمبلغ '+ $("#Amount").val();

        var facton_row = document.getElementById("faction-row");
        if (window.getComputedStyle(facton_row).display != "none") {
            var selectedText = $("#FactionID option:selected").html();
            amount = ' لفئة ' + selectedText;
        };
        var msg = "سوف يتم تسديد لرقم " + $("#SubscriberNumber").val()+ amount + ' هل انت متأكد ';
        if (!confirm(msg)) {
            i('not confirmed');
            return false;
        } else {

            i('confirmed');
            hideInfo();
            return true;

        }
    }

    var formHelper = {
        onSuccess: function(data) {
            log('topup-.onSuccess');
            resetButton();
            i('data> ' + data);
            if (data.Success) {
                lastSNO = $("#SubscriberNumber").val();
                lastAmount = $("#Amount").val();
                var msg = "تم التسديد لرقم " + $("#SubscriberNumber").val() + ' بنجاح';
                alert(msg);


                var sid = parseInt($("#ServiceID").val());
                i('queryYM sid:' + sid);
                if (sid === Services.YemenMobile) {
                    $('#Amount').val('');
                    queryYM();
                } else {
                    $('#crudform')[0].reset();
                }
            } else {
                alert(data.Message);
            }
        },
        onBegin: function(context) {
            var msg = "سوف يتم تسديد لرقم " + $("#SubscriberNumber").val() + ' هل انت متأكد';
            if (!confirm(msg)) {
                i('confirmed');
                hideInfo();
                return false;
            } else {
                i('confirmed');
                return true;

            }

        }
    }

    function OnFormBegin(context) {
      return   formHelper.onBegin(context);
    }

    if ($("#Date")[0]) {
        $("#Date").val(getToday());
        $('#Date').prop('readonly', true);
    }

    function onCrudSuccess(data) {
        formHelper.onSuccess(data);
    }

    function onCrudFailure(xhr, status) {
        hideLoading();
        resetButton();
        log('on topup Failure');
        //  hideLoading();
        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);

    }



    //var data = { code: "gg" }
    //AjaxCall('/Clients/YMOfferPayment/GetPrice', data)
    //    .done(function (msg) {
    //        if (!confirm(msg)) {
    //            i('not confirmed');
    //            return false;
    //        } else {

    //            i('confirmed');
    //            hideInfo();
    //            return true;
    //        }
    //    })
    //    .fail(function (xhr, textStatus, errorThrown) {
    //        parseAndShowError(xhr, textStatus, errorThrown);
    //    });

</script>