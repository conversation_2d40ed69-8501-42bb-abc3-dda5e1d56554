/****************************************************************************
*                                                                           *
*    File:        msmdctr.h                                                 *
*    Owner:       MACHINE GENERATED                                         *
*---------------------------------------------------------------------------*
*    Description:                                                           *
*                                                                           *
*        Perfmon Counters, offset definition                                *
*                                                                           *
****************************************************************************/

/////////////////////////////////////////////////////////////////////////////
//
// msmdctr.h
// Offset definition file for extensible counter objects and counters.
//
// WARNING -- THIS FILE IS MACHINE GENERATED -- DO NOT EDIT.
// To create, use: `perl constants_from_perfmon_ini.pl msmdctr.ini >msmdctr.h`
//
// This file is used by LODCTR to load the names into the registry.
// Also used by the counter DLL code.
//
// These relative offsets must start at 0 and must be multiples of 2
// In the Open Procedure, they will be added to the 
// 'First Counter' and 'First Help' values for the device they belong to, 
// in order to determine the absolute location of the counter and 
// object names and corresponding Explain text in the registry.
//
/////////////////////////////////////////////////////////////////////////////


#define PF_PERFOBJECT_CONNECTION                           0
#define PF_PERFOBJECT_LOCK                                 2
#define PF_PERFOBJECT_THREAD                               4
#define PF_PERFOBJECT_MEMORY                               6
#define PF_PERFOBJECT_CACHE                                8
#define PF_PERFOBJECT_MDX                                  10
#define PF_PERFOBJECT_PROC_BASE                            12
#define PF_PERFOBJECT_PROC_AGG                             14
#define PF_PERFOBJECT_PROC_INDEX                           16
#define PF_PERFOBJECT_QUERY                                18
#define PF_PERFOBJECT_DM_PROCESSING                        20
#define PF_PERFOBJECT_DM_PREDICTION                        22
#define PF_PERFOBJECT_PC                                   24

#define PF_PERFCOUNT_CON_CUR_CONNECTIONS                   26
#define PF_PERFCOUNT_CON_RATE_REQUESTS                     28
#define PF_PERFCOUNT_CON_TOTAL_REQUESTS                    30
#define PF_PERFCOUNT_CON_RATE_SUCCESSES                    32
#define PF_PERFCOUNT_CON_TOTAL_SUCCESSES                   34
#define PF_PERFCOUNT_CON_RATE_FAILURES                     36
#define PF_PERFCOUNT_CON_TOTAL_FAILURES                    38
#define PF_PERFCOUNT_CON_CUR_USER_SESSIONS                 40
#define PF_PERFCOUNT_LATCH_CUR_WAITS                       42
#define PF_PERFCOUNT_LATCH_RATE_WAITS                      44
#define PF_PERFCOUNT_LOCK_CUR_LOCKS                        46
#define PF_PERFCOUNT_LOCK_CUR_WAITS                        48
#define PF_PERFCOUNT_LOCK_RATE_REQUESTS                    50
#define PF_PERFCOUNT_LOCK_RATE_GRANTS                      52
#define PF_PERFCOUNT_LOCK_RATE_WAITS                       54
#define PF_PERFCOUNT_LOCK_RATE_FAILS                       56
#define PF_PERFCOUNT_LOCK_RATE_UNLOCK_REQUESTS             58
#define PF_PERFCOUNT_LOCK_TOTAL_DEADLOCKS                  60
#define PF_PERFCOUNT_THREAD_PARSING_SHORT_THREADS_IDLE     62
#define PF_PERFCOUNT_THREAD_PARSING_SHORT_THREADS_BUSY     64
#define PF_PERFCOUNT_THREAD_PARSING_SHORT_JOBS_WAITING     66
#define PF_PERFCOUNT_THREAD_PARSING_SHORT_JOBS_RATE        68
#define PF_PERFCOUNT_THREAD_PARSING_LONG_THREADS_IDLE      70
#define PF_PERFCOUNT_THREAD_PARSING_LONG_THREADS_BUSY      72
#define PF_PERFCOUNT_THREAD_PARSING_LONG_JOBS_WAITING      74
#define PF_PERFCOUNT_THREAD_PARSING_LONG_JOBS_RATE         76
#define PF_PERFCOUNT_THREAD_QUERY_THREADS_IDLE             78
#define PF_PERFCOUNT_THREAD_QUERY_THREADS_BUSY             80
#define PF_PERFCOUNT_THREAD_QUERY_JOBS_WAITING             82
#define PF_PERFCOUNT_THREAD_QUERY_JOBS_RATE                84
#define PF_PERFCOUNT_THREAD_PROCESS_THREADS_IDLE           86
#define PF_PERFCOUNT_THREAD_PROCESS_THREADS_BUSY           88
#define PF_PERFCOUNT_THREAD_PROCESS_JOBS_WAITING           90
#define PF_PERFCOUNT_THREAD_PROCESS_JOBS_RATE              92
#define PF_PERFCOUNT_THREAD_IO_PROCESSING_THREADS_IDLE     94
#define PF_PERFCOUNT_THREAD_IO_PROCESSING_THREADS_BUSY     96
#define PF_PERFCOUNT_THREAD_IO_PROCESSING_JOBS_WAITING     98
#define PF_PERFCOUNT_THREAD_IO_PROCESSING_JOBS_RATE        100
#define PF_PERFCOUNT_THREAD_COMMAND_THREADS_IDLE			102
#define PF_PERFCOUNT_THREAD_COMMAND_THREADS_BUSY			104
#define PF_PERFCOUNT_THREAD_COMMAND_JOBS_WAITING			106
#define PF_PERFCOUNT_THREAD_COMMAND_JOBS_RATE				108
#define PF_PERFCOUNT_MEMORY_CUR_PAGES_64_ALLOC             	110
#define PF_PERFCOUNT_MEMORY_CUR_PAGES_64_LOOKASIDE         	112
#define PF_PERFCOUNT_MEMORY_CUR_PAGES_8_ALLOC              	114
#define PF_PERFCOUNT_MEMORY_CUR_PAGES_8_LOOKASIDE          	116
#define PF_PERFCOUNT_MEMORY_CUR_PAGES_1_ALLOC              	118
#define PF_PERFCOUNT_MEMORY_CUR_PAGES_1_LOOKASIDE          	120
#define PF_PERFCOUNT_MEMORY_CUR_CLEANER_PRICE              	122
#define PF_PERFCOUNT_MEMORY_RATE_CLEANER_BALANCE           	124
#define PF_PERFCOUNT_MEMORY_RATE_CLEANER_SHRINK_KB         	126
#define PF_PERFCOUNT_MEMORY_CUR_CLEANER_SHRINKABLE_KB      	128
#define PF_PERFCOUNT_MEMORY_CUR_CLEANER_NONSHRINKABLE_KB   	130
#define PF_PERFCOUNT_MEMORY_CUR_CLEANER_KB                 	132
#define PF_PERFCOUNT_MEMORY_CUR_MEMORY_USED_KB             	134
#define PF_PERFCOUNT_MEMORY_CUR_MEMORY_LIMIT_LOW_KB        	136
#define PF_PERFCOUNT_MEMORY_CUR_MEMORY_LIMIT_HIGH_KB       	138
#define PF_PERFCOUNT_MEMORY_CUR_SYSTEM_WLG_MEMORY_USED_KB  	140
#define PF_PERFCOUNT_MEMORY_CUR_SYSTEM_WLG_TRACKERS        	142
#define PF_PERFCOUNT_MEMORY_CUR_DEFAULT_WLG_MEMORY_USED_KB 	144
#define PF_PERFCOUNT_MEMORY_CUR_DEFAULT_WLG_TRACKERS       	146
#define PF_PERFCOUNT_MEMORY_CUR_ALL_DB_WLG_MEMORY_USED_KB  	148
#define PF_PERFCOUNT_MEMORY_CUR_ALL_DB_WLG_TRACKERS        	150
#define PF_PERFCOUNT_MEMORY_CUR_AGGCACHE_ALLOC             	152
#define PF_PERFCOUNT_MEMORY_CUR_QUOTA                      	154
#define PF_PERFCOUNT_MEMORY_CUR_QUOTA_BLOCKED              	156
#define PF_PERFCOUNT_MEMORY_CUR_FILESTORE_ALLOC            	158
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_FAULTS          	160
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_READ            	162
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_READ_KB         	164
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_WRITE							166
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_WRITE_KB        					168
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_IO_ERRORS       					170
#define PF_PERFCOUNT_MEMORY_TOTAL_FILESTORE_IO_ERRORS      					172
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_CLOCK_PAGE_EXAMINED				174
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_CLOCK_PAGE_HAVEREF				176
#define PF_PERFCOUNT_MEMORY_RATE_FILESTORE_CLOCK_PAGE_VALID					178
#define PF_PERFCOUNT_MEMORY_CUR_FILESTORE_MEMORY_PINNED_KB					180
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_DIMENSION_PROPERTY_FILE_KB		182
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_DIMENSION_PROPERTY_FILE_KB		184
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_DIMENSION_PROPERTY_FILE_KB	186
#define PF_PERFCOUNT_MEMORY_TOTAL_DIMENSION_PROPERTY_FILES					188
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_DIMENSION_INDEX_FILE_KB			190
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_DIMENSION_INDEX_FILE_KB			192
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_DIMENSION_INDEX_FILE_KB		194
#define PF_PERFCOUNT_MEMORY_TOTAL_DIMENSION_INDEX_FILES						196
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_DIMENSION_STRING_FILE_KB			198
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_DIMENSION_STRING_FILE_KB			200
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_DIMENSION_STRING_FILE_KB	202
#define PF_PERFCOUNT_MEMORY_TOTAL_DIMENSION_STRING_FILES					204
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_MAP_FILE_KB						206
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_MAP_FILE_KB						208
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_MAP_FILE_KB					210
#define PF_PERFCOUNT_MEMORY_TOTAL_MAP_FILES									212
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_AGGREGATION_MAP_FILE_KB			214
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_AGGREGATION_MAP_FILE_KB			216
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_AGGREGATION_MAP_FILE_KB		218
#define PF_PERFCOUNT_MEMORY_TOTAL_AGGREGATION_MAP_FILES						220
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_FACT_DATA_FILE_KB					222
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_FACT_DATA_FILE_KB				224
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_FACT_DATA_FILE_KB			226
#define PF_PERFCOUNT_MEMORY_TOTAL_FACT_DATA_FILES							228
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_FACT_STRING_FILE_KB				230
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_FACT_STRING_FILE_KB				232
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_FACT_STRING_FILE_KB			234
#define PF_PERFCOUNT_MEMORY_TOTAL_FACT_STRING_FILES							236
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_FACT_AGGREGATION_FILE_KB			238
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_FACT_AGGREGATION_FILE_KB			240
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_FACT_AGGREGATION_FILE_KB	242
#define PF_PERFCOUNT_MEMORY_TOTAL_FACT_AGGREGATION_FILES					244
#define PF_PERFCOUNT_MEMORY_CUR_IN_MEMORY_OTHER_FILE_KB    					246
#define PF_PERFCOUNT_MEMORY_RATE_IN_MEMORY_OTHER_FILE_KB   					248
#define PF_PERFCOUNT_MEMORY_POTENTIAL_IN_MEMORY_OTHER_FILE_KB				250
#define PF_PERFCOUNT_MEMORY_TOTAL_OTHER_FILES              					252
#define PF_PERFCOUNT_XM_PAGEABLE_KB                        					254
#define PF_PERFCOUNT_XM_NONPAGEABLE_KB                     					256
#define PF_PERFCOUNT_XM_MEMMAPPED_KB                       					258
#define PF_PERFCOUNT_MEMORY_CUR_MEMORY_LIMIT_HARD_KB       					260
#define PF_PERFCOUNT_MEMORY_CUR_MEMORY_LIMIT_VERTI_KB      					262
#define PF_PERFCOUNT_CACHE_CUR_BYTES                       					264
#define PF_PERFCOUNT_CACHE_RATE_BYTES                      					266
#define PF_PERFCOUNT_CACHE_CUR_ENTRIES                     					268
#define PF_PERFCOUNT_CACHE_RATE_INSERTS                    					270
#define PF_PERFCOUNT_CACHE_RATE_EVICTIONS                  					272
#define PF_PERFCOUNT_CACHE_TOTAL_INSERTS                   					274
#define PF_PERFCOUNT_CACHE_TOTAL_EVICTIONS                 					276
#define PF_PERFCOUNT_CACHE_RATE_DIRECT_HITS                					278
#define PF_PERFCOUNT_CACHE_RATE_MISSES                     					280
#define PF_PERFCOUNT_CACHE_RATE_LOOKUPS                    					282
#define PF_PERFCOUNT_CACHE_TOTAL_DIRECT_HITS               					284
#define PF_PERFCOUNT_CACHE_TOTAL_MISSES                    					286
#define PF_PERFCOUNT_CACHE_TOTAL_LOOKUPS                   					288
#define PF_PERFCOUNT_CACHE_RATIO_DIRECT_HITS               					290
#define PF_PERFCOUNT_CACHE_TOTAL_FILTER_ITERATOR_HITS      					292
#define PF_PERFCOUNT_CACHE_TOTAL_FILTER_ITERATOR_MISSES    					294
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVERS                  					296
#define PF_PERFCOUNT_MDX_CURRENT_CALCCOVERS                					298
#define PF_PERFCOUNT_MDX_TOTAL_SE_CALCCOVERS               					300
#define PF_PERFCOUNT_MDX_TOTAL_CP8_CALCCOVERS              					302
#define PF_PERFCOUNT_MDX_TOTAL_BULK_CALCCOVERS             					304
#define PF_PERFCOUNT_MDX_TOTAL_SINGLE_CELL_CALCCOVERS      					306
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVERS_SAME_GRAIN       					308
#define PF_PERFCOUNT_MDX_CURRENT_CACHED_CALCCOVERS         					310
#define PF_PERFCOUNT_MDX_TOTAL_CACHED_SE_CALCCOVERS        					312
#define PF_PERFCOUNT_MDX_TOTAL_CACHED_BULK_CALCCOVERS      					314
#define PF_PERFCOUNT_MDX_TOTAL_CACHED_OTHER_CALCCOVERS     					316
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVERS_EVICTIONS        					318
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVER_HASH_HITS         					320
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVER_CELL_HITS         					322
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVER_CELL_MISSES       					324
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVER_SUBCUBE_HITS      					326
#define PF_PERFCOUNT_MDX_TOTAL_CALCCOVER_SUBCUBE_MISSES    					328
#define PF_PERFCOUNT_MDX_TOTAL_SONARSUBCUBES               					330
#define PF_PERFCOUNT_MDX_TOTAL_GETVALUE                    					332
#define PF_PERFCOUNT_MDX_TOTAL_SINGLECELLRECOMPUTE         					334
#define PF_PERFCOUNT_MDX_TOTAL_FLATMACACHEINSERT           					336
#define PF_PERFCOUNT_MDX_TOTAL_REGISTRYMCACHEINSERT        					338
#define PF_PERFCOUNT_MDX_TOTAL_NONEMPTY                    					340
#define PF_PERFCOUNT_MDX_TOTAL_SLOWNONEMPTY                					342
#define PF_PERFCOUNT_MDX_TOTAL_CALCMEMBERNONEMPTY          					344
#define PF_PERFCOUNT_MDX_TOTAL_AUTOEXIST                   					346
#define PF_PERFCOUNT_MDX_TOTAL_EXISTING                    					348
#define PF_PERFCOUNT_PROC_BASE_RATE_READ_ROWS              					350
#define PF_PERFCOUNT_PROC_BASE_TOTAL_READ_ROWS             					352
#define PF_PERFCOUNT_PROC_BASE_RATE_CONVERT_ROWS           					354
#define PF_PERFCOUNT_PROC_BASE_TOTAL_CONVERT_ROWS          					356
#define PF_PERFCOUNT_PROC_BASE_RATE_WRITE_ROWS             					358
#define PF_PERFCOUNT_PROC_BASE_TOTAL_WRITE_ROWS            					360
#define PF_PERFCOUNT_PROC_AGG_CUR_PARTITIONS               					362
#define PF_PERFCOUNT_PROC_AGG_TOTAL_PARTITIONS             					364
#define PF_PERFCOUNT_PROC_AGG_CUR_MEM_ROWS                 					366
#define PF_PERFCOUNT_PROC_AGG_CUR_MEM_BYTES                					368
#define PF_PERFCOUNT_PROC_AGG_RATE_ROWS_MERGED             					370
#define PF_PERFCOUNT_PROC_AGG_RATE_ROWS_CREATED            					372
#define PF_PERFCOUNT_PROC_AGG_RATE_TEMPFILE_ROWS_WRITTEN   					374
#define PF_PERFCOUNT_PROC_AGG_RATE_TEMPFILE_BYTES_WRITTEN  					376
#define PF_PERFCOUNT_PROC_INDEX_CUR_PARTITIONS             					378
#define PF_PERFCOUNT_PROC_INDEX_TOTAL_PARTITIONS           					380
#define PF_PERFCOUNT_PROC_INDEX_RATE_ROWS                  					382
#define PF_PERFCOUNT_PROC_INDEX_TOTAL_ROWS                 					384
#define PF_PERFCOUNT_QUERY_CUR_QUERIES                     					386
#define PF_PERFCOUNT_QUERY_RATE_QUERIES                    					388
#define PF_PERFCOUNT_QUERY_TOTAL_QUERIES                   					390
#define PF_PERFCOUNT_QUERY_DIM_CUR_QUERIES                 					392
#define PF_PERFCOUNT_QUERY_DIM_RATE_QUERIES                					394
#define PF_PERFCOUNT_QUERY_DIM_TOTAL_QUERIES               					396
#define PF_PERFCOUNT_QUERY_RATE_ANSWERS                    					398
#define PF_PERFCOUNT_QUERY_TOTAL_ANSWERS                   					400
#define PF_PERFCOUNT_QUERY_RATE_BYTES_SENT                 					402
#define PF_PERFCOUNT_QUERY_TOTAL_BYTES_SENT                					404
#define PF_PERFCOUNT_QUERY_RATE_ROWS_SENT                  					406
#define PF_PERFCOUNT_QUERY_TOTAL_ROWS_SENT                 					408
#define PF_PERFCOUNT_QUERY_RATE_CACHE_DIRECT               					410
#define PF_PERFCOUNT_QUERY_RATE_CACHE_FILTERED             					412
#define PF_PERFCOUNT_QUERY_RATE_FILE                       					414
#define PF_PERFCOUNT_QUERY_TOTAL_CACHE_DIRECT              					416
#define PF_PERFCOUNT_QUERY_TOTAL_CACHE_FILTERED            					418
#define PF_PERFCOUNT_QUERY_TOTAL_FILE                      					420
#define PF_PERFCOUNT_QUERY_RATE_MAP_NUM_READ               					422
#define PF_PERFCOUNT_QUERY_RATE_MAP_BYTES_READ             					424
#define PF_PERFCOUNT_QUERY_RATE_DATA_NUM_READ              					426
#define PF_PERFCOUNT_QUERY_RATE_DATA_BYTES_READ            					428
#define PF_PERFCOUNT_QUERY_AVG_TIME_QUERY                  					430
#define PF_PERFCOUNT_QUERY_RATE_ROUND_TRIPS                					432
#define PF_PERFCOUNT_QUERY_TOTAL_ROUND_TRIPS               					434
#define PF_PERFCOUNT_QUERY_RATE_FLAT_CACHE_LOOKUPS         					436
#define PF_PERFCOUNT_QUERY_RATE_FLAT_CACHE_HITS            					438
#define PF_PERFCOUNT_QUERY_RATE_CALC_CACHE_LOOKUPS         					440
#define PF_PERFCOUNT_QUERY_RATE_CALC_CACHE_HITS            					442
#define PF_PERFCOUNT_QUERY_RATE_PERSISTED_CACHE_LOOKUPS    					444
#define PF_PERFCOUNT_QUERY_RATE_PERSISTED_CACHE_HITS       					446
#define PF_PERFCOUNT_QUERY_RATE_DIM_CACHE_LOOKUPS          					448
#define PF_PERFCOUNT_QUERY_RATE_DIM_CACHE_HITS             					450
#define PF_PERFCOUNT_QUERY_RATE_MG_CACHE_LOOKUPS           					452
#define PF_PERFCOUNT_QUERY_RATE_MG_CACHE_HITS              					454
#define PF_PERFCOUNT_QUERY_RATE_AGG_LOOKUPS                					456
#define PF_PERFCOUNT_QUERY_RATE_AGG_HITS                   					458
#define PF_PERFCOUNT_DM_PROCESSING_RATE_CASES              					460
#define PF_PERFCOUNT_DM_PROCESSING_CUR_MODELS              					462
#define PF_PERFCOUNT_DM_PREDICTION_CUR_QUERIES             					464
#define PF_PERFCOUNT_DM_PREDICTION_RATE_PREDICTIONS        					466
#define PF_PERFCOUNT_DM_PREDICTION_RATE_ROWS               					468
#define PF_PERFCOUNT_DM_PREDICTION_RATE_QUERIES            					470
#define PF_PERFCOUNT_DM_PREDICTION_TOTAL_QUERIES           					472
#define PF_PERFCOUNT_DM_PREDICTION_TOTAL_ROWS              					474
#define PF_PERFCOUNT_DM_PREDICTION_TOTAL_PREDICTIONS       					476
#define PF_PERFCOUNT_PC_RATE_NOTIFICATIONS									478
#define PF_PERFCOUNT_PC_RATE_PROC_CANCELLATIONS								480
#define PF_PERFCOUNT_PC_RATE_BEGIN											482
#define PF_PERFCOUNT_PC_RATE_COMPLETION										484
