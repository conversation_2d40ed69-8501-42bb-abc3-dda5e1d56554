# سكريبت فحص الملفات الناقصة في نظام AppTech MSMS

Write-Host @"
========================================
    فحص الملفات الناقصة - AppTech MSMS
========================================
"@ -ForegroundColor Cyan

$missingFiles = @()
$missingDatabases = @()
$configIssues = @()

Write-Host "`n1. فحص الملفات الأساسية للتطبيقات..." -ForegroundColor Yellow

# الملفات الأساسية المطلوبة لكل تطبيق
$requiredFiles = @{
    "API" = @(
        "wwwroot\api\Global.asax",
        "wwwroot\api\Web.config",
        "wwwroot\api\bin\AppTech.MSMS.Web.dll",
        "wwwroot\api\bin\AppTech.MSMS.Domain.dll",
        "wwwroot\api\bin\AppTech.BusinessLogic.dll",
        "wwwroot\api\bin\AppTech.Data.dll",
        "wwwroot\api\bin\AppTech.Security.dll",
        "wwwroot\api\bin\EntityFramework.dll",
        "wwwroot\api\maincs.erp",
        "wwwroot\api\license.lic"
    )
    "Client" = @(
        "wwwroot\client\Global.asax",
        "wwwroot\client\Web.config",
        "wwwroot\client\bin\AppTech.MSMS.Web.dll",
        "wwwroot\client\maincs.erp"
    )
    "Portal" = @(
        "wwwroot\portal\Global.asax",
        "wwwroot\portal\Web.config", 
        "wwwroot\portal\bin\AppTech.MSMS.Web.dll",
        "wwwroot\portal\maincs.erp",
        "wwwroot\portal\license.lic"
    )
    "TopupInspector" = @(
        "wwwroot\TopupInspector\TopupInspector.exe",
        "wwwroot\TopupInspector\TopupInspector.exe.config",
        "wwwroot\TopupInspector\AppTech.MSMS.Domain.dll",
        "wwwroot\TopupInspector\maincs.erp",
        "wwwroot\TopupInspector\license.lic"
    )
}

foreach ($app in $requiredFiles.Keys) {
    Write-Host "`n  فحص تطبيق: $app" -ForegroundColor Gray
    
    foreach ($file in $requiredFiles[$app]) {
        if (Test-Path $file) {
            Write-Host "    ✓ $file" -ForegroundColor Green
        } else {
            Write-Host "    ✗ $file" -ForegroundColor Red
            $missingFiles += @{App=$app; File=$file; Critical=$true}
        }
    }
}

Write-Host "`n2. فحص ملفات قاعدة البيانات..." -ForegroundColor Yellow

# البحث عن ملفات قاعدة البيانات في مواقع محتملة
$dbSearchPaths = @(
    "C:\Program Files\Microsoft SQL Server\MSSQL*\MSSQL\DATA\*.mdf",
    "C:\Program Files (x86)\Microsoft SQL Server\MSSQL*\MSSQL\DATA\*.mdf",
    "C:\Data\*.mdf",
    "D:\Data\*.mdf",
    "E:\Data\*.mdf",
    ".\App_Data\*.mdf",
    "wwwroot\api\App_Data\*.mdf"
)

$foundDatabases = @()
foreach ($path in $dbSearchPaths) {
    try {
        $files = Get-ChildItem $path -ErrorAction SilentlyContinue
        if ($files) {
            foreach ($file in $files) {
                $foundDatabases += $file.FullName
                Write-Host "  ✓ وُجدت قاعدة بيانات: $($file.FullName)" -ForegroundColor Green
            }
        }
    } catch {
        # تجاهل الأخطاء
    }
}

if ($foundDatabases.Count -eq 0) {
    Write-Host "  ⚠ لم يتم العثور على ملفات قاعدة بيانات .mdf" -ForegroundColor Yellow
    $missingDatabases += "لم يتم العثور على ملفات قاعدة البيانات الأصلية"
}

Write-Host "`n3. فحص ملفات التكوين..." -ForegroundColor Yellow

# فحص ملفات maincs.erp (ملفات سلاسل الاتصال المشفرة)
$erpFiles = @(
    "wwwroot\api\maincs.erp",
    "wwwroot\client\maincs.erp", 
    "wwwroot\portal\maincs.erp",
    "wwwroot\TopupInspector\maincs.erp"
)

foreach ($erpFile in $erpFiles) {
    if (Test-Path $erpFile) {
        $content = Get-Content $erpFile -Raw
        if ($content -and $content.Length -gt 10) {
            Write-Host "  ✓ $erpFile (حجم: $($content.Length) حرف)" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ $erpFile (فارغ أو تالف)" -ForegroundColor Yellow
            $configIssues += "$erpFile فارغ أو تالف"
        }
    } else {
        Write-Host "  ✗ $erpFile مفقود" -ForegroundColor Red
        $missingFiles += @{App="Config"; File=$erpFile; Critical=$true}
    }
}

# فحص ملفات web.config لسلاسل الاتصال
$webConfigs = @(
    "wwwroot\api\Web.config",
    "wwwroot\client\Web.config",
    "wwwroot\portal\Web.config"
)

foreach ($configFile in $webConfigs) {
    if (Test-Path $configFile) {
        try {
            [xml]$config = Get-Content $configFile
            $connectionStrings = $config.configuration.connectionStrings
            
            if ($connectionStrings -and $connectionStrings.add) {
                Write-Host "  ✓ $configFile (يحتوي على سلاسل اتصال)" -ForegroundColor Green
            } else {
                Write-Host "  ⚠ $configFile (لا يحتوي على سلاسل اتصال)" -ForegroundColor Yellow
                $configIssues += "$configFile لا يحتوي على سلاسل اتصال"
            }
            
            # فحص إعداد ConnectionStringSource
            $appSettings = $config.configuration.appSettings
            if ($appSettings) {
                $connectionSource = $appSettings.add | Where-Object { $_.key -eq "ConnectionStringSource" }
                if ($connectionSource) {
                    Write-Host "    ConnectionStringSource: $($connectionSource.value)" -ForegroundColor Gray
                }
            }
        } catch {
            Write-Host "  ✗ $configFile (خطأ في قراءة XML)" -ForegroundColor Red
            $configIssues += "$configFile تالف أو غير صالح"
        }
    }
}

Write-Host "`n4. فحص ملفات الترخيص..." -ForegroundColor Yellow

$licenseFiles = @(
    "wwwroot\api\license.lic",
    "wwwroot\portal\license.lic",
    "wwwroot\TopupInspector\license.lic"
)

foreach ($licFile in $licenseFiles) {
    if (Test-Path $licFile) {
        $content = Get-Content $licFile -Raw
        if ($content -and $content.Length -gt 50) {
            Write-Host "  ✓ $licFile" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ $licFile (قد يكون تالف)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ✗ $licFile مفقود" -ForegroundColor Red
        $missingFiles += @{App="License"; File=$licFile; Critical=$false}
    }
}

Write-Host "`n5. فحص SQL Server..." -ForegroundColor Yellow

$sqlInstances = @("localhost", ".\SQLEXPRESS", "localhost\SQLEXPRESS", "(localdb)\MSSQLLocalDB")
$foundSqlServer = $false

foreach ($instance in $sqlInstances) {
    try {
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION, @@SERVERNAME" -ErrorAction Stop -Timeout 5
        if ($result) {
            Write-Host "  ✓ SQL Server متاح: $instance" -ForegroundColor Green
            Write-Host "    الإصدار: $($result[0].Column1.Substring(0, 50))..." -ForegroundColor Gray
            
            # البحث عن قواعد بيانات AppTech
            $databases = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT name FROM sys.databases WHERE name LIKE '%AppTech%' OR name LIKE '%MSMS%'" -ErrorAction SilentlyContinue
            if ($databases) {
                foreach ($db in $databases) {
                    Write-Host "    ✓ قاعدة بيانات موجودة: $($db.name)" -ForegroundColor Green
                }
            } else {
                Write-Host "    ⚠ لا توجد قواعد بيانات AppTech" -ForegroundColor Yellow
                $missingDatabases += "قاعدة بيانات AppTech غير موجودة في $instance"
            }
            
            $foundSqlServer = $true
            break
        }
    } catch {
        Write-Host "  ✗ فشل الاتصال بـ: $instance" -ForegroundColor Red
    }
}

if (-not $foundSqlServer) {
    Write-Host "  ✗ لم يتم العثور على SQL Server" -ForegroundColor Red
    $missingDatabases += "SQL Server غير متاح"
}

# تقرير النتائج
Write-Host "`n" + "="*60 -ForegroundColor Cyan
Write-Host "تقرير الملفات الناقصة" -ForegroundColor Cyan
Write-Host "="*60 -ForegroundColor Cyan

if ($missingFiles.Count -gt 0) {
    Write-Host "`n🔴 الملفات الناقصة:" -ForegroundColor Red
    foreach ($missing in $missingFiles) {
        $status = if ($missing.Critical) { "حرج" } else { "غير حرج" }
        Write-Host "  [$($missing.App)] $($missing.File) - $status" -ForegroundColor Red
    }
}

if ($missingDatabases.Count -gt 0) {
    Write-Host "`n🔴 مشاكل قاعدة البيانات:" -ForegroundColor Red
    foreach ($dbIssue in $missingDatabases) {
        Write-Host "  $dbIssue" -ForegroundColor Red
    }
}

if ($configIssues.Count -gt 0) {
    Write-Host "`n🟡 مشاكل التكوين:" -ForegroundColor Yellow
    foreach ($configIssue in $configIssues) {
        Write-Host "  $configIssue" -ForegroundColor Yellow
    }
}

if ($missingFiles.Count -eq 0 -and $missingDatabases.Count -eq 0 -and $configIssues.Count -eq 0) {
    Write-Host "`n✅ جميع الملفات الأساسية موجودة!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  يوجد ملفات ناقصة أو مشاكل في التكوين" -ForegroundColor Yellow
    Write-Host "يرجى الحصول على الملفات الناقصة من السيرفر القديم قبل المتابعة" -ForegroundColor Yellow
}

Write-Host "`nالملفات المهمة للبحث عنها في السيرفر القديم:" -ForegroundColor White
Write-Host "1. ملفات قاعدة البيانات (.mdf, .ldf)" -ForegroundColor Cyan
Write-Host "2. ملفات maincs.erp (سلاسل الاتصال المشفرة)" -ForegroundColor Cyan
Write-Host "3. ملفات license.lic (تراخيص النظام)" -ForegroundColor Cyan
Write-Host "4. أي ملفات DLL مفقودة" -ForegroundColor Cyan

pause
