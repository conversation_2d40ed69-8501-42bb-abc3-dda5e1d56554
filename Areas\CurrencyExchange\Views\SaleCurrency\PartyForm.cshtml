﻿@model AppTech.MSMS.Domain.Models.SaleCurrency
@{
    Layout = "/Views/Shared/_FormLayout.cshtml";
    ViewBag.Title = "بيع عملة";
}

<div class="form-group">
    <div class="col-md-12">
        @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
        @Html.DropDownListFor(m => m.CurrencyID, (SelectList) ViewBag.Currencies)
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new {@class = "col-md-8"})
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ExchangePrice, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ExchangePrice, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.ExchangePrice, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ExchangeAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ExchangeAmount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.ExchangeAmount, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<script>

    $(function() {

        $('#ExchangePrice').prop('readonly', true);
        $('#ExchangeAmount').prop('readonly', true);
        $('#CurrencyID').on('change',
            function() {
                var curId = $('#CurrencyID').val();
                AjaxCall('/CurrencyExchange/BuyCurrency/ExchangeRate?currencyId=' + curId).done(function(response) {
                    i('respone ex rate :' + response);
                    $('#ExchangePrice').val(response);
                }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
            });

        $('#ExchangePrice').on('input',
            function() {
                var rate = Number($('#ExchangePrice').val());
                var amount = Number($('#Amount').val());
                var exAmount = rate * amount;
                $('#ExchangeAmount').val(exAmount);
            });


        $('#Amount').on('input',
            function() {
                var rate = Number($('#ExchangePrice').val());
                var amount = Number($('#Amount').val());
                var exAmount = rate * amount;
                $('#ExchangeAmount').val(exAmount);
            });
    })
    //$( "#myselect option:selected" ).text();
</script>