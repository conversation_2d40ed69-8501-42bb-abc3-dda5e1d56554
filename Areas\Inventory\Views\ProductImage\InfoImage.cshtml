﻿@model AppTech.MSMS.Domain.Models.ProductImage

@{
     Layout = "~/Views/Shared/_MultiForm.cshtml"; 
}

<input type="hidden" name="ProductID" value="@ViewBag.ProductID" />
@if (Model.ID == 0)
{
    <div>
        @*<div class="form-group">
                @Html.LabelFor(model => item.ProductID, new { @class = "control-label col-md-2" })
                <div class="col-md-3">
                    <input type="number" name="@ViewBag.ParentID" value="@Html.DropDownListFor(model => item.ProductID, (SelectList)ViewBag.ProductCategorys, new { htmlAttributes = new { @class = "form-control" } })" disabled />
                </div>
            </div>*@
        <div class="form-group">
            @Html.LabelFor(model => model.ImageName, new { @class = "control-label col-md-2" })
            <div style="position: relative;">
                <input type="file" name="ImageData" size="40" onchange="showImg(this)">
            </div>
            <img class="img-thumbnail" width="150" height="150" id="preview"
                 src="@Url.Action("GetImage", "ProductImage", new { Model.ID })" />
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Title, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Title, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Title)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description, new { htmlAttributes = new { @class = "form-control" } })
                @Html.ValidationMessageFor(model => model.Description)
            </div>
        </div>

        <div class="form-group">
            @Html.LabelFor(model => model.Extainfo, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Extainfo, new { htmlAttributes = new { @class = "form-control" } })

                @Html.ValidationMessageFor(model => model.Extainfo)
            </div>
        </div>
    </div>
}
else
{
    <p> else</p>
}



<script>
    hideLoading();
</script>
<script>

    $(function () {
        try {
            formHelper.onSuccess = function (data) {
                log('user party form');
                i('data> ' + data);
                $("#modal").modal('hide');
                $("#list").html(data);
            }
            formHelper.onBegin = function (context) {
                return true;
            };
        } catch (e) {
        }
    });
     $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });

</script>