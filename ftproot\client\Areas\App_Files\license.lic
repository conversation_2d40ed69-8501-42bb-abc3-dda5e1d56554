﻿PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTE2Ij8+PExpY2Vuc2VFbnRpdHkgeG1sbnM6eHNpPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxL1hNTFNjaGVtYS1pbnN0YW5jZSIgeG1sbnM6eHNkPSJodHRwOi8vd3d3LnczLm9yZy8yMDAxL1hNTFNjaGVtYSIgeHNpOnR5cGU9IkxpY2Vuc2VJbmZvIj48QXBwTmFtZT5hcHB0ZWNoPC9BcHBOYW1lPjxVSUQ+MVFUWDhLQy1PMFo0QVEtMUs0TU9WMy0xMlI2MlhXPC9VSUQ+PFR5cGU+U2luZ2xlPC9UeXBlPjxWZXJzaW9uVHlwZT5GdWxsPC9WZXJzaW9uVHlwZT48RXhwaXJ5RGF0ZT4wMDAxLTAxLTAxVDAwOjAwOjAwPC9FeHBpcnlEYXRlPjxDcmVhdGVEYXRlVGltZT4yMDIxLTAzLTAxVDAzOjQzOjU3Ljg2OTM1MzMrMDM6MDA8L0NyZWF0ZURhdGVUaW1lPjxTeXN0ZW1MZXZlbD5BZHZhbmNlZDwvU3lzdGVtTGV2ZWw+PE1heFVzZXJzPjA8L01heFVzZXJzPjxNYXhCcmFuY2hlcz4wPC9NYXhCcmFuY2hlcz48Q3VzdG9tZXJOdW1iZXI+MDwvQ3VzdG9tZXJOdW1iZXI+PFNpZ25hdHVyZSB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnIyI+PFNpZ25lZEluZm8+PENhbm9uaWNhbGl6YXRpb25NZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy9UUi8yMDAxL1JFQy14bWwtYzE0bi0yMDAxMDMxNSIgLz48U2lnbmF0dXJlTWV0aG9kIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnI3JzYS1zaGExIiAvPjxSZWZlcmVuY2UgVVJJPSIiPjxUcmFuc2Zvcm1zPjxUcmFuc2Zvcm0gQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjZW52ZWxvcGVkLXNpZ25hdHVyZSIgLz48L1RyYW5zZm9ybXM+PERpZ2VzdE1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNzaGExIiAvPjxEaWdlc3RWYWx1ZT52ZFpnbUQ4blRYNjJMOFpEWlJDRm1rMEoxaDA9PC9EaWdlc3RWYWx1ZT48L1JlZmVyZW5jZT48L1NpZ25lZEluZm8+PFNpZ25hdHVyZVZhbHVlPm9kNWk3NTZBMDNwbklnZCtsQlNCMkFPbUdvSTZXbEtCMDcyOFJnNGhVN1FSOWRqZ2NHa2pZTE5nK3FjOGFCT2p1aEM2N0FxY0Z0bHZSTHMzTUZDTUtiTWFVajM5NVkzdTNKYVZyOHRLdHUyVEVOVDRkREI3Z2ZybWNwVEdDVmMzS1Fxa2dHYzBERTk0YXdsQVVPci9VT0FGdHRyVVprSlYvS09MWHczYmpBNllhN01weTBXeFkyQ05VdVE5TmhucHRhb2RYZUFWWVIxUlJadGVKM2ZYNXR4TVVVaGUvL3ZOK1ltK3hTcG5JUHlBTzRpT3ZqNjlxSG1BeWh2KzUxeXNrVGI2dVYvc0tZVFlxbngvemhYeUFLVkhES2E0Z2lFL2lsaGRtakpSTEFFRkNycEx5L0N3MmNzOEdjRlh1dFdKQlB4Y3hvMFhBMlRsR2pweGdwOEIrQT09PC9TaWduYXR1cmVWYWx1ZT48L1NpZ25hdHVyZT48L0xpY2Vuc2VFbnRpdHk+