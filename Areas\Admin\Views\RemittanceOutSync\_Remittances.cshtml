﻿@model IEnumerable<AppTech.MSMS.Domain.Models.RemittanceIn>


<table class="table table-hover table-bordered table-responsive">
    <thead>
    <tr>


        <th>
            @Html.DisplayNameFor(model => model.RemittanceNumber)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Amount)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.CurrencyID)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.BeneficiaryName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.BeneficiaryPhone)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SenderName)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.SenderPhone)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Date)
        </th>
        <th>
            @Html.DisplayNameFor(model => model.Note)
        </th>

        <th></th>
    </tr>
    </thead>
    @foreach (var item in Model)
    {
        <tbody>
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.RemittanceNumber)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Amount)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Currency.Name)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.BeneficiaryName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.BeneficiaryPhone)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SenderName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.SenderPhone)
            </td>

            <td>
                @Html.DisplayFor(modelItem => item.Date)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.Note)
            </td>
            <td>
                <button class="btn btn-primary btn-round" onclick="openDetails(@item.ID)">أستعلام</button>
            </td>
        </tr>
        </tbody>
    }

</table>
<script>
    function openDetails(id) {
        i('open details id:' + id);
        //CrudHelper.openForm(id, 'سحب حوالة');
        window.location.href = '/#!/route/Admin/RemittanceOutSync/AddOrEdit/' + id;
    }
</script>