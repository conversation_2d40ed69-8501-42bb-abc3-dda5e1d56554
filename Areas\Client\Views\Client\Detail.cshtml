﻿@using AppTech.Common.Extensions
@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Code.HtmlHelpers
@using AppTech.MSMS.Web.Security
@model AppTech.MSMS.Domain.Models.Client


<div id="user-profile-1" class="user-profile row">
<div class="col-xs-12 col-sm-3 center">


    <div>
        <span class="profile-picture">
            <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content("~/Photos/profile-pic.jpg")"/>
        </span>

        <div class="space-4"></div>

        <div class="width-80 label label-info label-xlg arrowed-in arrowed-in-right">
            <div class="inline position-relative">
                <a href="" class="user-title-label dropdown-toggle" data-toggle="dropdown">
                    @if (Model.IsActive.AsBool())
                    {
                        <i class="ace-icon fa fa-circle light-green"></i>
                    }
                    else
                    {
                        <i class="ace-icon fa fa-circle red"></i>
                    }


                    &nbsp;
                    <span class="white">@Model.Name</span>
                </a>

                <ul class="align-left dropdown-menu dropdown-caret dropdown-lighter">
                    <li class="dropdown-header"> الحالة </li>

                    @if (Model.IsActive.AsBool())
                    {
                        <li>
                            <a href="">
                                <i class="ace-icon fa fa-circle green"></i>
                                &nbsp;
                                <span class="green">مفعل</span>
                            </a>
                        </li>
                    }
                    else
                    {
                        <li>
                            <a href="">
                                <i class="ace-icon fa fa-circle red"></i>
                                &nbsp;
                                <span class="red">موقف</span>
                            </a>
                        </li>
                    }


                </ul>
            </div>
        </div>
    </div>

    <div class="space-6"></div>

    <div class="profile-contact-info">
        <input type="hidden" id="id" value="@Model.ID"/>
        <div class="profile-contact-links align-right">
            <a class="btn btn-link " id="documents" href="">
                <i class="ace-icon fa fa-user-secret bigger-120 green"></i>
                الوثائق

            </a>
            @Html.ActionButton((string)ViewBag.PageName, "PERMISSIONS", "الصلاحيات", "user-secret bigger-120 green", "", "الصلاحيات", true, "permission")
            @*<a class="btn btn-link " id="permission" href="">
            <i class="ace-icon fa fa-user-secret bigger-120 green"></i>
            الصلاحيات
        </a>*@
          
            @if (DomainManager.SupportDistributors)
            {
                @Html.ActionButton((string)ViewBag.PageName, "UPGRADE", "   الترقيةالى موزع", "user-tie bigger-120 green", "", "   الترقيةالى موزع", false, "upgradeToDistributor")
            @*<button class="btn btn-link " id="upgradeToDistributor" href="">
                <i class="ace-icon fa fa-user bigger-120 green"></i>
                الترقيةالى 
            </button>*@
        }
            @if (DomainManager.SupportAgent)
            {
                @Html.ActionButton((string)ViewBag.PageName, "UPGRADE", "   الترقيةالى وكيل", "user-tie bigger-120 green", "", "   الترقيةالى وكيل", false, "upgrade")
                @*<button class="btn btn-link " id="upgrade" href="">
                <i class="ace-icon fa fa-user bigger-120 green"></i>
                الترقيةالى وكيل
            </button>*@
            }


            @if (DomainManager.LicensedModules.Any(x => x.Equals("Merchants")))
            {
                @Html.ActionButton((string)ViewBag.PageName, "UPGRADE", "   الترقيةالى تاجر", "store bigger-120 green", "", "   الترقيةالى تاجر", false, "upgradetomerchant")

                <button class="btn btn-link " id="upgradetomerchant" href="">
                    <i class="ace-icon fa fa-user bigger-120 green"></i>
                    الترقية الى تاجر

                </button>
            }

        </div>

        <div class="space-6"></div>

    </div>

    <div class="hr hr12 dotted"></div>
    <div class="clearfix">
        <div class="grid2">
            <span class="bigger-175 blue">@ViewBag.CurrentBalance</span>

            <br/>
            الرصيد
        </div>

        <div class="grid2">
            <span class="bigger-175 blue">@ViewBag.SlatingAmount</span>

            <br/>
            التسقيف
        </div>
    </div>

    <div class="hr hr16 dotted"></div>
</div>

<div class="col-xs-12 col-sm-9">

    <div class="space-12"></div>


    <div class="profile-user-info profile-user-info-striped">

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم حساب العميل </div>

            <div class="profile-info-value">
                <span class="editable" id="age"> @Html.DisplayFor(model => model.Number) </span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> اسم العميل </div>

            <div class="profile-info-value">
                <span class="editable" id="username"> @Html.DisplayFor(model => model.Name)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> الأيميل </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.Email)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> العنوان </div>

            <div class="profile-info-value">
                <i class="fa fa-map-marker light-orange bigger-110"></i>
                <span class="editable" id="country">اليمن</span>
                <span class="editable" id="city"> @Html.DisplayFor(model => model.Address)</span>
            </div>
        </div>

        @if (Model.Agent != null)
        {
            <div class="profile-info-row">
                <div class="profile-info-name"> مسجل  لدى وكيل </div>

                <div class="profile-info-value">
                    <span class="editable" id="login"> @Html.DisplayFor(model => model.Agent.Name)</span>
                </div>
            </div>
        }
        @if (DomainManager.SupportMultiBranching)
        {
            <div class="profile-info-row">
                <div class="profile-info-name">الفرع </div>

                <div class="profile-info-value">
                    <span class="editable" id="login"> @Html.DisplayFor(model => model.Branch.Name)</span>
                </div>
            </div>
        }
        <div class="profile-info-row">
            <div class="profile-info-name"> أرقام تواصل أخرى </div>

            <div class="profile-info-value">
                <span class="editable" id="login"> @Html.DisplayFor(model => model.ContactNumber)</span>
            </div>
        </div>
        <div class="profile-info-row">
            <div class="profile-info-name">نوع النشاط </div>

            <div class="profile-info-value">
                <span class="editable" id="signup"> @Html.DisplayFor(model => model.ShopName)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> تاريخ التسجيل </div>

            <div class="profile-info-value">
                <span class="editable" id="about"> @Html.DisplayFor(model => model.CreatedTime)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> نوع البطاقة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardType)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> رقم البطاقة </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardNumber)</span>
            </div>
        </div>

        <div class="profile-info-row">
            <div class="profile-info-name"> مكان الأصدار </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardIssuePlace)</span>
            </div>
        </div>


        <div class="profile-info-row">
            <div class="profile-info-name"> تاريخ الأصدار </div>

            <div class="profile-info-value">
                <span class="editable"> @Html.DisplayFor(model => model.CardIssueDate)</span>
            </div>
        </div>


    
    </div>

    <div class="space-20"></div>

    <div class="hr hr2 hr-double"></div>

    <div class="space-6"></div>
    <div class="center">
        <a href="/#!/route/Client/Client" class="btn btn-sm btn-primary btn-white btn-round">
            <i class="ace-icon fa fa-rss bigger-150 middle orange2"></i>
            <span class="bigger-110">الرجوع الى العملاء</span>

            <i class="icon-on-right ace-icon fa fa-arrow-right"></i>
        </a>
    </div>
</div>
</div>


@Html.Partial("_Modal")

<div id="modalDocuments" class="modal fade" role="dialog" aria-labelledby="model-title">
    <div class="modal-dialog" role="form">
        <div class="modal-content">

            <div class="modal-header" style="background: cornflowerblue">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="modal-title" style="color: white"></h4>
            </div>

            <div class="modal-body" style="padding: 40px 50px;">
                <form id="multiform" action="Client/Client/Documents" class="form-horizontal" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <div style="position: relative;">
                            <label>أختر صورة...</label>
                            <input type="file" name="ImageData" size="40" onchange="showImg(this)">
                            <a class="btn" href="javascript:;">


                            </a>
                        </div>
                    </div>

                    <img class="img-thumbnail" width="150" height="150" id="preview"
                         src="@Url.Action("GetImage", "Client",
                                  new {Model.ID})"/>
                   

                    @Html.Partial("_FormAction")
                </form>

            </div>
            <div class="modal-footer">
                <button type="button" id="cancelModal" class="btn btn-danger btn-default pull-left" data-dismiss="modal">
                    <i class="glyphicon glyphicon-remove"></i> خروج
                </button>
                <span class="alert"></span>

                <button class="btn btn-white btn-default btn-round pull-right" onclick="resetButton();">
                    <i class="ace-icon fa fa-undo bigger-110"></i>
                </button>
            </div>

        </div>

    </div>
</div>

<script src="~/Scripts/bootbox.js"></script>
<script>

    hideLoading();
    $("#documents").on('click',
        function() {

            var $modal = $("#modalDocuments");
            $modal.modal("show");
        });

    $("#permission").on('click',
        function() {
            var id = @Model.ID;
            var url = "/Client/Client/Permissions/" + id;
            openViewAsModal(url, "صلاحيات العميل");
        });
    

    $("#upgradeToDistributor").on(ace.click_event,
        function() {
            var id = @Model.ID;
            bootbox.confirm("سوف يتم ترقية العميل الى موزع , هل انت متأكد?",
                function(result) {
                    if (result) {
                        showLoading();
                        try {
                            $.ajax({
                                url: '/Client/Client/UpgradeToDistributor',
                                data: { id: id },
                                success: function(data) {
                                    hideLoading();
                                    alert(data);
                                    $("#upgrade").hide();
                                    window.location.href = '/#!/route/agency/Distributor';
                                },
                                error: function(xhr, ajaxOptions, thrownError) {
                                    hideLoading();
                                    handleXhr(xhr);
                                }
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }
                });
        });

    $("#upgrade").on(ace.click_event,
        function() {
            var id = @Model.ID;
            bootbox.confirm("سوف يتم ترقية العميل الى وكيل , هل انت متأكد?",
                function(result) {
                    if (result) {
                        showLoading();
                        try {
                            $.ajax({
                                url: '/Client/Client/UpgradeToAgent',
                                data: { id: id },
                                success: function(data) {
                                    hideLoading();
                                    alert(data);
                                    window.location.href = '/#!/route/agency/agent';
                                },
                                error: function(xhr, ajaxOptions, thrownError) {
                                    hideLoading();
                                    handleXhr(xhr);
                                }
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }
                });
        });


    
							 
							 
							 
							  $("#upgradetomerchant").on(ace.click_event,
        function () {
            var id = @Model.ID;
            bootbox.confirm("سوف يتم ترقية العميل الى تاجر , هل انت متأكد?",
                function (result) {
                    if (result) {
                        showLoading();
                        try {
                            $.ajax({
                                url: '/Client/Client/UpgradeToMerchant',
                                data: { id: id },
                                success: function (data) {
                                    hideLoading();
                                    alert(data);
                                    showSuccess(data);
                                    $("#upgrade").hide();
                                    window.location.href = '/#!/route/merchants/merchant';
                                },
                                error: function (xhr, ajaxOptions, thrownError) {
                                    hideLoading();
                                    handleXhr(xhr);
                                }
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }
                });
        });

    $("#activate").on(ace.click_event,
        function() {
            var id = @Model.ID;
            bootbox.confirm("سوف يتم تفعيل حساب العميل , هل انت متأكد?",
                function(result) {
                    if (result) {
                        showLoading();
                        try {
                            $.ajax({
                                url: '/Client/Client/Activate',
                                data: { id: id },
                                success: function(data) {
                                    hideLoading();
                                    alert(data);
                                    showSuccess(data);

                                    $("#activate").hide();
                                },
                                error: function(xhr, ajaxOptions, thrownError) {
                                    hideLoading();
                                    handleXhr(xhr);
                                }
                            });
                        } catch (e) {
                            alert(e);
                        }
                    }
                });
        });

</script>

<script>

    function onCrudSuccess(data) {
        hideFormLoading();

        if (data.success) {
            $("#modalDocuments").modal('hide');
            ar('تم حفظ الصورة بنجاح');

        } else {
            ar('لم يتمكن النظام من حفظ الصورة');
        }

    }

    function onCrudFailure(xhr, status) {
        hideFormLoading();
        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);

    }

    $(function() {
        $("#formloader").hide();
        hideFormLoading();
        $("#multiform").submit(function(event) {
            i('onsubmit');
            showFormLoading();
            var dataString;
            event.preventDefault();
            var controller = $("#Controller").val();
            i('Model.ID:' + @Model.ID);
            var url = '@Url.Action("Documents", "Client", new {id = Model.ID})';
            if ($("#multiform").attr("enctype") === "multipart/form-data") {
                dataString = new FormData($("#multiform").get(0));
                contentType = false;
                processData = false;
            } else {
                log('not multipart');
            }
            $.ajax({
                type: "POST",
                url: url,
                data: dataString,
                dataType:
                    "json", //change to your own, else read my note above on enabling the JsonValueProviderFactory in MVC
                contentType: contentType,
                processData: processData,
                success: function(data) {
                    onCrudSuccess(data);
                },
                error: function(xhr, textStatus, errorThrown) {
                    onCrudFailure(xhr);
                }
            });
        }); //end .submit()
    });
</script>