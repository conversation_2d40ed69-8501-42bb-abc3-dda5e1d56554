# تقرير نسخ الملفات يدوياً - AppTech MSMS

## 📋 **الملفات المطلوب نسخها:**

### **1. TopupProcessor المحدث (v1.2 - 4.2):**
**المصدر:** `E:\inetpub\New folder\TopupProcessor v1.2 - 4.2\`
**الهدف:** `E:\inetpub\TopupProcessor\`

**الملفات:**
- AppTech.BusinessLogic.dll
- AppTech.Common.dll  
- AppTech.Data.dll
- TopupInspector.exe
- TopupInspector.exe.config
- license.lic
- maincs.erp

### **2. ملفات CSV الأساسية:**
**المصدر:** `E:\inetpub\New folder\`
**الهدف:** `E:\inetpub\Data\`

**الملفات الأساسية:**
- Account.csv (13,439 سجل)
- Agent.csv
- Agent2023.csv
- Branch.csv
- UserInfo.csv
- UserRole.csv
- AccountUser.csv
- AccountParent.csv

**الملفات الإضافية:**
- acccount2021.csv
- acountusers.csv
- agg.csv
- balanc.csv
- balances 2021.csv
- cli.csv
- custm.csv
- groupitem.csv
- partiesACOUNTS.csv
- sim.csv
- siminvoice.csv

### **3. ملفات SQL:**
**المصدر:** `E:\inetpub\New folder\`
**الهدف:** `E:\inetpub\SQL\`

**الملفات:**
- 1111111.sql (PostgreSQL database)
- back.sql (backup)
- topup.sql (topup data)
- dv.sql

### **4. مجلد OLAP:**
**المصدر:** `E:\inetpub\New folder\OLAP\`
**الهدف:** `E:\inetpub\OLAP\`

**المحتوى المهم:**
- Config/msmdsrv.ini (إعدادات SSAS)
- Config/msmdsrv.bak (نسخة احتياطية)
- Data/ (ملفات البيانات)
- Backup/ (مجلد النسخ الاحتياطية)
- Log/ (مجلد السجلات)
- Temp/ (مجلد مؤقت)

### **5. ملفات التوثيق:**
**المصدر:** `E:\inetpub\New folder\`
**الهدف:** `E:\inetpub\Documentation\`

**الملفات:**
- deployment-commands.md (دليل النشر الشامل)
- nawafd missing indexed.xlsx (فهارس مفقودة)

## 🔧 **أوامر النسخ اليدوي:**

### **إنشاء المجلدات:**
```cmd
mkdir "E:\inetpub\TopupProcessor"
mkdir "E:\inetpub\Data"
mkdir "E:\inetpub\SQL"
mkdir "E:\inetpub\Documentation"
mkdir "E:\inetpub\OLAP"
```

### **نسخ TopupProcessor:**
```cmd
copy "E:\inetpub\New folder\TopupProcessor v1.2 - 4.2\*" "E:\inetpub\TopupProcessor\"
```

### **نسخ ملفات CSV:**
```cmd
copy "E:\inetpub\New folder\Account.csv" "E:\inetpub\Data\"
copy "E:\inetpub\New folder\Agent.csv" "E:\inetpub\Data\"
copy "E:\inetpub\New folder\Branch.csv" "E:\inetpub\Data\"
copy "E:\inetpub\New folder\UserInfo.csv" "E:\inetpub\Data\"
copy "E:\inetpub\New folder\balances 2021.csv" "E:\inetpub\Data\"
```

### **نسخ ملفات SQL:**
```cmd
copy "E:\inetpub\New folder\*.sql" "E:\inetpub\SQL\"
```

### **نسخ مجلد OLAP:**
```cmd
xcopy "E:\inetpub\New folder\OLAP" "E:\inetpub\OLAP" /E /I /Y
```

### **نسخ التوثيق:**
```cmd
copy "E:\inetpub\New folder\deployment-commands.md" "E:\inetpub\Documentation\"
copy "E:\inetpub\New folder\*.xlsx" "E:\inetpub\Documentation\"
```

## ✅ **التحقق من النسخ:**

### **فحص TopupProcessor:**
- تحقق من وجود 7 ملفات في `E:\inetpub\TopupProcessor\`
- تأكد من وجود TopupInspector.exe

### **فحص البيانات:**
- تحقق من وجود Account.csv (يجب أن يكون حجمه كبير)
- فحص ملفات CSV الأخرى

### **فحص OLAP:**
- تأكد من وجود `E:\inetpub\OLAP\Config\msmdsrv.ini`
- تأكد من وجود `E:\inetpub\OLAP\Config\msmdsrv.bak`

## 🎯 **الخطوات التالية بعد النسخ:**

1. **تشغيل سكريبت استعادة msmdsrv:**
   ```powershell
   .\restore-msmdsrv-config.ps1
   ```

2. **تشغيل سكريبت استعادة قاعدة البيانات:**
   ```powershell
   .\transfer-mdf-files.ps1
   ```

3. **تحديث ملفات التكوين:**
   - تحديث web.config
   - تحديث سلاسل الاتصال

4. **اختبار النظام:**
   - اختبار TopupProcessor
   - اختبار الاتصال بقاعدة البيانات
   - اختبار OLAP

## 📊 **الإحصائيات المتوقعة:**

- **TopupProcessor:** 7 ملفات (~2-5 MB)
- **ملفات CSV:** 19+ ملف (~10-50 MB)
- **ملفات SQL:** 4 ملفات (~1-10 MB)
- **OLAP:** مجلد كامل (~5-20 MB)
- **التوثيق:** 2 ملف (~1-5 MB)

**المجموع:** ~20-90 MB من الملفات المفيدة

---

**ملاحظة:** يمكن تنفيذ هذه العمليات يدوياً باستخدام Windows Explorer أو Command Prompt إذا فشلت السكريبتات.
