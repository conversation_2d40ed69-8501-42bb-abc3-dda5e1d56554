﻿@model IEnumerable<AppTech.MSMS.Domain.Models.AccountUser>
<table class="table table-hover">
    <thead>
    <tr>

        <th>
            @Html.DisplayNameFor(model => model.UserID)
        </th>
        <th>

            @Html.DisplayName("الحالة")
        </th>

        <th>
            @Html.DisplayNameFor(model => model.CreatedTime)
        </th>

        <th></th>
    </tr>
    </thead>
    @foreach (var item in Model)
    {
        <tbody>
        <tr>

            <td>
                @Html.DisplayFor(modelItem => item.UserInfo1.UserName)
            </td>

            <td>

                @if (item.UserInfo1.Status == 1)
                {
                    <button class="btn btn-link green disabled">
                        <i class="ace-icon fa fa-flag bigger-110 green"></i>
                        نشط
                    </button>
                }
                else
                {
                    <button class="btn btn-link red disabled">
                        <i class="ace-icon fa fa-flag bigger-110 red"></i>
                        موقف
                    </button>
                }

            </td>

            <td>
                @Html.DisplayFor(modelItem => item.CreatedTime)
            </td>
            <td>

                <button class="btn btn-link " onclick="resetpass(@item.UserID)">
                    <i class="ace-icon fa fa-key bigger-110 green"></i>
                    إعادة تعيين كلمة المرور
                </button>


                <Button class="btn btn-default" onclick="openEditModal('@item.UserID')">
                    <i class="ace-icon fa fa-flag bigger-110"></i>
                    تعديل الحالة
                </Button>
      
            </td>
        </tr>
        </tbody>
    }

</table>

<script>
    function openEditModal(id) {
        i('open modal id' + id);
        openViewAsModal('Security/User/EditUser?id=' + id, " تعديل حالة المستخدم");
    }
</script>