﻿@using AppTech.MSMS.Domain
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.SatelliteProvider
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
@Html.EditorFor(model => model.ID, new { htmlAttributes = new { @style = "display: none" } })

<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name)
        @Html.ValidationMessageFor(model => model.Name)
    </div>
</div>

<div class="form-group">
    <label class="col-md-2" style="text-align: left;">المحافظة</label>
    <div class="col-md-10">
        <select id="Provinces" name="" Class="select2" style="width: 110px;" placeholder="اختر محافظة" required></select>
    </div>
</div>
<div class="form-group">
    <label class="col-md-2" style="text-align: left;">المديرية</label>
    <div class="col-md-10">
        <select id="RegionID" name="RegionID" class="select2" style="width: 110px;" placeholder="اختر منطقة" required></select>
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Phone, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Phone, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.Phone)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.AccountID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AccountID")
                   {
                       Width = 300,
                       SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
                       FilterType = ComboBoxFilterType.Contains
                   })
        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.PrafitAmount, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.PrafitAmount, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.PrafitAmount)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">نوع الربح </label>
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.PrafitStatus, new[]
     {
         new SelectListItem {Text = "اختر النوع", Value = "", Selected = true},
         new SelectListItem {Text = "بالنسبة", Value = "1"},
         new SelectListItem {Text = "بالريال", Value = "2"}
     })

        @Html.ValidationMessageFor(model => model.PrafitStatus)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.MinSubscribe, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.MinSubscribe, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.MinSubscribe)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
        @Html.ValidationMessageFor(model => model.Note)
    </div>
</div>
<script>
    function loadProvincesList() {
        i('loadDataList');
        fillDataList('Provinces', '/Satellite/SatelliteProvider/GetProvinces');
    }
    function loadRegionList(id) {
        i('loadDataList');
        fillDataList('RegionID', '/Satellite/SatelliteProvider/GetRegions?ID=' + id);
    }
    loadProvincesList();
    loadRegionList(1);
    $("#Provinces").on('change',
        function () {
            var id = $("#Provinces option:selected").val();
            i('id: ' + id);
            loadRegionList(id);
        });
</script>
