﻿@using AppTech.MSMS.Domain
@model AppTech.MSMS.Web.Models.LoginModel

@{
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}

<div class="main-container">
    <div class="main-content">
        <div class="row">
            <div class="col-sm-10 col-sm-offset-1">
                <div class="login-container">
                    <div class="center">
                        <h1>
                            <span class="white" id="id-text2">@ClientLicense.Customer.CompanyInfo.Name </span>
                        </h1>
                    </div>

                    <div class="space-6"></div>
                    <div class="position-relative">
                        <div id="login-box" class="login-box visible widget-box no-border">
                            <div class="widget-body">
                                <div class="widget-main">
                                    <h4 class="header blue lighter bigger">
                                        
                                        @ClientLicense.Customer.CompanyInfo.Name
                                    </h4>
                                    <div class="space-6"></div>
                                    @using (Html.BeginForm("Admin", "Account", FormMethod.Post, new { id = "loginform" }))
                                    {

                                        @Html.AntiForgeryToken()
                                        @Html.ValidationSummary(true, "لم يتم مصادقة المستخدم ", new { @class = "text-danger" })

                                        <li>
                                            @Html.TextBoxFor(m => m.Email, new { placeholder = "اسم المستخدم" })
                                            @Html.ValidationMessageFor(m => m.Email, string.Empty, new { @class = "invalid" })
                                        </li>
                                        <li>
                                            @Html.PasswordFor(m => m.Password, new { placeholder = "كلمة المرور",autocomplete="off" })
                                            @Html.ValidationMessageFor(m => m.Password, string.Empty, new { @class = "invalid" })
                                        </li>
                                        <div class="space-6"></div>
                                      
                                        <li>
                                            <div class="form-group">
                                                <div class="col-md-offset-2 col-md-10">
                                                    @Html.GoogleCaptcha()
                                                    @Html.InvalidGoogleCaptchaLabel("لم يتمكن من المصادقه ال reCaptcha")
                                                </div>
                                            </div>

                                        </li>

                                      

                                        <div id="myLoadingElement" style="display: none;">
                                 
                                        </div>
                                        <div class="space-6"></div>

                                        <input class="width-50 pull-right btn btn-sm btn-primary" type="submit" id="btnLogin" value="التسجيل" />


                                      @Html.HiddenFor(m=>m.hdrandomSeed)
                                      @Html.HiddenFor(m=>m.Token)
                                        <div class="space-6"></div>
                                        <div class="space-6"></div>
                                        <p>رقم المعرف:<input type="text" id="UUID" name="UUID" style="width:300px" /></p>
                                   

                                       
                                        <div class="space-6"></div>
                                    }
                                    <button class="clipboard btn btn-white" data-clipboard-text="@Html.DisplayFor(model => model.UUID)">
                                        <i class="ace-icon fa fa-copy"></i> نسخ رقم المعرف
                                    </button>
                                    <div class="space-6"></div>
                                </div>
                            </div><!-- /.widget-body -->


                        </div><!-- /.login-box -->
                        <div class="center-block">
                            <span>KN:@DomainManager.DbName</span>
                            <span>Version:@DomainManager.ServerVersion</span>
                        </div>
                    </div><!-- /.position-relative -->
                </div>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.main-content -->

</div><!-- /.main-container -->


@*<script>

    var hasConsole = typeof console !== "undefined";

    var fingerprintReport = function () {
        Fingerprint2.get(function(components) {
            var murmur = Fingerprint2.x64hash128(components.map(function(pair) { return pair.value }).join(), 31);
         //   document.querySelector("#UUID").value = murmur;
            var uuid = finguard();
            document.querySelector("#UUID").value = uuid;

            console.log('Fingerprint2: '+murmur);
            console.log('finguard: '+uuid);
        });
    }

    var cancelId;
    var cancelFunction;

    // see usage note in the README
    if (window.requestIdleCallback) {
        cancelId = requestIdleCallback(fingerprintReport);
        cancelFunction = cancelIdleCallback;
    } else {
        cancelId = setTimeout(fingerprintReport, 500);
        cancelFunction = clearTimeout;
    }

    document.querySelector("#btn").addEventListener("click",
        function() {
            if (cancelId) {
                cancelFunction(cancelId);
                cancelId = undefined;
            }
            fingerprintReport();
        });
</script>*@


<!-- basic scripts -->
<!-- inline scripts related to this page -->
@*<script>
        function OnFormBegin(context) {
            //showFormLoading();
        }
        if ($("#Date")[0]) {
            $("#Date").val(getToday());
            $('#Date').prop('readonly', true);
        }
        function onSuccess(data) {
            i('onSuccess');
            ar('onSuccess');
           // hideFormLoading();
            window.location.href = '/#/dashboard';
        }

        function onFailure(xhr, status) {
            hideFormLoading();
            var msg = parseXhr(xhr);
            log('onCrudFailure xhr msg:' + msg);
            alert(msg);
            // showError(msg);

            //  log('responseText.Message:' +xhr.responseText.Message);
            //  hideLoading();
            //  handleXhr(xhr);
        }
    </script>*@

