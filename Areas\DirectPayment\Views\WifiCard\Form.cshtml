﻿@model AppTech.MSMS.Domain.Models.WifiCard

@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}
@Html.HiddenFor(model => model.ID)
@if (Model.ID == 0)
{
    <div class="form-group">
        @Html.Label("تصدير من ملف اكسل", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.ViaExcel, new { htmlAttributes = new { onClick = "toggle(this)" } })
        </div>
    </div>
    <div class="excel-browser">
        <div class="form-group">
            <label class="col-sm-2 control-label">أختر ملف الأكسل</label>
            <div style="position: relative;">
                <input type="file" name="excelfile" size="40">
            </div>
        </div>

    </div>
}

<div class="form-group">
    @Html.LabelFor(model => model.ProviderID, new { @class = "control-label col-md-2" })
    <div class="col-md-3">
        @Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.WifiProviders, new { htmlAttributes = new { @class = "form-control" } })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.FactionID, new { @class = "control-label col-md-2" })
    <div class="col-md-3">
        @Html.DropDownListFor(model => model.FactionID, (SelectList)ViewBag.WifiFactions, new { htmlAttributes = new { @class = "form-control" } })
    </div>
</div>
<div class="form-group sim-row">



    <div class="form-group">
        @Html.LabelFor(model => model.Number, new { @class = "control-label col-md-2" })
        <div class="col-md-1">
            @Html.EditorFor(model => model.Number)
            @Html.ValidationMessageFor(model => model.Number)
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
        <div class="col-md-1">
            @Html.EditorFor(model => model.Description)
            @Html.ValidationMessageFor(model => model.Description)
        </div>
    </div>

</div>

<script>
    $(".excel-browser").hide();
    function toggle(source) {
        if (source.checked) {

            $(".excel-browser").show();
            $(".sim-row").hide();
        }
        else {

            $(".excel-browser").hide();
            $(".sim-row").show();
        }
    }
</script>