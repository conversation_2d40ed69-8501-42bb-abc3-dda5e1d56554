﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Web.Areas.Merchants.Models.MerchantPaymentModel
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}

<span class="lbl">اسم التاجر </span>
<div class="form-group">
    <div class="col-md-10">
        @Html.Obout(new ComboBox("MerchantID")
        {
            Width = 230,
            FilterType = ComboBoxFilterType.Contains
        })

    </div>
</div>

<div class="hr hr-dotted hr-24"></div>

<span class="lbl">اسم الحساب </span>
<div class="form-group">
    <div class="col-md-10">
        @Html.Obout(new ComboBox("DebitorAccountID")
        {
            Width = 230,
            FilterType = ComboBoxFilterType.Contains
        })

    </div>
</div>
<div class="hr hr-dotted hr-24"></div>