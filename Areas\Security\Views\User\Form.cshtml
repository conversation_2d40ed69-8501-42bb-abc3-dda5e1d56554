﻿@model AppTech.MSMS.Domain.Models.UserInfo

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<input type="hidden" name="Type" value="Admin" />

@if (Model.ID == 0)
{

    <div class="form-group">
        @Html.Label("اسم المستخدم", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.UserName)
            @Html.ValidationMessageFor(model => model.UserName)
        </div>
    </div>

    <div class="form-group">
        @Html.Label("كلمة المرور", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Password)
            @Html.ValidationMessageFor(model => model.Password)
        </div>
    </div>

    <div class="form-group">
        @Html.Label("ملاحظات", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>
}
else
{
    <div class="form-group">
        @Html.Label("اسم المستخدم", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.UserName)
            @Html.ValidationMessageFor(model => model.UserName)
        </div>
    </div>
    <div class="form-group">
        @Html.Label("الحالة", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.Status, new[]
            {
                new SelectListItem {Text = "نشط", Value = "1"},
                new SelectListItem {Text = "موقف", Value = "0"}
            })
        </div>
    </div>

    <div class="form-group">
        @Html.Label("ملاحظات", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Note)
            @Html.ValidationMessageFor(model => model.Note)
        </div>
    </div>

}