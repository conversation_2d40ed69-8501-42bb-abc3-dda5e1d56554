# سكريبت إصلاح وتشغيل نظام AppTech MSMS
# يتعامل مع المشاكل الحالية ويحاول حلها تلقائياً

Write-Host @"
========================================
    إصلاح وتشغيل نظام AppTech MSMS
========================================
"@ -ForegroundColor Cyan

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "تحذير: لم يتم تشغيل السكريبت كمدير" -ForegroundColor Yellow
    Write-Host "بعض العمليات قد تفشل بدون صلاحيات المدير" -ForegroundColor Yellow
    $continue = Read-Host "هل تريد المتابعة؟ (y/n)"
    if ($continue -ne "y") { exit }
}

# إعداد مسار العمل
$originalPath = Get-Location
Set-Location "E:\inetpub"

try {
    Write-Host "`n1. فحص وإصلاح ملفات النظام..." -ForegroundColor Yellow
    
    # التحقق من وجود الملفات الأساسية
    $requiredFiles = @(
        "wwwroot\api\Global.asax",
        "wwwroot\api\Web.config",
        "wwwroot\api\bin\AppTech.MSMS.Web.dll",
        "wwwroot\client\Global.asax",
        "wwwroot\portal\Global.asax"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
            Write-Host "✗ ملف مفقود: $file" -ForegroundColor Red
        } else {
            Write-Host "✓ ملف موجود: $file" -ForegroundColor Green
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-Host "⚠ يوجد ملفات مفقودة. تحقق من سلامة النظام." -ForegroundColor Yellow
    }
    
    Write-Host "`n2. محاولة تفعيل IIS..." -ForegroundColor Yellow
    
    # محاولة تفعيل IIS بطرق مختلفة
    try {
        # الطريقة الأولى: PowerShell
        $iisFeature = Get-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -ErrorAction SilentlyContinue
        if ($iisFeature -and $iisFeature.State -eq "Disabled") {
            Write-Host "تفعيل IIS..." -ForegroundColor Gray
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole -All -NoRestart -ErrorAction SilentlyContinue
        }
        
        # الطريقة الثانية: DISM
        $dismResult = & dism /online /get-featureinfo /featurename:IIS-WebServerRole 2>$null
        if ($dismResult -like "*Disabled*") {
            Write-Host "تفعيل IIS باستخدام DISM..." -ForegroundColor Gray
            & dism /online /enable-feature /featurename:IIS-WebServerRole /all /norestart 2>$null
        }
        
        Write-Host "✓ تم محاولة تفعيل IIS" -ForegroundColor Green
    } catch {
        Write-Host "⚠ لم يتم تفعيل IIS تلقائياً - قد يحتاج تفعيل يدوي" -ForegroundColor Yellow
    }
    
    Write-Host "`n3. محاولة بدء خدمة IIS..." -ForegroundColor Yellow
    
    try {
        # محاولة بدء خدمة IIS
        $w3svc = Get-Service -Name W3SVC -ErrorAction SilentlyContinue
        if ($w3svc) {
            if ($w3svc.Status -ne "Running") {
                Start-Service W3SVC -ErrorAction SilentlyContinue
                Write-Host "✓ تم بدء خدمة IIS" -ForegroundColor Green
            } else {
                Write-Host "✓ خدمة IIS تعمل بالفعل" -ForegroundColor Green
            }
        } else {
            Write-Host "⚠ خدمة IIS غير موجودة - يجب تثبيت IIS أولاً" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠ لم يتم بدء خدمة IIS: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host "`n4. إنشاء Application Pools..." -ForegroundColor Yellow
    
    try {
        Import-Module WebAdministration -ErrorAction SilentlyContinue
        
        $appPools = @("AppTechAPI", "AppTechClient", "AppTechPortal")
        foreach ($poolName in $appPools) {
            try {
                if (Get-IISAppPool -Name $poolName -ErrorAction SilentlyContinue) {
                    Write-Host "Application Pool موجود: $poolName" -ForegroundColor Gray
                } else {
                    New-WebAppPool -Name $poolName -ErrorAction SilentlyContinue
                    Set-ItemProperty -Path "IIS:\AppPools\$poolName" -Name "managedRuntimeVersion" -Value "v4.0" -ErrorAction SilentlyContinue
                    Write-Host "✓ تم إنشاء Application Pool: $poolName" -ForegroundColor Green
                }
            } catch {
                Write-Host "⚠ فشل في إنشاء Application Pool: $poolName" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "⚠ لم يتم إنشاء Application Pools - IIS قد يكون غير مُفعل" -ForegroundColor Yellow
    }
    
    Write-Host "`n5. إنشاء المواقع..." -ForegroundColor Yellow
    
    try {
        $websites = @(
            @{Name="AppTechAPI"; Port=80; Path="E:\inetpub\wwwroot\api"; Pool="AppTechAPI"},
            @{Name="AppTechClient"; Port=8080; Path="E:\inetpub\wwwroot\client"; Pool="AppTechClient"},
            @{Name="AppTechPortal"; Port=8081; Path="E:\inetpub\wwwroot\portal"; Pool="AppTechPortal"}
        )
        
        foreach ($site in $websites) {
            try {
                if (Get-Website -Name $site.Name -ErrorAction SilentlyContinue) {
                    Write-Host "الموقع موجود: $($site.Name)" -ForegroundColor Gray
                } else {
                    if (Test-Path $site.Path) {
                        New-Website -Name $site.Name -Port $site.Port -PhysicalPath $site.Path -ApplicationPool $site.Pool -ErrorAction SilentlyContinue
                        Write-Host "✓ تم إنشاء الموقع: $($site.Name) على المنفذ $($site.Port)" -ForegroundColor Green
                    } else {
                        Write-Host "✗ مسار الموقع غير موجود: $($site.Path)" -ForegroundColor Red
                    }
                }
            } catch {
                Write-Host "⚠ فشل في إنشاء الموقع: $($site.Name)" -ForegroundColor Yellow
            }
        }
    } catch {
        Write-Host "⚠ لم يتم إنشاء المواقع - IIS قد يكون غير مُفعل" -ForegroundColor Yellow
    }
    
    Write-Host "`n6. فحص قاعدة البيانات..." -ForegroundColor Yellow
    
    # البحث عن SQL Server
    $sqlInstances = @("localhost", ".\SQLEXPRESS", "localhost\SQLEXPRESS", "(localdb)\MSSQLLocalDB")
    $workingInstance = $null
    
    foreach ($instance in $sqlInstances) {
        try {
            $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION" -ErrorAction Stop -Timeout 5
            if ($result) {
                $workingInstance = $instance
                Write-Host "✓ تم العثور على SQL Server: $instance" -ForegroundColor Green
                break
            }
        } catch {
            # تجاهل الأخطاء والمتابعة للتجربة التالية
        }
    }
    
    if ($workingInstance) {
        # محاولة إنشاء قاعدة البيانات
        try {
            $dbExists = Invoke-Sqlcmd -ServerInstance $workingInstance -Query "SELECT name FROM sys.databases WHERE name = 'AppTechMSMS'" -ErrorAction Stop
            if ($dbExists) {
                Write-Host "✓ قاعدة البيانات AppTechMSMS موجودة" -ForegroundColor Green
            } else {
                Write-Host "إنشاء قاعدة البيانات..." -ForegroundColor Gray
                $createDbQuery = "CREATE DATABASE [AppTechMSMS]"
                Invoke-Sqlcmd -ServerInstance $workingInstance -Query $createDbQuery -ErrorAction Stop
                Write-Host "✓ تم إنشاء قاعدة البيانات AppTechMSMS" -ForegroundColor Green
            }
        } catch {
            Write-Host "⚠ مشكلة في قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ لم يتم العثور على SQL Server" -ForegroundColor Red
        Write-Host "يرجى تثبيت SQL Server أو SQL Server Express" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "✗ خطأ عام في السكريبت: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Set-Location $originalPath
}

Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "انتهى إصلاح النظام" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

Write-Host "`nالخطوات التالية:" -ForegroundColor White
Write-Host "1. تحقق من تفعيل IIS يدوياً إذا لزم الأمر" -ForegroundColor Yellow
Write-Host "2. تأكد من تثبيت SQL Server" -ForegroundColor Yellow
Write-Host "3. اختبر الوصول للتطبيقات:" -ForegroundColor Yellow
Write-Host "   - API: http://localhost/" -ForegroundColor Cyan
Write-Host "   - Client: http://localhost:8080/" -ForegroundColor Cyan
Write-Host "   - Portal: http://localhost:8081/" -ForegroundColor Cyan

pause
