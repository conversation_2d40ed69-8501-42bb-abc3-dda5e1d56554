# Script to update web.config files with new connection strings

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Update Web.Config Files" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Get connection string from previous restore
$connectionInfoFile = "nawafd-connection-info.txt"
$newConnectionString = ""

if (Test-Path $connectionInfoFile) {
    $content = Get-Content $connectionInfoFile
    foreach ($line in $content) {
        if ($line.StartsWith("Data Source=")) {
            $newConnectionString = $line
            break
        }
    }
}

if (-not $newConnectionString) {
    Write-Host "Connection string not found. Please run database restore first." -ForegroundColor Red
    $newConnectionString = Read-Host "Enter connection string manually"
}

Write-Host "`nNew connection string:" -ForegroundColor Cyan
Write-Host $newConnectionString -ForegroundColor White

Write-Host "`n1. Searching for web.config files..." -ForegroundColor Yellow

# Search for web.config files
$webConfigFiles = Get-ChildItem -Path "E:\inetpub" -Name "web.config" -Recurse -ErrorAction SilentlyContinue

if ($webConfigFiles.Count -eq 0) {
    Write-Host "No web.config files found" -ForegroundColor Yellow
    exit 0
}

Write-Host "Found $($webConfigFiles.Count) web.config files:" -ForegroundColor Green
foreach ($file in $webConfigFiles) {
    Write-Host "  - $file" -ForegroundColor Gray
}

Write-Host "`n2. Updating web.config files..." -ForegroundColor Yellow

$updatedCount = 0

foreach ($configFile in $webConfigFiles) {
    $fullPath = Join-Path "E:\inetpub" $configFile
    
    try {
        Write-Host "`nProcessing: $configFile" -ForegroundColor Gray
        
        # Read current content
        $content = Get-Content $fullPath -Raw
        
        # Create backup
        $backupPath = "$fullPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Copy-Item $fullPath $backupPath -Force
        Write-Host "  Backup created: $backupPath" -ForegroundColor Gray
        
        # Update connection strings
        $updated = $false
        
        # Pattern 1: Standard connection string
        if ($content -match 'connectionString="[^"]*nawafd[^"]*"') {
            $content = $content -replace 'connectionString="[^"]*nawafd[^"]*"', "connectionString=`"$newConnectionString`""
            $updated = $true
            Write-Host "  Updated standard connection string" -ForegroundColor Green
        }
        
        # Pattern 2: Entity Framework connection string
        if ($content -match 'connectionString="[^"]*provider connection string[^"]*"') {
            $entityConnectionString = "metadata=res://*/Models.AppTechModel.csdl|res://*/Models.AppTechModel.ssdl|res://*/Models.AppTechModel.msl;provider=System.Data.SqlClient;provider connection string=`"$newConnectionString`""
            $content = $content -replace 'connectionString="[^"]*provider connection string[^"]*"', "connectionString=`"$entityConnectionString`""
            $updated = $true
            Write-Host "  Updated Entity Framework connection string" -ForegroundColor Green
        }
        
        # Pattern 3: Any connection string containing old server names
        $oldServerPatterns = @(
            "MSSQLSERVER17",
            "localhost\\MSSQLSERVER17",
            ".\\MSSQLSERVER17"
        )
        
        foreach ($pattern in $oldServerPatterns) {
            if ($content -match $pattern) {
                $content = $content -replace $pattern, "localhost"
                $updated = $true
                Write-Host "  Updated server name: $pattern -> localhost" -ForegroundColor Green
            }
        }
        
        if ($updated) {
            # Save updated content
            $content | Out-File -FilePath $fullPath -Encoding UTF8
            Write-Host "  File updated successfully" -ForegroundColor Green
            $updatedCount++
        } else {
            Write-Host "  No updates needed" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "  Error updating file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "Web.config update completed!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nSummary:" -ForegroundColor Cyan
Write-Host "- Files found: $($webConfigFiles.Count)" -ForegroundColor White
Write-Host "- Files updated: $updatedCount" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Test web applications" -ForegroundColor Cyan
Write-Host "2. Verify database connectivity" -ForegroundColor Cyan
Write-Host "3. Check TopupProcessor functionality" -ForegroundColor Cyan

pause
