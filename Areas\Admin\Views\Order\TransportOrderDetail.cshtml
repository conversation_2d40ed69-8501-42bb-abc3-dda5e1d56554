﻿+@model AppTech.MSMS.Domain.Models.TransportOrder

<div>


    <div class="space-6"></div>
    <span class="label label-info arrowed-in-right arrowed"> بيانات التذكرة</span>
    <div class="space-6"></div>


    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.TicketNumber) </div>
        <div class="profile-info-value">
            <span class="editable"> @Html.EditorFor(model => model.TicketNumber)</span>
            <button class="btn btn-white loading" onclick="saveTransNumber();" data-loading-text="<i class='fa fa-spinner fa-spin '></i> الرجاء الأنتظار" id="bootbox-save-ticket">
                <i class="ace-icon fa fa-save"></i>
                حفظ
            </button>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.FullName) </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.FullName)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.ContactNumber) </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.ContactNumber)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.SourceRegionID) </div>
        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.WERegion.Name)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.TargetRegionID) </div>
        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.WERegion1.Name)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.Date) </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.Date)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.Time) </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.Time)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> المبلغ </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.Amount)</span>
        </div>
    </div>

    <div class="profile-info-row">
        <div class="profile-info-name"> العملة </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.Currency.Name) </span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> الملاحظات </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.Note)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name">@Html.LabelFor(model => model.IsChild) </div>

        <div class="profile-info-value">
            <span class="editable"> @Html.DisplayFor(model => model.IsChild)</span>
        </div>
    </div>


    <div class="profile-info-row">
        <div class="profile-info-name"> الصوره المرفقة </div>

        <div class="profile-info-value">

            @if (!string.IsNullOrEmpty(Model.ImageName))
            {
                <img id="avatar" class="editable img-responsive" alt="Alex's Avatar" src="@Url.Content(Model.ImageName)"/>
            }

        </div>
    </div>


</div>

<script>
    
    function saveTransNumber() {

        var id = $("#mr_id").val();
        var num = $("#TicketNumber").val();

        i('id: ' + id + ' num ' + num);
        $.ajax({
            url: '@Url.Action("SaveTicketNumber", "Order")',
            data: { id: id, num: num },
            success: function(data) {

                if (data.Success) {
                    alert("تم حفظ رقم التذكرة بنجاح");
                    $('.trans-num').val(num);
                } else {
                    alert(data.Message);
                }
                resetButton();
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(xhr.responseText);
            }
        });
    }
</script>