﻿@model AppTech.MSMS.Web.Models.ClientPermissionModel

@{
    Layout = "~/Views/Shared/_FormModal.cshtml";
}
<input type="checkbox" onClick="toggle(this)"/>
<strong class="blue">أختر الكل</strong>
<br/>
<div class="hr hr-dotted hr-24"></div>
<input type="hidden" name="accountId" value="@Model.AccountID"/>
@foreach (var item in Model.Actions)
{
    <input id="chk@(item.Value)"
           name="items"
           type="checkbox"
           class="action"
           value="@item.Value"
           checked="@item.IsAllowed"/>
    <strong>@item.Text</strong> @*<br />*@

    <div class="space-10"></div>
}

<script>


    function toggle(source) {
        var checkboxes = $(".action");
        for (var i = 0, n = checkboxes.length; i < n; i++) {
            checkboxes[i].checked = source.checked;
        }
    }
    //$(function () {


    //    hideLoading();
    //    $("#crud-form").submit(function(event) {

    //        alert("perms submit");
    //        showLoading();
    //        var form = $("#crud-form");
    //        var url = "Print/Permissions";
    //        $.post(url,
    //            form.serialize(),
    //            function(data) {
    //                hideLoading();
    //                hideModal();
    //                alert(data);
    //                showSuccess(data);
    //            }
    //        );

    //    });
    //});

    //$(function() {

    //    formHelper.onSubmit = function() {

    //        alert("perms submit")
    //        var form = $("#crud-form");
    //        var url = "/Client/Permissions";
    //        $.post(url, form.serialize(),
    //            function (data) {
    //                hideModal();
    //                showAlert(data);
    //            }
    //        );
    //    }
    //});
</script>