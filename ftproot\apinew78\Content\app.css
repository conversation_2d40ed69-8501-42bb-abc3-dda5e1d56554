﻿.main-container .table .record-actions .dropdown-menu {
    position: initial !important;
}
.skin-1 .nav-list > li > a,
.skin-1 .nav-list > li,
.rtl .nav-list > li .submenu > li > a {
    border-top-left-radius: 15px !important;
    border-bottom-left-radius: 15px !important;
    background-color: #005170 !important;
}

.skin-1 .nav-list > li .submenu > li > a {
    border-top-color: #000 !important;
    background-color: #008cc2 !important;
    color: #fff !important;
}

.skin-1,
.skin-1 .nav-list > li .submenu {
    background-color: #ffffff !important;
    border-top-color: #ffffff !important;
}

    .skin-1 .nav-list > li:hover > a,
    .ace-nav > li.dark > a:hover {
        background-color: #0176a3 !important;
    }

.skin-1 .nav-list > li.open > a,
.skin-1 .sidebar-toggle {
    border-top-left-radius: 15px !important;
    border-bottom-left-radius: 15px !important;
    color: #ffffff !important;
    background-color: #005170 !important;
    border-color: #ffffff;
}

.skin-1 .nav-list > li.open > a,
.ace-nav > li.dark > a,
.skin-1 .nav-list > li .submenu > li > a:hover {
    background-color: #013245 !important;
}

.skin-2 {
    background-color: #005170 !important;
}

.skin-1 .nav-list > li > a {
    color: #ffffff !important;
}

.skin-1 .nav-list > li {
    border-color: #000000 !important;
    border-top-left-radius: 15px !important;
    border-bottom-left-radius: 15px !important;
}

.skin-1 .sidebar-toggle > .ace-icon {
    background-color: #222a2d !important;
    color: #fff !important;
    border-color: #fff !important;
}

.skin-1 .sidebar-toggle {
    background-color: #181e21;
    border-color: #ffffff;
}

.dropdown-toggle .icon {
    font-size: 19px;
}

.dropdown-toggle .menu-text {
    margin-right: 6px;
}

/*//////////////////////////////////////////////////////////////////
[ FONT ]*/

@font-face {
    font-family: Hacen_Algeria-Regular;
    src: url('../Content/fonts/Hacen_Algeria/Hacen_Algeria-Regular.ttf');
}

@font-face {
    font-family: Hacen_Algeria-Bold;
    src: url('../Content/fonts/Hacen_Algeria/Hacen_Algeria-Regular.ttf');
}

@font-face {
    font-family: Hacen_Algeria-Medium;
    src: url('../Content/fonts/Hacen_Algeria/Hacen_Algeria-Regular.ttf');
}

@font-face {
    font-family: Hacen_Algeria-Bold;
    src: url('../Content/fonts/Hacen_Algeria/Hacen_Algeria-Regular.ttf');
}

/*//////////////////////////////////////////////////////////////////
    
    /*bootstrap loader */

.main-container .table .record-actions .dropdown-menu {
    position: initial !important;
}

#loaderdd {
    border: 16px solid #21abd4;
    border-radius: 50%;
    border-top: 16px solid aqua;
    width: 40px;
    height: 40px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
}
#loader {



    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 35px;
    height: 35px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
}


@-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Add animation to "page content" */
.animate-bottom {
    position: relative;
    -webkit-animation-name: animatebottom;
    -webkit-animation-duration: 1s;
    animation-name: animatebottom;
    animation-duration: 1s
}

@-webkit-keyframes animatebottom {
    from { bottom:-100px; opacity:0 } 
    to { bottom:0px; opacity:1 }
}

@keyframes animatebottom { 
    from{ bottom:-100px; opacity:0 } 
    to{ bottom:0; opacity:1 }
}



/* end loader*/

.img-center {
    display: block;
    margin-left: auto;
    margin-right: auto;
    margin-top: 5px;
}
/* bootstrap override */
.carousel-indicators {
    left: 12px;
    top: 320px;
}

.row-first { margin-top: 25px; }

.row-backlink { padding-bottom: 10px; }





/* ie 10 is rebellious with image sizes */

.img-small {
    height: 85px !important;
    width: 85px !important;
}

.footer {
    color: #999;
    padding-bottom: 30px;
    padding-top: 35px;
}

.back-link { color: #333; }

#login-form { margin: 30px 0 100px 0; }

.alert-absolute {
    display: none;
    left: 0;
    margin-left: auto;
    margin-right: auto;
    position: absolute;
    right: 0;
    top: 145px;
    width: 600px;
    /*opacity:0;
    filter:alpha(opacity=0);*/
}

/*.alert-success {
  color: #213d20;
  background-color: #c7e073;
  border-color: #d6e9c6;
}*/

.alert-danger,
.alert-error {
    background-color: #ff8989;
    border-color: #eed3d7;
    color: #551b18;
}




.invalid {
    color: #D13232;
    padding-left: 8px;
}

.center { text-align: center; }

/* bootstrap override */

a.thumbnail:hover,
a.thumbnail:focus {
    -moz-box-shadow: 0 1px 4px rgba(131, 144, 157, 0.25);
    -webkit-box-shadow: 0 1px 4px rgba(131, 144, 157, 0.25);
    border-color: #83909d;
    box-shadow: 0 1px 4px rgba(131, 144, 157, 0.25);
}


.input-checkbox {
    padding-left: 5px;
    padding-right: 10px;
}

.box, .box-noborder {
    -moz-border-radius: 6px;
    -moz-box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
    -webkit-border-radius: 6px;
    -webkit-box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, .075);
}

.box-noborder { border: 0px solid #ddd; }

.box-filters {
    background: #f5f5f5;
    height: 564px;
    padding: 9px 0px 13px 20px;
}



.resetall {
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    background-color: #fcf8e3;
    border: 1px solid #fbeed5;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 4px 4px 4px 8px;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    width: 85px;
}

.resetall .close {
    line-height: 20px;

    position: relative;
    right: 5px;
}

.resetall .closelink {
    color: #888;
    font-size: 10.5pt;
    font-weight: normal;
}

/* transparent overlay over art work */

.text-overlay {
    -moz-opacity: 0.65; /* older Gecko-based browsers */
    background: #fff;
    bottom: 0;
    color: #000;
    filter: alpha(opacity=65); /* For IE6&7 */
    left: 0;
    opacity: 0.65;
    padding: 0 -5px;
    position: absolute;
    text-align: center;
    top: 0;
    width: 100%;
}


/* bootstrap enhancement */

.table-noline th,
.table-noline td { border-top: 1px solid #fff; }

.hr-narrow { margin: 2px 0; }

.admin-controls { margin: 30px 0px 40px 20px; }

.user-detail { padding: 20px 30px 0 20px; }

/* print style */

/*
	 CSS-Tricks Example
	 by Chris Coyier
	 http://css-tricks.com
*/

* {
    margin: 0;
    padding: 0;
}

body {
    font-family: Hacen_Algeria-Regular, sans-serif;
    /*font: 14px/1.4 Georgia, serif;*/
}

#page-wrap {
    margin: 0 auto;
    width: 800px;
}

textarea {
    border: 0;
    font: 14px Georgia, Serif;
    overflow: hidden;
    resize: none;
}

table {
    border-collapse: collapse;
}

    table td, table th {
        border: 1px solid black;
        padding: 5px;
    }


.center {
    display: block;
    text-align: center;
}


#header {
    color: white;
    font: bold 15px Helvetica, Sans-Serif;
    height: 15px;
    letter-spacing: 20px;
    margin: 20px 0;
    padding: 8px 0px;
    text-align: center;
    text-decoration: uppercase;
    width: 100%;
}

.reportheadertitle {
    font: bold 15px Helvetica, Sans-Serif;
    height: 30px;
    letter-spacing: 0px;
    margin-bottom: 20px;
    padding: 8px 0px;
    text-align: center;
    text-decoration: uppercase;
    width: 100%;
}

.reportheader {
    color: black;
    font: bold 15px Helvetica, Sans-Serif;
    height: 30px;
    letter-spacing: 0px;
    margin-bottom: 20px;
    padding: 8px 0px;
    text-align: center;
    text-decoration: uppercase;
    width: 100%;
}

#logo {
    height: 100px;
    width: 250px;
}

#address {
    float: left;
    height: 100px;
    margin-left: 10px;
    width: 250px;
}

#addressRight {
    float: right;
    height: 100px;
    margin-right: 10px;
    width: 250px;
}

#customer {
    overflow: hidden;
}

#logo {
    border: 1px solid #fff;
    float: center;
    margin-top: 25px;
    max-height: 100px;
    max-width: 540px;
    overflow: hidden;
    position: relative;
    text-align: right;
}

    #logo:hover, #logo.edit {
        border: 1px solid #000;
        margin-top: 0px;
        max-height: 125px;
    }

#logoctr {
    display: none;
}

#logo:hover #logoctr, #logo.edit #logoctr {
    background: #eee;
    display: block;
    line-height: 25px;
    padding: 0 5px;
    text-align: right;
}

#logohelp {
    display: none;
    font-style: italic;
    padding: 10px 5px;
    text-align: left;
}

    #logohelp input {
        margin-bottom: 5px;
    }

.edit #logohelp {
    display: block;
}

.edit #save-logo, .edit #cancel-logo {
    display: inline;
}

.edit #image, #save-logo, #cancel-logo, .edit #change-logo, .edit #delete-logo {
    display: none;
}

#customer-title {
    float: left;
    font-size: 20px;
    font-weight: bold;
}

#meta {
    float: right;
    margin-top: 1px;
    width: 300px;
}

    #meta td {
        text-align: right;
    }

        #meta td.meta-head {
            background: #eee;
            text-align: left;
        }

        #meta td textarea {
            height: 20px;
            text-align: right;
            width: 100%;
        }

#items {
    border: 1px solid black;
    clear: both;
    margin: 30px 0 0 0;
    width: 100%;
}

    #items th {
        background: #eee;
    }

    #items textarea {
        height: 50px;
        width: 80px;
    }

    #items tr.item-row td {
        border: 0;
        vertical-align: top;
    }

    #items td.description {
        width: 300px;
    }

    #items td.item-name {
        width: 175px;
    }

        #items td.description textarea, #items td.item-name textarea {
            width: 100%;
        }

    #items td.total-line {
        border-right: 0;
        text-align: right;
    }

    #items td.total-value {
        border-left: 0;
        padding: 10px;
    }

        #items td.total-value textarea {
            background: none;
            height: 20px;
        }

    #items td.balance {
        background: #eee;
    }

    #items td.blank {
        border: 0;
    }

#terms {
    margin: 20px 0 0 0;
    text-align: center;
}

    #terms h5 {
        border-bottom: 1px solid black;
        font: 13px Helvetica, Sans-Serif;
        letter-spacing: 10px;
        margin: 0 0 8px 0;
        padding: 0 0 8px 0;
        text-transform: uppercase;
    }

    #terms textarea {
        text-align: center;
        width: 100%;
    }

textarea:hover, textarea:focus, #items td.total-value textarea:hover, #items td.total-value textarea:focus, .delete:hover {
    background-color: #EEFF88;
}

.delete-wpr {
    position: relative;
}

.delete {
    background: #EEEEEE;
    border: 1px solid;
    color: #000;
    display: block;
    font-family: Verdana;
    font-size: 12px;
    font-weight: bold;
    left: -22px;
    padding: 0px 3px;
    position: absolute;
    text-decoration: none;
    top: -6px;
}
.ForImage {
    width: 100px;
    margin: 0 10px
}

.ForImage p {
    font-size: 20px
}
.ForImage a {
    color:#393939;
}

.ForImage :hover, img :hover img {
    cursor: pointer;
    transform: scale(1.1);
    transition: .6s;
}

.ForImage img {
    transform: scale(1);
    padding: 5px;
    transition: .3s;
    height: 100px
}
.ForImageRespons {
    display: inline-flex;
}
.mainForImageRespons {
    padding: 0 20px
}
@media screen and (min-width:320px) and (max-width:600px) {
    .ForImage {
        width: 20%;
        margin: 0px 3px
    }
    .ForImageRespons {
        align-items: center;
    }
.ForImage p {
    font-size: 10px
}

.ForImage img {
    transform: scale(1);
    padding: 5px;
    transition: .3s;
    height: auto;
}
h3.smaller {
    font-size: 16px;
}
.mainForImageRespons {
    padding: 0
}
.mod{
    width:100%
}
.forModFooter{
    margin-bottom:90px
}
}