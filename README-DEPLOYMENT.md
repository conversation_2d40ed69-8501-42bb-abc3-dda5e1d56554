# دليل تجهيز نظام AppTech MSMS للتشغيل

## نظرة عامة
هذا الدليل يوضح كيفية تجهيز نظام AppTech MSMS (Money Services Management System) للتشغيل على Windows Server مع IIS.

## المتطلبات الأساسية

### 1. نظام التشغيل
- Windows Server 2016 أو أحدث
- Windows 10/11 Pro (للتطوير)

### 2. البرامج المطلوبة
- **IIS (Internet Information Services)** مع ASP.NET 4.6+
- **SQL Server** أو **SQL Server Express**
- **.NET Framework 4.6.1** أو أحدث
- **PowerShell 5.0** أو أحدث

### 3. الصلاحيات
- صلاحيات المدير (Administrator) لتشغيل السكريبتات

## مكونات النظام

### التطبيقات
- **API** - واجهة برمجة التطبيقات الرئيسية (المنفذ 80)
- **Client** - تطبيق العملاء (المنفذ 8080)  
- **Portal** - البوابة الإدارية (المنفذ 8081)
- **TopupInspector** - تطبيق سطح المكتب للمراقبة

### الوحدات الوظيفية
- إدارة العملاء والوكالات
- التحويلات المالية والحوالات
- إدارة البطاقات والمحافظ
- صرف العملات
- إدارة المخزون والمنتجات
- التقارير والإحصائيات

## خطوات التجهيز

### الطريقة السريعة (موصى بها)

1. **افتح PowerShell كمدير**
   ```powershell
   # انقر بالزر الأيمن على PowerShell واختر "Run as Administrator"
   ```

2. **انتقل إلى مجلد النظام**
   ```powershell
   cd E:\inetpub
   ```

3. **تشغيل السكريبت الشامل**
   ```powershell
   .\deploy-apptech-system.ps1
   ```

### الطريقة المتقدمة (خطوة بخطوة)

#### 1. تفعيل IIS
```powershell
.\setup-iis.ps1
```

#### 2. إنشاء Application Pools
```powershell
.\create-app-pools.ps1
```

#### 3. إنشاء المواقع
```powershell
.\create-websites.ps1
```

#### 4. إعداد قاعدة البيانات
```powershell
.\setup-database.ps1
.\update-web-configs.ps1
```

#### 5. إعداد الصلاحيات
```powershell
.\setup-permissions.ps1
```

#### 6. اختبار النظام
```powershell
.\test-applications.ps1
```

## خيارات السكريبت الرئيسي

```powershell
# تجهيز كامل
.\deploy-apptech-system.ps1

# تخطي تفعيل IIS (إذا كان مُفعل مسبقاً)
.\deploy-apptech-system.ps1 -SkipIIS

# تخطي إعداد قاعدة البيانات
.\deploy-apptech-system.ps1 -SkipDatabase

# اختبار النظام فقط
.\deploy-apptech-system.ps1 -TestOnly
```

## روابط الوصول

بعد التجهيز الناجح، يمكن الوصول للتطبيقات عبر:

- **API**: http://localhost/
- **Client**: http://localhost:8080/
- **Portal**: http://localhost:8081/

## استكشاف الأخطاء

### خطأ HTTP 500 (خطأ خادم داخلي)
- تحقق من سلاسل الاتصال في web.config
- تأكد من تشغيل SQL Server
- راجع سجلات الأخطاء في Event Viewer

### خطأ HTTP 404 (الصفحة غير موجودة)
- تحقق من وجود ملف Global.asax
- تأكد من إعدادات Default Document
- راجع تكوين IIS للموقع

### مشاكل قاعدة البيانات
- تأكد من تشغيل SQL Server
- تحقق من صحة سلسلة الاتصال
- قم بتشغيل Entity Framework Migrations

### مشاكل الصلاحيات
- تأكد من صلاحيات IIS_IUSRS للمجلدات
- راجع صلاحيات Application Pool Identity
- تحقق من صلاحيات Temporary ASP.NET Files

## الملفات المهمة

### ملفات التكوين
- `web.config` - تكوين التطبيق
- `maincs.erp` - سلاسل الاتصال المشفرة
- `license.lic` - ملف الترخيص

### ملفات السكريبتات
- `deploy-apptech-system.ps1` - السكريبت الرئيسي
- `setup-iis.ps1` - تفعيل IIS
- `create-app-pools.ps1` - إنشاء Application Pools
- `create-websites.ps1` - إنشاء المواقع
- `setup-database.ps1` - إعداد قاعدة البيانات
- `update-web-configs.ps1` - تحديث ملفات التكوين
- `setup-permissions.ps1` - إعداد الصلاحيات
- `test-applications.ps1` - اختبار النظام

## الدعم والصيانة

### النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات دورياً
- احتفظ بنسخة من ملفات التكوين
- انسخ ملفات الترخيص والتشفير

### المراقبة
- راقب سجلات IIS
- تابع أداء Application Pools
- راقب استخدام قاعدة البيانات

### التحديثات
- احتفظ بنسخة احتياطية قبل أي تحديث
- اختبر التحديثات في بيئة تطوير أولاً
- راجع ملاحظات الإصدار

## معلومات إضافية

### المنافذ المستخدمة
- 80 - API (HTTP)
- 8080 - Client
- 8081 - Portal
- 443 - HTTPS (إذا تم تفعيله)

### قواعد البيانات
- `AppTechMSMS` - قاعدة البيانات الرئيسية
- يدعم SQL Server 2016 أو أحدث

### الأمان
- تشفير سلاسل الاتصال
- نظام ترخيص متقدم
- حماية من CSRF وXSS
- مصادقة متعددة المستويات

---

**ملاحظة**: هذا النظام مطور بواسطة AppTech Inc ويتطلب ترخيص صالح للتشغيل.
