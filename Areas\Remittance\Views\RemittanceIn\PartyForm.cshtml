﻿@model AppTech.MSMS.Domain.Models.RemittanceIn
@{
    Layout = "/Views/Shared/_FormLayout.cshtml";
    ViewBag.Title = "أرسال حوالة إكسبرس";
}

<div class="page-header">
    <h1 id="page-title">
        أرسال حوالة إكسبرس
    </h1>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.TargetPointID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.TargetPointID, (SelectList) ViewBag.Targets, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.TargetPointID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Amount, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList) ViewBag.Currencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.BeneficiaryPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.BeneficiaryPhone, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.BeneficiaryPhone, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderName, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderName, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderName, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SenderPhone, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SenderPhone, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.SenderPhone, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Date, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Date, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Purpose, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Purpose, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Purpose, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">العمولة</h3>
    </div>
    <div class="panel-body">


        <div class="form-group">
            @Html.Label("العمولة", new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                <input type="text" id="CommissionAmount" name="CommissionAmount" readonly="readonly"/>
            </div>
        </div>

        <div class="form-group">
            @Html.Label("إجمالي المبلغ", new {@class = "control-label col-md-2"})
            <div class="col-md-10">
                <input type="text" id="Total" name="Total" readonly="readonly"/>
            </div>
        </div>

    </div>

    <div class="panel-footer">
        <input type="button" class="btn btn-white" value="احتساب العمولة" onclick="calcCommission();"/>
    </div>
</div>

<script>

    function calcCommission() {
        var data = {
            CurrencyID: $("#CurrencyID").val(),
            Amount: $("#Amount").val(),
            RemittanceType: 0
        };
        AjaxCall('/Remittance/RemittanceIn/CalcCommission', JSON.stringify(data), 'POST').done(function(response) {

            $("#CommissionAmount").val(response);
            $("#Total").val(Number(response) + Number($('#Amount')));

        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });
    }

    $(function() {
        try {

            formHelper.onBegin = function() {

                var msg = "سوف يتم أرسال حوالة بمبلغ  " +
                    $("#Amount").val() +
                    ' ' +
                    $("#CurrencyID option:selected").text() +
                    ' هل انت متأكد';
                if (!confirm(msg)) {
                    i('not confirmed');
                    return false;
                } else {

                    i('confirmed');
                    showLoading();
                    return true;

                }

            }

        } catch (e) {

        }
    });
</script>

<script>

    function fillList(response, element, title) {
        if (response.length > 0) {
            $('#' + element).html('');
            var options = '';
            options += '<option value="Select">' + title + '  </option>';
            for (var i = 0; i < response.length; i++) {
                options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
            }
            $('#' + element).append(options);
        }
    }

</script>