﻿@model AppTech.MSMS.Domain.Models.Agent
@using Obout.Mvc.ComboBox
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Name, "", new { @class = "text-danger" })
    </div>
</div>
<div class="form-group">
    @Html.LabelFor(model => model.PhoneNumber, new { @class = "control-label col-md-2" })

    <div class="col-sm-10">
        <span class="input-icon input-icon-right">
            @Html.EditorFor(model => model.PhoneNumber, new { @class = "input-medium input-mask-phone", id = "form-field-phone", placeholder = "رقم الحساب" })
            @Html.ValidationMessageFor(model => model.PhoneNumber)
            <i class="ace-icon fa fa-phone fa-flip-horizontal"></i>
        </span>
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ContactNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ContactNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.ContactNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Address, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Address, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Address, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>


<h4 class="header blue bolder smaller">بيانات البطاقة</h4>
<div class="space-4"></div>
<div class="form-group">
    <label class="col-sm-2 control-label">نوع البطاقة</label>

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.CardType, new[]
        {
            new SelectListItem {Text = "شخصية", Value = "شخصية"},
            new SelectListItem {Text = "جواز سفر", Value = "جواز سفر"},
            new SelectListItem {Text = "عائلية", Value = "عائلية"},
            new SelectListItem {Text = "عسكرية", Value = "عسكرية"},
            new SelectListItem {Text = "أخرى", Value = "أخرى"}
        })


    </div>
    @Html.ValidationMessageFor(model => model.CardType)
</div>

<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label">رقم البطاقة</label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.CardNumber, new { @class = "col-xs-12 col-sm-10" })
        @Html.ValidationMessageFor(model => model.CardType)
    </div>
</div>

<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label">مكان الأصدار </label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.CardIssuePlace, new { @class = "col-xs-12 col-sm-10" })
        @Html.ValidationMessageFor(model => model.CardIssuePlace)
    </div>
</div>
<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label date-picker">تاريخ الأصدار</label>
    <div class="col-sm-10">
        @Html.EditorFor(model => model.CardIssueDate, new { @class = "col-xs-12 col-sm-10 date-picker" })
        @Html.ValidationMessageFor(model => model.CardIssueDate)
    </div>
</div>

<script>

    $(function () {
        try {
          
            $('.date-picker').datepicker({
                dateFormat: "dd/MM/yy",
                changeMonth: true,
                changeYear: true,
                yearRange: "-60:+0",
                autoclose: true,
                todayHighlight: true
            }).next().on('click',
                function () {
                    $(this).prev().focus();
                });
        } catch (e) {
            alert("Couldnt set date-picker: " + e);
        }
    });

</script>