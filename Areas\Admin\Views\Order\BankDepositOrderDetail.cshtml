﻿@model AppTech.MSMS.Domain.Models.BankDeposit
<div class="space-6"></div>
<span class="label label-info"> &nbsp; &nbsp; &nbsp; تفاصيل الطلب &nbsp;</span>
<div class="space-6"></div>

<div class="profile-info-row">
    <div class="profile-info-name"> الدولة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Country.Name)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> المدينة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Province.Name)</span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> البنك-(السوفت) </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.BankName)</span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> عملة الدفع </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Currency.Name) </span>
    </div>
</div>

<div class="profile-info-row">
    <div class="profile-info-name"> عملة القبض </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Currency1.Name) </span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> السعر </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Amount)</span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> الشركة المحولة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.SourceName)</span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> الشركة المستلمة </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.TargetName)</span>
    </div>
</div>


<div class="profile-info-row">
    <div class="profile-info-name"> الملاحظات </div>

    <div class="profile-info-value">
        <span class="editable"> @Html.DisplayFor(model => model.Note)</span>
    </div>
</div>

@*<div class="profile-info-row">
        <div class="profile-info-name"> صورة السند </div>

        <div class="profile-info-value">
            @if (!string.IsNullOrEmpty(Model.ImageName))
            {
                <img id="avatar" class="editable img-responsive" alt="" src="@Url.Content(Model.ImageName)" />
            }

        </div>
    </div>*@