﻿@model AppTech.MSMS.Domain.Models.SMSMessage
@{
    Layout = "/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.PhoneNumber, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.PhoneNumber, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.PhoneNumber, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.Message, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Message, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Message, "", new {@class = "text-danger"})
    </div>
</div>