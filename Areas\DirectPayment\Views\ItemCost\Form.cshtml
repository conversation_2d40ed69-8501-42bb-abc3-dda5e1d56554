﻿@model AppTech.MSMS.Domain.Models.ItemCost
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="form-group">
    @Html.LabelFor(model => model.ProviderID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ProviderID, (SelectList)ViewBag.Providers)
        @Html.ValidationMessageFor(model => model.ProviderID)
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList)ViewBag.Services)
        @Html.ValidationMessageFor(model => model.ServiceID)
    </div>
</div>


<div class="form-group" id="saba-options">
    @Html.Label("النوع", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.Type, new[]
        {

            new SelectListItem {Text = "دفع مسبق", Value = "1"},
            new SelectListItem {Text = "فوترة", Value = "2"},
            new SelectListItem {Text = "وحدات مفتوح", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.Type, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="checkbox">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price, "", new { @class = "text-danger" })
        </div>
    </div>
</div>




<script>

    function setSabaOptions() {
        if (Number($('#ServiceID').val()) == 3) {
            $('#saba-options').show();
        }
        else
            $('#saba-options').hide();
    }

    $(function () {
        setSabaOptions();
       
        $('#ServiceID').on('change',
            function () {
                i('serv on change');
                setSabaOptions();
            });
    })
</script>