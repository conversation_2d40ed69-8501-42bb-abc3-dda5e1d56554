# سكريبت استعادة إعدادات msmdsrv من النسخة الاحتياطية

Write-Host @"
========================================
    استعادة إعدادات msmdsrv
========================================
"@ -ForegroundColor Cyan

# مسارات الملفات
$backupConfigPath = "E:\inetpub\OLAP\Config"
$backupIniFile = "$backupConfigPath\msmdsrv.ini"
$backupBakFile = "$backupConfigPath\msmdsrv.bak"

# مسارات SQL Server Analysis Services
$sqlServerPaths = @(
    "C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Config",
    "C:\Program Files\Microsoft SQL Server\MSAS15.MSSQLSERVER\OLAP\Config",
    "C:\Program Files\Microsoft SQL Server\MSAS13.MSSQLSERVER\OLAP\Config",
    "C:\Program Files\Microsoft SQL Server\MSAS12.MSSQLSERVER\OLAP\Config"
)

Write-Host "`n1. فحص ملفات النسخة الاحتياطية..." -ForegroundColor Yellow

if (Test-Path $backupIniFile) {
    $iniSize = (Get-Item $backupIniFile).Length / 1KB
    Write-Host "✓ وُجد ملف الإعدادات: msmdsrv.ini ($([math]::Round($iniSize, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "✗ ملف الإعدادات غير موجود: $backupIniFile" -ForegroundColor Red
    exit 1
}

if (Test-Path $backupBakFile) {
    $bakSize = (Get-Item $backupBakFile).Length / 1KB
    Write-Host "✓ وُجد ملف النسخة الاحتياطية: msmdsrv.bak ($([math]::Round($bakSize, 2)) KB)" -ForegroundColor Green
} else {
    Write-Host "⚠ ملف النسخة الاحتياطية غير موجود: $backupBakFile" -ForegroundColor Yellow
}

Write-Host "`n2. البحث عن SQL Server Analysis Services..." -ForegroundColor Yellow

$targetConfigPath = $null
foreach ($path in $sqlServerPaths) {
    if (Test-Path $path) {
        $targetConfigPath = $path
        Write-Host "✓ وُجد مجلد SSAS: $path" -ForegroundColor Green
        break
    } else {
        Write-Host "✗ غير موجود: $path" -ForegroundColor Red
    }
}

if (-not $targetConfigPath) {
    Write-Host "Warning: SQL Server Analysis Services not found" -ForegroundColor Yellow
    Write-Host "Please install SSAS first or specify path manually" -ForegroundColor Gray

    $manualPath = Read-Host "Enter SSAS Config folder path (or press Enter to skip)"
    if ($manualPath -and (Test-Path $manualPath)) {
        $targetConfigPath = $manualPath
        Write-Host "Path set: $targetConfigPath" -ForegroundColor Green
    } else {
        Write-Host "Operation cancelled" -ForegroundColor Yellow
        exit 0
    }
}

Write-Host "`n3. فحص الملفات الحالية..." -ForegroundColor Yellow

$currentIniFile = "$targetConfigPath\msmdsrv.ini"
$currentBakFile = "$targetConfigPath\msmdsrv.bak"

if (Test-Path $currentIniFile) {
    $currentSize = (Get-Item $currentIniFile).Length / 1KB
    Write-Host "⚠ يوجد ملف إعدادات حالي: msmdsrv.ini ($([math]::Round($currentSize, 2)) KB)" -ForegroundColor Yellow
    
    $overwrite = Read-Host "هل تريد استبداله بالنسخة الاحتياطية؟ (y/n)"
    if ($overwrite -ne "y") {
        Write-Host "تم إلغاء العملية" -ForegroundColor Yellow
        exit 0
    }
    
    # إنشاء نسخة احتياطية من الملف الحالي
    $backupCurrent = "$targetConfigPath\msmdsrv.ini.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $currentIniFile $backupCurrent -Force
    Write-Host "✓ تم حفظ نسخة احتياطية: $backupCurrent" -ForegroundColor Green
}

Write-Host "`n4. نسخ ملفات الإعدادات..." -ForegroundColor Yellow

try {
    # نسخ ملف الإعدادات الرئيسي
    Copy-Item $backupIniFile $currentIniFile -Force
    Write-Host "✓ تم نسخ: msmdsrv.ini" -ForegroundColor Green
    
    # نسخ ملف النسخة الاحتياطية إذا كان موجوداً
    if (Test-Path $backupBakFile) {
        Copy-Item $backupBakFile $currentBakFile -Force
        Write-Host "✓ تم نسخ: msmdsrv.bak" -ForegroundColor Green
    }
    
} catch {
    Write-Host "✗ خطأ في نسخ الملفات: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n5. تحليل إعدادات msmdsrv..." -ForegroundColor Yellow

try {
    [xml]$configXml = Get-Content $currentIniFile
    
    $dataDir = $configXml.ConfigurationSettings.DataDir
    $logDir = $configXml.ConfigurationSettings.LogDir
    $backupDir = $configXml.ConfigurationSettings.BackupDir
    $tempDir = $configXml.ConfigurationSettings.TempDir
    
    Write-Host "معلومات الإعدادات:" -ForegroundColor Cyan
    Write-Host "  مجلد البيانات: $dataDir" -ForegroundColor Gray
    Write-Host "  مجلد السجلات: $logDir" -ForegroundColor Gray
    Write-Host "  مجلد النسخ الاحتياطية: $backupDir" -ForegroundColor Gray
    Write-Host "  مجلد المؤقت: $tempDir" -ForegroundColor Gray
    
    # فحص وجود المجلدات
    $directories = @($dataDir, $logDir, $backupDir, $tempDir)
    foreach ($dir in $directories) {
        if (Test-Path $dir) {
            Write-Host "  ✓ موجود: $dir" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ غير موجود: $dir" -ForegroundColor Yellow
            Write-Host "    سيتم إنشاؤه عند تشغيل SSAS" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "⚠ خطأ في تحليل ملف الإعدادات: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n6. فحص خدمة SQL Server Analysis Services..." -ForegroundColor Yellow

$ssasServices = @(
    "MSSQLServerOLAPService",
    "MSOLAP`$MSSQLSERVER17",
    "MSOLAP`$MSSQLSERVER"
)

$foundService = $null
foreach ($serviceName in $ssasServices) {
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            $foundService = $service
            Write-Host "✓ وُجدت خدمة SSAS: $serviceName ($($service.Status))" -ForegroundColor Green
            break
        }
    } catch {
        Write-Host "✗ لم توجد خدمة: $serviceName" -ForegroundColor Red
    }
}

if ($foundService) {
    if ($foundService.Status -eq "Running") {
        Write-Host "⚠ الخدمة تعمل حالياً - يُنصح بإعادة تشغيلها لتطبيق الإعدادات الجديدة" -ForegroundColor Yellow
        
        $restart = Read-Host "هل تريد إعادة تشغيل خدمة SSAS؟ (y/n)"
        if ($restart -eq "y") {
            try {
                Write-Host "إيقاف الخدمة..." -ForegroundColor Gray
                Stop-Service -Name $foundService.Name -Force
                Start-Sleep -Seconds 5
                
                Write-Host "تشغيل الخدمة..." -ForegroundColor Gray
                Start-Service -Name $foundService.Name
                
                Write-Host "✓ تم إعادة تشغيل خدمة SSAS" -ForegroundColor Green
            } catch {
                Write-Host "✗ خطأ في إعادة تشغيل الخدمة: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "الخدمة متوقفة - يمكن تشغيلها الآن" -ForegroundColor Gray
    }
} else {
    Write-Host "⚠ لم يتم العثور على خدمة SSAS" -ForegroundColor Yellow
    Write-Host "يرجى تثبيت SQL Server Analysis Services" -ForegroundColor Gray
}

Write-Host "`n7. إنشاء تقرير الاستعادة..." -ForegroundColor Yellow

$report = @"
# تقرير استعادة إعدادات msmdsrv
تاريخ الاستعادة: $(Get-Date)

## الملفات المستعادة:
- المصدر: $backupIniFile
- الهدف: $currentIniFile
- الحجم: $([math]::Round((Get-Item $currentIniFile).Length / 1KB, 2)) KB

## إعدادات SSAS:
- مجلد البيانات: $dataDir
- مجلد السجلات: $logDir  
- مجلد النسخ الاحتياطية: $backupDir
- مجلد المؤقت: $tempDir

## حالة الخدمة:
- اسم الخدمة: $($foundService.Name)
- الحالة: $($foundService.Status)

## الخطوات التالية:
1. تشغيل خدمة SQL Server Analysis Services
2. اختبار الاتصال بـ SSAS
3. استعادة قواعد البيانات التحليلية إذا لزم الأمر
4. تحديث سلاسل الاتصال في التطبيقات

## ملاحظات:
- تم حفظ نسخة احتياطية من الإعدادات السابقة
- الإعدادات المستعادة من النسخة الاحتياطية الأصلية
- قد تحتاج لتحديث مسارات المجلدات حسب البيئة الجديدة
"@

$report | Out-File -FilePath "$targetConfigPath\restore-report.txt" -Encoding UTF8

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "انتهت استعادة إعدادات msmdsrv!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nتم حفظ تقرير الاستعادة في: $targetConfigPath\restore-report.txt" -ForegroundColor Cyan

pause
