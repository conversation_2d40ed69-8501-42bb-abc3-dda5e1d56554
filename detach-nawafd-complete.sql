-- سكريبت فصل قاعدة بيانات nawafd (MDF + LDF)
-- يحل مشكلة "File In Use" لكلا الملفين

USE master;

PRINT '========================================';
PRINT 'فصل قاعدة بيانات nawafd للنسخ';
PRINT '========================================';

-- 1. عرض معلومات قاعدة البيانات قبل الفصل
PRINT '';
PRINT '1. معلومات قاعدة البيانات الحالية:';
PRINT '------------------------------------';

SELECT 
    db.name AS DatabaseName,
    mf.name AS LogicalName,
    mf.physical_name AS PhysicalPath,
    mf.type_desc AS FileType,
    CAST(mf.size * 8.0 / 1024 AS DECIMAL(10,2)) AS SizeMB
FROM sys.master_files mf
INNER JOIN sys.databases db ON mf.database_id = db.database_id
WHERE db.name = 'nawafd';

-- 2. فحص الاتصالات النشطة
PRINT '';
PRINT '2. فحص الاتصالات النشطة:';
PRINT '----------------------------';

SELECT 
    session_id,
    login_name,
    host_name,
    program_name,
    status
FROM sys.dm_exec_sessions 
WHERE database_id = DB_ID('nawafd');

-- 3. إنهاء جميع الاتصالات النشطة
PRINT '';
PRINT '3. إنهاء الاتصالات النشطة...';

DECLARE @kill varchar(8000) = '';
SELECT @kill = @kill + 'KILL ' + CONVERT(varchar(5), session_id) + ';'
FROM sys.dm_exec_sessions
WHERE database_id = DB_ID('nawafd') AND session_id <> @@SPID;

IF LEN(@kill) > 0
BEGIN
    PRINT 'إنهاء الاتصالات: ' + @kill;
    EXEC(@kill);
END
ELSE
    PRINT 'لا توجد اتصالات نشطة';

-- 4. تعيين قاعدة البيانات في وضع المستخدم الواحد
PRINT '';
PRINT '4. تعيين وضع المستخدم الواحد...';
ALTER DATABASE [nawafd] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
PRINT 'تم تعيين وضع المستخدم الواحد';

-- 5. فصل قاعدة البيانات
PRINT '';
PRINT '5. فصل قاعدة البيانات...';

EXEC sp_detach_db 
    @dbname = 'nawafd',
    @skipchecks = 'true';

PRINT 'تم فصل قاعدة البيانات بنجاح!';

-- 6. التحقق من الفصل
PRINT '';
PRINT '6. التحقق من الفصل:';
PRINT '--------------------';

IF NOT EXISTS (SELECT 1 FROM sys.databases WHERE name = 'nawafd')
    PRINT '✓ تم فصل قاعدة البيانات بنجاح';
ELSE
    PRINT '✗ فشل في فصل قاعدة البيانات';

PRINT '';
PRINT '========================================';
PRINT 'يمكنك الآن نسخ الملفات التالية:';
PRINT '========================================';
PRINT 'ملف البيانات: E:\MSSQLDATA\nawafd.mdf';
PRINT 'ملف السجل: E:\MSSQLDATA\nawafd_0.ldf';
PRINT '';
PRINT 'بعد النسخ، لإعادة إرفاق قاعدة البيانات:';
PRINT '==========================================';
PRINT 'CREATE DATABASE [nawafd] ON';
PRINT '(FILENAME = ''E:\MSSQLDATA\nawafd.mdf''),';
PRINT '(FILENAME = ''E:\MSSQLDATA\nawafd_0.ldf'')';
PRINT 'FOR ATTACH;';
PRINT '';
PRINT 'أو إذا فُقد ملف السجل:';
PRINT '========================';
PRINT 'CREATE DATABASE [nawafd] ON';
PRINT '(FILENAME = ''E:\MSSQLDATA\nawafd.mdf'')';
PRINT 'FOR ATTACH_REBUILD_LOG;';
