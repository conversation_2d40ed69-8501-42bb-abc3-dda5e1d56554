﻿@model AppTech.MSMS.Web.Models.SignupModel

@{
    ViewBag.Title = "Sign up";
}

<div class="row row-first box-noborder">
<div class="span6">

    <div class="span6">

        <div style="padding: 40px 40px 80px 0;">
            @Html.ValidationSummary(true)

            <form method="post" action="/signup">

                @Html.AntiForgeryToken()
                <fieldset>
                    <legend>Sign Up</legend>
                    <ol class="unstyled">
                        <li>
                            @Html.TextBoxFor(m => m.FirstName, new {placeholder = "First Name"})
                            @Html.ValidationMessageFor(m => m.FirstName, string.Empty, new {@class = "invalid"})
                        </li>
                        <li>
                            @Html.TextBoxFor(m => m.LastName, new {placeholder = "Last Name"})
                            @Html.ValidationMessageFor(m => m.LastName, string.Empty, new {@class = "invalid"})
                        </li>
                        <li>
                            @Html.TextBoxFor(m => m.City, new {placeholder = "City"})
                            @Html.ValidationMessageFor(m => m.City, string.Empty, new {@class = "invalid"})
                        </li>
                        <li>
                            @Html.TextBoxFor(m => m.Country, new {placeholder = "Country"})
                            @Html.ValidationMessageFor(m => m.Country, string.Empty, new {@class = "invalid"})
                        </li>
                        <li>
                            @Html.TextBoxFor(m => m.Email, new {placeholder = "Email"})
                            @Html.ValidationMessageFor(m => m.Email, string.Empty, new {@class = "invalid"})
                        </li>
                        <li>
                            @Html.PasswordFor(m => m.Password, new {placeholder = "Password"})
                            @Html.ValidationMessageFor(m => m.Password, string.Empty, new {@class = "invalid"})
                        </li>
                    </ol>
                </fieldset>
                <input class="btn" type="submit" value="Sign up"/>&nbsp; &nbsp;&nbsp;
                @Html.ActionLink("Log in", "Login") if you already have an account.
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript">

        // to learn more about JavaScript and jQuery Design Patterns please
        // see our "Pro JavaScript and jQuery Patterns" package which is available
        // on our website at www.dofactory.com


        // ** namespace pattern
        // ** revealing module pattern
        // ** singleton pattern
        Patterns.namespace("Art").Signup = (function() {

            var activatePage = function() {
                $("#facebook").on("clicked",
                    function() {
                        $(this).parent("form").submit();
                    });
            };

            var start = function() {
                activatePage();
            };

            // the revealing part of the revealing module pattern
            return { start: start };
        })();

        $(function() {

            // ** facade pattern

            var signup = Patterns.Art.Signup;
            signup.start();
        });
    </script>
}

/////
@model AppTech.MSMS.Web.Models.LoginModel

@{
    ViewBag.Title = "تسجيل الدخول";
    var returnUrl = ViewBag.ReturnUrl != null ? "?ReturnUrl=" + ViewBag.ReturnUrl : "";
    Layout = "~/Views/Shared/_LoginLayout.cshtml";
}


<body class="login-layout  rtl skin-2">
<div class="main-container">
    <div class="main-content">
        <div class="row">
            <div class="col-sm-10 col-sm-offset-1">
                <div class="login-container">
                    <div class="center">
                        <h1>
                            <i class="ace-icon fa fa-leaf green"></i>
                            <span class="red">BC </span>
                            <span class="white" id="id-text2"> Mobile</span>
                        </h1>
                        @* <h4 class="blue" id="id-company-text">&copy; </h4>*@
                    </div>

                    <div class="space-6"></div>
                    <div class="position-relative">
                        <div id="login-box" class="login-box visible widget-box no-border">
                            <div class="widget-body">
                                <div class="widget-main">
                                    <h4 class="header blue lighter bigger">
                                        <i class="ace-icon fa fa-coffee green"></i>
                                        قم بأدخال بياناتك
                                    </h4>
                                    <div class="space-6"></div>
                                    @* @using (Html.BeginForm("login", "AccountService","Hii"))
                                            {*@
                                    @*<form method="post" action="/AccountService/login@(returnUrl)">*@
                                    @using (Ajax.BeginForm(new AjaxOptions
                                    {
                                        OnBegin = "OnFormBegin",
                                        LoadingElementId = "formloader",
                                        OnSuccess = "onSuccess",
                                        OnFailure = "onFailure",
                                        UpdateTargetId = "list",
                                        InsertionMode = InsertionMode.ReplaceWith
                                    }))
                                    {
                                        <fieldset>
                                            <legend>تسجيل الدخول</legend>


                                            @Html.AntiForgeryToken()
                                            @Html.ValidationSummary(true)
                                            <ol class="unstyled">
                                                <li>
                                                    @Html.TextBoxFor(m => m.Email, new {placeholder = "اسم المستخدم"})
                                                    @Html.ValidationMessageFor(m => m.Email, string.Empty, new {@class = "invalid"})
                                                </li>
                                                <li>
                                                    @Html.PasswordFor(m => m.Password, new {placeholder = "كلمة المرور"})
                                                    @Html.ValidationMessageFor(m => m.Password, string.Empty, new {@class = "invalid"})
                                                </li>
                                                @*    <li>
                                                            <label class="checkbox">
                                                                <input class="input-checkbox" type="checkbox" value="" id="checkbox" name="checkbox">
                                                                Remember Me?
                                                            </label>
                                                            <input name="RememberMe" id="RememberMe" type="hidden" value="false" />
                                                        </li>*@
                                            </ol>
                                            <br/>

                                            <input class="width-35 pull-right btn btn-sm btn-primary" type="submit" value="التسجيل"/>
                                            &nbsp;
                                            &nbsp;
                                            &nbsp;


                                        </fieldset>
                                    }
                                    @*</form>*@
                                    <div class="space-6"></div>
                                </div>
                            </div><!-- /.widget-body -->
                        </div><!-- /.login-box -->
                    </div><!-- /.position-relative -->
                </div>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.main-content -->
</div><!-- /.main-container -->
<!-- basic scripts -->

<!-- inline scripts related to this page -->

<script>
    function OnFormBegin(context) {
        //showFormLoading();
    }

    if ($("#Date")[0]) {
        $("#Date").val(getToday());
        $('#Date').prop('readonly', true);
    }

    function onSuccess(data) {
        i('onSuccess');
        // hideFormLoading();
        $("#modal").modal('hide');
        showSuccess("تم حفط السجل بنجاح");
        try {
            var pager = Patterns.Art.Pager;
            pager.activateList();

        } catch (e) {
            alert(e);
        }
    }

    function onFailure(xhr, status) {
        hideFormLoading();
        var msg = parseXhr(xhr);
        log('onCrudFailure xhr msg:' + msg);
        alert(msg);
        // showError(msg);

        //  log('responseText.Message:' +xhr.responseText.Message);
        //  hideLoading();
        //  handleXhr(xhr);
    }
</script>
<script type="text/javascript">


    //jQuery(function($) {
    //    $(document).on('click',
    //        '.toolbar a[data-target]',
    //        function(e) {
    //            e.preventDefault();
    //            var target = $(this).data('target');
    //            $('.widget-box.visible').removeClass('visible'); //hide others
    //            $(target).addClass('visible'); //show target
    //        });
    //});


    ////you don't need this, just used for changing background
    //jQuery(function($) {
    //    $('#btn-login-dark').on('click',
    //        function(e) {
    //            $('body').attr('class', 'login-layout');
    //            $('#id-text2').attr('class', 'white');
    //            $('#id-company-text').attr('class', 'blue');

    //            e.preventDefault();
    //        });
    //    $('#btn-login-light').on('click',
    //        function(e) {
    //            $('body').attr('class', 'login-layout light-login');
    //            $('#id-text2').attr('class', 'grey');
    //            $('#id-company-text').attr('class', 'blue');

    //            e.preventDefault();
    //        });
    //    $('#btn-login-blur').on('click',
    //        function(e) {
    //            $('body').attr('class', 'login-layout blur-login');
    //            $('#id-text2').attr('class', 'white');
    //            $('#id-company-text').attr('class', 'light-blue');

    //            e.preventDefault();
    //        });

    //});
</script>
</body>