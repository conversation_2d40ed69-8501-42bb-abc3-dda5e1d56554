﻿@model AppTech.MSMS.Domain.Models.ConsumeInvoice

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}


<div class="row">

    <div class="form-group">
        @Html.LabelFor(model => model.StartDate, new {@class = "control-label col-md-2"})
        <div class="col-md-4">
            @Html.EditorFor(model => model.StartDate, new {htmlAttributes = new {@class = "date-picker"}})
            @Html.ValidationMessageFor(model => model.StartDate, "", new {@class = "text-danger"})
        </div>
    </div>

    <div class="form-group">
        @Html.LabelFor(model => model.EndDate, new {@class = "control-label col-md-2"})
        <div class="col-md-4">
            @Html.EditorFor(model => model.EndDate, new {htmlAttributes = new {@class = "date-picker"}})
            @Html.ValidationMessageFor(model => model.EndDate, "", new {@class = "text-danger"})
        </div>
    </div>

</div>


<div class="form-group">
    @Html.LabelFor(model => model.UnitPrice, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.UnitPrice, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.UnitPrice, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.SubscriptionFee, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriptionFee, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.SubscriptionFee, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.FineAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.FineAmount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.FineAmount, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.FineNote, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.FineNote, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.FineNote, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.DebitorAccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.DebitorAccountID, (SelectList) ViewBag.Subscribers, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.DebitorAccountID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.LateAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.LateAmount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.LateAmount, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.ExtraAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.ExtraAmount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.ExtraAmount, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Quantity, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Quantity, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.Quantity, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.TotalAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.TotalAmount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.TotalAmount, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Amount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Amount, new {htmlAttributes = new {@class = ""}})
        @Html.ValidationMessageFor(model => model.Amount, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Note, "", new {@class = "text-danger"})
    </div>
</div>

<script>

    $(function() {
        try {

            $('.date-picker').datepicker({
                dateFormat: "dd/MM/yy",
                changeMonth: true,
                changeYear: true,
                yearRange: "-60:+0",
                autoclose: true,
                todayHighlight: true
            }).next().on('click',
                function() {
                    $(this).prev().focus();
                });
        } catch (e) {
            alert("Couldnt set date-picker: " + e);
        }


        $('#DebitorAccountID').on('change',
            function() {
                i('DebitorAccountID onchange');
                var url = "/Inventory/ConsumeInvoice/GetLastQuantity?accountId=" + $('#DebitorAccountID').val();
                AjaxCall(url).done(function(response) {
                    $('#LateAmount').val(response);
                }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
            });

        $('#Amount').prop('readonly', true);
        $('#Quantity').prop('readonly', true);
        $('#TotalAmount').prop('readonly', true);
        $('#LateAmount').on('input',
            function() {
                calc();
            });


        $('#ExtraAmount').on('input',
            function() {
                calc();
            });

        $('#UnitPrice').on('input',
            function() {
                calc();
            });


    });

    function calc() {
        var lastQnty = Number($('#LateAmount').val());
        var currentQnty = Number($('#ExtraAmount').val());
        var rate = currentQnty - lastQnty;
        Number($('#Quantity').val(rate));

        var price = Number($('#UnitPrice').val());
        var netAmount = rate * price;
        $('#TotalAmount').val(netAmount);

        var subscription = Number($('#SubscriptionFee').val());
        var fine = Number($('#FineAmount').val());
        //var late = Number($('#LateAmount').val());

        i(' fine' + fine);
        i(' subscription' + subscription);
        i(' netAmount ' + netAmount);
        var totalAmount = netAmount + fine + subscription;
        i(' totalAmount ' + totalAmount);
        $('#Amount').val(totalAmount);
    }
    //$( "#myselect option:selected" ).text();
</script>