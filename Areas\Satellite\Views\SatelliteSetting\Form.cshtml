﻿@model AppTech.MSMS.Domain.Settings.SatelliteSetting


<div class="row">
    <div class="col-xs-12 col-sm-6">
        @using (Ajax.BeginForm(new AjaxOptions
        {
            OnBegin = "return OnFormBegin()",
            OnSuccess = "onCrudSuccess",
            OnFailure = "onCrudFailure",
            LoadingElementId = "formloader"
        }))
        {
            foreach (var property in ViewData.ModelMetadata.Properties)
            {
                <div class="form-group col-md-12">
                    @Html.Label(property.DisplayName, new { @class = "control-label col-md-3" })
                    <div class="col-md-9">
                        @Html.DropDownList(property.PropertyName, (SelectList)ViewBag.CreditorAccounts)
                    </div>
                </div>
            }


            <div class="space-10"></div>

            <div class="space-32"></div>
            <div class="space-32"></div>
            <div class="hr hr32 hr-dotted"></div>
            @Html.Partial("_FormAction")
        }
    </div>


</div>



<script>

    function loadFirstList() {
        i('loadDataList');
        fillDataList('PropertyName', '/Satellite/SatelliteSetting/GetAccounts');
    }
    //loadFirstList();
</script>


