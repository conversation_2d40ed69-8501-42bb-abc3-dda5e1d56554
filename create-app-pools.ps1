# سكريبت إنشاء Application Pools لنظام AppTech MSMS
Import-Module WebAdministration

Write-Host "إنشاء Application Pools..." -ForegroundColor Green

# إنشاء Application Pool للـ API
$apiPoolName = "AppTechAPI"
if (Get-IISAppPool -Name $apiPoolName -ErrorAction SilentlyContinue) {
    Write-Host "حذف Application Pool الموجود: $apiPoolName" -ForegroundColor Yellow
    Remove-WebAppPool -Name $apiPoolName
}

Write-Host "إنشاء Application Pool: $apiPoolName" -ForegroundColor Yellow
New-WebAppPool -Name $apiPoolName
Set-ItemProperty -Path "IIS:\AppPools\$apiPoolName" -Name "managedRuntimeVersion" -Value "v4.0"
Set-ItemProperty -Path "IIS:\AppPools\$apiPoolName" -Name "enable32BitAppOnWin64" -Value $false
Set-ItemProperty -Path "IIS:\AppPools\$apiPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\$apiPoolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\$apiPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"

# إنشاء Application Pool للـ Client
$clientPoolName = "AppTechClient"
if (Get-IISAppPool -Name $clientPoolName -ErrorAction SilentlyContinue) {
    Write-Host "حذف Application Pool الموجود: $clientPoolName" -ForegroundColor Yellow
    Remove-WebAppPool -Name $clientPoolName
}

Write-Host "إنشاء Application Pool: $clientPoolName" -ForegroundColor Yellow
New-WebAppPool -Name $clientPoolName
Set-ItemProperty -Path "IIS:\AppPools\$clientPoolName" -Name "managedRuntimeVersion" -Value "v4.0"
Set-ItemProperty -Path "IIS:\AppPools\$clientPoolName" -Name "enable32BitAppOnWin64" -Value $false
Set-ItemProperty -Path "IIS:\AppPools\$clientPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\$clientPoolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\$clientPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"

# إنشاء Application Pool للـ Portal
$portalPoolName = "AppTechPortal"
if (Get-IISAppPool -Name $portalPoolName -ErrorAction SilentlyContinue) {
    Write-Host "حذف Application Pool الموجود: $portalPoolName" -ForegroundColor Yellow
    Remove-WebAppPool -Name $portalPoolName
}

Write-Host "إنشاء Application Pool: $portalPoolName" -ForegroundColor Yellow
New-WebAppPool -Name $portalPoolName
Set-ItemProperty -Path "IIS:\AppPools\$portalPoolName" -Name "managedRuntimeVersion" -Value "v4.0"
Set-ItemProperty -Path "IIS:\AppPools\$portalPoolName" -Name "enable32BitAppOnWin64" -Value $false
Set-ItemProperty -Path "IIS:\AppPools\$portalPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
Set-ItemProperty -Path "IIS:\AppPools\$portalPoolName" -Name "recycling.periodicRestart.time" -Value "00:00:00"
Set-ItemProperty -Path "IIS:\AppPools\$portalPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"

Write-Host "تم إنشاء جميع Application Pools بنجاح!" -ForegroundColor Green

# عرض Application Pools المُنشأة
Write-Host "`nApplication Pools المُنشأة:" -ForegroundColor Cyan
Get-IISAppPool | Where-Object {$_.Name -like "AppTech*"} | Format-Table Name, State, ManagedRuntimeVersion
