@echo off
echo ========================================
echo       تجهيز نظام AppTech MSMS
echo ========================================

echo.
echo 1. فحص حالة IIS...
sc query w3svc
if %errorlevel% == 0 (
    echo IIS موجود في النظام
) else (
    echo IIS غير مثبت - يجب تثبيته يدوياً
)

echo.
echo 2. محاولة بدء خدمة IIS...
net start w3svc
if %errorlevel% == 0 (
    echo تم بدء IIS بنجاح
) else (
    echo فشل في بدء IIS - قد يحتاج تفعيل
)

echo.
echo 3. فحص المنافذ...
netstat -an | findstr :80
netstat -an | findstr :443

echo.
echo 4. فحص ملفات النظام...
if exist "wwwroot\api\Global.asax" (
    echo ✓ تطبيق API موجود
) else (
    echo ✗ تطبيق API مفقود
)

if exist "wwwroot\client\Global.asax" (
    echo ✓ تطبيق Client موجود
) else (
    echo ✗ تطبيق Client مفقود
)

if exist "wwwroot\portal\Global.asax" (
    echo ✓ تطبيق Portal موجود
) else (
    echo ✗ تطبيق Portal مفقود
)

echo.
echo 5. فحص قواعد البيانات...
sqlcmd -S localhost -E -Q "SELECT @@VERSION" 2>nul
if %errorlevel% == 0 (
    echo ✓ SQL Server متاح
) else (
    echo ✗ SQL Server غير متاح
)

echo.
echo ========================================
echo       انتهى فحص النظام
echo ========================================
pause
