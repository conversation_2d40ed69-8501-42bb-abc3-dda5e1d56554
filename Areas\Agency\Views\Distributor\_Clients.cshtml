﻿@model IEnumerable<AppTech.MSMS.Domain.Models.Client>
<div id="clist">
    <table class="table table-hover">
        <thead>
            <tr>


                <th>
                    رقم الحساب
                </th>
                <th>
                    اسم الحساب
                </th>

                <th>
                    @Html.DisplayName("الحالة")
                </th>

                <th>
                    @Html.DisplayNameFor(model => model.CreatedTime)
                </th>

                <th></th>
            </tr>
        </thead>
        @foreach (var item in Model)
        {
            <tbody>
                <tr>

                    <td>
                        @Html.DisplayFor(modelItem => item.Number)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Name)
                    </td>

                    <td>

                        @if (item.Status == 1)
                        {
                            <button class="btn btn-link green disabled">
                                <i class="ace-icon fa fa-flag bigger-110 green"></i>
                                نشط
                            </button>
                        }
                        else
                        {
                            <button class="btn btn-link red disabled">
                                <i class="ace-icon fa fa-flag bigger-110 red"></i>
                                موقف
                            </button>
                        }

                    </td>

                    <td>
                        @Html.DisplayFor(modelItem => item.CreatedTime)
                    </td>
                    <td>
                        <Button class="btn btn-default" onclick="unbind('@item.ID','@ViewBag.ParentID')">
                            <i class="ace-icon fa fa-flag bigger-110"></i>
                            فك الربط
                        </Button>

                    </td>
                </tr>
            </tbody>
        }

    </table>

    </div>
    <script>
    function unbind(cid,aid) {
        i('open modal id' + cid +" aid: "+aid);
        var agId =@ViewBag.ParentID;
        if (confirm('سوف يتم فك الربط , هل انت متأكد')) {
        var url = 'Agency/Distributor/RemoveClient?parentId=' + agId + '&clientId=' + cid;

        AjaxCall(url)
            .done(function (response) {
              //  ar(response.Message);
                $("#clist").replaceWith(response);
            }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
    }
    }
    </script>
