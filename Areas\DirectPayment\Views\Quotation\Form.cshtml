﻿@model AppTech.MSMS.Domain.Models.Quotation
@using Obout.Mvc.ComboBox

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.ServiceID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ServiceID, (SelectList)ViewBag.Services)
        @Html.ValidationMessageFor(model => model.ServiceID)
    </div>
</div>

<div class="form-group" id="saba-options">
    @Html.Label("النوع", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.Type, new[]
        {
            
            new SelectListItem {Text = "دفع مسبق", Value = "1"},
            new SelectListItem {Text = "فوترة", Value = "2"},
            new SelectListItem {Text = "وحدات مفتوح", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.Type, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.AccountState, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountState, new[]
        {
            new SelectListItem {Text = "كافة الحسابات", Value = "1"},
            new SelectListItem {Text = "مجموعة", Value = "2"},
            new SelectListItem {Text = "حساب محدد", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.AccountState, "", new { @class = "text-danger" })
    </div>
</div>



<div class="form-group" id="specifc">
    <div class="col-md-12">
        @Html.Label("اسم الحساب", new { @class = "control-label col-md-2" })
        @Html.Obout(new ComboBox("AccountID")
        {
            Width = 300,
            SelectedValue = Model.AccountID == 0 ? null : Model.AccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })

        @Html.ValidationMessageFor(model => model.AccountID)
    </div>
</div>

<div class="form-group" id="group">
    @Html.LabelFor(model => model.AccountGroupID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountGroupID, (SelectList)ViewBag.Groups, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.AccountGroupID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.Price, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Price, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Price)
    </div>
</div>

<script>

    function setState() {

        var num = Number($("#AccountState").children("option:selected").val());
        i('selected state: ' + num);
        if (num === 1) {
            $('#specifc').hide();
            $('#group').hide();

        } else if (num === 3) {
            $('#specifc').show();
            $('#group').hide();

        } else if (num === 2) {
            $('#specifc').hide();
            $('#group').show();
        }
    }

    function setSabaOptions() {
        if (Number($('#ServiceID').val()) == 3) {
            $('#saba-options').show();
        }
        else
            $('#saba-options').hide();
    }

    $(function () {
        setState();
        setSabaOptions();
        $('#AccountState').on('change',
            function () {
                setState();
            });

        $('#ServiceID').on('change',
            function () {
                i('serv on change');
                setSabaOptions();
            });
    })
</script>