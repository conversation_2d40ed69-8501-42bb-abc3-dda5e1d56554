﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="angularjs" version="1.7.8.1" targetFramework="net461" />
  <package id="Antlr" version="3.5.0.2" targetFramework="net461" />
  <package id="AutoMapper" version="9.0.0" targetFramework="net461" />
  <package id="bootstrap" version="3.3.7" targetFramework="net461" />
  <package id="elmah" version="1.2.2" targetFramework="net461" />
  <package id="elmah.corelibrary" version="1.2.2" targetFramework="net461" />
  <package id="EntityFramework" version="6.3.0" targetFramework="net461" />
  <package id="jQuery" version="3.4.1" targetFramework="net461" />
  <package id="jQuery.UI.Combined" version="1.12.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.2" targetFramework="net461" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.2" targetFramework="net461" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.2" targetFramework="net461" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.Razor" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.SignalR" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.SignalR.Core" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.SignalR.JS" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.SignalR.SystemWeb" version="2.4.1" targetFramework="net461" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.OData" version="5.7.0" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages.Data" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.AspNet.WebPages.WebData" version="3.2.7" targetFramework="net461" />
  <package id="Microsoft.Bcl" version="1.1.10" targetFramework="net461" />
  <package id="Microsoft.Bcl.Build" version="1.0.21" targetFramework="net461" />
  <package id="Microsoft.CodeAnalysis.FxCopAnalyzers" version="2.9.7" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.CodeAnalysis.VersionCheckAnalyzer" version="2.9.7" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.CodeQuality.Analyzers" version="2.9.7" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.Data.Edm" version="5.8.4" targetFramework="net461" />
  <package id="Microsoft.Data.OData" version="5.8.4" targetFramework="net461" />
  <package id="Microsoft.jQuery.Unobtrusive.Ajax" version="3.2.6" targetFramework="net461" />
  <package id="Microsoft.Net.Http" version="2.2.29" targetFramework="net461" />
  <package id="Microsoft.NetCore.Analyzers" version="2.9.7" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.NetFramework.Analyzers" version="2.9.7" targetFramework="net461" developmentDependency="true" />
  <package id="Microsoft.Owin" version="4.0.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.0.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Security" version="4.0.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.Cookies" version="4.0.1" targetFramework="net461" />
  <package id="Microsoft.Owin.Security.OAuth" version="4.0.1" targetFramework="net461" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net45" />
  <package id="Modernizr" version="2.8.3" targetFramework="net461" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net461" />
  <package id="Owin" version="1.0" targetFramework="net45" />
  <package id="RestSharp" version="106.6.10" targetFramework="net461" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net461" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net461" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net461" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net461" />
  <package id="System.Spatial" version="5.8.4" targetFramework="net461" />
  <package id="WebGrease" version="1.6.0" targetFramework="net461" />
</packages>