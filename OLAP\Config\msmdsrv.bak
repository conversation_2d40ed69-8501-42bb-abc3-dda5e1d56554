<ConfigurationSettings>
	<DataDir>C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Data</DataDir>
	<LogDir>C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Log</LogDir>
	<BackupDir>C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Backup</BackupDir>
	<AllowedBrowsingFolders>C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Backup\|C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Log\|C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Data\</AllowedBrowsingFolders>
	<CollationName>Latin1_General_CI_AS</CollationName>
	<Language>1033</Language>
	<TempDir>C:\Program Files\Microsoft SQL Server\MSAS14.MSSQLSERVER17\OLAP\Temp</TempDir>
	<DeploymentMode>2</DeploymentMode>
	<TMCompatibilitySKU>0</TMCompatibilitySKU>
	<ServerLocation>0</ServerLocation>
	<EnabledIncompleteCode>0</EnabledIncompleteCode>
	<AutoSetDefaultInitialCatalog>1</AutoSetDefaultInitialCatalog>
	<OnPremDataAccess>0</OnPremDataAccess>
	<ErrorReportingMode>0</ErrorReportingMode>
	<EnableDebugActivityTracking>1</EnableDebugActivityTracking>
	<Security>
		<DataProtection>
			<RequiredProtectionLevel>1</RequiredProtectionLevel>
		</DataProtection>
		<AdministrativeDataProtection>
			<RequiredProtectionLevel>1</RequiredProtectionLevel>
		</AdministrativeDataProtection>
		<RequireClientAuthentication>1</RequireClientAuthentication>
		<SecurityPackageList/>
		<DisableClientImpersonation>0</DisableClientImpersonation>
		<BuiltinAdminsAreServerAdmins>1</BuiltinAdminsAreServerAdmins>
		<ServiceAccountIsServerAdmin>1</ServiceAccountIsServerAdmin>
		<ErrorMessageMode>2</ErrorMessageMode>
		<CellPermissionMode>0</CellPermissionMode>
		<HighTrustTokenSignerCert/>
		<NormalTrustTokenSignerCert/>
		<ServerSchannelTokenSignerCert/>
	</Security>
	<DataSourceImpersonationAccount>
		<UserName>NT AUTHORITY\NetworkService</UserName>
		<Password/>
	</DataSourceImpersonationAccount>
	<Network>
		<Listener>
			<RequestSizeThreshold>4095</RequestSizeThreshold>
			<MaxAllowedRequestSize>0</MaxAllowedRequestSize>
			<ServerSendTimeout>60000</ServerSendTimeout>
			<ServerReceiveTimeout>60000</ServerReceiveTimeout>
			<IPV4Support>2</IPV4Support>
			<IPV6Support>2</IPV6Support>
		</Listener>
		<TCP>
			<MaxPendingSendCount>12</MaxPendingSendCount>
			<MaxPendingReceiveCount>4</MaxPendingReceiveCount>
			<MinPendingReceiveCount>2</MinPendingReceiveCount>
			<MaxCompletedReceiveCount>9</MaxCompletedReceiveCount>
			<ScatterReceiveMultiplier>5</ScatterReceiveMultiplier>
			<MaxPendingAcceptExCount>10</MaxPendingAcceptExCount>
			<MinPendingAcceptExCount>2</MinPendingAcceptExCount>
			<InitialConnectTimeout>10</InitialConnectTimeout>
			<SendBufferSize>7300</SendBufferSize>
			<SocketOptions>
				<SendBufferSize>0</SendBufferSize>
				<ReceiveBufferSize>0</ReceiveBufferSize>
				<DisableNonblockingMode>1</DisableNonblockingMode>
				<EnableNagleAlgorithm>0</EnableNagleAlgorithm>
				<EnableLingerOnClose>0</EnableLingerOnClose>
				<LingerTimeout>0</LingerTimeout>
			</SocketOptions>
		</TCP>
		<Requests>
			<EnableBinaryXML>0</EnableBinaryXML>
			<EnableCompression>0</EnableCompression>
		</Requests>
		<Responses>
			<EnableBinaryXML>1</EnableBinaryXML>
			<EnableCompression>1</EnableCompression>
			<CompressionLevel>9</CompressionLevel>
		</Responses>
		<ListenOnlyOnLocalConnections>0</ListenOnlyOnLocalConnections>
		<ListenOnTCPConnections>1</ListenOnTCPConnections>
	</Network>
	<ExternalConnectionTimeout>60</ExternalConnectionTimeout>
	<ExternalCommandTimeout>3600</ExternalCommandTimeout>
	<IdleConnectionTimeout>0</IdleConnectionTimeout>
	<IdleOrphanSessionTimeout>120</IdleOrphanSessionTimeout>
	<MinIdleSessionTimeout>2700</MinIdleSessionTimeout>
	<MaxIdleSessionTimeout>0</MaxIdleSessionTimeout>
	<WriteTransactionIdleTimeout>0</WriteTransactionIdleTimeout>
	<BackgroundSessionRollbackBatchingInterval>1</BackgroundSessionRollbackBatchingInterval>
	<ServerTimeout>3600</ServerTimeout>
	<AdminTimeout>0</AdminTimeout>
	<CommitTimeout>0</CommitTimeout>
	<ForceCommitTimeout>30000</ForceCommitTimeout>
	<RequestPrioritization>
		<Enabled>0</Enabled>
		<StatisticsStoreSize>1024</StatisticsStoreSize>
	</RequestPrioritization>
	<Port>0</Port>
	<InstanceVisible>1</InstanceVisible>
	<LocalCubeServerPortInConnectionString>0</LocalCubeServerPortInConnectionString>
	<EnableFast1033Locale>0</EnableFast1033Locale>
	<ServerThreadLocale>0</ServerThreadLocale>
	<Log>
		<File>msmdsrv.log</File>
		<FileBufferSize>0</FileBufferSize>
		<MaxFileSizeMB>0</MaxFileSizeMB>
		<MaxNumberOfLogFiles>0</MaxNumberOfLogFiles>
		<MessageLogs>File;Console;System</MessageLogs>
		<ErrorLog>
			<ErrorLogFileName/>
			<ErrorLogFileSize>4</ErrorLogFileSize>
			<KeyErrorAction>0</KeyErrorAction>
			<KeyErrorLogFile/>
			<KeyErrorLimit>0</KeyErrorLimit>
			<KeyErrorLimitAction>0</KeyErrorLimitAction>
			<LogErrorTypes>
				<KeyNotFound>1</KeyNotFound>
				<KeyDuplicate>0</KeyDuplicate>
				<NullKeyConvertedToUnknown>0</NullKeyConvertedToUnknown>
				<NullKeyNotAllowed>1</NullKeyNotAllowed>
				<CalculationError>0</CalculationError>
			</LogErrorTypes>
			<IgnoreDataTruncation>0</IgnoreDataTruncation>
		</ErrorLog>
		<QueryLog>
			<QueryLogSampling>10</QueryLogSampling>
			<QueryLogFileName/>
			<QueryLogFileSize>4</QueryLogFileSize>
			<QueryLogConnectionString/>
			<QueryLogTableName>OlapQueryLog</QueryLogTableName>
			<CreateQueryLogTable>0</CreateQueryLogTable>
			<UBOMaxQueries>100</UBOMaxQueries>
		</QueryLog>
		<Exception>
			<CreateAndSendCrashReports>1</CreateAndSendCrashReports>
			<CrashReportsFolder/>
			<SQLDumperFlagsOn>0x0</SQLDumperFlagsOn>
			<SQLDumperFlagsOff>0x0</SQLDumperFlagsOff>
			<MiniDumpFlagsOn>0x0</MiniDumpFlagsOn>
			<MiniDumpFlagsOff>0x0</MiniDumpFlagsOff>
			<MinidumpErrorList>0xC1000000, 0xC1000001, 0xc1000016, 0xC102003F, 0xC1360054, 0xC1360055, 0xC1000012</MinidumpErrorList>
			<ExceptionHandlingMode>0</ExceptionHandlingMode>
			<CriticalErrorHandling>1</CriticalErrorHandling>
			<MaxExceptions>500</MaxExceptions>
			<MaxDuplicateDumps>1</MaxDuplicateDumps>
			<MaxDuplicateDumpsOnError>3</MaxDuplicateDumpsOnError>
			<ExitOnMemoryHeapCorruption>0</ExitOnMemoryHeapCorruption>
			<MemoryHeapCheckPeriod>60</MemoryHeapCheckPeriod>
			<ExitOnMemoryHeapCheckFailed>0</ExitOnMemoryHeapCheckFailed>
			<RecycleEngine>0</RecycleEngine>
			<CloudStackTraceDepth>35</CloudStackTraceDepth>
			<RemoveIMBIObjectsFromFilteredDump>31</RemoveIMBIObjectsFromFilteredDump>
			<MemoryThresholdForFilteredDumpGB>0</MemoryThresholdForFilteredDumpGB>
			<EnableSingleHugeDumpInCloudService>0</EnableSingleHugeDumpInCloudService>
		</Exception>
		<Trace>
			<TraceMaxRowsetSize>1000000</TraceMaxRowsetSize>
			<TraceBackgroundDistributionPeriod>2000</TraceBackgroundDistributionPeriod>
			<TraceBackgroundFlushPeriod>2000</TraceBackgroundFlushPeriod>
			<TraceRowsetBackgroundFlushPeriod>500</TraceRowsetBackgroundFlushPeriod>
			<TraceFileBufferSize>12000</TraceFileBufferSize>
			<TraceFileWriteTrailerPeriod>300000</TraceFileWriteTrailerPeriod>
			<TraceQueryResponseTextChunkSize>8192</TraceQueryResponseTextChunkSize>
			<TraceReportFQDN>0</TraceReportFQDN>
			<TraceRequestParameters>1</TraceRequestParameters>
			<TraceProtocolTraffic>0</TraceProtocolTraffic>
			<TraceXMSchedulerInAzurePeriodSec>30</TraceXMSchedulerInAzurePeriodSec>
			<EnableTracingRecalcProblems>3</EnableTracingRecalcProblems>
		</Trace>
		<FlightRecorder>
			<Enabled>1</Enabled>
			<FileSizeMB>10</FileSizeMB>
			<LogDurationSec>3600</LogDurationSec>
			<SnapshotFrequencySec>120</SnapshotFrequencySec>
			<TraceDefinitionFile/>
			<SnapshotDefinitionFile/>
		</FlightRecorder>
	</Log>
	<Memory>
		<MemoryHeapType>-1</MemoryHeapType>
		<HeapTypeForObjects>-1</HeapTypeForObjects>
		<HardMemoryLimit>0</HardMemoryLimit>
		<TotalMemoryLimit>80</TotalMemoryLimit>
		<LowMemoryLimit>65</LowMemoryLimit>
		<VertiPaqMemoryLimit>60</VertiPaqMemoryLimit>
		<MidMemoryPrice>10</MidMemoryPrice>
		<HighMemoryPrice>1000</HighMemoryPrice>
		<VirtualMemoryLimit>80</VirtualMemoryLimit>
		<SessionMemoryLimit>50</SessionMemoryLimit>
		<MinimumAllocatedMemory>25</MinimumAllocatedMemory>
		<WaitCountIfHighMemory>10</WaitCountIfHighMemory>
		<DefaultPagesCountToReuse>2</DefaultPagesCountToReuse>
		<HandleIA64AlignmentFaults>0</HandleIA64AlignmentFaults>
		<PreAllocate>0</PreAllocate>
		<VertiPaqPagingPolicy>1</VertiPaqPagingPolicy>
		<PagePoolRestrictNumaNode>0</PagePoolRestrictNumaNode>
		<HeapTypeForObjectGroupCount>0</HeapTypeForObjectGroupCount>
		<UseCountersPerCPU>-1</UseCountersPerCPU>
	</Memory>
	<FileStore>
		<MemoryLimitMin>0</MemoryLimitMin>
		<MemoryLimit>90</MemoryLimit>
		<UnbufferedThreshold>0</UnbufferedThreshold>
		<PerformanceTrace>0</PerformanceTrace>
		<PercentScanPerPrice>0.1</PercentScanPerPrice>
		<RandomFileAccessMode>0</RandomFileAccessMode>
		<MemoryModel>
			<Tax>5</Tax>
			<Income>1</Income>
			<MaximumBalance>1000000</MaximumBalance>
			<MinimumBalance>-300000</MinimumBalance>
			<InitialBonus>2</InitialBonus>
		</MemoryModel>
	</FileStore>
	<CoordinatorExecutionMode>-4</CoordinatorExecutionMode>
	<CoordinatorShutdownMode>0</CoordinatorShutdownMode>
	<FilteredDumpMode>0</FilteredDumpMode>
	<MiniDumpMode>0</MiniDumpMode>
	<CoordinatorCancelCount>1000</CoordinatorCancelCount>
	<CoordinatorBuildMaxThreads>4</CoordinatorBuildMaxThreads>
	<CoordinatorQueryMaxThreads>16</CoordinatorQueryMaxThreads>
	<CoordinatorQueryBalancingFactor>-1</CoordinatorQueryBalancingFactor>
	<CoordinatorQueryBoostPriorityLevel>3</CoordinatorQueryBoostPriorityLevel>
	<LockManager>
		<DefaultLockTimeoutMS>-1</DefaultLockTimeoutMS>
		<LockWaitGranularityMS>5000</LockWaitGranularityMS>
		<DeadlockDetectionGranularityMS>30000</DeadlockDetectionGranularityMS>
	</LockManager>
	<ThreadPool>
		<Parsing>
			<Short>
				<NumThreads>0</NumThreads>
				<PriorityRatio>0</PriorityRatio>
				<Concurrency>2</Concurrency>
				<StackSizeKB>512</StackSizeKB>
				<GroupAffinity/>
			</Short>
			<Long>
				<NumThreads>0</NumThreads>
				<PriorityRatio>0</PriorityRatio>
				<Concurrency>2</Concurrency>
				<StackSizeKB>0</StackSizeKB>
				<GroupAffinity/>
			</Long>
		</Parsing>
		<Query>
			<MinThreads>0</MinThreads>
			<MaxThreads>0</MaxThreads>
			<PriorityRatio>2</PriorityRatio>
			<Concurrency>2</Concurrency>
			<StackSizeKB>0</StackSizeKB>
			<GroupAffinity/>
		</Query>
		<Command>
			<MinThreads>0</MinThreads>
			<MaxThreads>0</MaxThreads>
			<PriorityRatio>2</PriorityRatio>
			<Concurrency>2</Concurrency>
			<StackSizeKB>0</StackSizeKB>
			<GroupAffinity/>
		</Command>
		<Process>
			<MinThreads>0</MinThreads>
			<MaxThreads>0</MaxThreads>
			<PriorityRatio>2</PriorityRatio>
			<Concurrency>2</Concurrency>
			<StackSizeKB>0</StackSizeKB>
			<GroupAffinity/>
		</Process>
		<IOProcess>
			<MinThreads>0</MinThreads>
			<MaxThreads>0</MaxThreads>
			<PriorityRatio>2</PriorityRatio>
			<Concurrency>2</Concurrency>
			<StackSizeKB>0</StackSizeKB>
			<GroupAffinity/>
			<PerNumaNode>-1</PerNumaNode>
		</IOProcess>
	</ThreadPool>
	<OLAP>
		<Memory>
			<DefaultPageSizeForData>65536</DefaultPageSizeForData>
			<DefaultPageSizeForDataHeader>8192</DefaultPageSizeForDataHeader>
			<DefaultPageSizeForIndex>8192</DefaultPageSizeForIndex>
			<DefaultPageSizeForIndexHeader>8192</DefaultPageSizeForIndexHeader>
			<DefaultPageSizeForString>65536</DefaultPageSizeForString>
			<DefaultPageSizeForHash>8192</DefaultPageSizeForHash>
			<DefaultPageSizeForProp>8192</DefaultPageSizeForProp>
		</Memory>
		<LazyProcessing>
			<Enabled>1</Enabled>
			<SleepIntervalSecs>5</SleepIntervalSecs>
			<MaxCPUUsage>0.5</MaxCPUUsage>
			<MaxObjectsInParallel>2</MaxObjectsInParallel>
			<MaxRetries>3</MaxRetries>
		</LazyProcessing>
		<ProcessPlan>
			<MemoryLimit>65</MemoryLimit>
			<MemoryLimitErrorEnabled>1</MemoryLimitErrorEnabled>
			<MemoryAdjustConst>1000000</MemoryAdjustConst>
			<MemoryAdjustFactor>1</MemoryAdjustFactor>
			<CacheRowsetRows>65536</CacheRowsetRows>
			<CacheRowsetToDisk>1</CacheRowsetToDisk>
			<ForceMultiPass>0</ForceMultiPass>
			<DistinctBuffer>0x10000</DistinctBuffer>
			<OptimizeSchema>0x1</OptimizeSchema>
			<MaxTableDepth>0x100</MaxTableDepth>
			<EnableTableGrouping>0</EnableTableGrouping>
			<EnableRolapDimQueryTableGrouping>1</EnableRolapDimQueryTableGrouping>
			<EnableRolapDistinctCountOnDataSource>0</EnableRolapDistinctCountOnDataSource>
		</ProcessPlan>
		<ProactiveCaching>
			<DimensionLatencyAccuracy>5</DimensionLatencyAccuracy>
			<PartitionLatencyAccuracy>60</PartitionLatencyAccuracy>
			<DefaultRefreshInterval>60</DefaultRefreshInterval>
		</ProactiveCaching>
		<Process>
			<CheckDistinctRecordSortOrder>1</CheckDistinctRecordSortOrder>
			<RecordsReportGranularity>10000</RecordsReportGranularity>
			<BufferMemoryLimit>60</BufferMemoryLimit>
			<BufferRecordLimit>1048576</BufferRecordLimit>
			<CacheRecordLimit>0</CacheRecordLimit>
			<AggregationMemoryLimitMin>10</AggregationMemoryLimitMin>
			<AggregationMemoryLimitMax>80</AggregationMemoryLimitMax>
			<PropertyBufferRecordLimitEnabled>0</PropertyBufferRecordLimitEnabled>
			<AggregationNewAlgo>1</AggregationNewAlgo>
			<AggregationPerfLog2>0</AggregationPerfLog2>
			<MapFormatMask>0x10100100</MapFormatMask>
			<DeepCompressValue>1</DeepCompressValue>
			<DataPlacementOptimization>0</DataPlacementOptimization>
			<DimensionPropertyKeyCache>1</DimensionPropertyKeyCache>
			<DimensionPropertyKeyCacheLimit>0</DimensionPropertyKeyCacheLimit>
			<DimensionPropertyShortPath>1</DimensionPropertyShortPath>
			<EnableRetries>1</EnableRetries>
			<AggIndexBuildThreshold>65536</AggIndexBuildThreshold>
			<AggIndexBuildEnabled>0</AggIndexBuildEnabled>
			<IndexBuildThreshold>4096</IndexBuildThreshold>
			<IndexBuildEnabled>1</IndexBuildEnabled>
			<AggregationsBuildEnabled>1</AggregationsBuildEnabled>
			<DataSliceInitEnabled>1</DataSliceInitEnabled>
			<DataFileInitEnabled>0</DataFileInitEnabled>
			<IndexFileInitEnabled>0</IndexFileInitEnabled>
			<DatabaseConnectionPoolMax>50</DatabaseConnectionPoolMax>
			<DatabaseConnectionPoolTimeout>120000</DatabaseConnectionPoolTimeout>
			<DatabaseConnectionPoolMaxLifeTime>0</DatabaseConnectionPoolMaxLifeTime>
			<DatabaseConnectionPoolConnectTimeout>60</DatabaseConnectionPoolConnectTimeout>
			<DatabaseConnectionPoolGeneralTimeout>60</DatabaseConnectionPoolGeneralTimeout>
			<XMLAConnectionPoolTimeout>720000</XMLAConnectionPoolTimeout>
			<ROLAPDimensionProcessingEffort>300000</ROLAPDimensionProcessingEffort>
		</Process>
		<Query>
			<CalculationEvaluationPolicy>4</CalculationEvaluationPolicy>
			<CalculationAdvancedEvaluatorDisableMask>0</CalculationAdvancedEvaluatorDisableMask>
			<QueryOptimizerRatio>-1</QueryOptimizerRatio>
			<QueryOptimizerPolicy>1</QueryOptimizerPolicy>
			<SpaceDecomposition>8</SpaceDecomposition>
			<CalculationCoverPolicy>0</CalculationCoverPolicy>
			<NonEmptyBehaviorMode>1</NonEmptyBehaviorMode>
			<CalcMeasureAggPolicy>0</CalcMeasureAggPolicy>
			<CellByCellCalculationMode>0</CellByCellCalculationMode>
			<CalculationPassMode>0</CalculationPassMode>
			<SessionCubesMode>1</SessionCubesMode>
			<CalculationPrefetchLocality>
				<ApplyIntersect>1</ApplyIntersect>
				<ApplySubtract>1</ApplySubtract>
				<PrefetchLowerGranularities>1</PrefetchLowerGranularities>
			</CalculationPrefetchLocality>
			<PreciseSubtract>0</PreciseSubtract>
			<CloneCoverItemForClosestWins>0</CloneCoverItemForClosestWins>
			<LookupNormalizedSubcubeSlices>0</LookupNormalizedSubcubeSlices>
			<UseCalculationCacheRegistry>1</UseCalculationCacheRegistry>
			<EnableSubSelectCaching>1</EnableSubSelectCaching>
			<UseDataCacheRegistry>1</UseDataCacheRegistry>
			<UseDataCacheFreeLastPageMemory>1</UseDataCacheFreeLastPageMemory>
			<UseDataCacheRegistryMultiplyKey>1</UseDataCacheRegistryMultiplyKey>
			<UseDataCacheRegistryHashTable>1</UseDataCacheRegistryHashTable>
			<DataCacheRegistryMaxIterations>1000</DataCacheRegistryMaxIterations>
			<CalculationCacheRegistryMaxIterations>100</CalculationCacheRegistryMaxIterations>
			<AutoSelectDataStoreAndHashPageSize>1</AutoSelectDataStoreAndHashPageSize>
			<DataStorePageSize>8192</DataStorePageSize>
			<DataStoreHashPageSize>8192</DataStoreHashPageSize>
			<DataStoreStringPageSize>65536</DataStoreStringPageSize>
			<AllowPresizeHashTable>1</AllowPresizeHashTable>
			<DataCache>
				<MemoryModel>
					<Tax>5</Tax>
					<Income>1</Income>
					<MaximumBalance>1000000</MaximumBalance>
					<MinimumBalance>-300000</MinimumBalance>
					<InitialBonus>2</InitialBonus>
				</MemoryModel>
			</DataCache>
			<CachedPageAlloc>
				<MemoryModel>
					<Tax>5</Tax>
					<Income>1</Income>
					<MaximumBalance>100000</MaximumBalance>
					<MinimumBalance>-30000</MinimumBalance>
					<InitialBonus>2</InitialBonus>
				</MemoryModel>
			</CachedPageAlloc>
			<CellStore>
				<MemoryModel>
					<Tax>5</Tax>
					<Income>2</Income>
					<MaximumBalance>1000000</MaximumBalance>
					<MinimumBalance>-300000</MinimumBalance>
					<InitialBonus>4</InitialBonus>
				</MemoryModel>
			</CellStore>
			<CopyLinkedDataCacheAndRegistry>0</CopyLinkedDataCacheAndRegistry>
			<UseDataSlice>1</UseDataSlice>
			<IndexUseEnabled>1</IndexUseEnabled>
			<AggregationsUseEnabled>1</AggregationsUseEnabled>
			<DefaultDrillthroughMaxRows>10000</DefaultDrillthroughMaxRows>
			<MapHandleAlgorithm>3</MapHandleAlgorithm>
			<UseMaterializedIterators>1</UseMaterializedIterators>
			<MaxRolapOrConditions>150</MaxRolapOrConditions>
			<ExpressNonEmptyUseEnabled>1</ExpressNonEmptyUseEnabled>
			<UseSinglePassForDimSecurityAutoExist>1</UseSinglePassForDimSecurityAutoExist>
			<ConvertDeletedToUnknown>0</ConvertDeletedToUnknown>
			<DimensionPropertyCacheSize>4000000</DimensionPropertyCacheSize>
			<AllowSEFiltering>1</AllowSEFiltering>
			<AllowOptimizedResponse>1</AllowOptimizedResponse>
			<AllowBinaryPagedResponse>0</AllowBinaryPagedResponse>
			<AllowNonXMLIslands>1</AllowNonXMLIslands>
			<IgnoreNullRolapRows>1</IgnoreNullRolapRows>
			<UseVBANet>1</UseVBANet>
			<DisableStrongAttributeRelationships>0</DisableStrongAttributeRelationships>
			<CalculatedVisualTotalStyleForSubselects>0</CalculatedVisualTotalStyleForSubselects>
			<EnableCalculatedMemberUsageForCalculationLRU>1</EnableCalculatedMemberUsageForCalculationLRU>
			<CalculationLRUSize>20</CalculationLRUSize>
			<CalculationLRUMinSize>60</CalculationLRUMinSize>
			<CalculationLRUMaxSize>3000</CalculationLRUMaxSize>
			<DisableDCSliceIndex>0x0</DisableDCSliceIndex>
			<DCSliceIndexThreshold>0</DCSliceIndexThreshold>
			<DisablePrefetchForOutlineCalcs>0x0</DisablePrefetchForOutlineCalcs>
			<DisableFusionOfStorageEngineSubspaces>0x0</DisableFusionOfStorageEngineSubspaces>
			<DisableCalcExpressNonEmpty>0</DisableCalcExpressNonEmpty>
			<ComplexSlice>
				<Enabled>1</Enabled>
				<MaxAttributesForPartitions>5</MaxAttributesForPartitions>
				<MaxAttributesForDimensions>5</MaxAttributesForDimensions>
				<AttributeProjectionRatio>10</AttributeProjectionRatio>
				<MaxSets>5</MaxSets>
				<MaxCells>10000</MaxCells>
			</ComplexSlice>
			<AggregateHybridPlan>1</AggregateHybridPlan>
			<SpaceEvaluatorPlan>0</SpaceEvaluatorPlan>
			<DimensionCacheLookupMode>0</DimensionCacheLookupMode>
			<NamedSetShallowExistsMode>0</NamedSetShallowExistsMode>
			<AxisSetShallowExistsMode>0</AxisSetShallowExistsMode>
			<MdxSubqueries>15</MdxSubqueries>
			<FactPrefetchMode>0</FactPrefetchMode>
			<LazyEnabled>1</LazyEnabled>
			<CrossjoinSetExistsMode>0</CrossjoinSetExistsMode>
			<AutoExistMemoryLimit>65</AutoExistMemoryLimit>
			<SkipROLAPDatasourceMatching>0</SkipROLAPDatasourceMatching>
			<AllowPartialComplexSliceProjection>1</AllowPartialComplexSliceProjection>
			<VisualTotalsEnabledForUnaryOperatorsAndSubSelects>0</VisualTotalsEnabledForUnaryOperatorsAndSubSelects>
			<CellByCellCalculationModeForUnaryOperatorAndSemiAdditiveMeasure>0</CellByCellCalculationModeForUnaryOperatorAndSemiAdditiveMeasure>
			<MaxCalculationOverlapCount>5</MaxCalculationOverlapCount>
			<DAXMDIgnoreCalculatedMemberAsDefaultMember>0</DAXMDIgnoreCalculatedMemberAsDefaultMember>
		</Query>
		<Jobs>
			<ProcessProperty>
				<MemoryModel>
					<Tax>5</Tax>
					<Income>1</Income>
					<MaximumBalance>1000000</MaximumBalance>
					<MinimumBalance>-300000</MinimumBalance>
					<InitialBonus>2</InitialBonus>
				</MemoryModel>
			</ProcessProperty>
			<ProcessPartition>
				<MemoryModel>
					<Tax>5</Tax>
					<Income>100</Income>
					<MaximumBalance>1000000000</MaximumBalance>
					<MinimumBalance>-3000000</MinimumBalance>
					<InitialBonus>0.01</InitialBonus>
				</MemoryModel>
			</ProcessPartition>
			<ProcessAggregation>
				<MemoryModel>
					<Tax>5</Tax>
					<Income>50</Income>
					<MaximumBalance>1000000000</MaximumBalance>
					<MinimumBalance>-3000000</MinimumBalance>
					<InitialBonus>0.01</InitialBonus>
				</MemoryModel>
			</ProcessAggregation>
		</Jobs>
	</OLAP>
	<Feature>
		<ManagedCodeEnabled>1</ManagedCodeEnabled>
		<LinkInsideInstanceEnabled>1</LinkInsideInstanceEnabled>
		<LinkToOtherInstanceEnabled>0</LinkToOtherInstanceEnabled>
		<LinkFromOtherInstanceEnabled>0</LinkFromOtherInstanceEnabled>
		<ConnStringEncryptionEnabled>1</ConnStringEncryptionEnabled>
		<UseCachedPageAllocators>0</UseCachedPageAllocators>
		<ComUdfEnabled>0</ComUdfEnabled>
		<ResourceMonitoringEnabled>1</ResourceMonitoringEnabled>
	</Feature>
	<AllowCLRStoredProcedureCallsInFiberMode>0</AllowCLRStoredProcedureCallsInFiberMode>
	<DataMining>
		<AllowSessionMiningModels>0</AllowSessionMiningModels>
		<AllowAdHocOpenRowsetQueries>0</AllowAdHocOpenRowsetQueries>
		<AllowedProvidersInOpenRowset/>
		<MaxConcurrentPredictionQueries>0</MaxConcurrentPredictionQueries>
		<MaxTimeSeriesPredictionCacheSize>150000</MaxTimeSeriesPredictionCacheSize>
		<Algorithms>
			<Microsoft_Association_Rules>
				<Enabled>1</Enabled>
			</Microsoft_Association_Rules>
			<Microsoft_Clustering>
				<Enabled>1</Enabled>
			</Microsoft_Clustering>
			<Microsoft_Decision_Trees>
				<Enabled>1</Enabled>
			</Microsoft_Decision_Trees>
			<Microsoft_Naive_Bayes>
				<Enabled>1</Enabled>
			</Microsoft_Naive_Bayes>
			<Microsoft_Neural_Network>
				<Enabled>1</Enabled>
			</Microsoft_Neural_Network>
			<Microsoft_Sequence_Clustering>
				<Enabled>1</Enabled>
			</Microsoft_Sequence_Clustering>
			<Microsoft_Time_Series>
				<Enabled>1</Enabled>
			</Microsoft_Time_Series>
			<Microsoft_Linear_Regression>
				<Enabled>1</Enabled>
			</Microsoft_Linear_Regression>
			<Microsoft_Logistic_Regression>
				<Enabled>1</Enabled>
			</Microsoft_Logistic_Regression>
		</Algorithms>
		<Services>
			<MicrosoftAssociationRules>
				<ProgID>8E8A3C1F-DB79-4cbf-814F-C3C7EA632D7B</ProgID>
				<MinimumSupport>0</MinimumSupport>
				<MaximumSupport>1</MaximumSupport>
				<MinimumItemsetSize>0</MinimumItemsetSize>
				<MaximumItemsetSize>3</MaximumItemsetSize>
				<MaximumItemsetCount>200000</MaximumItemsetCount>
				<MinimumProbability>0.4</MinimumProbability>
				<MinimumImportance>-999999999</MinimumImportance>
				<OptimizedPredictionCount>0</OptimizedPredictionCount>
				<AutodetectMinimumSupport>0</AutodetectMinimumSupport>
			</MicrosoftAssociationRules>
			<MicrosoftClustering>
				<ProgID>FE8EF196-F59A-492d-A723-8E0C0332CF85</ProgID>
				<ClusteringMethod>1</ClusteringMethod>
				<ClusterCount>10</ClusterCount>
				<MinimumSupport>1</MinimumSupport>
				<ModellingCardinality>10</ModellingCardinality>
				<StoppingTolerance>10</StoppingTolerance>
				<SampleSize>50000</SampleSize>
				<MaximumInputAttributes>255</MaximumInputAttributes>
				<MaximumStates>100</MaximumStates>
				<MinimumClusterCases>1</MinimumClusterCases>
				<ClusterCountDeviation>10</ClusterCountDeviation>
				<ClusterCountPrior>10</ClusterCountPrior>
				<Normalization>1</Normalization>
				<CaseLikelihoodNormalizationMethod>MARGINAL</CaseLikelihoodNormalizationMethod>
			</MicrosoftClustering>
			<MicrosoftDecisionTrees>
				<ProgID>F39BEF7F-5EE9-453b-A6B8-ED1E0FBB731B</ProgID>
				<MaximumInputAttributes>255</MaximumInputAttributes>
				<MaximumOutputAttributes>255</MaximumOutputAttributes>
				<ScoreMethod>4</ScoreMethod>
				<SplitMethod>3</SplitMethod>
				<MinimumLeafCases>10</MinimumLeafCases>
				<MinimumSupport>10</MinimumSupport>
				<ComplexityPenalty>0</ComplexityPenalty>
				<MaximumBucketsForContinuousSplit>10</MaximumBucketsForContinuousSplit>
				<ForceMaximumBucketsForContinuousSplit>0</ForceMaximumBucketsForContinuousSplit>
				<MaximumContinuousInputAttributes>50</MaximumContinuousInputAttributes>
				<ForceRegressor/>
				<AcyclicGraph/>
			</MicrosoftDecisionTrees>
			<MicrosoftNaiveBayes>
				<ProgID>E884B4C5-4F70-4e28-B97C-82BBC1DB4EFB</ProgID>
				<MaximumInputAttributes>255</MaximumInputAttributes>
				<MaximumOutputAttributes>255</MaximumOutputAttributes>
				<MaximumStates>100</MaximumStates>
				<MinimumDependencyProbability>0.5</MinimumDependencyProbability>
			</MicrosoftNaiveBayes>
			<MicrosoftSequenceClustering>
				<ProgID>02F4DB41-AD83-412a-8DCA-E4760D61C087</ProgID>
				<ClusterCount>10</ClusterCount>
				<MinimumSupport>10</MinimumSupport>
				<MaximumStates>100</MaximumStates>
				<MaximumSequenceStates>64</MaximumSequenceStates>
				<HoldoutMethod>Uniform</HoldoutMethod>
				<MinimumClusterCases>10</MinimumClusterCases>
			</MicrosoftSequenceClustering>
			<MicrosoftTimeSeries>
				<ProgID>D598EB8C-D1A9-42a3-AFAD-5ECD590C6333</ProgID>
				<MinimumSupport>10</MinimumSupport>
				<ComplexityPenalty>0.1</ComplexityPenalty>
				<PeriodicityHint>{1}</PeriodicityHint>
				<HistoricModelCount>1</HistoricModelCount>
				<HistoricModelGap>10</HistoricModelGap>
				<MissingValueSubstitution>None</MissingValueSubstitution>
				<AutoDetectPeriodicity>0.6</AutoDetectPeriodicity>
				<MinimumSeriesValue>-1E+308</MinimumSeriesValue>
				<MaximumSeriesValue>1E+308</MaximumSeriesValue>
				<MinimumLeafCases>10</MinimumLeafCases>
				<PredictionSmoothing>0.5</PredictionSmoothing>
				<ArimaAROrder>-1</ArimaAROrder>
				<ArimaDifferenceOrder>-1</ArimaDifferenceOrder>
				<ArimaMAOrder>-1</ArimaMAOrder>
				<InstabilityCutoffWeight>1</InstabilityCutoffWeight>
				<ForecastMethod>MIXED</ForecastMethod>
			</MicrosoftTimeSeries>
			<MicrosoftNeuralNetwork>
				<ProgID>0769A20B-0F45-4b55-93E0-0EA08B1E49A7</ProgID>
				<HoldoutPercentage>30</HoldoutPercentage>
				<HoldoutSeed>0</HoldoutSeed>
				<MaximumInputAttributes>255</MaximumInputAttributes>
				<MaximumOutputAttributes>255</MaximumOutputAttributes>
				<MaximumStates>100</MaximumStates>
				<SampleSize>10000</SampleSize>
				<HiddenNodeRatio>4</HiddenNodeRatio>
				<HoldoutTolerance>0.001</HoldoutTolerance>
				<BrentTolerance>0.1</BrentTolerance>
			</MicrosoftNeuralNetwork>
			<MicrosoftLinearRegression>
				<ProgID>4E954761-41EC-4fb4-858F-20E09F3149CF</ProgID>
				<MaximumInputAttributes>255</MaximumInputAttributes>
				<MaximumOutputAttributes>255</MaximumOutputAttributes>
				<ForceRegressor/>
			</MicrosoftLinearRegression>
			<MicrosoftLogisticRegression>
				<ProgID>2ACC1917-A984-403f-B710-8981F6BF125B</ProgID>
				<HoldoutPercentage>30</HoldoutPercentage>
				<HoldoutSeed>0</HoldoutSeed>
				<MaximumInputAttributes>255</MaximumInputAttributes>
				<MaximumOutputAttributes>255</MaximumOutputAttributes>
				<MaximumStates>100</MaximumStates>
				<SampleSize>10000</SampleSize>
				<HoldoutTolerance>0.001</HoldoutTolerance>
				<BrentTolerance>0.1</BrentTolerance>
			</MicrosoftLogisticRegression>
		</Services>
	</DataMining>
	<DSO>
		<RemoteRepositoryConnectionString/>
		<RepositoryConnectionString/>
		<RemoteLocksDirectory/>
		<LocksDirectory/>
	</DSO>
	<LimitSystemFileCacheSizeMB>0</LimitSystemFileCacheSizeMB>
	<LimitSystemFileCachePeriod>1000</LimitSystemFileCachePeriod>
	<VertiPaq>
		<ThreadPool>
			<CPUs>0</CPUs>
			<GroupAffinity/>
			<WorkStealing>1</WorkStealing>
		</ThreadPool>
		<DefaultSegmentRowCount>0</DefaultSegmentRowCount>
		<ProcessingTimeboxSecPerMRow>-1</ProcessingTimeboxSecPerMRow>
		<NumaSegmentLoadPolicy>-1</NumaSegmentLoadPolicy>
		<ScaleOutEnabled>0</ScaleOutEnabled>
		<CheckDimensionFilelistRequested>0</CheckDimensionFilelistRequested>
		<ScaleOutPlanType>0</ScaleOutPlanType>
		<QueryRetry>1</QueryRetry>
		<MaxDatabaseSizeOnDiskMB>1024</MaxDatabaseSizeOnDiskMB>
		<SEQueryRegistry>
			<Size>512</Size>
			<MinKCycles>0</MinKCycles>
			<MinCyclesPerRow>0</MinCyclesPerRow>
			<MaxArbShpSize>16384</MaxArbShpSize>
		</SEQueryRegistry>
		<ImageLoadStreamBufferMB>1024</ImageLoadStreamBufferMB>
		<LoadIMBIDataParallel>1</LoadIMBIDataParallel>
		<LoadIMBIDataParallelForTM>3</LoadIMBIDataParallelForTM>
		<SaveIMBIDataParallel>0</SaveIMBIDataParallel>
		<SkipDataFileInDQMode>0</SkipDataFileInDQMode>
		<SkipVertipaqInDQMode>-1</SkipVertipaqInDQMode>
		<EnableDisklessTMImageSave>1</EnableDisklessTMImageSave>
		<EnableDisklessUDMImageSave>1</EnableDisklessUDMImageSave>
		<EnableProcessingSimplifiedLocks>0</EnableProcessingSimplifiedLocks>
		<AutoImageSaveForScopedConnCommands>0</AutoImageSaveForScopedConnCommands>
		<Log>
			<ErrorLog>
				<LogErrorTypes>
					<KeyNotFound>0</KeyNotFound>
					<KeyDuplicate>2</KeyDuplicate>
					<NullKeyNotAllowed>2</NullKeyNotAllowed>
				</LogErrorTypes>
			</ErrorLog>
		</Log>
	</VertiPaq>
	<Tabular>
		<AllowAmbiguousPath>0</AllowAmbiguousPath>
		<ValidateImageLOGonUpload>1</ValidateImageLOGonUpload>
		<ValidateTMMetadataOnReload>1</ValidateTMMetadataOnReload>
	</Tabular>
	<DAX>
		<PredicateCheckSpoolCardinalityThreshold>5000</PredicateCheckSpoolCardinalityThreshold>
		<QueryPlanMaxOperators>10000</QueryPlanMaxOperators>
		<SecurityFilteringBehavior>1</SecurityFilteringBehavior>
		<RootTablePredicateTablePushSliceThreshold>0.2</RootTablePredicateTablePushSliceThreshold>
		<PredicateTableRowCountThreshold>800</PredicateTableRowCountThreshold>
		<EnableVariationNotation>1</EnableVariationNotation>
		<DQ>
			<EnableDQCalcCol>1</EnableDQCalcCol>
		</DQ>
	</DAX>
	<ASPaaS>
		<ExceptionDumpErrorList/>
		<EmitDatabaseUsageStats>0</EmitDatabaseUsageStats>
	</ASPaaS>
</ConfigurationSettings>