﻿@model IEnumerable<AppTech.MSMS.Domain.Models.CardFaction>
@{
    Layout = "~/Views/Shared/_TableLayout.cshtml";
}

<p>
    <a class="blue" href="" onclick="openModal('@ViewBag.CardTypeID')">
        <i class="ace-icon fa fa-plus bigger-150"></i>
        <span>إضافة فئة جديدة</span>
    </a>
</p>
<div id="list">
    @Html.Partial("_cFactions")
</div>
    @Html.Partial("_Modal")

    <script>
        function openModal(id) {
            i('open modal id' + id);
            openViewAsModal('Cards/CardFaction/AddOrEditFaction?ID=' + id, " جديد");
        }
    </script>
