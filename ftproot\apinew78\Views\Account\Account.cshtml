﻿@model AppTech.MSMS.Web.Models.AccountModel

@{
    ViewBag.Title = "الملف الشخصي";
}
@functions{

    public string GetAntiForgeryToken()
    {
        string cookieToken, formToken;
        AntiForgery.GetTokens(null, out cookieToken, out formToken);
        return cookieToken + ":" + formToken;
    }

}
<style type="text/css">
    label { display: block; }

    .invalid, .error {
        color: red;
        margin-top: 10px;
    }
</style>
<div class="row row-first box-noborder">

    <div class="span6">
        <div style="padding: 20px;">
            <fieldset>
                <legend> البيانات</legend>


                <div class="form-group">
                    @Html.LabelFor(model => model.FirstName, new {@class = "control-label col-md-2"})
                    <div class="col-md-10">
                        @Html.DisplayFor(model => model.FirstName)
                    </div>
                </div>

            </fieldset>

            <br/>
        </div>
    </div>


    <a href="/#!/account/changepassword" class="btn btn-link"> تغيير كلمة المرور</a>

</div>