# سكريبت البحث عن ملفات Laravel و XAMPP

Write-Host "البحث عن ملفات Laravel و XAMPP..." -ForegroundColor Green

# البحث عن مجلدات XAMPP
Write-Host "`n1. البحث عن مجلدات XAMPP..." -ForegroundColor Yellow
$drives = @("C:", "D:", "E:", "F:")
$xamppFound = $false

foreach ($drive in $drives) {
    if (Test-Path $drive) {
        Write-Host "البحث في القرص: $drive" -ForegroundColor Gray
        
        # البحث عن مجلد xampp
        $xamppPaths = @(
            "$drive\xampp",
            "$drive\XAMPP", 
            "$drive\Program Files\xampp",
            "$drive\Program Files (x86)\xampp"
        )
        
        foreach ($path in $xamppPaths) {
            if (Test-Path $path) {
                Write-Host "✓ وُجد XAMPP في: $path" -ForegroundColor Green
                $xamppFound = $true
                
                # فحص محتويات XAMPP
                $htdocsPath = "$path\htdocs"
                if (Test-Path $htdocsPath) {
                    Write-Host "  ✓ مجلد htdocs موجود: $htdocsPath" -ForegroundColor Green
                    
                    # البحث عن مجلد laravel
                    $laravelPath = "$htdocsPath\laravel"
                    if (Test-Path $laravelPath) {
                        Write-Host "  ✓ مجلد Laravel موجود: $laravelPath" -ForegroundColor Green
                        
                        # فحص ملفات Laravel
                        $laravelFiles = @(
                            "$laravelPath\artisan",
                            "$laravelPath\composer.json",
                            "$laravelPath\app",
                            "$laravelPath\config",
                            "$laravelPath\routes"
                        )
                        
                        foreach ($file in $laravelFiles) {
                            if (Test-Path $file) {
                                Write-Host "    ✓ $file موجود" -ForegroundColor Gray
                            } else {
                                Write-Host "    ✗ $file مفقود" -ForegroundColor Red
                            }
                        }
                    } else {
                        Write-Host "  ✗ مجلد Laravel غير موجود في htdocs" -ForegroundColor Red
                    }
                    
                    # عرض محتويات htdocs
                    Write-Host "  محتويات htdocs:" -ForegroundColor Gray
                    try {
                        Get-ChildItem $htdocsPath -Directory | Select-Object -First 10 | ForEach-Object {
                            Write-Host "    - $($_.Name)" -ForegroundColor Gray
                        }
                    } catch {
                        Write-Host "    خطأ في قراءة محتويات htdocs" -ForegroundColor Red
                    }
                } else {
                    Write-Host "  ✗ مجلد htdocs غير موجود" -ForegroundColor Red
                }
            }
        }
    }
}

if (-not $xamppFound) {
    Write-Host "✗ لم يتم العثور على XAMPP في النظام" -ForegroundColor Red
}

# البحث عن ملفات Laravel في مواقع أخرى
Write-Host "`n2. البحث عن ملفات Laravel في مواقع أخرى..." -ForegroundColor Yellow

$searchPaths = @(
    "C:\laravel",
    "D:\laravel", 
    "E:\laravel",
    "C:\www",
    "D:\www",
    "E:\www",
    "C:\webroot",
    "D:\webroot",
    "E:\webroot"
)

$laravelFound = $false
foreach ($path in $searchPaths) {
    if (Test-Path $path) {
        Write-Host "✓ وُجد مجلد محتمل: $path" -ForegroundColor Green
        $laravelFound = $true
        
        # فحص إذا كان مشروع Laravel
        $artisanFile = "$path\artisan"
        $composerFile = "$path\composer.json"
        
        if ((Test-Path $artisanFile) -and (Test-Path $composerFile)) {
            Write-Host "  ✓ هذا مشروع Laravel صالح" -ForegroundColor Green
            
            # قراءة معلومات من composer.json
            try {
                $composer = Get-Content $composerFile | ConvertFrom-Json
                if ($composer.require."laravel/framework") {
                    Write-Host "  ✓ إصدار Laravel: $($composer.require."laravel/framework")" -ForegroundColor Green
                }
            } catch {
                Write-Host "  ⚠ خطأ في قراءة composer.json" -ForegroundColor Yellow
            }
        } else {
            Write-Host "  ✗ ليس مشروع Laravel صالح" -ForegroundColor Red
        }
    }
}

if (-not $laravelFound) {
    Write-Host "✗ لم يتم العثور على مشاريع Laravel" -ForegroundColor Red
}

# البحث عن ملفات PHP عامة
Write-Host "`n3. البحث عن ملفات PHP..." -ForegroundColor Yellow

try {
    $phpFiles = Get-ChildItem -Path "E:\" -Recurse -Include "*.php" -ErrorAction SilentlyContinue | Select-Object -First 10
    if ($phpFiles) {
        Write-Host "✓ وُجدت ملفات PHP:" -ForegroundColor Green
        foreach ($file in $phpFiles) {
            Write-Host "  - $($file.FullName)" -ForegroundColor Gray
        }
    } else {
        Write-Host "✗ لم يتم العثور على ملفات PHP" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ خطأ في البحث عن ملفات PHP" -ForegroundColor Red
}

# فحص خدمات الويب المُثبتة
Write-Host "`n4. فحص خدمات الويب المُثبتة..." -ForegroundColor Yellow

# فحص Apache
try {
    $apacheService = Get-Service -Name "Apache*" -ErrorAction SilentlyContinue
    if ($apacheService) {
        Write-Host "✓ خدمة Apache موجودة: $($apacheService.Name) - $($apacheService.Status)" -ForegroundColor Green
    } else {
        Write-Host "✗ خدمة Apache غير موجودة" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ خطأ في فحص خدمة Apache" -ForegroundColor Red
}

# فحص MySQL
try {
    $mysqlService = Get-Service -Name "MySQL*" -ErrorAction SilentlyContinue
    if ($mysqlService) {
        Write-Host "✓ خدمة MySQL موجودة: $($mysqlService.Name) - $($mysqlService.Status)" -ForegroundColor Green
    } else {
        Write-Host "✗ خدمة MySQL غير موجودة" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ خطأ في فحص خدمة MySQL" -ForegroundColor Red
}

Write-Host "`nانتهى البحث" -ForegroundColor Green
