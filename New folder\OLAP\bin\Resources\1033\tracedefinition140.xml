<TRACEDEFINITION>
  <!--Please clear local AS profiler trace definition cache 
         (<Microsoft SQL Server installation path>\<version>\Tools\Profiler\TraceDefinitions\Microsoft Analysis Services TraceDefinition <latest version number>.xml) 
         to reflect the latest update
      The following files also need corresponding update:
         pfshtrace.h, pftrace.cpp, FlightRecorderTraceDef_Template.xml, FlightRecorderTraceDefWithEngineActivity_Template.xml
         <enlistment>\Sql\mpu\ssms\packages\Profiler\trc_clnt\utest\Xml\Microsoft SQL Server TraceDefinition <version>.0.0.xml-->
  <!--Please follow the instructions here when updating tracedefinition.xml:
  http://sharepoint/sites/sqlserver/sqlwiki/Instructions%20to%20Re-generate%20the%20event%20definitions%20info%20for%20AS%20and%20DB%20Engine.aspx-->

  <_locDefinition>
    <!-- To Do: uncomment following line to set elements as none-localizable by default -->
    <_locDefault _loc="locNone"/>
  </_locDefinition>

  <TRACEPROVIDER>
    <NAME>Microsoft Analysis Services</NAME>
    <VERSION>
      <MAJOR>14</MAJOR>
      <MINOR>0</MINOR>
      <BUILDNUMBER>0</BUILDNUMBER>
    </VERSION>
    <TYPE>RTM</TYPE>
    <DESCRIPTION>Normal trace definition</DESCRIPTION>
  </TRACEPROVIDER>

  <EVENTCATEGORYLIST>
    <EVENTCATEGORY>
      <NAME>Security Audit</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of database audit event classes.</DESCRIPTION>
      <EVENTLIST>
        <EVENT>
          <ID>1</ID>
          <NAME>Audit Login</NAME>
          <DESCRIPTION>Collects all new connection events since the trace was started, such as when a client requests a connection to a server running an instance of SQL Server.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>2</ID>
          <NAME>Audit Logout</NAME>
          <DESCRIPTION>Collects all new disconnect events since the trace was started, such as when a client issues a disconnect command.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>4</ID>
          <NAME>Audit Server Starts And Stops</NAME>
          <DESCRIPTION>Records service shut down, start, and pause activities.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Instance Shutdown</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Instance Started</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Instance Paused</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>Instance Continued</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>18</ID>
          <NAME>Audit Object Permission Event</NAME>
          <DESCRIPTION>Records object permission changes.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>19</ID>
          <NAME>Audit Admin Operations Event</NAME>
          <DESCRIPTION>Records server backup/restore/synchronize/attach/detach/imageload/imagesave.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>ImageLoad</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>ImageSave</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Progress Reports</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for progress reporting.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>5</ID>
          <NAME>Progress Report Begin</NAME>
          <DESCRIPTION>Progress report begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Process</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Merge</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Delete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DeleteOldAggregations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Rebuild</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>Commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>Rollback</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>CreateIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>CreateTable</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>InsertInto</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Transaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Initialize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>Discretize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>CreateView</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>WriteData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>ReadData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>GroupData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>GroupDataRecord</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>BuildIndex</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Aggregate</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>BuildDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>WriteDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>BuildDMDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>ExecuteSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>ExecuteModifiedSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>Connecting</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>BuildAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>MergeAggsOnDisk</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>BuildIndexForRigidAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>BuildIndexForFlexibleAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>WriteAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>WriteSegment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>DataMiningProgress</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>ReadBufferFullReport</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>ProactiveCacheConversion</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>Build Processing Schedule</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>41</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>42</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>43</ID>
                  <NAME>Analyze\Encode Data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>44</ID>
                  <NAME>Compress Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>45</ID>
                  <NAME>Write Table Column</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>46</ID>
                  <NAME>Relationship Build Prepare</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>47</ID>
                  <NAME>Build Relationship Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>48</ID>
                  <NAME>Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>49</ID>
                  <NAME>Metadata Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>Data Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>51</ID>
                  <NAME>Post Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>52</ID>
                  <NAME>Metadata traversal during Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>53</ID>
                  <NAME>VertiPaq</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>54</ID>
                  <NAME>Hierarchy processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>55</ID>
                  <NAME>Switching dictionary</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>57</ID>
                  <NAME>Tabular transaction commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>58</ID>
                  <NAME>Sequence point</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>59</ID>
                  <NAME>Tabular object processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>60</ID>
                  <NAME>Saving database</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>61</ID>
                  <NAME>Tokenization store processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>63</ID>
                  <NAME>Check segment indexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>64</ID>
                  <NAME>Check tabular data structure</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>65</ID>
                  <NAME>Check column data for duplicates or null values</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>6</ID>
          <NAME>Progress Report End</NAME>
          <DESCRIPTION>Progress report end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Process</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Merge</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Delete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DeleteOldAggregations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Rebuild</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>Commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>Rollback</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>CreateIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>CreateTable</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>InsertInto</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Transaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Initialize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>Discretize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>CreateView</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>WriteData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>ReadData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>GroupData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>GroupDataRecord</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>BuildIndex</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Aggregate</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>BuildDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>WriteDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>BuildDMDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>ExecuteSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>ExecuteModifiedSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>Connecting</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>BuildAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>MergeAggsOnDisk</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>BuildIndexForRigidAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>BuildIndexForFlexibleAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>WriteAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>WriteSegment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>DataMiningProgress</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>ReadBufferFullReport</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>ProactiveCacheConversion</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>Build Processing Schedule</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>41</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>42</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>43</ID>
                  <NAME>Analyze\Encode Data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>44</ID>
                  <NAME>Compress Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>45</ID>
                  <NAME>Write Table Column</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>46</ID>
                  <NAME>Relationship Build Prepare</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>47</ID>
                  <NAME>Build Relationship Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>48</ID>
                  <NAME>Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>49</ID>
                  <NAME>Metadata Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>Data Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>51</ID>
                  <NAME>Post Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>52</ID>
                  <NAME>Metadata traversal during Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>53</ID>
                  <NAME>VertiPaq</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>54</ID>
                  <NAME>Hierarchy processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>55</ID>
                  <NAME>Switching dictionary</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>57</ID>
                  <NAME>Tabular transaction commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>58</ID>
                  <NAME>Sequence point</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>59</ID>
                  <NAME>Tabular object processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>60</ID>
                  <NAME>Saving database</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>61</ID>
                  <NAME>Tokenization store processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>63</ID>
                  <NAME>Check segment indexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>64</ID>
                  <NAME>Check tabular data structure</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>65</ID>
                  <NAME>Check column data for duplicates or null values</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>7</ID>
          <NAME>Progress Report Current</NAME>
          <DESCRIPTION>Progress report current.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Process</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Merge</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Delete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DeleteOldAggregations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Rebuild</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>Commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>Rollback</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>CreateIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>CreateTable</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>InsertInto</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Transaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Initialize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>Discretize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>CreateView</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>WriteData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>ReadData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>GroupData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>GroupDataRecord</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>BuildIndex</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Aggregate</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>BuildDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>WriteDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>BuildDMDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>ExecuteSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>ExecuteModifiedSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>Connecting</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>BuildAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>MergeAggsOnDisk</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>BuildIndexForRigidAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>BuildIndexForFlexibleAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>WriteAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>WriteSegment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>DataMiningProgress</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>ReadBufferFullReport</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>ProactiveCacheConversion</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>Build Processing Schedule</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>41</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>42</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>43</ID>
                  <NAME>Analyze\Encode Data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>44</ID>
                  <NAME>Compress Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>45</ID>
                  <NAME>Write Table Column</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>46</ID>
                  <NAME>Relationship Build Prepare</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>47</ID>
                  <NAME>Build Relationship Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>48</ID>
                  <NAME>Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>49</ID>
                  <NAME>Metadata Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>Data Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>51</ID>
                  <NAME>Post Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>52</ID>
                  <NAME>Metadata traversal during Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>53</ID>
                  <NAME>VertiPaq</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>54</ID>
                  <NAME>Hierarchy processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>55</ID>
                  <NAME>Switching dictionary</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>57</ID>
                  <NAME>Tabular transaction commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>58</ID>
                  <NAME>Sequence point</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>59</ID>
                  <NAME>Tabular object processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>60</ID>
                  <NAME>Saving database</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>61</ID>
                  <NAME>Tokenization store processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>63</ID>
                  <NAME>Check segment indexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>64</ID>
                  <NAME>Check tabular data structure</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>65</ID>
                  <NAME>Check column data for duplicates or null values</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>8</ID>
          <NAME>Progress Report Error</NAME>
          <DESCRIPTION>Progress report error.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Process</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Merge</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Delete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DeleteOldAggregations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Rebuild</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>Commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>Rollback</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>CreateIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>CreateTable</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>InsertInto</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Transaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Initialize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>Discretize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>CreateView</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>WriteData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>ReadData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>GroupData</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>GroupDataRecord</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>BuildIndex</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Aggregate</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>BuildDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>WriteDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>BuildDMDecode</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>ExecuteSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>ExecuteModifiedSQL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>Connecting</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>BuildAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>MergeAggsOnDisk</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>BuildIndexForRigidAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>BuildIndexForFlexibleAggs</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>WriteAggsAndIndexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>WriteSegment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>DataMiningProgress</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>ReadBufferFullReport</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>ProactiveCacheConversion</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>Build Processing Schedule</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>41</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>42</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>43</ID>
                  <NAME>Analyze\Encode Data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>44</ID>
                  <NAME>Compress Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>45</ID>
                  <NAME>Write Table Column</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>46</ID>
                  <NAME>Relationship Build Prepare</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>47</ID>
                  <NAME>Build Relationship Segment</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>48</ID>
                  <NAME>Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>49</ID>
                  <NAME>Metadata Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>Data Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>51</ID>
                  <NAME>Post Load</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>52</ID>
                  <NAME>Metadata traversal during Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>53</ID>
                  <NAME>VertiPaq</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>54</ID>
                  <NAME>Hierarchy processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>55</ID>
                  <NAME>Switching dictionary</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>57</ID>
                  <NAME>Tabular transaction commit</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>58</ID>
                  <NAME>Sequence point</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>59</ID>
                  <NAME>Tabular object processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>60</ID>
                  <NAME>Saving database</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>61</ID>
                  <NAME>Tokenization store processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>63</ID>
                  <NAME>Check segment indexes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>64</ID>
                  <NAME>Check tabular data structure</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>65</ID>
                  <NAME>Check column data for duplicates or null values</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Queries Events</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for queries.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>9</ID>
          <NAME>Query Begin</NAME>
          <DESCRIPTION>Query begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>MDXQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DMXQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>SQLQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DAXQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>JSON</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>44</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>10</ID>
          <NAME>Query End</NAME>
          <DESCRIPTION>Query end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>MDXQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DMXQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>SQLQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DAXQuery</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>JSON</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>



    <EVENTCATEGORY>
      <NAME>Command Events</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for commands.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>15</ID>
          <NAME>Command Begin</NAME>
          <DESCRIPTION>Command begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>Create</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Alter</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Delete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Process</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DesignAggregations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>WBInsert</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>WBUpdate</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>WBDelete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>MergePartitions</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Subscribe</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Batch</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>BeginTransaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>CommitTransaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>RollbackTransaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>GetTransactionState</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>Cancel</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>Import80MiningModels</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>SetAuthContext</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>ImageLoad</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>ImageSave</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>CloneDatabase</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>CreateTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>AlterTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>DeleteTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>ProcessTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>Interpret</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>ExtAuth</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>DBCC</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>RenameTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>SequencePointTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>UpgradeTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>MergePartitionsTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>DisableDatabase</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>Tabular JSON Command</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>Evict</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>CommitImport</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10000</ID>
                  <NAME>Other</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>44</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>16</ID>
          <NAME>Command End</NAME>
          <DESCRIPTION>Command end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>Create</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Alter</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Delete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Process</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DesignAggregations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>WBInsert</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>WBUpdate</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>WBDelete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>Backup</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>Restore</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>MergePartitions</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Subscribe</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Batch</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>BeginTransaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>CommitTransaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>RollbackTransaction</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>GetTransactionState</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>Cancel</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>Synchronize</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>Import80MiningModels</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>Attach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Detach</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>SetAuthContext</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>ImageLoad</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>ImageSave</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>CloneDatabase</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>CreateTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>AlterTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>DeleteTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>ProcessTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>Interpret</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>ExtAuth</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>DBCC</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>RenameTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>SequencePointTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>UpgradeTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>MergePartitionsTabular</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>DisableDatabase</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>Tabular JSON Command</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>Evict</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>CommitImport</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10000</ID>
                  <NAME>Other</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>


      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Errors and Warnings</NAME>
      <TYPE>2</TYPE>
      <DESCRIPTION>Collection of events for server errors.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>17</ID>
          <NAME>Error</NAME>
          <DESCRIPTION>Server error.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>48</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>


    <EVENTCATEGORY>
      <NAME>Discover Server State Events</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for server state discovers.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>33</ID>
          <NAME>Server State Discover Begin</NAME>
          <DESCRIPTION>Start of Server State Discover.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DISCOVER_CONNECTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>DISCOVER_SESSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DISCOVER_TRANSACTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>DISCOVER_DB_CONNECTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>DISCOVER_JOBS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>DISCOVER_LOCKS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>DISCOVER_PERFORMANCE_COUNTERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>DISCOVER_MEMORYUSAGE</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>DISCOVER_JOB_PROGRESS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>DISCOVER_MEMORYGRANT</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>34</ID>
          <NAME>Server State Discover Data</NAME>
          <DESCRIPTION>Contents of the Server State Discover Response.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DISCOVER_CONNECTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>DISCOVER_SESSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DISCOVER_TRANSACTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>DISCOVER_DB_CONNECTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>DISCOVER_JOBS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>DISCOVER_LOCKS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>DISCOVER_PERFORMANCE_COUNTERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>DISCOVER_MEMORYUSAGE</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>DISCOVER_JOB_PROGRESS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>DISCOVER_MEMORYGRANT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>DISCOVER_COMMANDS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>DISCOVER_COMMAND_OBJECTS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>DISCOVER_OBJECT_ACTIVITY</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>DISCOVER_OBJECT_MEMORY_USAGE</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>35</ID>
          <NAME>Server State Discover End</NAME>
          <DESCRIPTION>End of Server State Discover.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DISCOVER_CONNECTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>DISCOVER_SESSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DISCOVER_TRANSACTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>DISCOVER_DB_CONNECTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>DISCOVER_JOBS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>DISCOVER_LOCKS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>DISCOVER_PERFORMANCE_COUNTERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>DISCOVER_MEMORYUSAGE</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>DISCOVER_JOB_PROGRESS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>DISCOVER_MEMORYGRANT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>DISCOVER_COMMANDS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>DISCOVER_COMMAND_OBJECTS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>DISCOVER_OBJECT_ACTIVITY</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>DISCOVER_OBJECT_MEMORY_USAGE</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Discover Events</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for discover requests.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>36</ID>
          <NAME>Discover Begin</NAME>
          <DESCRIPTION>Start of Discover Request.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>DBSCHEMA_CATALOGS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DBSCHEMA_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>DBSCHEMA_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DBSCHEMA_PROVIDER_TYPES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>MDSCHEMA_CUBES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>MDSCHEMA_DIMENSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>MDSCHEMA_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>MDSCHEMA_LEVELS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>MDSCHEMA_MEASURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>MDSCHEMA_PROPERTIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>MDSCHEMA_MEMBERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>MDSCHEMA_FUNCTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>MDSCHEMA_ACTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>MDSCHEMA_SETS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>DISCOVER_INSTANCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>MDSCHEMA_KPIS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>MDSCHEMA_MEASUREGROUPS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>MDSCHEMA_COMMANDS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>DMSCHEMA_MINING_SERVICES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>DMSCHEMA_MINING_SERVICE_PARAMETERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>DMSCHEMA_MINING_FUNCTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>DMSCHEMA_MINING_MODEL_CONTENT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>DMSCHEMA_MINING_MODEL_XML</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>DMSCHEMA_MINING_MODELS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>DMSCHEMA_MINING_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>DISCOVER_DATASOURCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>DISCOVER_PROPERTIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>DISCOVER_SCHEMA_ROWSETS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>DISCOVER_ENUMERATORS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>DISCOVER_KEYWORDS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>DISCOVER_LITERALS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>DISCOVER_XML_METADATA</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>DISCOVER_TRACES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>DISCOVER_TRACE_DEFINITION_PROVIDERINFO</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>DISCOVER_TRACE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>DISCOVER_TRACE_EVENT_CATEGORIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>DMSCHEMA_MINING_STRUCTURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>DMSCHEMA_MINING_STRUCTURE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>DISCOVER_MASTER_KEY</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>MDSCHEMA_INPUT_DATASOURCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>DISCOVER_LOCATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>41</ID>
                  <NAME>DISCOVER_PARTITION_DIMENSION_STAT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>42</ID>
                  <NAME>DISCOVER_PARTITION_STAT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>43</ID>
                  <NAME>DISCOVER_DIMENSION_STAT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>44</ID>
                  <NAME>MDSCHEMA_MEASUREGROUP_DIMENSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>45</ID>
                  <NAME>DISCOVER_XEVENT_PACKAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>46</ID>
                  <NAME>DISCOVER_XEVENT_OBJECTS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>47</ID>
                  <NAME>DISCOVER_XEVENT_OBJECT_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>48</ID>
                  <NAME>DISCOVER_XEVENT_SESSION_TARGETS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>49</ID>
                  <NAME>DISCOVER_XEVENT_SESSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>DISCOVER_STORAGE_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>51</ID>
                  <NAME>DISCOVER_STORAGE_TABLE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>52</ID>
                  <NAME>DISCOVER_STORAGE_TABLE_COLUMN_SEGMENTS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>53</ID>
                  <NAME>DISCOVER_CALC_DEPENDENCY</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>54</ID>
                  <NAME>DISCOVER_CSDL_METADATA</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>55</ID>
                  <NAME>DISCOVER_RESOURCE_POOLS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>56</ID>
                  <NAME>TMSCHEMA_MODEL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>57</ID>
                  <NAME>TMSCHEMA_DATA_SOURCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>58</ID>
                  <NAME>TMSCHEMA_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>59</ID>
                  <NAME>TMSCHEMA_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>60</ID>
                  <NAME>TMSCHEMA_ATTRIBUTE_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>61</ID>
                  <NAME>TMSCHEMA_PARTITIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>62</ID>
                  <NAME>TMSCHEMA_RELATIONSHIPS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>63</ID>
                  <NAME>TMSCHEMA_MEASURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>64</ID>
                  <NAME>TMSCHEMA_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>65</ID>
                  <NAME>TMSCHEMA_LEVELS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>67</ID>
                  <NAME>TMSCHEMA_TABLE_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>68</ID>
                  <NAME>TMSCHEMA_COLUMN_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>69</ID>
                  <NAME>TMSCHEMA_PARTITION_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>70</ID>
                  <NAME>TMSCHEMA_SEGMENT_MAP_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>71</ID>
                  <NAME>TMSCHEMA_DICTIONARY_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>72</ID>
                  <NAME>TMSCHEMA_COLUMN_PARTITION_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>73</ID>
                  <NAME>TMSCHEMA_RELATIONSHIP_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>74</ID>
                  <NAME>TMSCHEMA_RELATIONSHIP_INDEX_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>75</ID>
                  <NAME>TMSCHEMA_ATTRIBUTE_HIERARCHY_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>76</ID>
                  <NAME>TMSCHEMA_HIERARCHY_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>77</ID>
                  <NAME>DISCOVER_RING_BUFFERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>78</ID>
                  <NAME>TMSCHEMA_KPIS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>79</ID>
                  <NAME>TMSCHEMA_STORAGE_FOLDERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>80</ID>
                  <NAME>TMSCHEMA_STORAGE_FILES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>81</ID>
                  <NAME>TMSCHEMA_SEGMENT_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>82</ID>
                  <NAME>TMSCHEMA_CULTURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>83</ID>
                  <NAME>TMSCHEMA_OBJECT_TRANSLATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>84</ID>
                  <NAME>TMSCHEMA_LINGUISTIC_METADATA</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>85</ID>
                  <NAME>TMSCHEMA_ANNOTATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>86</ID>
                  <NAME>TMSCHEMA_PERSPECTIVES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>87</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>88</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>89</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>90</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_MEASURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>91</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_ROLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>92</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_ROLE_MEMBERSHIPS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>93</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_TABLE_PERMISSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>94</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_VARIATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>38</ID>
          <NAME>Discover End</NAME>
          <DESCRIPTION>End of Discover Request.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>DBSCHEMA_CATALOGS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DBSCHEMA_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>DBSCHEMA_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DBSCHEMA_PROVIDER_TYPES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>MDSCHEMA_CUBES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>MDSCHEMA_DIMENSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>MDSCHEMA_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>MDSCHEMA_LEVELS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>MDSCHEMA_MEASURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>MDSCHEMA_PROPERTIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>MDSCHEMA_MEMBERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>MDSCHEMA_FUNCTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>MDSCHEMA_ACTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>MDSCHEMA_SETS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>DISCOVER_INSTANCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>MDSCHEMA_KPIS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>MDSCHEMA_MEASUREGROUPS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>MDSCHEMA_COMMANDS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>DMSCHEMA_MINING_SERVICES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>DMSCHEMA_MINING_SERVICE_PARAMETERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>DMSCHEMA_MINING_FUNCTIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>DMSCHEMA_MINING_MODEL_CONTENT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>DMSCHEMA_MINING_MODEL_XML</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>DMSCHEMA_MINING_MODELS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>DMSCHEMA_MINING_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>DISCOVER_DATASOURCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>26</ID>
                  <NAME>DISCOVER_PROPERTIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>27</ID>
                  <NAME>DISCOVER_SCHEMA_ROWSETS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>28</ID>
                  <NAME>DISCOVER_ENUMERATORS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>29</ID>
                  <NAME>DISCOVER_KEYWORDS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>DISCOVER_LITERALS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>31</ID>
                  <NAME>DISCOVER_XML_METADATA</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>32</ID>
                  <NAME>DISCOVER_TRACES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>33</ID>
                  <NAME>DISCOVER_TRACE_DEFINITION_PROVIDERINFO</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>34</ID>
                  <NAME>DISCOVER_TRACE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>35</ID>
                  <NAME>DISCOVER_TRACE_EVENT_CATEGORIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>36</ID>
                  <NAME>DMSCHEMA_MINING_STRUCTURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>37</ID>
                  <NAME>DMSCHEMA_MINING_STRUCTURE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>38</ID>
                  <NAME>DISCOVER_MASTER_KEY</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>39</ID>
                  <NAME>MDSCHEMA_INPUT_DATASOURCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>DISCOVER_LOCATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>41</ID>
                  <NAME>DISCOVER_PARTITION_DIMENSION_STAT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>42</ID>
                  <NAME>DISCOVER_PARTITION_STAT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>43</ID>
                  <NAME>DISCOVER_DIMENSION_STAT</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>44</ID>
                  <NAME>MDSCHEMA_MEASUREGROUP_DIMENSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>45</ID>
                  <NAME>DISCOVER_XEVENT_PACKAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>46</ID>
                  <NAME>DISCOVER_XEVENT_OBJECTS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>47</ID>
                  <NAME>DISCOVER_XEVENT_OBJECT_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>48</ID>
                  <NAME>DISCOVER_XEVENT_SESSION_TARGETS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>49</ID>
                  <NAME>DISCOVER_XEVENT_SESSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>DISCOVER_STORAGE_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>51</ID>
                  <NAME>DISCOVER_STORAGE_TABLE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>52</ID>
                  <NAME>DISCOVER_STORAGE_TABLE_COLUMN_SEGMENTS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>53</ID>
                  <NAME>DISCOVER_CALC_DEPENDENCY</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>54</ID>
                  <NAME>DISCOVER_CSDL_METADATA</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>55</ID>
                  <NAME>DISCOVER_RESOURCE_POOLS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>56</ID>
                  <NAME>TMSCHEMA_MODEL</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>57</ID>
                  <NAME>TMSCHEMA_DATA_SOURCES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>58</ID>
                  <NAME>TMSCHEMA_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>59</ID>
                  <NAME>TMSCHEMA_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>60</ID>
                  <NAME>TMSCHEMA_ATTRIBUTE_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>61</ID>
                  <NAME>TMSCHEMA_PARTITIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>62</ID>
                  <NAME>TMSCHEMA_RELATIONSHIPS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>63</ID>
                  <NAME>TMSCHEMA_MEASURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>64</ID>
                  <NAME>TMSCHEMA_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>65</ID>
                  <NAME>TMSCHEMA_LEVELS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>67</ID>
                  <NAME>TMSCHEMA_TABLE_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>68</ID>
                  <NAME>TMSCHEMA_COLUMN_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>69</ID>
                  <NAME>TMSCHEMA_PARTITION_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>70</ID>
                  <NAME>TMSCHEMA_SEGMENT_MAP_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>71</ID>
                  <NAME>TMSCHEMA_DICTIONARY_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>72</ID>
                  <NAME>TMSCHEMA_COLUMN_PARTITION_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>73</ID>
                  <NAME>TMSCHEMA_RELATIONSHIP_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>74</ID>
                  <NAME>TMSCHEMA_RELATIONSHIP_INDEX_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>75</ID>
                  <NAME>TMSCHEMA_ATTRIBUTE_HIERARCHY_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>76</ID>
                  <NAME>TMSCHEMA_HIERARCHY_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>77</ID>
                  <NAME>DISCOVER_RING_BUFFERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>78</ID>
                  <NAME>TMSCHEMA_KPIS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>79</ID>
                  <NAME>TMSCHEMA_STORAGE_FOLDERS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>80</ID>
                  <NAME>TMSCHEMA_STORAGE_FILES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>81</ID>
                  <NAME>TMSCHEMA_SEGMENT_STORAGES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>82</ID>
                  <NAME>TMSCHEMA_CULTURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>83</ID>
                  <NAME>TMSCHEMA_OBJECT_TRANSLATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>84</ID>
                  <NAME>TMSCHEMA_LINGUISTIC_METADATA</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>85</ID>
                  <NAME>TMSCHEMA_ANNOTATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>86</ID>
                  <NAME>TMSCHEMA_PERSPECTIVES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>87</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_TABLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>88</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_COLUMNS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>89</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_HIERARCHIES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>90</ID>
                  <NAME>TMSCHEMA_PERSPECTIVE_MEASURES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>91</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_ROLES</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>92</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_ROLE_MEMBERSHIPS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>93</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_TABLE_PERMISSIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>94</ID>
                  <NAME>PF_TRACE_EVENT_SUBCLASS_TMSCHEMA_VARIATIONS</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Notification Events</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of notification events.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>39</ID>
          <NAME>Notification</NAME>
          <DESCRIPTION>Notification event.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>Proactive Caching Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Proactive Caching End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Flight Recorder Started</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Flight Recorder Stopped</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>Configuration Properties Updated</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>SQL Trace</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>Object Created</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>Object Deleted</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>Object Altered</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>9</ID>
                  <NAME>Proactive Caching Polling Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>Proactive Caching Polling End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Flight Recorder Snapshot Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Flight Recorder Snapshot End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>13</ID>
                  <NAME>Proactive Caching: notifiable object updated</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>14</ID>
                  <NAME>Lazy Processing: start processing</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>15</ID>
                  <NAME>Lazy Processing: processing complete</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>16</ID>
                  <NAME>SessionOpened Event Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>17</ID>
                  <NAME>SessionOpened Event End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>18</ID>
                  <NAME>SessionClosing Event Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>19</ID>
                  <NAME>SessionClosing Event End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>CubeOpened Event Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>CubeOpened Event End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>CubeClosing Event Begin</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>CubeClosing Event End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>Transaction abort requested</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>25</ID>
                  <NAME>Opened data source connection</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>40</ID>
          <NAME>User Defined</NAME>
          <DESCRIPTION>User defined Event.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Session Events</NAME>
      <TYPE>1</TYPE>
      <DESCRIPTION>Collection of session events.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>41</ID>
          <NAME>Existing Connection</NAME>
          <DESCRIPTION>Existing user connection.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>42</ID>
          <NAME>Existing Session</NAME>
          <DESCRIPTION>Existing session.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>43</ID>
          <NAME>Session Initialize</NAME>
          <DESCRIPTION>Session Initialize.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>45</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>

    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Locks</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of lock related events.</DESCRIPTION>
      <EVENTLIST>

        <EVENT>
          <ID>50</ID>
          <NAME>Deadlock</NAME>
          <DESCRIPTION>Metadata locks deadlock.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>51</ID>
          <NAME>Lock Timeout</NAME>
          <DESCRIPTION>Metadata lock timeout.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>52</ID>
          <NAME>Lock Acquired</NAME>
          <DESCRIPTION>The locks were acquired by the transaction</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>53</ID>
          <NAME>Lock Released</NAME>
          <DESCRIPTION>The locks were released by the transaction</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>54</ID>
          <NAME>Lock Waiting</NAME>
          <DESCRIPTION>The locks are held by another transaction and therefore this transaction is blocking until the locks are released</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

      </EVENTLIST>

    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Query Processing</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of key events during the process of a query execution.</DESCRIPTION>
      <EVENTLIST>
        <EVENT>
          <ID>70</ID>
          <NAME>Query Cube Begin</NAME>
          <DESCRIPTION>Query cube begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>71</ID>
          <NAME>Query Cube End</NAME>
          <DESCRIPTION>Query cube end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>72</ID>
          <NAME>Calculate Non Empty Begin</NAME>
          <DESCRIPTION>Calculate non empty begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>73</ID>
          <NAME>Calculate Non Empty Current</NAME>
          <DESCRIPTION>Calculate non empty current.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Get Data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Process Calculated Members</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Post Order</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>74</ID>
          <NAME>Calculate Non Empty End</NAME>
          <DESCRIPTION>Calculate non empty end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>75</ID>
          <NAME>Serialize Results Begin</NAME>
          <DESCRIPTION>Serialize results begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>76</ID>
          <NAME>Serialize Results Current</NAME>
          <DESCRIPTION>Serialize results current.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Serialize Axes</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Serialize Cells</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Serialize SQL Rowset</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>Serialize Flattened Rowset</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>77</ID>
          <NAME>Serialize Results End</NAME>
          <DESCRIPTION>Serialize results end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>78</ID>
          <NAME>Execute MDX Script Begin</NAME>
          <DESCRIPTION>Execute MDX script begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>MDX Script</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>MDX Script Command</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>79</ID>
          <NAME>Execute MDX Script Current</NAME>
          <DESCRIPTION>Execute MDX script current. Deprecated.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>126</ID>
          <NAME>Execute MDX Script Error</NAME>
          <DESCRIPTION>An error occurred during MDX script execution.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>MDX Script</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>MDX Script Command</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>80</ID>
          <NAME>Execute MDX Script End</NAME>
          <DESCRIPTION>Execute MDX script end.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>MDX Script</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>MDX Script Command</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>81</ID>
          <NAME>Query Dimension</NAME>
          <DESCRIPTION>Query dimension.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Cache data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Non-cache data</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>11</ID>
          <NAME>Query Subcube</NAME>
          <DESCRIPTION>Query subcube, for Usage Based Optimization.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Cache data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Non-cache data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Internal data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>SQL data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Measure Group Structural Change</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>Measure Group Deletion</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>12</ID>
          <NAME>Query Subcube Verbose</NAME>
          <DESCRIPTION>Query subcube with detailed information. This event may have a negative impact on performance when turned on.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>21</ID>
                  <NAME>Cache data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>22</ID>
                  <NAME>Non-cache data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>23</ID>
                  <NAME>Internal data</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>24</ID>
                  <NAME>SQL data</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>60</ID>
          <NAME>Get Data From Aggregation</NAME>
          <DESCRIPTION>Answer query by getting data from aggregation. This event may have a negative impact on performance when turned on.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>61</ID>
          <NAME>Get Data From Cache</NAME>
          <DESCRIPTION>Answer query by getting data from one of the caches. This event may have a negative impact on performance when turned on.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Get data from measure group cache</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>Get data from flat cache</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>Get data from calculation cache</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>Get data from persisted cache</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>82</ID>
          <NAME>VertiPaq SE Query Begin</NAME>
          <DESCRIPTION>VertiPaq SE Query</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Tabular Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>User Hierarchy Processing Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Batch VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>Internal VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Internal Tabular Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>User Hierarchy Processing Query Internal</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>Query Plan VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>Local VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>Remote VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>VertiPaq Cache Probe</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>83</ID>
          <NAME>VertiPaq SE Query End</NAME>
          <DESCRIPTION>VertiPaq SE Query</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>Tabular Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>User Hierarchy Processing Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>Batch VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>10</ID>
                  <NAME>Internal VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>11</ID>
                  <NAME>Internal Tabular Query</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>12</ID>
                  <NAME>User Hierarchy Processing Query Internal</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>20</ID>
                  <NAME>Query Plan VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>30</ID>
                  <NAME>Local VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>40</ID>
                  <NAME>Remote VertiPaq Scan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>50</ID>
                  <NAME>VertiPaq Cache Probe</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>9</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>84</ID>
          <NAME>Resource Usage</NAME>
          <DESCRIPTION>Reports reads, writes, cpu usage after end of commands and queries.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>85</ID>
          <NAME>VertiPaq SE Query Cache Match</NAME>
          <DESCRIPTION>VertiPaq SE Query Cache Use</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>VertiPaq Cache Exact Match</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>86</ID>
          <NAME>VertiPaq SE Query Cache Miss</NAME>
          <DESCRIPTION>VertiPaq SE Query Cache Miss</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>0</ID>
                  <NAME>VertiPaq Cache Not Found</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>15</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>32</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>33</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>98</ID>
          <NAME>DirectQuery Begin</NAME>
          <DESCRIPTION>DirectQuery Begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>99</ID>
          <NAME>DirectQuery End</NAME>
          <DESCRIPTION>DirectQuery End.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
		
        <EVENT>
          <ID>110</ID>
          <NAME>Calculation Evaluation</NAME>
          <DESCRIPTION>Information about the evaluation of calculations. This event will have a negative impact on performance when turned on.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>InitEvalNode Start</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>InitEvalNode End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>BuildEvalNode Start</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>BuildEvalNode End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>5</ID>
                  <NAME>PrepareEvalNode Start</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>6</ID>
                  <NAME>PrepareEvalNode End</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>7</ID>
                  <NAME>RunEvalNode Start</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>8</ID>
                  <NAME>RunEvalNode End</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>111</ID>
          <NAME>Calculation Evaluation Detailed Information</NAME>
          <DESCRIPTION>Detailed information about the evaluation of calculations. This event will have a negative impact on performance when turned on.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>100</ID>
                  <NAME>BuildEvalNode Eliminated Empty Calculations</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>101</ID>
                  <NAME>BuildEvalNode Subtracted Calculation Spaces</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>102</ID>
                  <NAME>BuildEvalNode Applied Visual Totals</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>103</ID>
                  <NAME>BuildEvalNode Detected Cached Evaluation Node</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>104</ID>
                  <NAME>BuildEvalNode Detected Cached Evaluation Results</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>105</ID>
                  <NAME>PrepareEvalNode Begin Prepare Evaluation Item</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>106</ID>
                  <NAME>PrepareEvalNode Finished Prepare Evaluation Item</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>107</ID>
                  <NAME>RunEvalNode Finished Calculating Item</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>

        <EVENT>
          <ID>112</ID>
          <NAME>DAX Query Plan</NAME>
          <DESCRIPTION>DAX logical/physical plan tree for VertiPaq and DirectQuery modes.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
              <EVENTCOLUMNSUBCLASSLIST>
                <EVENTCOLUMNSUBCLASS>
                  <ID>1</ID>
                  <NAME>DAX VertiPaq Logical Plan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>2</ID>
                  <NAME>DAX VertiPaq Physical Plan</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>3</ID>
                  <NAME>DAX DirectQuery Algebrizer Tree</NAME>
                </EVENTCOLUMNSUBCLASS>
                <EVENTCOLUMNSUBCLASS>
                  <ID>4</ID>
                  <NAME>DAX DirectQuery Logical Plan</NAME>
                </EVENTCOLUMNSUBCLASS>
              </EVENTCOLUMNSUBCLASSLIST>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>35</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>37</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>40</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>120</ID>
          <NAME>DAX Extension Execution Begin</NAME>
          <DESCRIPTION>DAX extension function execution begin event.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>50</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>121</ID>
          <NAME>DAX Extension Execution End</NAME>
          <DESCRIPTION>DAX extension function execution end event.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>50</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>122</ID>
          <NAME>DAX Extension Trace Error</NAME>
          <DESCRIPTION>DAX extension function error trace event directly traced by extension authors.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>50</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>123</ID>
          <NAME>DAX Extension Trace Info</NAME>
          <DESCRIPTION>DAX extension function informational/telemetry trace event directly traced by extension authors.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>50</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>124</ID>
          <NAME>DAX Extension Trace Verbose</NAME>
          <DESCRIPTION>DAX extension function verbose trace event directly traced by extension authors.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>50</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>File Load and Save</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for file load and save operations reporting.</DESCRIPTION>
      <EVENTLIST>
        <EVENT>
          <ID>90</ID>
          <NAME>File Load Begin</NAME>
          <DESCRIPTION>File Load Begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>91</ID>
          <NAME>File Load End</NAME>
          <DESCRIPTION>File Load End.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>92</ID>
          <NAME>File Save Begin</NAME>
          <DESCRIPTION>File Save Begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>93</ID>
          <NAME>File Save End</NAME>
          <DESCRIPTION>File Save End</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>94</ID>
          <NAME>PageOut Begin</NAME>
          <DESCRIPTION>PageOut Begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>95</ID>
          <NAME>PageOut End</NAME>
          <DESCRIPTION>PageOut End</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>96</ID>
          <NAME>PageIn Begin</NAME>
          <DESCRIPTION>PageIn Begin.</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>97</ID>
          <NAME>PageIn End</NAME>
          <DESCRIPTION>PageIn End</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>Resource Governance</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events for resource governance.</DESCRIPTION>
      <EVENTLIST>
        <EVENT>
          <ID>113</ID>
          <NAME>WLGroup CPU Throttling</NAME>
          <DESCRIPTION>Workload Group is throttled on CPU usage</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>114</ID>
          <NAME>WLGroup Exceeds Memory Limit</NAME>
          <DESCRIPTION>Workload group exceeds the memory limit</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>1</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
        <EVENT>
          <ID>115</ID>
          <NAME>WLGroup Exceeds Processing Limit</NAME>
          <DESCRIPTION>Workload group exceeds the processing limit</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
      </EVENTLIST>
    </EVENTCATEGORY>

    <EVENTCATEGORY>
      <NAME>M Data Provider Events</NAME>
      <TYPE>0</TYPE>
      <DESCRIPTION>Collection of events collected from the M Data Provider</DESCRIPTION>
      <EVENTLIST>
        <EVENT>
          <ID>130</ID>
          <NAME>Execute Source Query</NAME>
          <DESCRIPTION>Collection of all queries that are executed against the data source</DESCRIPTION>
          <EVENTCOLUMNLIST>
            <EVENTCOLUMN>
              <ID>0</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>2</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>3</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>4</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>5</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>6</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>7</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>8</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>10</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>11</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>12</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>13</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>14</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>22</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>23</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>24</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>25</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>28</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>36</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>39</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>41</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>42</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>43</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>46</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>47</ID>
            </EVENTCOLUMN>
            <EVENTCOLUMN>
              <ID>49</ID>
            </EVENTCOLUMN>
          </EVENTCOLUMNLIST>
        </EVENT>
      </EVENTLIST>
    </EVENTCATEGORY>
  </EVENTCATEGORYLIST>

  <COLUMNLIST>
    <COLUMN>
      <ID>0</ID>
      <TYPE>1</TYPE>
      <NAME>EventClass</NAME>
      <DESCRIPTION>Event Class is used to categorize events.</DESCRIPTION>
      <FILTERABLE>false</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>1</ID>
      <TYPE>1</TYPE>
      <NAME>EventSubclass</NAME>
      <DESCRIPTION>Event Subclass provides additional information about each event class.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>2</ID>
      <TYPE>5</TYPE>
      <NAME>CurrentTime</NAME>
      <DESCRIPTION>Time at which the event started, when available. For filtering, expected formats are 'YYYY-MM-DD' and 'YYYY-MM-DD HH:MM:SS'.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>3</ID>
      <TYPE>5</TYPE>
      <NAME>StartTime</NAME>
      <DESCRIPTION>Time at which the event started, when available. For filtering, expected formats are 'YYYY-MM-DD' and 'YYYY-MM-DD HH:MM:SS'.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>4</ID>
      <TYPE>5</TYPE>
      <NAME>EndTime</NAME>
      <DESCRIPTION>Time at which the event ended. This column is not populated for starting event classes, such as SQL:BatchStarting or SP:Starting. For filtering, expected formats are 'YYYY-MM-DD' and 'YYYY-MM-DD HH:MM:SS'.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>5</ID>
      <TYPE>2</TYPE>
      <NAME>Duration</NAME>
      <DESCRIPTION>Amount of time (in milliseconds) taken by the event.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>6</ID>
      <TYPE>2</TYPE>
      <NAME>CPUTime</NAME>
      <DESCRIPTION>Amount of CPU time (in milliseconds) used by the event.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>

    <COLUMN>
      <ID>7</ID>
      <TYPE>1</TYPE>
      <NAME>JobID</NAME>
      <DESCRIPTION>Job ID for progress.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>8</ID>
      <TYPE>8</TYPE>
      <NAME>SessionType</NAME>
      <DESCRIPTION>Session type (what entity caused the operation).</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>9</ID>
      <TYPE>2</TYPE>
      <NAME>ProgressTotal</NAME>
      <DESCRIPTION>Progress total.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>10</ID>
      <TYPE>2</TYPE>
      <NAME>IntegerData</NAME>
      <DESCRIPTION>Integer data.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>11</ID>
      <TYPE>8</TYPE>
      <NAME>ObjectID</NAME>
      <DESCRIPTION>Object ID (note this is a string).</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>12</ID>
      <TYPE>1</TYPE>
      <NAME>ObjectType</NAME>
      <DESCRIPTION>Object type.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>13</ID>
      <TYPE>8</TYPE>
      <NAME>ObjectName</NAME>
      <DESCRIPTION>Object name.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>14</ID>
      <TYPE>8</TYPE>
      <NAME>ObjectPath</NAME>
      <DESCRIPTION>Object path.  A comma-separated list of parents, starting with the object's parent.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>15</ID>
      <TYPE>8</TYPE>
      <NAME>ObjectReference</NAME>
      <DESCRIPTION>Object reference.  Encoded as XML for all parents, using tags to describe the object.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>22</ID>
      <TYPE>1</TYPE>
      <NAME>Severity</NAME>
      <DESCRIPTION>Severity level of an exception.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>23</ID>
      <TYPE>1</TYPE>
      <NAME>Success</NAME>
      <DESCRIPTION>1 = success. 0 = failure (for example, a 1 means success of a permissions check and a 0 means a failure of that check).</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>24</ID>
      <TYPE>1</TYPE>
      <NAME>Error</NAME>
      <DESCRIPTION>Error number of a given event.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>25</ID>
      <TYPE>1</TYPE>
      <NAME>ConnectionID</NAME>
      <DESCRIPTION>Unique connection ID.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>28</ID>
      <TYPE>8</TYPE>
      <NAME>DatabaseName</NAME>
      <DESCRIPTION>Name of the database in which the statement of the user is running.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>32</ID>
      <TYPE>8</TYPE>
      <NAME>NTUserName</NAME>
      <DESCRIPTION>Windows user name.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>33</ID>
      <TYPE>8</TYPE>
      <NAME>NTDomainName</NAME>
      <DESCRIPTION>Windows domain to which the user belongs.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>35</ID>
      <TYPE>8</TYPE>
      <NAME>ClientHostName</NAME>
      <DESCRIPTION>Name of the computer on which the client is running. This data column is populated if the host name is provided by the client.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>36</ID>
      <TYPE>1</TYPE>
      <NAME>ClientProcessID</NAME>
      <DESCRIPTION>The process ID of the client application.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>37</ID>
      <TYPE>8</TYPE>
      <NAME>ApplicationName</NAME>
      <DESCRIPTION>Name of the client application that created the connection to the server. This column is populated with the values passed by the application rather than the displayed name of the program.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>39</ID>
      <TYPE>8</TYPE>
      <NAME>SessionID</NAME>
      <DESCRIPTION>Session GUID.</DESCRIPTION>
      <FILTERABLE>false</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>40</ID>
      <TYPE>8</TYPE>
      <NAME>NTCanonicalUserName</NAME>
      <DESCRIPTION>User name in canonical form.  For example, engineering.microsoft.com/software/someone.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>41</ID>
      <TYPE>1</TYPE>
      <NAME>SPID</NAME>
      <DESCRIPTION>Server process ID.  This uniquely identifies a user session. This directly corresponds to the session GUID used by XML/A.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>42</ID>
      <TYPE>9</TYPE>
      <NAME>TextData</NAME>
      <DESCRIPTION>Text data associated with the event.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>43</ID>
      <TYPE>8</TYPE>
      <NAME>ServerName</NAME>
      <DESCRIPTION>Name of the server producing the event.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>44</ID>
      <TYPE>9</TYPE>
      <NAME>RequestParameters</NAME>
      <DESCRIPTION>Parameters for parameterized queries and commands.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>45</ID>
      <TYPE>9</TYPE>
      <NAME>RequestProperties</NAME>
      <DESCRIPTION>XMLA request properties.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>46</ID>
      <TYPE>8</TYPE>
      <NAME>ActivityID</NAME>
      <DESCRIPTION>The activity ID is used to track the request(s) from the same operation.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>47</ID>
      <TYPE>8</TYPE>
      <NAME>RequestID</NAME>
      <DESCRIPTION>The request ID is used to track a request.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>48</ID>
      <TYPE>9</TYPE>
      <NAME>CalculationExpression</NAME>
      <DESCRIPTION>Expression being evaluated when error occurred.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>49</ID>
      <TYPE>1</TYPE>
      <NAME>ErrorType</NAME>
      <DESCRIPTION>Error Type: 0 = unknown, 1 = user, 2 = system.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>false</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>
    <COLUMN>
      <ID>50</ID>
      <TYPE>9</TYPE>
      <NAME>FunctionName</NAME>
      <DESCRIPTION>Name of the Dax extension function being executed.</DESCRIPTION>
      <FILTERABLE>true</FILTERABLE>
      <REPEATABLE>true</REPEATABLE>
      <REPEATEDBASE>false</REPEATEDBASE>
    </COLUMN>   
  </COLUMNLIST>
</TRACEDEFINITION>
