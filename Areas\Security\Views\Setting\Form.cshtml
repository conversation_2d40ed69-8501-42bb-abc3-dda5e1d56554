﻿@model AppTech.MSMS.Domain.MsSetting
  
<div class="row" style="margin: 30px">
    <div class="col-xs-12 col-sm-6">
        @using (Ajax.BeginForm(new AjaxOptions
        {
            OnBegin = "return OnFormBegin()",
            OnSuccess = "onCrudSuccess",
            OnFailure = "onCrudFailure",
            LoadingElementId = "formloader"
        }))
        {
        <table id="search-filter-table" class="table table-responsive borderless">
            @foreach (var property in ViewData.ModelMetadata.Properties)
            {

                if (property.PropertyName.Contains("Remittance"))
                {
                    if (AppTech.MSMS.Domain.DomainManager.DirectRemittance)
                    {
                        <tr>
                            <td> <span class="lbl">  @Html.Label(property.DisplayName) </span></td>
                            <td> @Html.Editor(property.PropertyName) </td>
                        </tr>
                    }
                }
                else if (property.PropertyName.Contains("Currency"))
                {
                    if (AppTech.MSMS.Domain.DomainManager.ConfigModules.Contains("CurrencyExchange"))
                    {
                        <tr>
                            <td> <span class="lbl">  @Html.Label(property.DisplayName) </span></td>
                            <td> @Html.Editor(property.PropertyName) </td>
                        </tr>
                    }
                }
                else if (property.PropertyName.Contains("Transfer"))
                {
                    if (AppTech.MSMS.Domain.DomainManager.ConfigModules.Contains("Remittance"))
                    {
                        <tr>
                            <td>        <span class="lbl">  @Html.Label(property.DisplayName) </span></td>
                            <td> @Html.Editor(property.PropertyName) </td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td> <span class="lbl">  @Html.Label(property.DisplayName) </span></td>
                        <td> @Html.Editor(property.PropertyName) </td>
                    </tr>

                }
            }


          

        </table>
            <div class="space-10"></div>

            <div class="space-32"></div>
            <div class="space-32"></div>
            <div class="hr hr32 hr-dotted"></div>
        @Html.Partial("_FormAction")
        }
    </div>


</div>
@*<script>
    $(function () {
        //$('.notifyOnOrders').attr('checked', true);
        ace.data = new ace.data_storage();
        var notify = ace.data.get('notify_orders',false);
        var notifyInterval = ace.data.get('notify_interval', 5);
        $('#notifyOnOrders').attr('checked', notify);
        $('#notifyInterval').val(notifyInterval);

        $("#save").on('click',
            function () {
                i('onclikck');
                //ace.data_storage.set();
                try {
                    var notify = ace.data.get('notify_orders');
                    i('is notify= ' + notify);
        
                    //    var val = $("#notifyOnOrders").checked;

                    var val = $("#notifyOnOrders").is(':checked');

                    i('val= '+val);
                    ace.data.set('notify_orders', val);
                    ace.data.set('notifyInterval', $("#notifyInterval").val());
                } catch (e) {
                    alert(e);
                }

                ////   ace.data = new ace.data_storage();
                //    var name = ace.data.get('namez');

            });
    })
</script>*@