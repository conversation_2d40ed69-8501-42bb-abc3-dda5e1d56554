﻿@model AppTech.MSMS.Domain.Models.Person
<div id="modal" class="modal fade" role="dialog" aria-labelledby="model-title">
    <div class="modal-dialog" role="form">
        <div class="modal-content">

            <div class="modal-header" style="background: cornflowerblue">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="modal-title" style="color: white"></h4>
            </div>

            <div class="modal-body" style="padding: 40px 50px;">
                <form id="multiform" action="Remittance/RemittanceOut/BeneficiaryInfo" class="form-horizontal" method="POST" enctype="multipart/form-data">
                    <div class="form-horizontal">

                        @Html.ValidationSummary(true, "", new {@class = "text-danger"})
                        <div class="form-group">
                            @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.PhoneNumber, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.PhoneNumber, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.PhoneNumber, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.Address, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.Address, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.Address, "", new {@class = "text-danger"})
                            </div>
                        </div>


                        <div class="form-group">
                            @Html.LabelFor(model => model.Nationality, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.Nationality, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.Nationality, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.CardType, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.CardType, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.CardType, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.CardNumber, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.CardNumber, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.CardNumber, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.CardIssuePlace, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.CardIssuePlace, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.CardIssuePlace, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.CardIssueDate, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.CardIssueDate, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.CardIssueDate, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(model => model.CardExpireDate, new {@class = "control-label col-md-2"})
                            <div class="col-md-10">
                                @Html.EditorFor(model => model.CardExpireDate, new {htmlAttributes = new {@class = "form-control"}})
                                @Html.ValidationMessageFor(model => model.CardExpireDate, "", new {@class = "text-danger"})
                            </div>
                        </div>

                        <div class="form-group">
                            <div style="position: relative;">
                                <label>أختر صورة...</label>
                                <input type="file" name="ImageData" size="40" onchange="showImg(this)">
                            </div>
                        </div>

                        <img class="img-thumbnail" width="150" height="150" id="preview"
                             src="@Url.Action("GetImage", "RemittanceOut", new {Model.Name})"/>

                        @Html.Partial("_FormAction")



                        @*<div class="form-group">
            <div class="col-md-offset-2 col-md-10">
                <input type="submit" value="Create" class="btn btn-default" />
            </div>
        </div>*@
                    </div>


                </form>
            </div>

            <div class="modal-footer">
                <button type="button" id="cancelModal" class="btn btn-danger btn-default pull-left" data-dismiss="modal"><i class="glyphicon glyphicon-remove"></i> خروج</button>
                <span class="alert"></span>
                <button class="btn btn-white btn-default btn-round pull-right" type="reset">
                    <i class="ace-icon fa fa-undo bigger-110"></i>
                </button>
            </div>

        </div>
    </div>
</div>