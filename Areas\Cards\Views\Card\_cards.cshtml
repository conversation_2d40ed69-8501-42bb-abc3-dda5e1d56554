﻿@model IEnumerable<AppTech.MSMS.Domain.Models.Card>
<table class="table table-hover">
    <thead>
        <tr>
            <th>الكرت</th>
            <th>كلمة السر</th>
            <th>الوصف</th>
            <th></th>
        </tr>
    </thead>
    @foreach (var Card in Model)
    {
        <tbody>
            <tr>
                @if (Card != null)
                {

                    <td>@Html.DisplayFor(modelItem => Card.Name)</td>
                    <td>@Html.DisplayFor(modelItem => Card.Password)</td>
                    <td>@Html.DisplayFor(modelItem => Card.Note)</td>
                    <td>
                        <Button class="btn btn-link" onclick="openEditModal('@Card.ID','@Card.CardFactionID','@Card.CardTypeID')">
                            <i class="ace-icon fa fa-edit bigger-110"></i>
                            تعديل
                        </Button>
                          <Button class="btn btn-link" onclick="unbind('@Card.ID')">
                            <i class="ace-icon fa fa-trash-o bigger-110"></i>
                            حذف
                        </Button>
                    </td>
                }
            </tr>
        </tbody>
    }
</table>
<script>
     function openModal(id) {
        i('open modal id' + id);
        openViewAsModal('Cards/Card/AddOrEditCards?ID=' + id, " جديد");
    }
    function openEditModal(id,CardFactionID,CardTypeID) {
        i('open modal id' + id);
        openViewAsModal('Cards/Card/AddOrEditCards?ID=' + id + '&CardTypeID='+ CardTypeID + '&CardFactionID='+ CardFactionID );
    }

     function unbind(id) {
         i('remove bundle id' + id);
         var confirms = confirm("هل تريد حذف العنصر؟");
         if (confirms ==true) {

        var fId =@ViewBag.CardFactionID;
         
        i('remove bundle fId' + fId);
        var url = 'Cards/Card/Remove?id=' + id+ '&CardFactionID=' + fId;

        AjaxCall(url)
            .done(function (response) {
              //  ar(response.Message);
                $("#list").replaceWith(response);
            }).fail(function (xhr, textStatus, errorThrown) {
                    parseAndShowError(xhr, textStatus, errorThrown);
                });
         }

    }

</script>
