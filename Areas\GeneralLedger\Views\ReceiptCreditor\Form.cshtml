﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.ReceiptCreditor
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<input type="hidden" id="TransNumber" name="TransNumber" value="@DateTime.Now.ToString("yyyyMMddhhmm")" />
<div class="form-group form-inline">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>

<div class="form-group">
    <div class="col-md-12">
        @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
        @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
    </div>
</div>

<div class="form-group">
    @Html.Label("الحساب", new { @class = "control-label col-md-2" })
    <div class="col-md-10">

        @Html.Obout(new ComboBox("CreditorAccountID")
        {
            Width = 300,
            SelectedValue = Model.CreditorAccountID == 0 ? null : Model.CreditorAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })

        @Html.ValidationMessageFor(model => model.CreditorAccountID)
    </div>

</div>

<div class="form-group">
    @Html.Label(" الحساب المقابل", new { @class = "control-label col-md-2" })
    <div class="col-md-10">

        @Html.Obout(new ComboBox("DebitorAccountID")
        {
            Width = 300,
            SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains,
            LoadingText = "Loading"
        })

        @Html.ValidationMessageFor(model => model.DebitorAccountID)
    </div>
</div>


<div class="form-group">

    @Html.Label("التاريخ", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "date-picker" } })
        @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
    </div>
</div>



<div class="form-group">
    @Html.LabelFor(model => model.RefNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.RefNumber, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.RefNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.AttachmentNumbers, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.AttachmentNumbers, new { htmlAttributes = new { @class = "" } })
        @Html.ValidationMessageFor(model => model.AttachmentNumbers, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>