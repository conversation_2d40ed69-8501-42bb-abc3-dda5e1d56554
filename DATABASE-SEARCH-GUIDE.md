# دليل البحث عن قاعدة البيانات الأصلية - AppTech MSMS

## 📍 المسارات المحتملة لقاعدة البيانات

### 1. مسارات SQL Server الافتراضية

#### SQL Server Express:
```
C:\Program Files\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA\
C:\Program Files\Microsoft SQL Server\MSSQL14.SQLEXPRESS\MSSQL\DATA\
C:\Program Files\Microsoft SQL Server\MSSQL13.SQLEXPRESS\MSSQL\DATA\
C:\Program Files (x86)\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\DATA\
```

#### SQL Server Standard/Enterprise:
```
C:\Program Files\Microsoft SQL Server\MSSQL15.MSSQLSERVER\MSSQL\DATA\
C:\Program Files\Microsoft SQL Server\MSSQL14.MSSQLSERVER\MSSQL\DATA\
C:\Program Files\Microsoft SQL Server\MSSQL13.MSSQLSERVER\MSSQL\DATA\
```

#### LocalDB:
```
C:\Users\<USER>\AppData\Local\Microsoft\Microsoft SQL Server Local DB\Instances\
```

### 2. مسارات مخصصة محتملة

#### مجلدات البيانات المخصصة:
```
C:\Data\
D:\Data\
E:\Data\
C:\Database\
D:\Database\
E:\Database\
C:\SQL\Data\
D:\SQL\Data\
```

#### مجلدات التطبيق:
```
E:\inetpub\App_Data\
E:\inetpub\wwwroot\api\App_Data\
E:\inetpub\Database\
C:\AppTech\Database\
D:\AppTech\Database\
```

## 🔍 أسماء قواعد البيانات المحتملة

ابحث عن ملفات بهذه الأسماء:

### الأسماء المحتملة:
- `AppTechMSMS.mdf` / `AppTechMSMS.ldf`
- `AppTech.mdf` / `AppTech.ldf`
- `MSMS.mdf` / `MSMS.ldf`
- `AppTechDB.mdf` / `AppTechDB.ldf`
- `MoneyServices.mdf` / `MoneyServices.ldf`
- `FinancialSystem.mdf` / `FinancialSystem.ldf`

### ملفات النسخ الاحتياطية:
- `AppTechMSMS.bak`
- `AppTech.bak`
- `MSMS.bak`

## 🔐 معلومات فك التشفير

### ملف maincs.erp الحالي:
```
المسار: wwwroot\api\maincs.erp
المحتوى: NOAn9nCXghTNa0sK0+qcYtK8Lnf5YSSsFK1ytM8ENbZUcQo062tIZDDRN8k6oY7BxHlwgX6ArtThaLo6rvqaQAYCMO8Nl5RR0DyhlRTBya17ZGcFgGyqknAhSJ8fAipuP5IPI14eKxJIQvcxvzn+OwHxP+ZABug4LqpKUj44E/k=
```

هذا الملف يحتوي على سلاسل الاتصال المشفرة. يحتاج إلى:
- مكتبة `AppTech.Security.dll` لفك التشفير
- أو الحصول على ملف maincs.erp من السيرفر القديم العامل

## 📋 قائمة فحص للسيرفر القديم

### 1. ملفات قاعدة البيانات:
- [ ] البحث في مجلدات SQL Server DATA
- [ ] البحث في مجلدات مخصصة للبيانات
- [ ] البحث عن ملفات .bak (نسخ احتياطية)
- [ ] فحص SQL Server Management Studio للقواعد المرفقة

### 2. ملفات التكوين:
- [ ] نسخ ملفات maincs.erp من جميع التطبيقات
- [ ] نسخ ملفات web.config مع سلاسل الاتصال
- [ ] فحص ملفات app.config للتطبيقات المكتبية

### 3. ملفات الترخيص:
- [ ] نسخ ملفات license.lic
- [ ] نسخ ملفات LicenseVerify.cer

### 4. ملفات النظام:
- [ ] التأكد من وجود جميع ملفات DLL
- [ ] نسخ أي ملفات تكوين إضافية

## 🔧 أوامر البحث في السيرفر القديم

### البحث عن ملفات قاعدة البيانات:
```cmd
# البحث في القرص C
dir C:\ /s *.mdf | findstr -i apptech
dir C:\ /s *.mdf | findstr -i msms

# البحث في جميع الأقراص
for %i in (C D E F) do dir %i:\ /s *.mdf 2>nul | findstr -i apptech
```

### البحث باستخدام PowerShell:
```powershell
# البحث عن ملفات قاعدة البيانات
Get-ChildItem -Path C:\ -Recurse -Include "*.mdf" -ErrorAction SilentlyContinue | Where-Object {$_.Name -like "*AppTech*" -or $_.Name -like "*MSMS*"}

# البحث عن ملفات النسخ الاحتياطية
Get-ChildItem -Path C:\ -Recurse -Include "*.bak" -ErrorAction SilentlyContinue | Where-Object {$_.Name -like "*AppTech*" -or $_.Name -like "*MSMS*"}
```

### فحص SQL Server:
```sql
-- عرض جميع قواعد البيانات
SELECT name, database_id, create_date FROM sys.databases;

-- البحث عن قواعد بيانات AppTech
SELECT name FROM sys.databases WHERE name LIKE '%AppTech%' OR name LIKE '%MSMS%';

-- عرض مسارات ملفات قاعدة البيانات
SELECT 
    db.name AS DatabaseName,
    mf.name AS LogicalName,
    mf.physical_name AS PhysicalPath,
    mf.type_desc AS FileType
FROM sys.master_files mf
INNER JOIN sys.databases db ON mf.database_id = db.database_id
WHERE db.name LIKE '%AppTech%' OR db.name LIKE '%MSMS%';
```

## 📤 خطوات النقل

### 1. نسخ قاعدة البيانات:
```sql
-- إنشاء نسخة احتياطية
BACKUP DATABASE [AppTechMSMS] TO DISK = 'C:\Backup\AppTechMSMS.bak'

-- أو فصل قاعدة البيانات ونسخ الملفات
USE master;
ALTER DATABASE [AppTechMSMS] SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
EXEC sp_detach_db 'AppTechMSMS';
```

### 2. نسخ الملفات المطلوبة:
- نسخ ملفات .mdf و .ldf
- نسخ ملفات maincs.erp
- نسخ ملفات license.lic
- نسخ أي ملفات تكوين مخصصة

### 3. استعادة في السيرفر الجديد:
```sql
-- استعادة من النسخة الاحتياطية
RESTORE DATABASE [AppTechMSMS] FROM DISK = 'C:\Backup\AppTechMSMS.bak'

-- أو إرفاق الملفات
CREATE DATABASE [AppTechMSMS] ON 
(FILENAME = 'C:\Data\AppTechMSMS.mdf'),
(FILENAME = 'C:\Data\AppTechMSMS.ldf')
FOR ATTACH;
```

## ⚠️ تحذيرات مهمة

1. **لا تنشئ قاعدة بيانات جديدة** حتى تتأكد من عدم وجود القاعدة الأصلية
2. **احتفظ بنسخة احتياطية** من جميع الملفات قبل النقل
3. **تأكد من صحة ملفات maincs.erp** لأنها تحتوي على معلومات الاتصال الحقيقية
4. **اختبر الاتصال** بقاعدة البيانات قبل تشغيل التطبيق

## 📞 الخطوات التالية

بعد العثور على الملفات:
1. أخبرني بالملفات التي وجدتها
2. سأساعدك في نقلها وتكوينها
3. سنختبر الاتصال والتشغيل
4. سنحل أي مشاكل تظهر
