# سكريبت التجهيز الشامل لنظام AppTech MSMS
# يجب تشغيله كمدير (Run as Administrator)

param(
    [switch]$SkipIIS,
    [switch]$SkipDatabase,
    [switch]$TestOnly
)

Write-Host @"
========================================
    تجهيز نظام AppTech MSMS للتشغيل
========================================
"@ -ForegroundColor Cyan

# التحقق من صلاحيات المدير
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "✗ يجب تشغيل هذا السكريبت كمدير (Run as Administrator)" -ForegroundColor Red
    Write-Host "انقر بالزر الأيمن على PowerShell واختر 'Run as Administrator'" -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "✓ تم تشغيل السكريبت بصلاحيات المدير" -ForegroundColor Green

# إعداد مسار العمل
$originalPath = Get-Location
Set-Location "E:\inetpub"

try {
    if (-not $TestOnly) {
        # الخطوة 1: تفعيل IIS
        if (-not $SkipIIS) {
            Write-Host "`n" + "="*50 -ForegroundColor Yellow
            Write-Host "الخطوة 1: تفعيل IIS" -ForegroundColor Yellow
            Write-Host "="*50 -ForegroundColor Yellow
            
            if (Test-Path "setup-iis.ps1") {
                & ".\setup-iis.ps1"
            } else {
                Write-Host "⚠ ملف setup-iis.ps1 غير موجود - تخطي تفعيل IIS" -ForegroundColor Yellow
            }
        }

        # الخطوة 2: إنشاء Application Pools
        Write-Host "`n" + "="*50 -ForegroundColor Yellow
        Write-Host "الخطوة 2: إنشاء Application Pools" -ForegroundColor Yellow
        Write-Host "="*50 -ForegroundColor Yellow
        
        if (Test-Path "create-app-pools.ps1") {
            & ".\create-app-pools.ps1"
        } else {
            Write-Host "⚠ ملف create-app-pools.ps1 غير موجود" -ForegroundColor Yellow
        }

        # الخطوة 3: إنشاء المواقع
        Write-Host "`n" + "="*50 -ForegroundColor Yellow
        Write-Host "الخطوة 3: إنشاء المواقع" -ForegroundColor Yellow
        Write-Host "="*50 -ForegroundColor Yellow
        
        if (Test-Path "create-websites.ps1") {
            & ".\create-websites.ps1"
        } else {
            Write-Host "⚠ ملف create-websites.ps1 غير موجود" -ForegroundColor Yellow
        }

        # الخطوة 4: إعداد قاعدة البيانات
        if (-not $SkipDatabase) {
            Write-Host "`n" + "="*50 -ForegroundColor Yellow
            Write-Host "الخطوة 4: إعداد قاعدة البيانات" -ForegroundColor Yellow
            Write-Host "="*50 -ForegroundColor Yellow
            
            if (Test-Path "setup-database.ps1") {
                & ".\setup-database.ps1"
            } else {
                Write-Host "⚠ ملف setup-database.ps1 غير موجود" -ForegroundColor Yellow
            }
            
            # تحديث ملفات web.config
            if (Test-Path "update-web-configs.ps1") {
                & ".\update-web-configs.ps1"
            } else {
                Write-Host "⚠ ملف update-web-configs.ps1 غير موجود" -ForegroundColor Yellow
            }
        }

        # الخطوة 5: إعداد الصلاحيات
        Write-Host "`n" + "="*50 -ForegroundColor Yellow
        Write-Host "الخطوة 5: إعداد الصلاحيات" -ForegroundColor Yellow
        Write-Host "="*50 -ForegroundColor Yellow
        
        if (Test-Path "setup-permissions.ps1") {
            & ".\setup-permissions.ps1"
        } else {
            Write-Host "⚠ ملف setup-permissions.ps1 غير موجود" -ForegroundColor Yellow
        }

        # إعادة تشغيل IIS
        Write-Host "`nإعادة تشغيل IIS..." -ForegroundColor Yellow
        try {
            iisreset /restart
            Write-Host "✓ تم إعادة تشغيل IIS بنجاح" -ForegroundColor Green
        } catch {
            Write-Host "⚠ فشل في إعادة تشغيل IIS: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }

    # الخطوة 6: اختبار النظام
    Write-Host "`n" + "="*50 -ForegroundColor Yellow
    Write-Host "الخطوة 6: اختبار النظام" -ForegroundColor Yellow
    Write-Host "="*50 -ForegroundColor Yellow
    
    if (Test-Path "test-applications.ps1") {
        & ".\test-applications.ps1"
    } else {
        Write-Host "⚠ ملف test-applications.ps1 غير موجود" -ForegroundColor Yellow
    }

} catch {
    Write-Host "✗ خطأ في تنفيذ السكريبت: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Set-Location $originalPath
}

Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "انتهى تجهيز النظام" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

Write-Host "`nالخطوات التالية:" -ForegroundColor White
Write-Host "1. تحقق من نتائج الاختبارات أعلاه" -ForegroundColor Yellow
Write-Host "2. إذا ظهرت أخطاء HTTP 500، راجع سلاسل الاتصال" -ForegroundColor Yellow
Write-Host "3. قم بتشغيل Entity Framework Migrations إذا لزم الأمر" -ForegroundColor Yellow
Write-Host "4. اختبر تسجيل الدخول للتطبيقات" -ForegroundColor Yellow

Write-Host "`nروابط التطبيقات:" -ForegroundColor White
Write-Host "API: http://localhost/" -ForegroundColor Cyan
Write-Host "Client: http://localhost:8080/" -ForegroundColor Cyan
Write-Host "Portal: http://localhost:8081/" -ForegroundColor Cyan

Write-Host "`nاستخدام السكريبت:" -ForegroundColor White
Write-Host ".\deploy-apptech-system.ps1                 # تجهيز كامل" -ForegroundColor Gray
Write-Host ".\deploy-apptech-system.ps1 -SkipIIS        # تخطي تفعيل IIS" -ForegroundColor Gray
Write-Host ".\deploy-apptech-system.ps1 -SkipDatabase   # تخطي قاعدة البيانات" -ForegroundColor Gray
Write-Host ".\deploy-apptech-system.ps1 -TestOnly       # اختبار فقط" -ForegroundColor Gray

pause
