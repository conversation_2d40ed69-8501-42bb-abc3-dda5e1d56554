﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.Client
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<h4 class="header blue bolder smaller">بيانات عامة</h4>
@Html.HiddenFor(model => model.Channel, new {value = "web"})

<div class="form-group">
    <div class="col-md-12">
        @Html.Label("الحساب", new {@class = "control-label col-md-2"})
        @Html.Obout(new ComboBox("SyncAccountID")
        {
            Width = 300,
            SelectedValue = Model.SyncAccountID == null ? null : Model.SyncAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains
        })

        @Html.ValidationMessageFor(model => model.SyncAccountID)
    </div>
</div>


<div class="form-group">
    @Html.Label("رقم الهاتف", new {@class = "control-label col-md-2"})

    <div class="col-sm-10">
        <span class="input-icon input-icon-right">
            @Html.EditorFor(model => model.PhoneNumber, new {@class = "input-medium input-mask-phone", id = "form-field-phone", placeholder = "رقم الحساب"})
            @Html.ValidationMessageFor(model => model.PhoneNumber)
            <i class="ace-icon fa fa-phone fa-flip-horizontal"></i>
        </span>
    </div>
</div>


<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-name">نوع العميل</label>
    <div class="col-sm-10">
        @Html.EnumDropDownListFor(model => model.Type)
        @Html.ValidationMessageFor(model => model.Type)
    </div>
</div>

<div class="form-group">
    @Html.Label("مجموعة الصلاحيات", new {@class = "control-label col-md-2"})

    <div class="col-sm-10">
        @Html.DropDownListFor(model => model.ActivateBy, (SelectList) ViewBag.Groups, new {@class = ""})
    </div>
</div>
<div class="space-4"></div>


<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-note">ملاحظات</label>

    <div class="col-sm-10">
        @Html.EditorFor(model => model.Note, new {htmlAttributes = new {@class = "form-control", id = "form-field-note"}})

    </div>
</div>

<div class="space-4"></div>

<h4 class="header blue bolder smaller">بيانات التواصل</h4>


<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="form-field-website">العنوان</label>

    <div class="col-sm-10">
        @Html.EditorFor(model => model.Address, new {htmlAttributes = new {@class = "form-control", id = "form-field-note"}})
        @Html.ValidationMessageFor(model => model.Address)
    </div>
</div>

<div class="space-4"></div>

<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="email">الأيميل</label>

    <div class="col-sm-10">
        <span class="input-icon input-icon-right">
            @Html.EditorFor(model => model.Email, new {@class = "form-control", id = "form-field-email"})
            @Html.ValidationMessageFor(model => model.Email)
            <i class="ace-icon fa fa-mail-reply"></i>
        </span>
    </div>
</div>

<div class="space"></div>
@if (Model.ID == 0)
{
    <h4 class="header blue bolder smaller">ييانات تسجيل الدخول</h4>
    <div class="form-group">
        <label class="col-sm-2 control-label no-padding-right">اسم المستخدم</label>
        <div class="col-sm-10">
            <input type="text" name="Username" id="Username" class=""/>
            @Html.ValidationMessageFor(model => model.Username)
        </div>
    </div>

    <div class="space-4"></div>

    <div class="form-group">
        <label class="col-sm-2 control-label no-padding-right">كلمة المرور</label>
        <div class="col-sm-10">
            <input type="text" name="Password" class=""/>
            @Html.ValidationMessageFor(model => model.Password)
        </div>
    </div>
}


<script>

    $(function() {
        i('on load');
        $("#SyncAccountID").on('change',
            function() {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#Name").val(name);
            });
    });
</script>