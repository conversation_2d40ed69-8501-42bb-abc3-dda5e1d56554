<<type>>
<Module>
<Module>
<Module>
<Module>
<<type>>
uh8mvmgNA4LuUjtYl9.hION5AIvQem4E1TyKf
qmr224QqXmD5UanGrH
hION5AIvQem4E1TyKf
qmr224QqXmD5UanGrH
<<type>>
<>f__AnonymousType0`1
<>f__AnonymousType0`1
<>f__AnonymousType0`1
<>f__AnonymousType0`1
<<type>>
<>f__AnonymousType1`2
<>f__AnonymousType1`2
<>f__AnonymousType1`2
<>f__AnonymousType1`2
<<type>>
<>f__AnonymousType2`2
<>f__AnonymousType2`2
<>f__AnonymousType2`2
<>f__AnonymousType2`2
<<type>>
<>f__AnonymousType3`2
<>f__AnonymousType3`2
<>f__AnonymousType3`2
<>f__AnonymousType3`2
<<type>>
<>f__AnonymousType4`2
<>f__AnonymousType4`2
<>f__AnonymousType4`2
<>f__AnonymousType4`2
<<type>>
<>f__AnonymousType5`3
<>f__AnonymousType5`3
<>f__AnonymousType5`3
<>f__AnonymousType5`3
<<type>>
<>f__AnonymousType6`2
<>f__AnonymousType6`2
<>f__AnonymousType6`2
<>f__AnonymousType6`2
<<type>>
boSOx4oSVuWtSEF1hW.dd0IQEiPbKKjT68EHk
Derhim.DerhimApi
dd0IQEiPbKKjT68EHk
DerhimApi
JIiue36ZR
get_userID
CZwDuO5rL
get_password
pl6taNBF6
get_userName
PNHAo77f9
MD5
JNMUevjuW
GetServiceType
CkQpESEu4
GetErrorCodeMsg
x4299I7XY
OnResponse
yhKie2ZsP
BuildHeader
iJ3IRyM52
BuildBody
LVkHBATXI
GetQueryResponse
clVS2UOcZ
serviceClient
mnBkFx1Fq
_disposed
N26N5EobC
userID
txtdvrIIe
userName
<<type>>
HashidsNet.Hashids
HashidsNet.Hashids
Hashids
Hashids
KqjPSffqb
SetupSeps
M67jcyP3t
SetupGuards
iMoaTS5yr
Hash
nTPYCUaRK
Unhash
Vi0w5Vp3w
ConsistentShuffle
gUt0VLnP4
hexValidator
njUqym6Y2
hexSplitter
F41Mp1LGi
alphabet
KgkXaqA8u
guards
ywKof55NK
guardsRegex
oKHLfkqHK
minHashLength
V6FZH83YY
salt
IoLK09pJr
seps
WlpytrT6c
sepsRegex
<<type>>
AppTech.MSMS.Domain.AppTechInfo
AppTech.MSMS.Domain.AppTechInfo
AppTechInfo
AppTechInfo
gQTvrIGoK
<Name>k__BackingField
ROg2H2et8
<Contact>k__BackingField
Ho58VROli
<Website>k__BackingField
zcR3QNV67
<Email>k__BackingField
OyrbYvDS6
<DeveloperName>k__BackingField
gJxEbhXX4
<ContactNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.DevSetting
AppTech.MSMS.Domain.DevSetting
DevSetting
DevSetting
b8o5EHj0m
<Debug>k__BackingField
<<type>>
AppTech.MSMS.Domain.AppContext
AppTech.MSMS.Domain.AppContext
AppContext
AppContext
<<type>>
AppTech.MSMS.Domain.DomainManager
AppTech.MSMS.Domain.DomainManager
DomainManager
DomainManager
cckBODrIB
InitSecurity
KJDlB3Lkn
InitLicensedServices
wMtT5MpCc
CheckDb
BYyClFOcR
InitDocuments
YBwgaPNwq
IntiModules
OJGcjTPKo
AppTech
AByJFniCg
CreateImageDirectories
KfC1GBIee
InitEntities
xVCrgY3KJ
<AdminstrationBranch>k__BackingField
yx6QOUlc6
<LicensedModules>k__BackingField
<<type>>
AppTech.MSMS.Domain.Res
AppTech.MSMS.Domain.Res
Res
Res
XX7FD95Er
<LoginAttemptTable>k__BackingField
f0OVSkspp
<UserLogin>k__BackingField
<<type>>
AppTech.MSMS.Domain.FrancyExtensions
AppTech.MSMS.Domain.FrancyExtensions
FrancyExtensions
FrancyExtensions
<<type>>
AppTech.MSMS.Domain.FrancyGateway
AppTech.MSMS.Domain.FrancyGateway
FrancyGateway
FrancyGateway
kPmRhuyJe
ExecuteGomalaAsync
VQ2f5RDRf
ExecuteTopupAsync
Mi6epoHCQ
ExecuteBagatAsync
dyGxGpqCt
ExecuteAdenNetAsync
r0Gs5Lr5F
ExecuteRiyalBagat
iP9Os8ByU
ExecuteRiyalTopup
WSch7auQL
<>n__0
HU06TP9fG
_disposed
<<type>>
AppTech.MSMS.Domain.ClientLicense
AppTech.MSMS.Domain.ClientLicense
ClientLicense
ClientLicense
ywT4t2J85
<Customer>k__BackingField
<<type>>
AppTech.MSMS.Domain.Customer
AppTech.MSMS.Domain.Customer
Customer
Customer
o6AmGH3FO
<CompanyInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.CompanyInfo
AppTech.MSMS.Domain.CompanyInfo
CompanyInfo
CompanyInfo
exD7VXPfg
<Name>k__BackingField
OqOWV2ZT4
<Description>k__BackingField
v7AnGafQ7
<Address>k__BackingField
sM4z1fBC9
<Email>k__BackingField
EXKAGxeJAV
<Website>k__BackingField
EJ3AAt0nsw
<Contacts>k__BackingField
NP1AUAixNT
<ExtraContacts>k__BackingField
kDMApxUp5C
<More>k__BackingField
<<type>>
AppTech.MSMS.Domain.MsError
AppTech.MSMS.Domain.MsError
MsError
MsError
<<type>>
AppTech.MSMS.Domain.MsLogger
AppTech.MSMS.Domain.MsLogger
MsLogger
MsLogger
AkDA9gAHJk
LockObj
<<type>>
AppTech.MSMS.Domain.ServiceFactory
AppTech.MSMS.Domain.ServiceFactory
ServiceFactory
ServiceFactory
<<type>>
xrbAoJY5M4oL0cY2w2.d50nP9NxIDqJ0P8YUw
AppTech.MSMS.Domain.TestClass
d50nP9NxIDqJ0P8YUw
TestClass
aPvAiwaoD5
ReceiveSmsMessage
OyjAI0P5uC
SendSms
vYGAHYLCXI
GetTransactonType
<<type>>
AppTech.MSMS.Domain.TestHelper
AppTech.MSMS.Domain.TestHelper
TestHelper
TestHelper
<<type>>
h4A5cnhXi9FB3EAmhf.otb6rQ950snbFBK7vH
AppTech.MSMS.Domain.RemittanceHelper
otb6rQ950snbFBK7vH
RemittanceHelper
SOOAuxj0KC
IsNameLessThanThree
<<type>>
AppTech.MSMS.Domain.MsmsModule
AppTech.MSMS.Domain.MsmsModule
MsmsModule
MsmsModule
Yi3ANgjBe6
<ID>k__BackingField
muYAD8511e
<Name>k__BackingField
HdQAtBDbCm
<Title>k__BackingField
i7hAdIAYOb
<Icon>k__BackingField
<<type>>
AppTech.MSMS.Domain.MsSetting
AppTech.MSMS.Domain.MsSetting
MsSetting
MsSetting
b6GASZTE9X
<AllowOnlyPermittedUsers>k__BackingField
xLtAkFnO7v
<AllowLoginAttemps>k__BackingField
sooAPfAuSR
<AutoPermitDeviceOnActivateAccount>k__BackingField
sOXAjbsYiZ
<RegisterationFullNamedModitory>k__BackingField
G5gAan5ybs
<RegisterationCardModitory>k__BackingField
pHaAYiJS7Y
<SessionTimeout>k__BackingField
bbNAwJUiFR
<ShowExpiredBalances>k__BackingField
Ci7A0npnFE
<AddSeperatorsToAmount>k__BackingField
SYtAqpj3SN
<AddTwoDecimalToAmount>k__BackingField
qZeAMYQ6Cc
<AlertOnNewOrders>k__BackingField
KAyAXNoge0
<OrdersNotifyInterval>k__BackingField
bB8AoILvVX
<DisableOrders>k__BackingField
w9NALHAKHq
<AccountFullNamedModitory>k__BackingField
CY6AZEDIuJ
<Points_GrantParentPermissionsToPoint>k__BackingField
oUwAKCMpcr
<Points_GrantParentQuotationToPoint>k__BackingField
LfRAyW3TDL
<Points_GrantParentProviderPoints>k__BackingField
Y1gAvuHxK5
<VoucherRepeatTime>k__BackingField
RytA2UutPa
<CashTransferSuspended>k__BackingField
UeGA8fIOgP
<Agency_CashTransferSuspendedExceptBetweenTree>k__BackingField
XLYA3eZqqh
<Agency_CashTransferSuspendedExceptFromParents>k__BackingField
UN8AbtSScx
<CashTransfer_CommissionMandotry>k__BackingField
z4YAExcGqe
<DisableExpressRemittance>k__BackingField
PwoA5p5WyZ
<Remittance_SendByServicProvider>k__BackingField
sOpABZYGPB
<Remittance_ReceiveByServiceProvider>k__BackingField
zIqAlXDEiQ
<Transfers_ReceiverCardMandatory>k__BackingField
ARFATTMYXN
<DirectCurrencyExchange>k__BackingField
hcjACW8VGv
<DisableCurrencyExchange>k__BackingField
S7UAg2OjEU
<DisableCurrencyExchangeOnNoUpdatedDate>k__BackingField
q1ZAch8Sqs
<CE_CheckBalViaAdmin>k__BackingField
<<type>>
AppTech.MSMS.Domain.MsUnitOfWork
AppTech.MSMS.Domain.MsUnitOfWork
MsUnitOfWork
MsUnitOfWork
<<type>>
AppTech.MSMS.Domain.FundRepo`1
AppTech.MSMS.Domain.FundRepo`1
FundRepo`1
FundRepo`1
Ni8AJ6oMwt
business
<<type>>
AppTech.MSMS.Domain.Repo`2
AppTech.MSMS.Domain.Repo`2
Repo`2
Repo`2
RQyA1ZbyJb
business
<<type>>
AppTech.MSMS.Domain.Clients
AppTech.MSMS.Domain.Clients
Clients
Clients
<<type>>
AppTech.MSMS.Domain.test
AppTech.MSMS.Domain.test
test
test
vVOArh18CI
x
<<type>>
AppTech.MSMS.Domain.Context
AppTech.MSMS.Domain.Context
Context
Context
<<type>>
AppTech.MSMS.Domain.CuredBy
AppTech.MSMS.Domain.CuredBy
CuredBy
CuredBy
<<type>>
AppTech.MSMS.Domain.TopupClass
AppTech.MSMS.Domain.TopupClass
TopupClass
TopupClass
<<type>>
AppTech.MSMS.Domain.TopupCurer
AppTech.MSMS.Domain.TopupCurer
TopupCurer
TopupCurer
<<type>>
AppTech.MSMS.Domain.TopupType
AppTech.MSMS.Domain.TopupType
TopupType
TopupType
<<type>>
AppTech.MSMS.Domain.TopupServiceType
AppTech.MSMS.Domain.TopupServiceType
TopupServiceType
TopupServiceType
<<type>>
AppTech.MSMS.Domain.TopupMethod
AppTech.MSMS.Domain.TopupMethod
TopupMethod
TopupMethod
<<type>>
AppTech.MSMS.Domain.LineType
AppTech.MSMS.Domain.LineType
LineType
LineType
<<type>>
AppTech.MSMS.Domain.ClientStatus
AppTech.MSMS.Domain.ClientStatus
ClientStatus
ClientStatus
<<type>>
AppTech.MSMS.Domain.ProductStatus
AppTech.MSMS.Domain.ProductStatus
ProductStatus
ProductStatus
<<type>>
AppTech.MSMS.Domain.ProductType
AppTech.MSMS.Domain.ProductType
ProductType
ProductType
<<type>>
AppTech.MSMS.Domain.TransferType
AppTech.MSMS.Domain.TransferType
TransferType
TransferType
<<type>>
AppTech.MSMS.Domain.InvoiceType
AppTech.MSMS.Domain.InvoiceType
InvoiceType
InvoiceType
<<type>>
AppTech.MSMS.Domain.PaidState
AppTech.MSMS.Domain.PaidState
PaidState
PaidState
<<type>>
AppTech.MSMS.Domain.ProviderType
AppTech.MSMS.Domain.ProviderType
ProviderType
ProviderType
<<type>>
AppTech.MSMS.Domain.GroupType
AppTech.MSMS.Domain.GroupType
GroupType
GroupType
<<type>>
AppTech.MSMS.Domain.AccountState
AppTech.MSMS.Domain.AccountState
AccountState
AccountState
<<type>>
AppTech.MSMS.Domain.ItemState
AppTech.MSMS.Domain.ItemState
ItemState
ItemState
<<type>>
AppTech.MSMS.Domain.BagatType
AppTech.MSMS.Domain.BagatType
BagatType
BagatType
<<type>>
AppTech.MSMS.Domain.ClientType
AppTech.MSMS.Domain.ClientType
ClientType
ClientType
<<type>>
AppTech.MSMS.Domain.CommissionType
AppTech.MSMS.Domain.CommissionType
CommissionType
CommissionType
<<type>>
AppTech.MSMS.Domain.SimStatus
AppTech.MSMS.Domain.SimStatus
SimStatus
SimStatus
<<type>>
AppTech.MSMS.Domain.LoanStatus
AppTech.MSMS.Domain.LoanStatus
LoanStatus
LoanStatus
<<type>>
AppTech.MSMS.Domain.BillStatus
AppTech.MSMS.Domain.BillStatus
BillStatus
BillStatus
<<type>>
AppTech.MSMS.Domain.TopupStatus
AppTech.MSMS.Domain.TopupStatus
TopupStatus
TopupStatus
<<type>>
AppTech.MSMS.Domain.ExchangeType
AppTech.MSMS.Domain.ExchangeType
ExchangeType
ExchangeType
<<type>>
AppTech.MSMS.Domain.OfferType
AppTech.MSMS.Domain.OfferType
OfferType
OfferType
<<type>>
AppTech.MSMS.Domain.EntryStatus
AppTech.MSMS.Domain.EntryStatus
EntryStatus
EntryStatus
<<type>>
AppTech.MSMS.Domain.PaymentStatus
AppTech.MSMS.Domain.PaymentStatus
PaymentStatus
PaymentStatus
<<type>>
AppTech.MSMS.Domain.PaymentMode
AppTech.MSMS.Domain.PaymentMode
PaymentMode
PaymentMode
<<type>>
AppTech.MSMS.Domain.OrderStatus
AppTech.MSMS.Domain.OrderStatus
OrderStatus
OrderStatus
<<type>>
AppTech.MSMS.Domain.PartyStatus
AppTech.MSMS.Domain.PartyStatus
PartyStatus
PartyStatus
<<type>>
AppTech.MSMS.Domain.UserStatus
AppTech.MSMS.Domain.UserStatus
UserStatus
UserStatus
<<type>>
AppTech.MSMS.Domain.RemittancePointType
AppTech.MSMS.Domain.RemittancePointType
RemittancePointType
RemittancePointType
<<type>>
AppTech.MSMS.Domain.RemittanceType
AppTech.MSMS.Domain.RemittanceType
RemittanceType
RemittanceType
<<type>>
AppTech.MSMS.Domain.RemittanceStatus
AppTech.MSMS.Domain.RemittanceStatus
RemittanceStatus
RemittanceStatus
<<type>>
AppTech.MSMS.Domain.UserType
AppTech.MSMS.Domain.UserType
UserType
UserType
<<type>>
AppTech.MSMS.Domain.MasterService
AppTech.MSMS.Domain.MasterService
MasterService
MasterService
<<type>>
AppTech.MSMS.Domain.PartyPortalType
AppTech.MSMS.Domain.PartyPortalType
PartyPortalType
PartyPortalType
<<type>>
AppTech.MSMS.Domain.CustomerBusinessType
AppTech.MSMS.Domain.CustomerBusinessType
CustomerBusinessType
CustomerBusinessType
<<type>>
AppTech.MSMS.Domain.YmLoanStatus
AppTech.MSMS.Domain.YmLoanStatus
YmLoanStatus
YmLoanStatus
<<type>>
AppTech.MSMS.Domain.SimType
AppTech.MSMS.Domain.SimType
SimType
SimType
<<type>>
AppTech.MSMS.Domain.BalanceState
AppTech.MSMS.Domain.BalanceState
BalanceState
BalanceState
<<type>>
AppTech.MSMS.Domain.SatellitePayStatus
AppTech.MSMS.Domain.SatellitePayStatus
SatellitePayStatus
SatellitePayStatus
<<type>>
AppTech.MSMS.Domain.Path
AppTech.MSMS.Domain.Path
Path
Path
<<type>>
AppTech.MSMS.Domain.Helper
AppTech.MSMS.Domain.Helper
Helper
Helper
XIDAQ5Srgh
GetChannel
<<type>>
AppTech.MSMS.Domain.XmlProvider
AppTech.MSMS.Domain.XmlProvider
XmlProvider
XmlProvider
<<type>>
AppTech.MSMS.Domain.Updates.DbScriptManager
AppTech.MSMS.Domain.Updates.DbScriptManager
DbScriptManager
DbScriptManager
sEPAFOqnuM
SaveScriptVersion
NcwAVvrRYy
Execute
ISgARaPZlx
DbScriptTable
<<type>>
AppTech.MSMS.Domain.Updates.DbScriptManager2017
AppTech.MSMS.Domain.Updates.DbScriptManager2017
DbScriptManager2017
DbScriptManager2017
GMjAfrJEyc
SaveScriptVersion
TsZAebjT6m
Execute
tfOAxHJGrA
DbScriptTable
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater
AppTech.MSMS.Domain.Updates.DbUpdater
DbUpdater
DbUpdater
qpBAsxlul3
updateClientDistrub
tqOAOgCjA8
updateClientExtra
NaDAhH0lei
foratAccountPermission
OXGA64qEpm
AddPages
M6UA4gbJ3E
WifiPaymentUpdates
lNSAmFhwmZ
TopupPermissions
bWpA7osLoy
OrderNewPermissions
t8EAWLUC8e
UpdateScripts
mvAAnjytOw
CreateVersionTable
MreAzo4sRi
CreateLoginAttempts
z9QUGlGOhj
IsSmoExist
IIHUAwT7Cn
UpdateBatches
r8VUUEgQqu
FixPointsParentAccounts
OUDUpJq0OK
ServiceCliam_SetAccountID_AsNullable
fXrU9FyFHo
ServiceCliamNewFields
sx7UiGn99B
TopupCommission_Add_Field
j0rUI8Smw5
AddReceiveAnyTransfer
pNaUHqH0xW
AddCheckLoanService
LteUux8fVA
RemoveTopup_lineType_Check
uNTUNchSnw
SimInvoiceUpdates_v2
p3CUDJZ2Wh
SimUpdates
YA8UtUK4v1
JournalUpdates
dbAUdWTsT4
SimInvoiceUpdates
De1USQdcYS
ProviderTableUpdates
iRwUkXeWWk
BagatTableUpdates
ACrUP1Nism
Execute
s0pUjbSkjH
Execute
veqUavx9Dt
Exist
ScvUYKZ8ab
ExecuteConstraint
u4yUwXtcFa
ExecuteIndex
gg0U0NA6DB
InitDocuments
GyBUqsf5jT
DbScriptTable
<<type>>
AppTech.MSMS.Domain.Topup.TopupFactory
AppTech.MSMS.Domain.Topup.TopupFactory
TopupFactory
TopupFactory
<<type>>
AppTech.MSMS.Domain.JournalEntries.JournalEntryModel
AppTech.MSMS.Domain.JournalEntries.JournalEntryModel
JournalEntryModel
JournalEntryModel
ndgUMirb5c
<ID>k__BackingField
tsVUXnd2hj
<UserID>k__BackingField
ba3UoKr5aM
<RecordID>k__BackingField
TrdULkuaXL
<RecordNumber>k__BackingField
calUZtEHjN
<Date>k__BackingField
iS6UKpx5v2
<Note>k__BackingField
bMEUyXk6U3
<IsDebited>k__BackingField
KS6Uv1EURC
<Entries>k__BackingField
LX7U2v70go
<RefNumber>k__BackingField
WJTU8JVbLK
<ReqType>k__BackingField
<<type>>
AppTech.MSMS.Domain.JournalEntries.Entry
AppTech.MSMS.Domain.JournalEntries.Entry
Entry
Entry
rUxU3bSPVK
<Amount>k__BackingField
whNUbS1uuD
<CurrencyID>k__BackingField
bbDUEl8t6Q
<CreditorAccountID>k__BackingField
rZ3U5V4o3N
<DebitorAccountID>k__BackingField
EJTUBZdb7i
<DebitorNote>k__BackingField
OiwUl6nLBr
<CreditorNote>k__BackingField
<<type>>
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager
JournalEntryManager
JournalEntryManager
VSUUTbbPl1
GetVoucherId
JUXUCrU3dB
AddEntries
qinUgaHaXg
EditJournalEntry
lZ1UcvWne3
_session
C6tUJjq5YR
_voucherId
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi
AppTech.MSMS.Domain.Providers.AlAssedApi
AlAssedApi
AlAssedApi
WwjU1Wc5kX
GenerateToken
TpjUrIHcZn
BuildGomlaParam
YlsUQGhdKa
OnPaymentResponse
Dk2UFQyhMx
BalanceQuery
ETfUVtGRUc
QueryYmOffers
EgYURtMP0E
BuildQueryPara
OB9UffPW3k
GetQueryUrl
PTbUeoAIio
OnBalanceQueryResponse
AYBUxOToNE
OnOfferQueryRespone
wXiUsH84yW
ParseAdslBalance
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi
AppTech.MSMS.Domain.Providers.AlbayanApi
AlbayanApi
AlbayanApi
IbdUOUs7gs
OnResponse
zdjUhiN9t5
serviceClient
qJVU6X3egL
_disposed
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2
AppTech.MSMS.Domain.Providers.AlbayanApi_v2
AlbayanApi_v2
AlbayanApi_v2
wJUU4C9Ttc
OnResponse
jKqUmWW3gq
serviceClient
ymUU7tkp7n
_disposed
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi
AppTech.MSMS.Domain.Providers.AlhashediApi
AlhashediApi
AlhashediApi
yvgUWdgFFG
BuildGomlaParam
PSAUnjQxyv
BuildPaymentParam
TtMUz0dNhF
OnPaymentResponse
MWMpGNvdUa
BalanceQuery
uIipAF4Sao
BuildQueryPara
fLwpUoLJoF
GetQueryUrl
LTmppqtU02
OnBalanceQueryResponse
k3Xp9UBXjL
ParseAdslBalance
qjvpi7Yl9w
BuildBagatParam
GahpIu4If7
GetBagatUrl
CDmpHH7CR2
OnBagatResponse
rOJpuWTKd7
OnYmBagatResponse
FxipNmbfFO
QueryYmOffers
NXbpDLSpD0
OnOfferQueryRespone
a5DptjVJHG
GenerateToken
TgDpdigNyX
ParseErrorMessage
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI
AppTech.MSMS.Domain.Providers.EasyConnectAPI
EasyConnectAPI
EasyConnectAPI
w4lpS7xc7j
OnPaymentResponse
BpLpkyQCau
OnCheckResponse
HZKpP5l63I
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM
AppTech.MSMS.Domain.Providers.EasySIM_YRM
EasySIM_YRM
EasySIM_YRM
FGxpjpRuIu
OnPaymentResponse
n5Upac1unO
OnCheckResponse
V8HpYrr9wg
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi
AppTech.MSMS.Domain.Providers.GMaxApi
GMaxApi
GMaxApi
CY4pweXCQF
OnPaymentResponse
Hhep0NYePA
OnQueryResponse
OVvpqKA6Am
OnBalanceQueryResponse
vQhpMynXZO
OnOfferQueryRespone
kIUpX9JaRs
ParseAdslBalance
YZ7porMSYL
OnBagatResponse
Q7fpLeqd6A
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM
AppTech.MSMS.Domain.Providers.EasySIM
EasySIM
EasySIM
rBYpZg9hpc
OnPaymentResponse
qXXpKAZcJ6
OnCheckResponse
DmNpytCOiv
Timeout
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi
AppTech.MSMS.Domain.Providers.AbuOsamaApi
AbuOsamaApi
AbuOsamaApi
wAVpcCK2hT
get_mAccessToken
wOgpvRZscL
BuildPaymentParam
aYPp2jfwUn
OnPaymentResponse
HDjp8bTb8l
BalanceQuery
LERp3xCXE1
OnBalanceQueryResponse
WLYpbLWInI
ParseAdslBalance
tOZpEi4XCs
QueryYmOffers
eiVp5ogs0g
OnOfferQueryRespone
blgpBU75eO
OnBagatResponse
JqoplUMb7V
BuildBagatPara
sDNpTBW19R
ParseErrorMessage
pEkpC0pqlG
GenerateToken
S4hpgRkZrY
BalanceQueryAsync
VuRpJvYNkT
mAccessToken
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi
AppTech.MSMS.Domain.Providers.AlConApi
AlConApi
AlConApi
MYEp1AA657
OnTopupRespone
Gx4prybUSD
OnBalanceRespons
LFopQ7ojYl
MakeMtnOffer
Us2pF3Zypr
parseErrorResponse
NdvpVRVTu6
QueryYmOffers
AjepRuvi7U
YMOffer
J0lpf4Tx8g
Balance
I4jpedgRyu
isApi
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI
AppTech.MSMS.Domain.Providers.RialMobileAPI
RialMobileAPI
RialMobileAPI
FkupxAoHWj
OnResponse
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi
AppTech.MSMS.Domain.Providers.SharabiApi
SharabiApi
SharabiApi
qZB9UlHHrI
get_mAccessToken
IeJpsewXVo
BuildPaymentParam
v6CpOkfTIm
OnPaymentResponse
yEEphxXZx3
BalanceQuery
V3fp6dYIB3
OnBalanceQueryResponse
xTdp4V3ovB
ParseAdslBalance
GN7pm1yg40
QueryYmOffers
ujhp7rC6EI
OnOfferQueryRespone
hnDpWVaunp
OnBagatResponse
NpVpnUAKAn
BuildBagatPara
puTpzgXWdN
ParseErrorMessage
dDW9GGqKYR
GenerateToken
tOd9AmuMbn
BalanceQueryAsync
L1Q9p4XWA2
mAccessToken
<<type>>
AppTech.MSMS.Domain.Providers.TopupProviderApi
AppTech.MSMS.Domain.Providers.TopupProviderApi
TopupProviderApi
TopupProviderApi
JTj99l0D5E
<TransactionID>k__BackingField
vYX9iUEvBi
<BaseUrl>k__BackingField
ggt9IKp0ND
<ProviderTransId>k__BackingField
IA09HSHeEr
<MyTransId>k__BackingField
qKA9uvfGNb
<RecordNumber>k__BackingField
EOn9N9vArC
_disposed
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor
AppTech.MSMS.Domain.Providers.TopupProccessor
TopupProccessor
TopupProccessor
tpW9DM2l6Z
TestQuery
I3O9tx4oqL
OnResponse
VMS9djoLmk
ParseError
AUt9SJqkJg
_mTopupProviderApi
pko9kSPaei
_disposed
<<type>>
b8wfMLkWmGBXutxot0.pMZfVOuJwUk6jfHyEx
AppTech.MSMS.Domain.Topuping.AlanwarAPI
pMZfVOuJwUk6jfHyEx
AlanwarAPI
Np49amvE3a
get_UserName
JX89YBiIeO
get_Password
jeR906rF8g
get_token
qpc9MeDuFY
get_baseUrl
AHa9PONbLD
OnPaymentResponse
Ipl9jKYabD
OnQueryResponse
pb99waq3EZ
Password
NYL9qpxffM
token
dWs9Xgm2VI
baseUrl
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi
AppTech.MSMS.Domain.Topuping.AlGumaiiApi
AlGumaiiApi
AlGumaiiApi
RPM9osUJkC
BuildPaymentParam
Tw79Lc8856
OnPaymentResponse
MHs9ZO7l16
BuildBalanceQuery
ppt9K81DmZ
BalanceQuery
yaW9yf4uE0
OnBalanceQueryResponse
IPn9vZB2Ot
ParseAdslBalance
kwJ92KSykZ
MakeSPBagat
F3S982FZsm
MakeMtnOffer
OV793XXb4q
OnBagatResponse
DeD9bd5mdc
QueryYmOffers
F1y9EByCAA
OnOfferQueryRespone
gKD95QBgGO
BalanceQueryAsync
O5a9BrIDKs
GenerateToken
CMj9lCb8Qh
ParseErrorMessage
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI
AppTech.MSMS.Domain.Topuping.AlmoheetAPI
AlmoheetAPI
AlmoheetAPI
xdC9TFZMLr
OnQueryResponse
gQt9CbX8ne
OnPaymentResponse
D7u9goDbBL
GetTransId
EZ19cToGPH
OnReportResponse
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetReportResponse
AppTech.MSMS.Domain.Topuping.AlmoheetReportResponse
AlmoheetReportResponse
AlmoheetReportResponse
vpM9JQAta0
<SERVICE_NAME>k__BackingField
mDJ91Ue5fH
<TRANS_DATE>k__BackingField
IfA9rfKYmu
<MOBILE>k__BackingField
lSl9QuxHMv
<AMOUNT>k__BackingField
tAV9Fy9Zbl
<NOTE>k__BackingField
TLK9VWaaH9
<STATUS>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetReportRequest
AppTech.MSMS.Domain.Topuping.AlmoheetReportRequest
AlmoheetReportRequest
AlmoheetReportRequest
JlU9R7fXL9
<serviceId>k__BackingField
DH99f7JQwD
<MOBILE>k__BackingField
SeG9egEgKF
<FROM_DATE>k__BackingField
F3c9xm8dn5
<TO_DATE>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetTopupRequest
AppTech.MSMS.Domain.Topuping.AlmoheetTopupRequest
AlmoheetTopupRequest
AlmoheetTopupRequest
Plq9sHloGN
<MOBILE>k__BackingField
d5s9OFqy3H
<TRANS_TYPE>k__BackingField
T8B9hBtbx0
<Amount>k__BackingField
PdN96nOPLT
<RequestType>k__BackingField
S5194wKTle
<transId>k__BackingField
FKp9mXLJ8I
<ProductId>k__BackingField
Ke097hhwhN
<UserId>k__BackingField
m8s9WqLpyJ
<passowrd>k__BackingField
QcE9nm83YC
<FROM_DATE>k__BackingField
hDn9zCXLla
<TO_DATE>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi
AppTech.MSMS.Domain.Topuping.AlMutarrebApi
AlMutarrebApi
AlMutarrebApi
gcLiGSuCni
OnPaymentReponse
fEkiA5ACQc
OnOfferReponse
bV3iU7o4GP
serviceClient
HjdipkHc4i
action
Mrhi9rqm7e
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebServices
AppTech.MSMS.Domain.Topuping.AlmutarrebServices
AlmutarrebServices
AlmutarrebServices
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2
AlmutarrebAPI_v2
AlmutarrebAPI_v2
DapiiKVw4k
OnPaymentReponse
o0DiIfOPCw
OnOfferReponse
Oa0iHuStuO
serviceClient
pibiuQ215l
action
yc3iNH2paE
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebV2Services
AppTech.MSMS.Domain.Topuping.AlmutarrebV2Services
AlmutarrebV2Services
AlmutarrebV2Services
<<type>>
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi
AlSareeaOnLineApi
AlSareeaOnLineApi
lIaiDQ1kBJ
OnResponse
Y4eit9cpes
serviceClient
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi_v2
AppTech.MSMS.Domain.Topuping.AppTechApi_v2
AppTechApi_v2
AppTechApi_v2
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi_v3
AppTech.MSMS.Domain.Topuping.AppTechApi_v3
AppTechApi_v3
AppTechApi_v3
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi
AppTech.MSMS.Domain.Topuping.AtheerApi
AtheerApi
AtheerApi
DJiidqIsIa
serviceClient
U3qiS71kD3
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_Riyal
AppTech.MSMS.Domain.Topuping.AtheerApi_Riyal
AtheerApi_Riyal
AtheerApi_Riyal
jTEikyfMDo
serviceClient
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South
AppTech.MSMS.Domain.Topuping.AtheerApi_South
AtheerApi_South
AtheerApi_South
vpxiP2e7Mq
OnResponse
TqeijxTgrg
serviceClient
s7miaeKlXl
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2
AtheerApi_South_v2
AtheerApi_South_v2
qdJiYAYy3N
OnResponse
e7eiwdRVn0
serviceClient
W8Qi0lKuMH
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2
AppTech.MSMS.Domain.Topuping.AtheerApi_v2
AtheerApi_v2
AtheerApi_v2
r0aiqRPxq8
OnResponse
o2uiMWQEoj
serviceClient
Fw1iX6Jmx6
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3
AppTech.MSMS.Domain.Topuping.AtheerApi_v3
AtheerApi_v3
AtheerApi_v3
H4CioFrTYV
OnResponse
vRsiLIKTnm
serviceClient
AfEiZ9s3Si
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi
AppTech.MSMS.Domain.Topuping.BaAmerApi
BaAmerApi
BaAmerApi
OReiKjdDqM
OnPaymentReponse
c6kiyFFiZT
OnOfferReponse
zBbivk6MdI
serviceClient
iHEi2StqsD
action
Dh2i8VEyPk
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerServices
AppTech.MSMS.Domain.Topuping.BaAmerServices
BaAmerServices
BaAmerServices
<<type>>
AppTech.MSMS.Domain.Topuping.FrenchiApi
AppTech.MSMS.Domain.Topuping.FrenchiApi
FrenchiApi
FrenchiApi
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI
AppTech.MSMS.Domain.Topuping.ForMeAPI
ForMeAPI
ForMeAPI
T6Li32kkfs
OnResponse
Rpuib6xRyB
serviceClient
qL5iEjQPkw
_disposed
<<type>>
oqGKTHOyseFvcT5oIk.t6dxjs7yyR0waOpuFa
AppTech.MSMS.Domain.Topuping.JuzaifaAPI
t6dxjs7yyR0waOpuFa
JuzaifaAPI
oTeiTfni1w
get_UserID
Ds8igQDuno
get_password
QgTicYh1Vi
get_vClientID
Onyi5VC4N0
BuildHeader
LkuiB8RD45
BuildBody
OoLil7nJmE
GetErrorCodeMsg
kLGi1MZF1m
serviceClient
UseiCWUjiv
UserID
TxhiJK3hrL
vClientID
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi
AppTech.MSMS.Domain.Topuping.MainCenterApi
MainCenterApi
MainCenterApi
XyfirSnK4V
GetPath
JkyiQPm5i5
OpResponse
sgQiFuB0dP
GetBagatNumber
cS3iV8eKFB
GenerateToken
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi
AppTech.MSMS.Domain.Topuping.AppTechApi
AppTechApi
AppTechApi
niMiRPRG8j
GenerateToken
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi
AppTech.MSMS.Domain.Topuping.OnsSoftApi
OnsSoftApi
OnsSoftApi
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi
AppTech.MSMS.Domain.Topuping.QulaidiApi
QulaidiApi
QulaidiApi
sptifwmxo2
serviceClient
OjLiew959S
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulApi
AppTech.MSMS.Domain.Topuping.TadawulApi
TadawulApi
TadawulApi
QdwixJirQ4
OnResponse
FDoisOWk5v
_proxy
nyIiOIkfXB
<Header>k__BackingField
DH4ihRnt6O
_disposed
eWfi6oAHiY
action
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulServiceType
AppTech.MSMS.Domain.Topuping.TadawulServiceType
TadawulServiceType
TadawulServiceType
<<type>>
AppTech.MSMS.Domain.Topuping.TopupException
AppTech.MSMS.Domain.Topuping.TopupException
TopupException
TopupException
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper
AppTech.MSMS.Domain.Topuping.TopupHelper
TopupHelper
TopupHelper
Fu9i4UmNS6
QueryMtnInfo
SIJimXqo5W
TranslateOffers
ypvi7gCmih
GetExecutionPeriod
Q0DiW3lbei
GetExceptionStatus
lG5inWponj
_session
<<type>>
AppTech.MSMS.Domain.Topuping.Providers
AppTech.MSMS.Domain.Topuping.Providers
Providers
Providers
<<type>>
AppTech.MSMS.Domain.Topuping.TelNetwork
AppTech.MSMS.Domain.Topuping.TelNetwork
TelNetwork
TelNetwork
<<type>>
AppTech.MSMS.Domain.Topuping.Services
AppTech.MSMS.Domain.Topuping.Services
Services
Services
<<type>>
AppTech.MSMS.Domain.Topuping.TopupUtils
AppTech.MSMS.Domain.Topuping.TopupUtils
TopupUtils
TopupUtils
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI
AppTech.MSMS.Domain.Topuping.YemenPostAPI
YemenPostAPI
YemenPostAPI
mqQizwowvS
OnResponse
NO6IGN1vCg
OnOfferResponse
TtPIAlijBW
InitiCertificate
Nx0IUQLdV7
SaveProviderPublicKey
epaIpRUanp
serviceClient
X1UI9lR8iQ
keySize
j07IijQUTM
_optimalAsymmetricEncryptionPadding
DTUIIEZjPS
publicKey
DaHIHoNT8F
requestInfo
JBLIudNBkA
_disposed
<<type>>
AppTech.MSMS.Domain.Topuping.YemenSaeedApi
AppTech.MSMS.Domain.Topuping.YemenSaeedApi
YemenSaeedApi
YemenSaeedApi
<<type>>
AppTech.MSMS.Domain.Topuping.YmLoanResponse
AppTech.MSMS.Domain.Topuping.YmLoanResponse
YmLoanResponse
YmLoanResponse
N8nINq8Klu
<Message>k__BackingField
rZ1ID3t8Mi
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure
TopupClosure
TopupClosure
dHAItMfW4O
SaveBalance
XwNIdLk9LF
Log
<<type>>
AppTech.MSMS.Domain.Sync.EbsAccountService
AppTech.MSMS.Domain.Sync.EbsAccountService
EbsAccountService
EbsAccountService
<<type>>
AppTech.MSMS.Domain.Sync.EbsJournalManager
AppTech.MSMS.Domain.Sync.EbsJournalManager
EbsJournalManager
EbsJournalManager
<<type>>
AppTech.MSMS.Domain.Sync.RemittanceSync
AppTech.MSMS.Domain.Sync.RemittanceSync
RemittanceSync
RemittanceSync
gf1ISxwInI
GetReceivedTargetId
sv7IkM84Zv
Throw
xLfIPu9Oq9
InitPaged
r2uIjLPjQm
CreatePaged
XRZIaRFIZH
GetCurrencyName
t11IYM4kPM
PendingTransfer
Mb7Iwgtn97
AddEntryDetails
lqgI0MBLHA
_dbHelper
<<type>>
AppTech.MSMS.Domain.Sync.SyncResult
AppTech.MSMS.Domain.Sync.SyncResult
SyncResult
SyncResult
ymHIq6eWYa
<ID>k__BackingField
pVOIMIIe76
<TargetID>k__BackingField
Hn4IXPFyQj
<SourceID>k__BackingField
gnGIoPWGaG
<RecordNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sync.SyncSmsDb
AppTech.MSMS.Domain.Sync.SyncSmsDb
SyncSmsDb
SyncSmsDb
<<type>>
AppTech.MSMS.Domain.Sync.SyncBalanceSheetReport
AppTech.MSMS.Domain.Sync.SyncBalanceSheetReport
SyncBalanceSheetReport
SyncBalanceSheetReport
jJGILiv16v
GetGrossBalanceSheet
dyWIZTVAkP
GetDetailedBalanceSheet
HCSIKNK9Pi
BuildDetialedQuery
UbhIyb16oS
BuildDetialedQueryAllCurrencies
O6QIvYm8Jg
Query
ODlI2Ly2F9
_db
Q9YI8fV4mr
EntriesView
<<type>>
AppTech.MSMS.Domain.Sync.SyncDb
AppTech.MSMS.Domain.Sync.SyncDb
SyncDb
SyncDb
R9KI3HgyHP
CreateAccount
ojlIbmj4qR
GenerateAccountNumber
IUsIEfH6A2
DeleteAccount
<<type>>
AppTech.MSMS.Domain.Sync.SyncJournalManager
AppTech.MSMS.Domain.Sync.SyncJournalManager
SyncJournalManager
SyncJournalManager
CpvI5eTZAf
GetVoucher
ul3IBfXtu0
AddDebitedEntry
fdwIlrW7qs
AddCreditedEntry
INGITvdN3L
DeleteJournal
YgiIC4ji87
IsEntryExist
TL5IgYILv3
_session
<<type>>
AppTech.MSMS.Domain.Sync.SyncSetting
AppTech.MSMS.Domain.Sync.SyncSetting
SyncSetting
SyncSetting
FfTIcswgdE
_dbHelper
bL7IJ0gtmR
<TopupAccountID>k__BackingField
RVhI14Jyo3
<CurrencyExchageAccountID>k__BackingField
vNvIrg3DSM
<CashDepositAccountID>k__BackingField
AVVIQR1kG7
<CommissionAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.BaseSetting`1
AppTech.MSMS.Domain.Settings.BaseSetting`1
BaseSetting`1
BaseSetting`1
n3RIFuwpok
<SettingName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.CardSetting
AppTech.MSMS.Domain.Settings.CardSetting
CardSetting
CardSetting
RTEIVKLLnb
<SettingName>k__BackingField
BA6IRtlbNV
<PrafitAccount>k__BackingField
QTaIfgWbtv
<DisableGames>k__BackingField
BIYIe2xr4h
<DisableCards>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.CashTransSetting
AppTech.MSMS.Domain.Settings.CashTransSetting
CashTransSetting
CashTransSetting
T1JIxP6SGu
<CashTransferSuspended>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.MainSetting`1
AppTech.MSMS.Domain.Settings.MainSetting`1
MainSetting`1
MainSetting`1
<<type>>
AppTech.MSMS.Domain.Settings.SimSetting
AppTech.MSMS.Domain.Settings.SimSetting
SimSetting
SimSetting
XukIs0XNkH
<SettingName>k__BackingField
Ar5IOmEqnZ
<NewSimPrice>k__BackingField
pxOIhPvKPa
<ReplaceSimPrice>k__BackingField
MnvI63wRRo
<RefundInReplaceSim>k__BackingField
IggI4NpAsR
<OpenSimCardNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.SmsSetting
AppTech.MSMS.Domain.Settings.SmsSetting
SmsSetting
SmsSetting
<<type>>
AppTech.MSMS.Domain.Settings.TopupSetting
AppTech.MSMS.Domain.Settings.TopupSetting
TopupSetting
TopupSetting
rcLImUMCJt
<RestrictRepeatTime>k__BackingField
i9uI7Ei9VK
<AllowtRepeatAmount>k__BackingField
gtIIWh9BYx
<ExchangeAccountID>k__BackingField
gtQInQI6eN
<SeparateYmQuotaBagat>k__BackingField
bNxIz9MhI7
<NotifyAfterTopup>k__BackingField
gMuHGfrx8X
<TopupAsOrder>k__BackingField
tGwHAjHnrC
<DisableTopup>k__BackingField
kneHUE1Lx3
<ElectriciyLimitPercent>k__BackingField
CWQHpB899b
<Topup_DisableGomala>k__BackingField
XrjH9kaAqC
<Topup_PreventGomalaForPoints>k__BackingField
waXHibgAT6
<Topup_PreventGomalaForClients>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.SatelliteSetting
AppTech.MSMS.Domain.Settings.SatelliteSetting
SatelliteSetting
SatelliteSetting
xbOHIZLAxp
<SettingName>k__BackingField
HoiHHtMlD0
<PrafitAccount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Settings.WifiSetting
AppTech.MSMS.Domain.Settings.WifiSetting
WifiSetting
WifiSetting
TlFHujx9uR
<SettingName>k__BackingField
bX2HNiFDJ0
<PrafitAccount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService
AppTech.MSMS.Domain.Sessions.LoginService
LoginService
LoginService
k8THDEM4cs
BlockDevice
AHMHth8E23
IsDevicePermitted
JNVHd4Wxw8
_credentials
pq7HSUyix3
_disposed
Pf6Hka17Na
_unitOfWork
<<type>>
AppTech.MSMS.Domain.Sessions.AdminSession
AppTech.MSMS.Domain.Sessions.AdminSession
AdminSession
AdminSession
<<type>>
AppTech.MSMS.Domain.Sessions.UserSession
AppTech.MSMS.Domain.Sessions.UserSession
UserSession
UserSession
VuQHPAoiWA
OnSuccess
SyyHjKcGSO
InitBranching
ES1HaKJuT1
<IncludeBalance>k__BackingField
xPGHYteb64
<Type>k__BackingField
UrcHwPo2B3
<Party>k__BackingField
ablH0l1RMO
<CurrentBalance>k__BackingField
RWrHqrIcnG
<IsPrimary>k__BackingField
XHwHMqaAp1
<IsPOR>k__BackingField
jjAHXLhk14
<RemittancePoint>k__BackingField
pnWHoPOEFV
<Claims>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sessions.AgentSession
AppTech.MSMS.Domain.Sessions.AgentSession
AgentSession
AgentSession
rCxHZml8oM
set_Agent
lbpHLfqfu8
ValidateAgent
bjsHKrw2H0
<Agent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Sessions.MerchantSession
AppTech.MSMS.Domain.Sessions.MerchantSession
MerchantSession
MerchantSession
sJTHySa2qB
set_Merchant
CORHvYasOo
<Merchant>k__BackingField
<<type>>
AppTech.MSMS.Domain.Rest.GenericApi
AppTech.MSMS.Domain.Rest.GenericApi
GenericApi
GenericApi
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest
AppTech.MSMS.Domain.Rest.ApiRequest
ApiRequest
ApiRequest
J6FH2V6dxd
InitRestClient
fN9H8CTHf2
<TransactionID>k__BackingField
tpMH34nVXG
mHeaders
iCLHbZOnrQ
_url
lHvHEUqPBT
_disposed
<<type>>
AppTech.MSMS.Domain.Reports.AccountBalanceReport
AppTech.MSMS.Domain.Reports.AccountBalanceReport
AccountBalanceReport
AccountBalanceReport
<<type>>
AppTech.MSMS.Domain.Reports.AccountReport
AppTech.MSMS.Domain.Reports.AccountReport
AccountReport
AccountReport
<<type>>
AppTech.MSMS.Domain.Reports.DataList
AppTech.MSMS.Domain.Reports.DataList
DataList
DataList
sCvH5p6auE
<Name>k__BackingField
jxnHBltAXE
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.AgentsReport
AppTech.MSMS.Domain.Reports.AgentsReport
AgentsReport
AgentsReport
FyBHlSEyiJ
GetDetails
sT8HT9hwpm
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.BalancesReport
AppTech.MSMS.Domain.Reports.BalancesReport
BalancesReport
BalancesReport
<<type>>
AppTech.MSMS.Domain.Reports.CardReport
AppTech.MSMS.Domain.Reports.CardReport
CardReport
CardReport
PyOHCSLRlH
GetDetails
Kd9HguQEwP
GetGross
h18HcToGqg
GetOrderDetails
VjAHJUt8FP
GetOrderGross
<<type>>
AppTech.MSMS.Domain.Reports.CurrencyExchangeReport
AppTech.MSMS.Domain.Reports.CurrencyExchangeReport
CurrencyExchangeReport
CurrencyExchangeReport
c6AH1MG3a7
GetGross
olXHroDWCx
GetDetails
<<type>>
AppTech.MSMS.Domain.Reports.FundReport
AppTech.MSMS.Domain.Reports.FundReport
FundReport
FundReport
LdMHQyb6Ev
GetGrossBalanceSheet
<<type>>
AppTech.MSMS.Domain.Reports.IdleAccountsReport
AppTech.MSMS.Domain.Reports.IdleAccountsReport
IdleAccountsReport
IdleAccountsReport
<<type>>
AppTech.MSMS.Domain.Reports.InvoiceReport
AppTech.MSMS.Domain.Reports.InvoiceReport
InvoiceReport
InvoiceReport
mVqHFxONHW
GetConsumeResult
<<type>>
AppTech.MSMS.Domain.Reports.PointsBalanceReport
AppTech.MSMS.Domain.Reports.PointsBalanceReport
PointsBalanceReport
PointsBalanceReport
<<type>>
AppTech.MSMS.Domain.Reports.SimReport
AppTech.MSMS.Domain.Reports.SimReport
SimReport
SimReport
TDTHVsKVKv
GetDetails
dsKHRUcDiE
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.TopupAccountsReport
AppTech.MSMS.Domain.Reports.TopupAccountsReport
TopupAccountsReport
TopupAccountsReport
ru8HfEbc6J
get_ReportView
oCOHeLw2IX
ReportView
<<type>>
AppTech.MSMS.Domain.Reports.TopupClosureReport
AppTech.MSMS.Domain.Reports.TopupClosureReport
TopupClosureReport
TopupClosureReport
<<type>>
AppTech.MSMS.Domain.Reports.TopupOrderReport
AppTech.MSMS.Domain.Reports.TopupOrderReport
TopupOrderReport
TopupOrderReport
wD1HxQKIBu
GetDetails
TgaHs8aZIK
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.VoucherReport
AppTech.MSMS.Domain.Reports.VoucherReport
VoucherReport
VoucherReport
oBcHOmsgNr
GetGross
V6KHhIBQxS
GetDetails
<<type>>
AppTech.MSMS.Domain.Reports.WalletReport
AppTech.MSMS.Domain.Reports.WalletReport
WalletReport
WalletReport
<<type>>
AppTech.MSMS.Domain.Reports.WifiReport
AppTech.MSMS.Domain.Reports.WifiReport
WifiReport
WifiReport
dZ7H6naEt2
GetDetails
I0lH4kFhX8
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.TransferReport
AppTech.MSMS.Domain.Reports.TransferReport
TransferReport
TransferReport
<<type>>
AppTech.MSMS.Domain.Reports.OrderReport
AppTech.MSMS.Domain.Reports.OrderReport
OrderReport
OrderReport
<<type>>
AppTech.MSMS.Domain.Reports.RemittanceReport
AppTech.MSMS.Domain.Reports.RemittanceReport
RemittanceReport
RemittanceReport
GRAHm1UETF
GetGross
QBtH7NpKCW
GetDetails
<<type>>
AppTech.MSMS.Domain.Reports.TopupReport
AppTech.MSMS.Domain.Reports.TopupReport
TopupReport
TopupReport
gfWHzp0iqO
get_ReportView
MKTuAJeVJf
get_ReportView_AllAccounts
vWIHWAy0vZ
BuildConditionByItem
Mq2HntGGfQ
BuildConditionForAgent
YNXuGuBpu4
ReportView
iocuUonXJF
ReportView_AllAccounts
<<type>>
AppTech.MSMS.Domain.Reports.TransactionReport
AppTech.MSMS.Domain.Reports.TransactionReport
TransactionReport
TransactionReport
Fbyup6usZ0
GetDetails
Wbcu9xZ0sp
GetGross
<<type>>
AppTech.MSMS.Domain.Reports.AgentTransReport
AppTech.MSMS.Domain.Reports.AgentTransReport
AgentTransReport
AgentTransReport
<<type>>
AppTech.MSMS.Domain.Reports.AgentTransModel
AppTech.MSMS.Domain.Reports.AgentTransModel
AgentTransModel
AgentTransModel
e8euiA5smy
<AgentID>k__BackingField
wahuI9DNPI
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.BalanceSheetReport
AppTech.MSMS.Domain.Reports.BalanceSheetReport
BalanceSheetReport
BalanceSheetReport
PKxuHTUsj9
GetGrossBalanceSheet
UcCuumW3Ar
GetGross
XEuuNFTrr2
GetPreBalanceDateCondition
ml9uDBpVIx
GetDetailedBalanceSheet
KrMutUFNni
GetDetails_AllCurrencies
Vbiudo3YfW
BuildDetialedQueryWithPreBalance_v2
GOnuS2Abwk
GetQueryParameters
<<type>>
AppTech.MSMS.Domain.Reports.Models.AccountBalanceModel
AppTech.MSMS.Domain.Reports.Models.AccountBalanceModel
AccountBalanceModel
AccountBalanceModel
nEVukufun8
<AccountID>k__BackingField
vh4uPB8mEk
<ActiveAccountID>k__BackingField
BarujRD2Sn
<MainAccountID>k__BackingField
jYtuaDrVUt
<CurrencyID>k__BackingField
dgDuYVHX4n
<GroupID>k__BackingField
PAcuwNklIc
<Type>k__BackingField
oY6u0RAmjv
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.BalanceStuates
AppTech.MSMS.Domain.Reports.Models.BalanceStuates
BalanceStuates
BalanceStuates
<<type>>
AppTech.MSMS.Domain.Reports.Models.AccountModel
AppTech.MSMS.Domain.Reports.Models.AccountModel
AccountModel
AccountModel
uncuqiyc9A
<Type>k__BackingField
EeuuM3EBBA
<Level>k__BackingField
JAouXVeKhK
<CurrencyID>k__BackingField
eNtuo7AqLh
<IgnoreZeroBalnces>k__BackingField
c8UuL7Wdtm
<TillNow>k__BackingField
TvNuZUR7wY
<ShowLevelAccountsOnly>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.AccountReportType
AppTech.MSMS.Domain.Reports.Models.AccountReportType
AccountReportType
AccountReportType
<<type>>
AppTech.MSMS.Domain.Reports.Models.CardModel
AppTech.MSMS.Domain.Reports.Models.CardModel
CardModel
CardModel
iwouKYL0hp
<State>k__BackingField
S6Vuy7tPF4
<AccountID>k__BackingField
N0JuvTttKH
<ReportType>k__BackingField
CPyu2DKoxi
<CardTypeID>k__BackingField
pECu8Fh1sd
<CardFactionID>k__BackingField
tTvu3YZg4i
<IsOrder>k__BackingField
eaqubgacqr
<GroupByAccounts>k__BackingField
OT1uEJqi1g
<GroupByTypes>k__BackingField
ClOu5VbKcD
<GroupByFactions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.CurrencyExchangeModel
AppTech.MSMS.Domain.Reports.Models.CurrencyExchangeModel
CurrencyExchangeModel
CurrencyExchangeModel
p5NuB6vII8
<ReportType>k__BackingField
IxpulGwkMu
<Type>k__BackingField
nvduTIYcnO
<CurrencyID>k__BackingField
N8yuCuixkW
<AccountID>k__BackingField
xqSugMZSYk
<ExchangeAccountID>k__BackingField
D3gucIsdQh
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.FundReportModel
AppTech.MSMS.Domain.Reports.Models.FundReportModel
FundReportModel
FundReportModel
yfCuJSnAKw
<AccountID>k__BackingField
tTuu1Lhwdx
<CurrencyID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.IdleAccountsModel
AppTech.MSMS.Domain.Reports.Models.IdleAccountsModel
IdleAccountsModel
IdleAccountsModel
OnFurYSgwk
<State>k__BackingField
o1CuQP6OMZ
<AccountID>k__BackingField
EAPuFRwGkv
<ReportType>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.InvoiceModel
AppTech.MSMS.Domain.Reports.Models.InvoiceModel
InvoiceModel
InvoiceModel
ioIuV4QOqe
<AccountID>k__BackingField
NkhuRt3Bqt
<Type>k__BackingField
kDhufLi4r4
<InvoiceType>k__BackingField
O2CueOuFdv
<PaidState>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.SimReportModel
AppTech.MSMS.Domain.Reports.Models.SimReportModel
SimReportModel
SimReportModel
mOruxFnLPA
<GroupByAccount>k__BackingField
KAWus7vbM1
<GroupByUser>k__BackingField
tCkuOwZqGk
<AccountID>k__BackingField
t8Yuhpf05E
<UserID>k__BackingField
aY2u6qUnHm
<SimNumber>k__BackingField
M3Qu4KSjHQ
<State>k__BackingField
Dr7ume1LMP
<SimType>k__BackingField
eZ5u7uhty0
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.SimState
AppTech.MSMS.Domain.Reports.Models.SimState
SimState
SimState
<<type>>
AppTech.MSMS.Domain.Reports.Models.SlatingReportModel
AppTech.MSMS.Domain.Reports.Models.SlatingReportModel
SlatingReportModel
SlatingReportModel
GmJuW2hbDn
<AccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TopupOrderModel
AppTech.MSMS.Domain.Reports.Models.TopupOrderModel
TopupOrderModel
TopupOrderModel
h85unqpmle
<AccountID>k__BackingField
YlBuzZqpuQ
<ServiceID>k__BackingField
mIBNGWebqO
<ReportType>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.VoucherModel
AppTech.MSMS.Domain.Reports.Models.VoucherModel
VoucherModel
VoucherModel
YeONAkVe9n
<Type>k__BackingField
EnCNUcwSmn
<AccountID>k__BackingField
wQkNpXEaQD
<CurrencyID>k__BackingField
lZmN9Sjq5p
<UserID>k__BackingField
LdHNiJnVKx
<ReportType>k__BackingField
DhONISi8Hx
<GroupByAccounts>k__BackingField
FTkNHERZwa
<GroupByUsers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.VoucherType
AppTech.MSMS.Domain.Reports.Models.VoucherType
VoucherType
VoucherType
<<type>>
AppTech.MSMS.Domain.Reports.Models.WalletReportModel
AppTech.MSMS.Domain.Reports.Models.WalletReportModel
WalletReportModel
WalletReportModel
tl4NuTITM1
<Type>k__BackingField
F8PNNLhi46
<CurrencyID>k__BackingField
QdnNDKxkHM
<AccountID>k__BackingField
prmNthpLRP
<AccountNumber>k__BackingField
WLCNd5GxAo
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.WalletType
AppTech.MSMS.Domain.Reports.Models.WalletType
WalletType
WalletType
<<type>>
AppTech.MSMS.Domain.Reports.Models.WifiModel
AppTech.MSMS.Domain.Reports.Models.WifiModel
WifiModel
WifiModel
g2aNSFIQ0d
<State>k__BackingField
TYWNkv4RLt
<AccountID>k__BackingField
BdkNPRyohP
<ReportType>k__BackingField
URINjCxLuB
<ProviderID>k__BackingField
EymNatiPwy
<FactionID>k__BackingField
hNoNYtNOny
<GroupByAccounts>k__BackingField
mA5Nwo910E
<GroupByProviders>k__BackingField
zWpN00lesb
<GroupByFactions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TransferModel
AppTech.MSMS.Domain.Reports.Models.TransferModel
TransferModel
TransferModel
W2GNqpZv0H
<External>k__BackingField
i69NMx6NSs
<InArabic>k__BackingField
O81NXMwjcD
<InOut>k__BackingField
jfbNodLEjx
<AccountID>k__BackingField
dojNL7PPfr
<UserID>k__BackingField
FRENZp5IpG
<CurrencyID>k__BackingField
PQINKiSiXn
<ExchangerID>k__BackingField
XwJNybMRgE
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.RemittReportModel
AppTech.MSMS.Domain.Reports.Models.RemittReportModel
RemittReportModel
RemittReportModel
xdUNv2uPIF
<ReportType>k__BackingField
CVcN2oyKmQ
<Type>k__BackingField
MMNN8XZGcX
<TargetID>k__BackingField
RYvN37ieve
<SourceID>k__BackingField
F0QNb3avbJ
<CurrencyID>k__BackingField
kpFNEfDcTG
<AccountID>k__BackingField
E98N5TBB8u
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.AuditLogModel
AppTech.MSMS.Domain.Reports.Models.AuditLogModel
AuditLogModel
AuditLogModel
ekvNBlwFvG
<UserID>k__BackingField
XaoNlh1HH3
<PageName>k__BackingField
srfNTZCeod
<Action>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.OrderModel
AppTech.MSMS.Domain.Reports.Models.OrderModel
OrderModel
OrderModel
RLSNCV3Ety
<AccountID>k__BackingField
PIkNggRm1Z
<ServiceID>k__BackingField
OHiNcmgufY
<OrderState>k__BackingField
nNsNJsqmL7
<Status>k__BackingField
ImfN197A6k
<IsMobile>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TopupModel
AppTech.MSMS.Domain.Reports.Models.TopupModel
TopupModel
TopupModel
R9cNr5LjoV
<AccountID>k__BackingField
xtINQk7yDr
<ServiceID>k__BackingField
egfNFQmhbM
<ProviderID>k__BackingField
THmNVFHFAK
<Status>k__BackingField
Pg2NR5kyVm
<Type>k__BackingField
jhJNfcfpAR
<AllBranchAccounts>k__BackingField
wJXNeQxlBq
<WithoutPointsTopup>k__BackingField
C8TNxcaoUp
<GrossGrouping>k__BackingField
DtMNs7nTTX
<WithAgentClients>k__BackingField
nsUNOg2dni
<GroupByAccounts>k__BackingField
mw1Nh3plbY
<GroupByServices>k__BackingField
YmkN61S7SA
<GroupByProviders>k__BackingField
u29N4l8dri
<GroupByStatus>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TopupOperatorModel
AppTech.MSMS.Domain.Reports.Models.TopupOperatorModel
TopupOperatorModel
TopupOperatorModel
OMcNmoDTZR
<ProviderID>k__BackingField
g9ON7bISgI
<AccountID>k__BackingField
sQQNWufcr0
<Operator>k__BackingField
tnuNnk9Upi
<ItemID>k__BackingField
TKSNzZ426U
<SNO>k__BackingField
de3DGjQbAF
<LineType>k__BackingField
U6pDA1p8Ri
<Status>k__BackingField
OJIDU7AF3D
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TransactionModel
AppTech.MSMS.Domain.Reports.Models.TransactionModel
TransactionModel
TransactionModel
rAoDpXwNVu
<Type>k__BackingField
fM2D94Zns2
<AccountID>k__BackingField
yH9DiYoQ27
<ServiceID>k__BackingField
cRvDI3VJHB
<UserID>k__BackingField
RmxDHqZRI5
<IsAgent>k__BackingField
C1nDuwMqjH
<GroupByPoints>k__BackingField
<<type>>
AppTech.MSMS.Domain.Reports.Models.TransferReportModel
AppTech.MSMS.Domain.Reports.Models.TransferReportModel
TransferReportModel
TransferReportModel
Fo9DNHPMYN
<SenderClientID>k__BackingField
XU5DDfCIeC
<ReceiverClientID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Offline.OffLineHelper
AppTech.MSMS.Domain.Offline.OffLineHelper
OffLineHelper
OffLineHelper
QYQDtMiXqX
ExtractDate
<<type>>
AppTech.MSMS.Domain.Offline.SmsRequest
AppTech.MSMS.Domain.Offline.SmsRequest
SmsRequest
SmsRequest
Ag3Ddnt5Ku
<UN>k__BackingField
u9KDSoKWjJ
<PSS>k__BackingField
mAPDkaTr6A
<MSG>k__BackingField
RRWDPIc29i
<Target>k__BackingField
nG8DjBIMcg
<SNO>k__BackingField
Be4DaqNSe8
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Offline.SmsResponse
AppTech.MSMS.Domain.Offline.SmsResponse
SmsResponse
SmsResponse
l2jDYr1JFE
<Success>k__BackingField
gGqDw6hmK0
<MSG>k__BackingField
aixD0JeiMO
<SNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Offline.SmsRequestHandler
AppTech.MSMS.Domain.Offline.SmsRequestHandler
SmsRequestHandler
SmsRequestHandler
<<type>>
AppTech.MSMS.Domain.Offline.SmsType
AppTech.MSMS.Domain.Offline.SmsType
SmsType
SmsType
<<type>>
AppTech.MSMS.Domain.Hubs.SignalRProvider
AppTech.MSMS.Domain.Hubs.SignalRProvider
SignalRProvider
SignalRProvider
c24Dq5N72j
OnMessageArrived
x7pDMYBfJa
hubConnection
ig1DXnvv7U
hubProxy
<<type>>
AppTech.MSMS.Domain.Express.RemittanceException
AppTech.MSMS.Domain.Express.RemittanceException
RemittanceException
RemittanceException
<<type>>
AppTech.MSMS.Domain.Express.RemittanceResponse
AppTech.MSMS.Domain.Express.RemittanceResponse
RemittanceResponse
RemittanceResponse
h7hDoISIIC
<Status>k__BackingField
X9fDLcuVJq
<Success>k__BackingField
H2IDZufqgJ
<Result>k__BackingField
cKcDKmN5xk
<Message>k__BackingField
xtCDyPvctj
<ProviderResponse>k__BackingField
BmhDvpg9Iy
<Number>k__BackingField
qF9D2F6Kb5
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Express.RemittanceRequest
AppTech.MSMS.Domain.Express.RemittanceRequest
RemittanceRequest
RemittanceRequest
DwrD8ERyaq
<ID>k__BackingField
qfUD395ZBp
<Number>k__BackingField
yj4DbiSiJ6
<Amount>k__BackingField
RgHDEBdIwD
<CurrencyID>k__BackingField
hLHD5lWhue
<BeneficiaryName>k__BackingField
xHeDB5F3ZC
<BeneficiaryPhone>k__BackingField
RNCDlN0NO5
<SenderName>k__BackingField
iMeDT30tZD
<SenderPhone>k__BackingField
ndFDCxZbTZ
<TargetNumber>k__BackingField
xexDgXApNB
<Note>k__BackingField
jNtDckOC1D
<Purpose>k__BackingField
ny5DJO5ecs
<Status>k__BackingField
MyOD1Z7Ql8
<CommissionAmount>k__BackingField
CiWDr7DVm2
<CommissionCurrencyID>k__BackingField
phTDQST6aF
<ExchangeAccountID>k__BackingField
kl0DFFOXrI
<RefNumber>k__BackingField
EpQDVZqdul
<Channel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService
SyncRemittanceOutService
SyncRemittanceOutService
DLODRLUUtx
AddRecordInSyncSystem
yLbDfT1CJe
Notify
UsvDeO1Gms
_RemittanceIn
GF7DxEMBGi
<AutoNumbering>k__BackingField
wqtDs935GE
_remittanceCommission
iruDO6auwf
syncDeliveredTransfer
Hm2DhQuycw
ServiceId
JrgD6Jo5h9
incomingTransfer
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService
AccountBindService
AccountBindService
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService
ExchangerTargetService
ExchangerTargetService
<<type>>
AppTech.MSMS.Domain.Notifications.Firebase
AppTech.MSMS.Domain.Notifications.Firebase
Firebase
Firebase
jByD45il2o
Fire
ccSDmuCoxl
Log
<<type>>
AppTech.MSMS.Domain.Notifications.FirebaseCredential
AppTech.MSMS.Domain.Notifications.FirebaseCredential
FirebaseCredential
FirebaseCredential
y46D7YLx4k
<type>k__BackingField
jRHDW6LbMU
<project_id>k__BackingField
FAxDnPjnqM
<private_key_id>k__BackingField
ypGDzfU6Ms
<private_key>k__BackingField
QwdtG3cJVA
<client_email>k__BackingField
CeatA0mjIu
<client_id>k__BackingField
YNdtU1pHol
<auth_uri>k__BackingField
lJxtp9ItfK
<token_uri>k__BackingField
fh2t962qur
<auth_provider_x509_cert_url>k__BackingField
shMtin78Pv
<client_x509_cert_url>k__BackingField
<<type>>
AppTech.MSMS.Domain.Notifications.FmcProvider
AppTech.MSMS.Domain.Notifications.FmcProvider
FmcProvider
FmcProvider
ee8tILdmBG
_listener
<<type>>
AppTech.MSMS.Domain.Notifications.NotificationBuilder
AppTech.MSMS.Domain.Notifications.NotificationBuilder
NotificationBuilder
NotificationBuilder
<<type>>
AppTech.MSMS.Domain.Notifications.NotificationListener
AppTech.MSMS.Domain.Notifications.NotificationListener
NotificationListener
NotificationListener
<<type>>
AppTech.MSMS.Domain.Notifications.Notifier
AppTech.MSMS.Domain.Notifications.Notifier
Notifier
Notifier
<<type>>
AppTech.MSMS.Domain.Notifications.NotificationService
AppTech.MSMS.Domain.Notifications.NotificationService
NotificationService
NotificationService
<<type>>
AppTech.MSMS.Domain.Services.ReceiptCreditorService
AppTech.MSMS.Domain.Services.ReceiptCreditorService
ReceiptCreditorService
ReceiptCreditorService
<<type>>
AppTech.MSMS.Domain.Services.ReceiptDebitorService
AppTech.MSMS.Domain.Services.ReceiptDebitorService
ReceiptDebitorService
ReceiptDebitorService
<<type>>
AppTech.MSMS.Domain.Services.AccountDocumentService
AppTech.MSMS.Domain.Services.AccountDocumentService
AccountDocumentService
AccountDocumentService
<<type>>
AppTech.MSMS.Domain.Services.AccountEntryService`1
AppTech.MSMS.Domain.Services.AccountEntryService`1
AccountEntryService`1
AccountEntryService`1
<<type>>
AppTech.MSMS.Domain.Services.AccountRegionService
AppTech.MSMS.Domain.Services.AccountRegionService
AccountRegionService
AccountRegionService
<<type>>
AppTech.MSMS.Domain.Services.AdminNotificationService
AppTech.MSMS.Domain.Services.AdminNotificationService
AdminNotificationService
AdminNotificationService
<<type>>
AppTech.MSMS.Domain.Services.DistributorService
AppTech.MSMS.Domain.Services.DistributorService
DistributorService
DistributorService
SlDtHdI2RK
CreateNewUser
Hj8tuq6aII
<AutoNumbering>k__BackingField
wUFtNaeN0J
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CoverageOrderService
AppTech.MSMS.Domain.Services.CoverageOrderService
CoverageOrderService
CoverageOrderService
wYWtDmLWoT
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateAccountService
AppTech.MSMS.Domain.Services.CurrencyRateAccountService
CurrencyRateAccountService
CurrencyRateAccountService
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoSyncService
AppTech.MSMS.Domain.Services.OrderInfoSyncService
OrderInfoSyncService
OrderInfoSyncService
<<type>>
AppTech.MSMS.Domain.Services.AsyncBagatService
AppTech.MSMS.Domain.Services.AsyncBagatService
AsyncBagatService
AsyncBagatService
J5sttlhMlq
_disposed
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService
AppTech.MSMS.Domain.Services.AsyncTopupService
AsyncTopupService
AsyncTopupService
a8XtdgdVnH
ExcuteAsOrder
fhXtSOCj1M
GenerateTransactionId
<<type>>
AppTech.MSMS.Domain.Services.BankDepositService
AppTech.MSMS.Domain.Services.BankDepositService
BankDepositService
BankDepositService
<<type>>
AppTech.MSMS.Domain.Services.BankService
AppTech.MSMS.Domain.Services.BankService
BankService
BankService
EX8tkupjDD
currentUnitOfWork
<<type>>
AppTech.MSMS.Domain.Services.BranchTargetService
AppTech.MSMS.Domain.Services.BranchTargetService
BranchTargetService
BranchTargetService
gCttPbmYjY
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ClaimGroupService
AppTech.MSMS.Domain.Services.ClaimGroupService
ClaimGroupService
ClaimGroupService
pQTtjnKqsU
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ConsumeInvoiceService
AppTech.MSMS.Domain.Services.ConsumeInvoiceService
ConsumeInvoiceService
ConsumeInvoiceService
<<type>>
AppTech.MSMS.Domain.Services.DbBackupService
AppTech.MSMS.Domain.Services.DbBackupService
DbBackupService
DbBackupService
uMetaT13VE
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.DeviceService
AppTech.MSMS.Domain.Services.DeviceService
DeviceService
DeviceService
DcWtYrnAqJ
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.EntryService`1
AppTech.MSMS.Domain.Services.EntryService`1
EntryService`1
EntryService`1
<<type>>
AppTech.MSMS.Domain.Services.ExchangerCommissionService
AppTech.MSMS.Domain.Services.ExchangerCommissionService
ExchangerCommissionService
ExchangerCommissionService
kZptwloe2S
CalcCommissionAmount
GEgt0SCfu1
<EditableFields>k__BackingField
fGUtqhZwiy
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.FeedbackService
AppTech.MSMS.Domain.Services.FeedbackService
FeedbackService
FeedbackService
<<type>>
AppTech.MSMS.Domain.Services.TargetGroupService
AppTech.MSMS.Domain.Services.TargetGroupService
TargetGroupService
TargetGroupService
FHRtMtuKGm
<SelectedItems>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.GroupService
AppTech.MSMS.Domain.Services.GroupService
GroupService
GroupService
ahrtXwBhmy
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PartyService
AppTech.MSMS.Domain.Services.PartyService
PartyService
PartyService
<<type>>
AppTech.MSMS.Domain.Services.ProviderCommissionService
AppTech.MSMS.Domain.Services.ProviderCommissionService
ProviderCommissionService
ProviderCommissionService
wZctonTOma
<OnValidate>b__7_1
JsftLwiTLe
<OnValidate>b__7_2
oB5tZxSv0w
_provider
nvvtK42Ske
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RegionService
AppTech.MSMS.Domain.Services.RegionService
RegionService
RegionService
<<type>>
AppTech.MSMS.Domain.Services.RegisterService
AppTech.MSMS.Domain.Services.RegisterService
RegisterService
RegisterService
dHotyBtw87
<Registeration>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.SyncRemittanceInService
AppTech.MSMS.Domain.Services.SyncRemittanceInService
SyncRemittanceInService
SyncRemittanceInService
wkStvthyJ6
InitTarget
D0Ft2RB3Og
CheckSenderAndReceiverNames
QYWt8ugSH2
AddRecordInSyncSystem
pW7t39sxBw
ExporRemittance
DS5tbE6lEs
GetResult
wS9tE8JZ2v
_accountService
yaVt5YuEKD
_service
soKtBaot2q
_remittancePoint
tZitlOHbHI
<AutoNumbering>k__BackingField
NV3tTFnoHE
remittanceSource
qdqtClLjvG
_serviceId
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService
AppTech.MSMS.Domain.Services.CommissionReceiptService
CommissionReceiptService
CommissionReceiptService
U8itgxbin2
<OnValidate>b__3_1
jA7tcSVTRt
<OnValidate>b__3_2
<<type>>
AppTech.MSMS.Domain.Services.SubscribService
AppTech.MSMS.Domain.Services.SubscribService
SubscribService
SubscribService
bjctJvcQCt
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.GsmService
AppTech.MSMS.Domain.Services.GsmService
GsmService
GsmService
ycst1aGwh0
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.YMaxFactionService
AppTech.MSMS.Domain.Services.YMaxFactionService
YMaxFactionService
YMaxFactionService
<<type>>
AppTech.MSMS.Domain.Services.TransferCommissionService
AppTech.MSMS.Domain.Services.TransferCommissionService
TransferCommissionService
TransferCommissionService
H7NtrqM2yv
CalcCommissionAmount
rRmtQlTKSh
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TransferInService
AppTech.MSMS.Domain.Services.TransferInService
TransferInService
TransferInService
e7UtFLSA4v
Notify
adjtVkDFH0
_disposed
Bn6tRWwHHs
_exchangerCommission
PPotfBIbnO
_exchangerService
iFGteJYBSE
_service
TIetx4jiqj
_serviceCommission
Lh2tsBseYZ
exchangerCommission
<<type>>
AppTech.MSMS.Domain.Services.TransferOutOrderService
AppTech.MSMS.Domain.Services.TransferOutOrderService
TransferOutOrderService
TransferOutOrderService
<<type>>
AppTech.MSMS.Domain.Services.TransferOutService
AppTech.MSMS.Domain.Services.TransferOutService
TransferOutService
TransferOutService
Ox9tOiwiop
Notify
t2tthfk4Cs
_disposed
jspt6RMALN
_exchangerCommission
I4at4WKPYX
_exchangerCommissionService
JIVtmvsqN2
_exchangerService
o2Zt7NNV5P
_service
<<type>>
AppTech.MSMS.Domain.Services.TransportOrderService
AppTech.MSMS.Domain.Services.TransportOrderService
TransportOrderService
TransportOrderService
<<type>>
AppTech.MSMS.Domain.Services.UserDeviceService
AppTech.MSMS.Domain.Services.UserDeviceService
UserDeviceService
UserDeviceService
<<type>>
AppTech.MSMS.Domain.Services.TransporterService
AppTech.MSMS.Domain.Services.TransporterService
TransporterService
TransporterService
gRmtW4mjQt
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AccountCoverageService
AppTech.MSMS.Domain.Services.AccountCoverageService
AccountCoverageService
AccountCoverageService
<<type>>
AppTech.MSMS.Domain.Services.NetworkRemittanceInService
AppTech.MSMS.Domain.Services.NetworkRemittanceInService
NetworkRemittanceInService
NetworkRemittanceInService
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService
AppTech.MSMS.Domain.Services.WifiCardService
WifiCardService
WifiCardService
P7itnh6aZI
GetCard
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService
AppTech.MSMS.Domain.Services.WifiFactionService
WifiFactionService
WifiFactionService
T7ptz2Z4re
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.WifiProviderService
AppTech.MSMS.Domain.Services.WifiProviderService
WifiProviderService
WifiProviderService
OYQdGdkBAO
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AccountSetting
AppTech.MSMS.Domain.Services.AccountSetting
AccountSetting
AccountSetting
PmbdArfFFV
<Subscribers>k__BackingField
Jw7dUOJN5W
<Agents>k__BackingField
J6jdpZ1N5B
<Clients>k__BackingField
B1yd9FYg4g
<Distributors>k__BackingField
n1pdiNTmRX
<Providers>k__BackingField
oTSdIdeh6H
<Funds>k__BackingField
EXJdHtVUD2
<Banks>k__BackingField
eEBdugo9pW
<Merchants>k__BackingField
SR2dNVPZfV
<ExternalBranches>k__BackingField
So8dDhMevX
<Exchangers>k__BackingField
dZmdtuAHXE
<SimInvoice>k__BackingField
di9ddxdVil
<SimPurchases>k__BackingField
TG8dStwRlG
<BuyCurrencyDiffer>k__BackingField
L8udkTvWDc
<SalsCurrencyDiffer>k__BackingField
CNMdPIM24j
<ServicesCommission>k__BackingField
F4Gdj4D3Hk
<RemittanceCommission>k__BackingField
Nl6dakjbSS
<TopupDifferials>k__BackingField
U2ddYflotv
<TopupCommissionMadeen>k__BackingField
g1qdwA6wcv
<Remittance>k__BackingField
TAnd0oXuxA
<CashLoan>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.SyncAccountSetting
AppTech.MSMS.Domain.Services.SyncAccountSetting
SyncAccountSetting
SyncAccountSetting
<<type>>
AppTech.MSMS.Domain.Services.BuyCurrencyService
AppTech.MSMS.Domain.Services.BuyCurrencyService
BuyCurrencyService
BuyCurrencyService
Ew1dqPXPf4
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.LoanOrderService
AppTech.MSMS.Domain.Services.LoanOrderService
LoanOrderService
LoanOrderService
<<type>>
AppTech.MSMS.Domain.Services.MsExtensions
AppTech.MSMS.Domain.Services.MsExtensions
MsExtensions
MsExtensions
<<type>>
AppTech.MSMS.Domain.Services.AccountService
AppTech.MSMS.Domain.Services.AccountService
AccountService
AccountService
UtkdMC42LQ
ParentAccountID
EkAdXAgYhf
GetDirectParentAccountID
KtPdoROwbM
GetParentAccountIdOrZero
gvOdLYhvhg
CanTransferBetweenPointsTree
bxhdZVYGXl
GetDirectParentAccountID2
EocdKxsv4D
GetTargetAccountID
jsTdyK3Li2
SuspendedOrderAmount
KdEdvKo6Gj
<OnUpdated>b__14_1
LfMd2oxOaX
<OnDeleteValidate>b__24_0
<<type>>
AppTech.MSMS.Domain.Services.AccountSlatingService
AppTech.MSMS.Domain.Services.AccountSlatingService
AccountSlatingService
AccountSlatingService
nDAd8JK3G6
Notify
gC0d3M39NT
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ActionService
AppTech.MSMS.Domain.Services.ActionService
ActionService
ActionService
<<type>>
AppTech.MSMS.Domain.Services.AuditLogService
AppTech.MSMS.Domain.Services.AuditLogService
AuditLogService
AuditLogService
<<type>>
AppTech.MSMS.Domain.Services.AgentService
AppTech.MSMS.Domain.Services.AgentService
AgentService
AgentService
CybdbLDKZ8
CreateNewUser
fMddEfk8X3
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AgentPointService
AppTech.MSMS.Domain.Services.AgentPointService
AgentPointService
AgentPointService
ipSd50IBRi
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.AgentPointUserService
AppTech.MSMS.Domain.Services.AgentPointUserService
AgentPointUserService
AgentPointUserService
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService
AppTech.MSMS.Domain.Services.ExternalBranchService
ExternalBranchService
ExternalBranchService
PeSdBYgt1g
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.FundUserService
AppTech.MSMS.Domain.Services.FundUserService
FundUserService
FundUserService
YhGdlU0S5k
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MsCrudBusiness`1
AppTech.MSMS.Domain.Services.MsCrudBusiness`1
MsCrudBusiness`1
MsCrudBusiness`1
RZAdT8dhuc
BuildRecord
EufdCTA5jR
_setting
v0TdgkATU2
<LogUserAction>k__BackingField
pdAdcKdpil
<Problem>k__BackingField
c4EdJxgIOq
<MainUnitOfWork>k__BackingField
ittd1BF55K
<FilterByBranch>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.BrochureService
AppTech.MSMS.Domain.Services.BrochureService
BrochureService
BrochureService
<<type>>
AppTech.MSMS.Domain.Services.CashDepositService
AppTech.MSMS.Domain.Services.CashDepositService
CashDepositService
CashDepositService
O9OdraRLaS
Notify
XpndQ4ocmp
mServiceId
tL0dFK1Ian
<LogUserAction>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CashWithdrawService
AppTech.MSMS.Domain.Services.CashWithdrawService
CashWithdrawService
CashWithdrawService
KB7dVtrKZo
Notify
q1MdRWdlKR
CheckIfByAgent
s2JdfbJweJ
<LogUserAction>k__BackingField
k74de3COpX
mServiceId
<<type>>
AppTech.MSMS.Domain.Services.ClientService
AppTech.MSMS.Domain.Services.ClientService
ClientService
ClientService
uPSdxhDmOv
GetParent
zUDdsYX71T
CreateNewUser
jCvdOPLuOU
Test
jRDdhaX3vj
<CreateUserWithClient>k__BackingField
QGNd65EUv9
<Registeration>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Notification
AppTech.MSMS.Domain.Services.Notification
Notification
Notification
qLPd4Mpeie
NotifyAndForget
OaBdmxhdvA
SendSmsIfAllowed
ctJd7vjvNc
<EditableFields>k__BackingField
ogddWMyjRD
<Auditable>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.OfferOrderService
AppTech.MSMS.Domain.Services.OfferOrderService
OfferOrderService
OfferOrderService
<<type>>
AppTech.MSMS.Domain.Services.RemittanceNumberService
AppTech.MSMS.Domain.Services.RemittanceNumberService
RemittanceNumberService
RemittanceNumberService
OChdnuFVmf
SaveNumber
<<type>>
AppTech.MSMS.Domain.Services.SaleCurrencyService
AppTech.MSMS.Domain.Services.SaleCurrencyService
SaleCurrencyService
SaleCurrencyService
AZJdzJtIs3
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService
AppTech.MSMS.Domain.Services.ServiceClaimService
ServiceClaimService
ServiceClaimService
ToLSGdYdgt
CheckServicePermission_v0
wjASACTQyO
CheckServicePermission_v2
K5LSUWNmbo
CheckServicePermission
<<type>>
AppTech.MSMS.Domain.Services.ClientAction
AppTech.MSMS.Domain.Services.ClientAction
ClientAction
ClientAction
d2aSpYaAq2
<Value>k__BackingField
f1eS9HOPub
<Text>k__BackingField
F71Si6j2G6
<IsAllowed>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ClientSmsService
AppTech.MSMS.Domain.Services.ClientSmsService
ClientSmsService
ClientSmsService
oNcSIMRwx8
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CurrencyService
AppTech.MSMS.Domain.Services.CurrencyService
CurrencyService
CurrencyService
dJHSHXk3TW
GetIdForOrderCallback
R3HSuM2OgU
<DefaultCurrecnyID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CurrencyExchangeService
AppTech.MSMS.Domain.Services.CurrencyExchangeService
CurrencyExchangeService
CurrencyExchangeService
lELSDHBF69
get_IsBuy
KShSd0Lf70
get_DirectCurrencyExchange
VqESNba0DX
MakeDirectExchangeCurrency
cYjSk5LFaO
_serviceId
IsWSPe0lEx
<LogUserAction>k__BackingField
uZaStNITYD
IsBuy
WmBSSFZl4w
DirectCurrencyExchange
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateService
AppTech.MSMS.Domain.Services.CurrencyRateService
CurrencyRateService
CurrencyRateService
<<type>>
AppTech.MSMS.Domain.Services.DepositOrderService
AppTech.MSMS.Domain.Services.DepositOrderService
DepositOrderService
DepositOrderService
<<type>>
AppTech.MSMS.Domain.Services.JournalService
AppTech.MSMS.Domain.Services.JournalService
JournalService
JournalService
ysoSj7dqa9
Entries
<<type>>
AppTech.MSMS.Domain.Services.JournalEntryService
AppTech.MSMS.Domain.Services.JournalEntryService
JournalEntryService
JournalEntryService
<<type>>
AppTech.MSMS.Domain.Services.ErrorService
AppTech.MSMS.Domain.Services.ErrorService
ErrorService
ErrorService
<<type>>
AppTech.MSMS.Domain.Services.ExchangerService
AppTech.MSMS.Domain.Services.ExchangerService
ExchangerService
ExchangerService
<<type>>
AppTech.MSMS.Domain.Services.YmFactionService
AppTech.MSMS.Domain.Services.YmFactionService
YmFactionService
YmFactionService
<<type>>
AppTech.MSMS.Domain.Services.BasicFactionService
AppTech.MSMS.Domain.Services.BasicFactionService
BasicFactionService
BasicFactionService
<<type>>
AppTech.MSMS.Domain.Services.FactionService
AppTech.MSMS.Domain.Services.FactionService
FactionService
FactionService
<<type>>
AppTech.MSMS.Domain.Services.FsmsReport`1
AppTech.MSMS.Domain.Services.FsmsReport`1
FsmsReport`1
FsmsReport`1
<<type>>
AppTech.MSMS.Domain.Services.FundService
AppTech.MSMS.Domain.Services.FundService
FundService
FundService
<<type>>
AppTech.MSMS.Domain.Services.GeneralInfoService
AppTech.MSMS.Domain.Services.GeneralInfoService
GeneralInfoService
GeneralInfoService
EekSasOSAY
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.InstructionService
AppTech.MSMS.Domain.Services.InstructionService
InstructionService
InstructionService
oM9SYNr4Wo
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.DoubleEntryService`1
AppTech.MSMS.Domain.Services.DoubleEntryService`1
DoubleEntryService`1
DoubleEntryService`1
<<type>>
AppTech.MSMS.Domain.Services.MerchantService
AppTech.MSMS.Domain.Services.MerchantService
MerchantService
MerchantService
TQbSwQT7Kf
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MerchantCategoryService
AppTech.MSMS.Domain.Services.MerchantCategoryService
MerchantCategoryService
MerchantCategoryService
zFSS0ZJp6y
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MerchantPaymentService
AppTech.MSMS.Domain.Services.MerchantPaymentService
MerchantPaymentService
MerchantPaymentService
<<type>>
AppTech.MSMS.Domain.Services.TopupNetworkService
AppTech.MSMS.Domain.Services.TopupNetworkService
TopupNetworkService
TopupNetworkService
<<type>>
AppTech.MSMS.Domain.Services.OpeningBalanceService
AppTech.MSMS.Domain.Services.OpeningBalanceService
OpeningBalanceService
OpeningBalanceService
f1ZSqi4KZb
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService
AppTech.MSMS.Domain.Services.OrderInfoService
OrderInfoService
OrderInfoService
cqmSMXaXoR
MakeRayalMobile
BBRSXrWAgt
MakeTopupPayment
IFSSoOTdQZ
MakeAccountSlating
KSaSLv5spV
MakeCurrencyExchange
axfSZsnIMk
x
TdCSKuywt7
DeleteChild
<<type>>
AppTech.MSMS.Domain.Services.IOrder
AppTech.MSMS.Domain.Services.IOrder
IOrder
IOrder
<<type>>
AppTech.MSMS.Domain.Services.OrderDetailService`1
AppTech.MSMS.Domain.Services.OrderDetailService`1
OrderDetailService`1
OrderDetailService`1
N5ZSyPPyCF
<LogUserAction>k__BackingField
idtSvwbUtU
<_provider>k__BackingField
jc2S2imNjT
_lock
<<type>>
AppTech.MSMS.Domain.Services.PageService
AppTech.MSMS.Domain.Services.PageService
PageService
PageService
<<type>>
AppTech.MSMS.Domain.Services.PaymentService`1
AppTech.MSMS.Domain.Services.PaymentService`1
PaymentService`1
PaymentService`1
CuES8C5vgv
<LogUserAction>k__BackingField
HcrS3xJJFp
<ServiceKeyName>k__BackingField
Ud1Sb8Xocq
<AccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PersonalInfoService
AppTech.MSMS.Domain.Services.PersonalInfoService
PersonalInfoService
PersonalInfoService
<<type>>
AppTech.MSMS.Domain.Services.SimInvoiceService
AppTech.MSMS.Domain.Services.SimInvoiceService
SimInvoiceService
SimInvoiceService
<<type>>
AppTech.MSMS.Domain.Services.SimpleEntryService
AppTech.MSMS.Domain.Services.SimpleEntryService
SimpleEntryService
SimpleEntryService
<<type>>
AppTech.MSMS.Domain.Services.TopupCommissionService
AppTech.MSMS.Domain.Services.TopupCommissionService
TopupCommissionService
TopupCommissionService
Tv3SEnEUf3
Notify
vX4S54Yxq9
<OnValidate>b__4_1
JtKSBF98mD
<OnValidate>b__4_2
Yr8SlOh5kY
serviceName
<<type>>
AppTech.MSMS.Domain.Services.TopupService
AppTech.MSMS.Domain.Services.TopupService
TopupService
TopupService
B7oSTyAtiE
LogSuspended
yCmSCyUX2j
ProcessOnSyncSystem
LWaSgOhsPK
OnProcessed
msoScqhmn3
GetStatusFromProvider
zP7SJO8oly
MakeAduitLog
HrxS1GGtXN
_disposed
MmiSr8BcgN
_topupSetting
yTISQgDkw0
<LogUserAction>k__BackingField
ldKSFGnNfp
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupOrderService
AppTech.MSMS.Domain.Services.TopupOrderService
TopupOrderService
TopupOrderService
jQ0SVvLStK
IsDirect
<<type>>
AppTech.MSMS.Domain.Services.CountryService
AppTech.MSMS.Domain.Services.CountryService
CountryService
CountryService
NurSRJsYbB
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ProvinceService
AppTech.MSMS.Domain.Services.ProvinceService
ProvinceService
ProvinceService
phISfMUmd2
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.CashInService
AppTech.MSMS.Domain.Services.CashInService
CashInService
CashInService
JPySextFbN
Notify
<<type>>
AppTech.MSMS.Domain.Services.Registeration
AppTech.MSMS.Domain.Services.Registeration
Registeration
Registeration
NMuSxfbNRV
DecrptPassword
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService
AppTech.MSMS.Domain.Services.RemittanceCommissionService
RemittanceCommissionService
RemittanceCommissionService
lcjSscEbHP
CalcCommissionAmount
egbSO3nle8
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceInService
AppTech.MSMS.Domain.Services.RemittanceInService
RemittanceInService
RemittanceInService
OMLShT7wbU
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService
AppTech.MSMS.Domain.Services.RemittanceOutService
RemittanceOutService
RemittanceOutService
jdZS64Qh7e
Notify
JU2S4GrR7C
_remittanceCommission
mMQSml5PI2
_RemittanceIn
prcS746i4N
syncDeliveredTransfer
KCGSW6yrK0
<AutoNumbering>k__BackingField
zQtSnuntP1
Services
QuvSzp0wSB
ServiceId
<<type>>
AppTech.MSMS.Domain.Services.RemittancePointService
AppTech.MSMS.Domain.Services.RemittancePointService
RemittancePointService
RemittancePointService
rtOkGgKq3c
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceRegionService
AppTech.MSMS.Domain.Services.RemittanceRegionService
RemittanceRegionService
RemittanceRegionService
cm8kAgdLtb
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RssService
AppTech.MSMS.Domain.Services.RssService
RssService
RssService
xC3kUe11vx
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PaymentCommissionService
AppTech.MSMS.Domain.Services.PaymentCommissionService
PaymentCommissionService
PaymentCommissionService
waWkpYsHXq
CalcCommissionAmount
jZbk9I9Cav
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PaymentEntryService
AppTech.MSMS.Domain.Services.PaymentEntryService
PaymentEntryService
PaymentEntryService
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService
AppTech.MSMS.Domain.Services.TopupProviderService
TopupProviderService
TopupProviderService
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService
AppTech.MSMS.Domain.Services.LiveTopupService
LiveTopupService
LiveTopupService
gaVkimQBpJ
AsOrderByAccount
<<type>>
AppTech.MSMS.Domain.Services.YMobileBagatService
AppTech.MSMS.Domain.Services.YMobileBagatService
YMobileBagatService
YMobileBagatService
qrZkIgbD4L
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.BagatService
AppTech.MSMS.Domain.Services.BagatService
BagatService
BagatService
fMckHgv00x
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.MoneyMasterService
AppTech.MSMS.Domain.Services.MoneyMasterService
MoneyMasterService
MoneyMasterService
<<type>>
AppTech.MSMS.Domain.Services.WERegionService
AppTech.MSMS.Domain.Services.WERegionService
WERegionService
WERegionService
hiHkuQfiPc
<EditableFields>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.ServiceInfoService
AppTech.MSMS.Domain.Services.ServiceInfoService
ServiceInfoService
ServiceInfoService
<<type>>
AppTech.MSMS.Domain.Services.SimCardOrderService
AppTech.MSMS.Domain.Services.SimCardOrderService
SimCardOrderService
SimCardOrderService
WH0kNaNS2X
InitSpecialSim
ONykDvoxm9
_simSetting
OUVktlvGvq
_refundAmount
<<type>>
AppTech.MSMS.Domain.Services.SmsService
AppTech.MSMS.Domain.Services.SmsService
SmsService
SmsService
nhYkdTC2Tc
SendToDispatcher
<<type>>
AppTech.MSMS.Domain.Services.CashOutService
AppTech.MSMS.Domain.Services.CashOutService
CashOutService
CashOutService
OkbkSEcDfl
Notify
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService
AppTech.MSMS.Domain.Services.TrailToupOrderService
TrailToupOrderService
TrailToupOrderService
LOqkkQRdIQ
<>n__0
<<type>>
AppTech.MSMS.Domain.Services.CashTransferService
AppTech.MSMS.Domain.Services.CashTransferService
CashTransferService
CashTransferService
miAkPiCXcS
Notify
gkgkjwk4jw
commission
aSmkaq8HeX
paymentCommission
UxdkYwZmGm
obj
<<type>>
AppTech.MSMS.Domain.Services.TransferOrderService
AppTech.MSMS.Domain.Services.TransferOrderService
TransferOrderService
TransferOrderService
qiNkw7bL2Q
GetExchangeAccountId
ODfk0OM2Jw
GetCurrencyID
<<type>>
AppTech.MSMS.Domain.Services.UserRoleService
AppTech.MSMS.Domain.Services.UserRoleService
UserRoleService
UserRoleService
<<type>>
AppTech.MSMS.Domain.Services.UserService
AppTech.MSMS.Domain.Services.UserService
UserService
UserService
eHukqvUrYh
SetPassword
OGgkMtqVtB
ResetAccountApiToken
JQakXyvSjO
CheckUserPassword
LfakofxeGD
<AdminID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.UserPageService
AppTech.MSMS.Domain.Services.UserPageService
UserPageService
UserPageService
<<type>>
AppTech.MSMS.Domain.Services.UserPermission
AppTech.MSMS.Domain.Services.UserPermission
UserPermission
UserPermission
rqTkLxelVs
set_Page
bZTkZtfdMQ
set_PageActions
OFdkKFREjk
<Page>k__BackingField
CJbkyPPs4Z
<PageActions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Page
AppTech.MSMS.Domain.Services.Page
Page
Page
<<type>>
AppTech.MSMS.Domain.Services.Action
AppTech.MSMS.Domain.Services.Action
Action
Action
Kf7kvYhyhc
<Value>k__BackingField
IrHk2HgT8a
<Text>k__BackingField
COfk8KyJYb
<Name>k__BackingField
kInk3Yttbf
<IsAllow>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.PermissionManager
AppTech.MSMS.Domain.Services.PermissionManager
PermissionManager
PermissionManager
AZLkbCfkeS
set_UserPermissions
VkNkEyRPoR
mIncludeAllPages
oNOk5MBJO2
mUserId
xGFkBOOhve
<UserPermissions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.UserPermissionService
AppTech.MSMS.Domain.Services.UserPermissionService
UserPermissionService
UserPermissionService
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService
AppTech.MSMS.Domain.Services.AccountUserService
AccountUserService
AccountUserService
<<type>>
AppTech.MSMS.Domain.Services.VoucherService
AppTech.MSMS.Domain.Services.VoucherService
VoucherService
VoucherService
<<type>>
AppTech.MSMS.Domain.Services.WithdrawOrderService
AppTech.MSMS.Domain.Services.WithdrawOrderService
WithdrawOrderService
WithdrawOrderService
FhOklLEOWk
<HasView>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.BagatPaymentService
AppTech.MSMS.Domain.Services.BagatPaymentService
BagatPaymentService
BagatPaymentService
RkokFwy69x
get_NoteMsg
GS1kTNYPvJ
CheckLastPaid
BtFkCsyWXJ
MakeJournalEntries
CHGkgimSdT
Topup
wJPkcY7vwK
ProccessViaProvider
fPwkJbkEAP
SendNotification
X9Hk1VYecN
OnProcessed
CjakrthTi5
GetStatusFromProvider
vNgkQEfZbo
MakeAduitLog
EGlkRHOaKA
_accountService
BT0kf1itFK
_bagat
otekeifYQX
_bagatActivated
j4qkxTEiQA
_bagatService
pqRks6fck5
_liveTopupService
yImkOOmUqN
_service
Rh5khZmX3g
_topupService
aLok6EIsia
failureMessgae
T4Fk4AYdVo
SyncJournal
vY4kmo4JWW
<AutoNumbering>k__BackingField
KBIk7HQVmw
currentProvider
KCykW164Hl
_disposed
anskVCe8Ya
NoteMsg
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService
WifiInventoryService
WifiInventoryService
tqjknCyIsg
ExtractAndSaveCards
PKukzfYrM6
wifiCardService
GoCPG4oS3E
wifiProviderService
DvWPAMdOth
InsertedCards
H9kPUUVbPr
RepeatedCards
OMUPpd4HHG
_disposed
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiMultiPaymentService
AppTech.MSMS.Domain.Services.WifiCards.WifiMultiPaymentService
WifiMultiPaymentService
WifiMultiPaymentService
CiTP9LIktI
faction
hoCPiT34GS
provider
dDOPIMGKZe
wifiCards
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiPaymentService
AppTech.MSMS.Domain.Services.WifiCards.WifiPaymentService
WifiPaymentService
WifiPaymentService
UxtPHEovce
wifiCard
UyUPuURQXL
faction
qKHPNwb31V
provider
ctBPDKoM0Z
_disposed
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiInventory
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiInventory
WifiInventory
WifiInventory
joPPt4h7uO
<ProviderID>k__BackingField
hPfPdTv3HJ
<FactionID>k__BackingField
jhQPS1eOmg
<Cards>k__BackingField
ND2PkPR9Kq
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiCardModel
AppTech.MSMS.Domain.Services.WifiCards.Models.WifiCardModel
WifiCardModel
WifiCardModel
cVVPP09HQb
<Username>k__BackingField
U6EPj2CET2
<Password>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleManager
AppTech.MSMS.Domain.Services.TopupPayments.BundleManager
BundleManager
BundleManager
sE8Pa0vh1v
SetGomalaAmount
iNoPYePr1C
SetQuantityAmount
EDjPwB0egp
KhedmatiCalc
bH4P0aLaf1
GetSubServiceID
g4TPqkYB4M
_currentUnitOfWork
oO8PMfYike
_currentSesssion
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleService
AppTech.MSMS.Domain.Services.TopupPayments.BundleService
BundleService
BundleService
Kp3PXdfxGn
GetBundle
Pj5PoY4XRT
GetBundle
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.Operator
AppTech.MSMS.Domain.Services.TopupPayments.Operator
Operator
Operator
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService
GomalaTopupService
GomalaTopupService
C8WPLc2A7F
ExcuteAsOrder
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.IBundleService`1
AppTech.MSMS.Domain.Services.TopupPayments.IBundleService`1
IBundleService`1
IBundleService`1
y3yPZZZhtD
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService
ItemCostService
ItemCostService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemService
AppTech.MSMS.Domain.Services.TopupPayments.ItemService
ItemService
ItemService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService
OperatorService
OperatorService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService
QuotationService
QuotationService
GHHPKCn8K0
GetFullQuoation
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService
RiyalMobileBagatService
RiyalMobileBagatService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileOrderService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileOrderService
RiyalMobileOrderService
RiyalMobileOrderService
nfkPy1YVHs
get_ServiceID
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService
RiyalMobileService
RiyalMobileService
RNePvrxeRv
_disposed
VFLP2Y46L8
<LogUserAction>k__BackingField
BrFP8Ww6oc
<VoucherName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileTopupService
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileTopupService
RiyalMobileTopupService
RiyalMobileTopupService
wPIP3uAkEE
serviceId
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.SuspendTopupService
AppTech.MSMS.Domain.Services.TopupPayments.SuspendTopupService
SuspendTopupService
SuspendTopupService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback
TopupCallback
TopupCallback
e8ePbEGKQI
ShowError
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.TopupPaymentService
AppTech.MSMS.Domain.Services.TopupPayments.TopupPaymentService
TopupPaymentService
TopupPaymentService
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.Models.IBundle
AppTech.MSMS.Domain.Services.TopupPayments.Models.IBundle
IBundle
IBundle
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.Models.TopupItem
AppTech.MSMS.Domain.Services.TopupPayments.Models.TopupItem
TopupItem
TopupItem
lEjPE9Dt2q
<ID>k__BackingField
vgcP5hLEJm
<Name>k__BackingField
W2UPBN6OON
<Operator>k__BackingField
HalPlxLNkN
<Service>k__BackingField
JsjPTvAfkd
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService
SimPurchaseService
SimPurchaseService
q0uPCj9pLB
get_SimByExcel
atvPgUaRXw
SimByExcel
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService
AppTech.MSMS.Domain.Services.Sims.SimSaleService
SimSaleService
SimSaleService
r59Pc7itP6
simService
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService
AppTech.MSMS.Domain.Services.Sims.SimTransferService
SimTransferService
SimTransferService
OMPPJQ2ANt
simService
<<type>>
AppTech.MSMS.Domain.Services.Sims.SpecialSimService
AppTech.MSMS.Domain.Services.Sims.SpecialSimService
SpecialSimService
SpecialSimService
<<type>>
AppTech.MSMS.Domain.Services.Security.AccountApiService
AppTech.MSMS.Domain.Services.Security.AccountApiService
AccountApiService
AccountApiService
gIGP1G1yoH
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Payment.IBagatService
AppTech.MSMS.Domain.Services.Payment.IBagatService
IBagatService
IBagatService
<<type>>
AppTech.MSMS.Domain.Services.Payment.SimService
AppTech.MSMS.Domain.Services.Payment.SimService
SimService
SimService
<<type>>
AppTech.MSMS.Domain.Services.Payment.TopupReceiptService
AppTech.MSMS.Domain.Services.Payment.TopupReceiptService
TopupReceiptService
TopupReceiptService
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService
AppTech.MSMS.Domain.Services.Payment.YmBagatService
YmBagatService
YmBagatService
clIPrWX64h
ExecuteBagat
pIAPQhmMNJ
InitLineType
A9RPFd0wFJ
SendNotification
HE5PVpv9H4
_disposed
mQxPRrYywX
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService
YmTopupBagatService
YmTopupBagatService
AmOPfpPlr0
<>n__0
<<type>>
AppTech.MSMS.Domain.Services.Parties.PointBalanceService
AppTech.MSMS.Domain.Services.Parties.PointBalanceService
PointBalanceService
PointBalanceService
BudPewngCJ
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService
AccountFrozenService
AccountFrozenService
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService
DoubleEntryBondService
DoubleEntryBondService
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.WalletInService
AppTech.MSMS.Domain.Services.GeneralLedger.WalletInService
WalletInService
WalletInService
<<type>>
AppTech.MSMS.Domain.Services.Clients.BranchClientService
AppTech.MSMS.Domain.Services.Clients.BranchClientService
BranchClientService
BranchClientService
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardFactionService
AppTech.MSMS.Domain.Services.Cards.CardFactionService
CardFactionService
CardFactionService
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardOrderService
AppTech.MSMS.Domain.Services.Cards.CardOrderService
CardOrderService
CardOrderService
PNIPxEGD1h
exchangeRate
VNGPssZOpy
cardFaction
a81POa1OZs
cardType
Rf9PhO7ss4
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardPaymentService
AppTech.MSMS.Domain.Services.Cards.CardPaymentService
CardPaymentService
CardPaymentService
nYMP6STUVI
card
jvLP4WEWHg
exchangeRate
P9mPmbNUkZ
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardService
AppTech.MSMS.Domain.Services.Cards.CardService
CardService
CardService
lrHP7Yr7kZ
GetCard
x1CPWrxX1I
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardTypeService
AppTech.MSMS.Domain.Services.Cards.CardTypeService
CardTypeService
CardTypeService
<<type>>
AppTech.MSMS.Domain.Services.Branching.BranchOrderService
AppTech.MSMS.Domain.Services.Branching.BranchOrderService
BranchOrderService
BranchOrderService
<<type>>
AppTech.MSMS.Domain.Services.Branching.BranchService
AppTech.MSMS.Domain.Services.Branching.BranchService
BranchService
BranchService
GdrPnslvxs
GetAccountBranch
k0xPzCGCvi
GetAccountID
oPNjGvtJ3P
IsBranchUser
o9ljATbj60
IsMainBranch
hxpjU3Owlj
<Adminstration>k__BackingField
<<type>>
BWUAwQGK9SSEDHuTAk.qHDhQZtchsQXAfqydj
AppTech.MSMS.Domain.Services.Accounting.AccountProfileService
qHDhQZtchsQXAfqydj
AccountProfileService
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PersonService`1
AppTech.MSMS.Domain.Services.Accounting.PersonService`1
PersonService`1
PersonService`1
rgfjpendeZ
ValidateNumbers
dHCj97Xqp4
_partiesService
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PartiesService
AppTech.MSMS.Domain.Services.Accounting.PartiesService
PartiesService
PartiesService
BmmjiBM180
currentUnitOfWork
<<type>>
AppTech.MSMS.Domain.Services.Accounting.SubscriberService
AppTech.MSMS.Domain.Services.Accounting.SubscriberService
SubscriberService
SubscriberService
<<type>>
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService
BaseRemittanceInService
BaseRemittanceInService
ryEjIdnmv8
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService
DirectRemittanceInService
DirectRemittanceInService
EbJjHaNnsp
CheckSenderAndReceiverNames
qsJjuyMZ2y
PushRemittance
A82jN6JZvW
OnExpressRespons
BNYjDivPLL
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.OrderSatelliteQuotaService
AppTech.MSMS.Domain.Services.Satellite.OrderSatelliteQuotaService
OrderSatelliteQuotaService
OrderSatelliteQuotaService
CJdjtj4SVJ
<EditableFields>k__BackingField
MVvjd245Qx
<ExtraCondition>k__BackingField
FEJjSAH9tv
<ViewName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService
SatelliteFactionService
SatelliteFactionService
qUbjkxe8Uw
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatelliteProviderService
AppTech.MSMS.Domain.Services.Satellite.SatelliteProviderService
SatelliteProviderService
SatelliteProviderService
NkZjPCSVPe
<AutoNumbering>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatellitePaymentService
AppTech.MSMS.Domain.Services.Satellite.SatellitePaymentService
SatellitePaymentService
SatellitePaymentService
Ymxjj3wCn2
Notify
cvnja271BN
faction
Q9gjYn6knT
provider
aHCjweuHOZ
_disposed
<<type>>
AppTech.MSMS.Domain.Models.Account
AppTech.MSMS.Domain.Models.Account
Account
Account
zHhj0ajDK9
<ID>k__BackingField
HKnjq9gRQa
<ParentNumber>k__BackingField
aFKjMknewi
<Name>k__BackingField
qZejXRCX9T
<Number>k__BackingField
pGWjoQauR2
<Type>k__BackingField
UnjjL2lSCX
<CreatedBy>k__BackingField
u08jZc67vB
<CreatedTime>k__BackingField
QGcjK5SSEi
<RowVersion>k__BackingField
QbujyCjWjk
<BranchID>k__BackingField
x3hjvBG4j7
<Status>k__BackingField
ICqj2wn712
<AccountLedgerID>k__BackingField
HKjj8kFdes
<Description>k__BackingField
L7Hj30XOFt
<IsCash>k__BackingField
pBujb31s1Q
<IsContraAccount>k__BackingField
mLGjEoKStt
<IsParty>k__BackingField
brLj5GpUY9
<Branch>k__BackingField
JYOjBKc4XD
<UserInfo>k__BackingField
tHMjlGeMC2
<AccountApis>k__BackingField
UqvjTVD6hk
<AccountDocuments>k__BackingField
o3YjCKBU5x
<AccountRegions>k__BackingField
LyJjg3dGPl
<AccountSlatings>k__BackingField
D4vjcvHvDA
<AccountUsers>k__BackingField
FJcjJsHCYm
<Agents>k__BackingField
TGwj1owOBg
<BagatPayments>k__BackingField
HZFjroDLbt
<Banks>k__BackingField
nRrjQqePaw
<BankDeposits>k__BackingField
OkFjFH89Ot
<BuyCurrencies>k__BackingField
NVsjVY01DY
<BuyCurrencies1>k__BackingField
FDojRZMFTv
<CardOrders>k__BackingField
tUdjfctKei
<CardPayments>k__BackingField
xh3je1WLgb
<CardTypes>k__BackingField
jKXjxhKjvN
<CashDeposits>k__BackingField
JEEjsfOuKo
<CashIns>k__BackingField
gWWjOBsiob
<CashOuts>k__BackingField
HE6jh5XBcy
<CashWithdraws>k__BackingField
Pboj6sMfiO
<Cheques>k__BackingField
eZkj4BYbLv
<Clients>k__BackingField
I3CjmT3HIU
<AccountNotifications>k__BackingField
Nlrj7vkmmB
<ServiceClaims>k__BackingField
bbTjW3GZmf
<CashTransfers>k__BackingField
WGZjn1FiIG
<CommissionReceipts>k__BackingField
rVxjzIRlJg
<CommissionReceiptLines>k__BackingField
qbQaGLPTtF
<ConsumeInvoices>k__BackingField
b1SaAdHJxI
<ConsumeInvoices1>k__BackingField
HbcaUPAWeE
<CoverageOrders>k__BackingField
KX3apTGvS1
<CoverageOrders1>k__BackingField
gJna9kLUdt
<CurrencyExchanges>k__BackingField
x42aiR1Hi1
<DepositOrders>k__BackingField
L5faI8p8al
<Devices>k__BackingField
xiraHbGkcl
<Distributors>k__BackingField
lhvauRA6nF
<Exchangers>k__BackingField
PNiaNhFc65
<ExternalBranches>k__BackingField
C0AaDIhpPN
<Feedbacks>k__BackingField
fASatKEQvO
<Funds>k__BackingField
A4yadrXAxi
<JournalEntries>k__BackingField
NE3aSr2N0M
<LiveTopups>k__BackingField
x2aakSgCs5
<LoanOrders>k__BackingField
Y42aPuyIQy
<Merchants>k__BackingField
x3majiRhXq
<MerchantPayments>k__BackingField
PbHaawXsJA
<OfferOrders>k__BackingField
RjUaYWISIE
<OpeningBalances>k__BackingField
KjJawQPo6J
<OrderInfoes>k__BackingField
Wdka09DUbg
<Parties>k__BackingField
A46aqePNFg
<Payments>k__BackingField
URNaMv3yAi
<PurchaseInvoices>k__BackingField
XcXaX8DF2K
<PurchaseInvoices1>k__BackingField
I84ao1ruRD
<Quotations>k__BackingField
wB5aL3Cl83
<ReceiptCreditors>k__BackingField
Y6caZ2uNZZ
<ReceiptDebitors>k__BackingField
VYsaKGQTJG
<RemittanceIns>k__BackingField
quOay53tTc
<RemittanceOuts>k__BackingField
ffYavOCg2e
<RiyalMobiles>k__BackingField
f64a2F824M
<RiyalMobiles1>k__BackingField
ntqa86yxPg
<SaleCurrencies>k__BackingField
ygCa3kiyfV
<SaleCurrencies1>k__BackingField
raMab4q2eX
<SaleInvoices>k__BackingField
S5BaEUxJAT
<SaleInvoices1>k__BackingField
LEda5nllIx
<SatellitePayments>k__BackingField
JvpaBri2uu
<SatelliteProviders>k__BackingField
wHJalr6Tmn
<Sims>k__BackingField
RoUaTqA5Vd
<SimCardOrders>k__BackingField
CaYaCXaGwA
<SimInvoices>k__BackingField
bM0agdioDr
<SimInvoices1>k__BackingField
WyKacWmE6H
<SimpleEntries>k__BackingField
QegaJuueMJ
<SimPurchases>k__BackingField
yXWa1ZEmYV
<SimPurchases1>k__BackingField
EeCar1HUt4
<Subscribers>k__BackingField
zMOaQW5Ii9
<Suppliers>k__BackingField
JD3aFhXOvj
<CashDeposits1>k__BackingField
bRxaV92bJ0
<CashWithdraws1>k__BackingField
LxlaRgO1RY
<Cheques1>k__BackingField
jpDaf4rB5q
<MerchantPayments1>k__BackingField
IwFaervrIm
<OrderSatelliteQuotas>k__BackingField
vg3ax0sgVw
<OrderSatelliteQuotas1>k__BackingField
DAIas2aadr
<ReceiptCreditors1>k__BackingField
OmFaOUlUqE
<ReceiptDebitors1>k__BackingField
JMNahyu53y
<CashIns1>k__BackingField
DXPa60lgoy
<RemittanceIns1>k__BackingField
EpBa4a771p
<RemittanceOuts1>k__BackingField
rB0amfMiSR
<SatellitePayments1>k__BackingField
umva7QUfLs
<SimpleEntries1>k__BackingField
JZcaWNPlc1
<CashOuts1>k__BackingField
QkianKBcoQ
<CashTransfers1>k__BackingField
SD8azBZaEP
<WifiPayments>k__BackingField
dm4YG1st4q
<Topups>k__BackingField
uS0YApKZgG
<Topups1>k__BackingField
MvHYUxTaCr
<TopupCommissions>k__BackingField
rSNYpCY8Vk
<TopupCommissions1>k__BackingField
tJHY94av0W
<TopupOrders>k__BackingField
pL1YilqfaX
<TopupProviders>k__BackingField
RMOYIvnnZh
<TrailToupOrders>k__BackingField
jGlYHm1YCk
<TransferIns>k__BackingField
iplYuasf4d
<TransferOrders>k__BackingField
d62YN1jI7B
<TransferOuts>k__BackingField
krsYDdD778
<Transporters>k__BackingField
ffJYtwOhmU
<TransportOrders>k__BackingField
lbDYdCD539
<WifiPayments1>k__BackingField
YbHYS5EKYI
<WifiProviders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountApi
AppTech.MSMS.Domain.Models.AccountApi
AccountApi
AccountApi
oxBYkBk2f7
<ID>k__BackingField
HZhYPA2RbR
<RowVersion>k__BackingField
iL5YjBmnqe
<Number>k__BackingField
wFsYafG4Ee
<AccountId>k__BackingField
d1KYY5ZwVq
<UserId>k__BackingField
ec5YwESSPd
<ApiKey>k__BackingField
bjUY0Ld3jt
<Token>k__BackingField
i4yYq2ig4V
<GuidNumber>k__BackingField
uBNYM8xAFb
<PublicKey>k__BackingField
q2BYXMqk5F
<PrivateKey>k__BackingField
jeoYoXcugj
<IpAddress>k__BackingField
zlCYLAvqVC
<Protocol>k__BackingField
c7WYZEwuaO
<Note>k__BackingField
j3OYKpBNay
<ExtraID>k__BackingField
EC6YyRKAkj
<BindID>k__BackingField
LcfYvNc8T3
<SyncID>k__BackingField
QTCY2dpqVi
<ParentID>k__BackingField
FY8Y8QkxxE
<IsAllowed>k__BackingField
HxXY3PcC3Q
<CheckIp>k__BackingField
ONpYbi9Sw4
<Channel>k__BackingField
tHmYE6CmVN
<Port>k__BackingField
md2Y5Y9gv5
<Permitted>k__BackingField
L7dYBQPDVV
<Primary>k__BackingField
NcAYlULVdG
<Binded>k__BackingField
hm2YTUiR8F
<Active>k__BackingField
oFcYCOGVgu
<Synced>k__BackingField
uNYYgNjfbo
<Status>k__BackingField
D9FYcHrxcK
<Type>k__BackingField
yylYJ6aKIC
<Binding>k__BackingField
w5uY1mLniv
<ExtraInfo>k__BackingField
I4hYrUsAET
<BranchID>k__BackingField
GGbYQNMJwc
<CreatedBy>k__BackingField
A47YF6b8Um
<CreatedTime>k__BackingField
Gr2YVIDMwF
<Account>k__BackingField
N7hYR3fUCP
<Branch>k__BackingField
sc7YfVQJUX
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountDocument
AppTech.MSMS.Domain.Models.AccountDocument
AccountDocument
AccountDocument
xqiYe9Tm8F
<ID>k__BackingField
wdaYxKYyEO
<RowVersion>k__BackingField
HRnYsOVMsr
<AccountID>k__BackingField
IBeYOf6dCY
<ImageName>k__BackingField
isrYhVqtVF
<Type>k__BackingField
LfrY69Uoqj
<Note>k__BackingField
PtMY4RF4EN
<BranchID>k__BackingField
fPuYmET53u
<CreatedBy>k__BackingField
TSRY7PAf5J
<CreatedTime>k__BackingField
cpdYWhpnT6
<Account>k__BackingField
uMmYndUCcI
<Branch>k__BackingField
CGCYzAJwTJ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountLedger
AppTech.MSMS.Domain.Models.AccountLedger
AccountLedger
AccountLedger
skGwGYDDk7
<ID>k__BackingField
mOJwAFfeUg
<Name>k__BackingField
gHawUlgqhG
<CrOrDr>k__BackingField
wtawp0P42Z
<Refernce>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountNotification
AppTech.MSMS.Domain.Models.AccountNotification
AccountNotification
AccountNotification
bcBw9WKho9
<ID>k__BackingField
wFYwiPhhJU
<RowVersion>k__BackingField
mEEwIYNh6U
<Message>k__BackingField
KDxwH8rfhF
<CreatedTime>k__BackingField
ekVwuxYOHA
<AccountID>k__BackingField
ywfwNVtpvA
<Sent>k__BackingField
VqPwDeC2B5
<RealTime>k__BackingField
EA7wtAd3Iv
<Title>k__BackingField
Lk9wdvtEOS
<Topic>k__BackingField
WgywSlRqyR
<AccountState>k__BackingField
F0KwkUPJfs
<GroupID>k__BackingField
fJNwP8t8Zp
<PriorityLevel>k__BackingField
WYWwjrioI2
<Status>k__BackingField
DeywaJZc31
<ResonseState>k__BackingField
AaywYrAwFV
<ResonseInfo>k__BackingField
pphwwtsVRA
<Channels>k__BackingField
B2Yw0BosPa
<DispatchedBy>k__BackingField
LigwqEdTHY
<SentTime>k__BackingField
bsHwMutwtb
<Channel>k__BackingField
R3JwXTMr8N
<ServiceID>k__BackingField
MG2wohS12w
<Dispatcher>k__BackingField
aCVwLwUyja
<IsSynce>k__BackingField
MKGwZdI8gC
<Direct>k__BackingField
Lu1wK87bgR
<Deleted>k__BackingField
bBYwypy25g
<Seen>k__BackingField
HXowvEun2a
<IsScheduled>k__BackingField
Sv7w2uOLIe
<IncludeSms>k__BackingField
agVw8wQTpH
<ScheduleTime>k__BackingField
J8lw3abyxe
<Note>k__BackingField
sqfwbusR4a
<ExtraInfo>k__BackingField
nC1wEutZiP
<ExtraID>k__BackingField
Aqpw5G2kNi
<CreatedBy>k__BackingField
BuVwBH8A3x
<BranchID>k__BackingField
y56wlPrun6
<Account>k__BackingField
MonwTUpOaS
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountRegion
AppTech.MSMS.Domain.Models.AccountRegion
AccountRegion
AccountRegion
HHywCBfuH9
<ID>k__BackingField
nZwwg8E5Di
<RowVersion>k__BackingField
Lwowc5ghEs
<Number>k__BackingField
fFwwJIU00Z
<AccountID>k__BackingField
lCLw1CiXdc
<RegionID>k__BackingField
HRLwr3hE9m
<BranchID>k__BackingField
sUDwQwcgp6
<CreatedBy>k__BackingField
uA3wFe3bG9
<CreatedTime>k__BackingField
tsxwVTBj3q
<Account>k__BackingField
zRfwREOdJs
<Branch>k__BackingField
cpAwfle0Cg
<Region>k__BackingField
NCewed7xiF
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountSlating
AppTech.MSMS.Domain.Models.AccountSlating
AccountSlating
AccountSlating
gwOwxuC0I5
<ID>k__BackingField
dL8wsuMZBv
<RowVersion>k__BackingField
rMLwO8hmIK
<AccountID>k__BackingField
VvvwhRxNtr
<Amount>k__BackingField
nJtw68d5xG
<Note>k__BackingField
a6Qw4TsyDR
<CreatedBy>k__BackingField
yB9wmebRZd
<BranchID>k__BackingField
yjQw71pBya
<CreatedTime>k__BackingField
GvCwWAu7Jj
<CurrencyID>k__BackingField
SfdwnduME8
<Account>k__BackingField
FChwzifTbJ
<Branch>k__BackingField
A0K0GxW8XA
<Currency>k__BackingField
AGT0ADRg5p
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountUser
AppTech.MSMS.Domain.Models.AccountUser
AccountUser
AccountUser
ssT0UmVPVl
<ID>k__BackingField
Bpw0pwSBS6
<RowVersion>k__BackingField
LuR09XXSwn
<RoleID>k__BackingField
xvn0iFNVE0
<ParentID>k__BackingField
Y0W0I7a7p4
<UserID>k__BackingField
yRd0H9Zq32
<PrimaryUser>k__BackingField
STG0uaKKwg
<DeviceID>k__BackingField
UhL0Nq1WLm
<CreatedBy>k__BackingField
tpD0DegRIG
<BranchID>k__BackingField
GrM0tF1a9R
<CreatedTime>k__BackingField
RyI0drcSw6
<AccountID>k__BackingField
t9f0S6npOo
<Account>k__BackingField
TlI0kAp5hZ
<UserInfo>k__BackingField
TNf0Ple3Hv
<Branch>k__BackingField
NKS0jtrfiT
<RoleInfo>k__BackingField
VaP0aR506E
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Address
AppTech.MSMS.Domain.Models.Address
Address
Address
Q3q0Y1isOw
<ID>k__BackingField
JCS0w5k13Q
<Name>k__BackingField
Bjj00mTtpL
<Street>k__BackingField
IJK0quPl2M
<City>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AdminNotification
AppTech.MSMS.Domain.Models.AdminNotification
AdminNotification
AdminNotification
K9C0MIBqS1
<ID>k__BackingField
Xqp0XQ0xPi
<RowVersion>k__BackingField
Uam0o2DQUn
<Message>k__BackingField
HVc0Lvcgto
<Type>k__BackingField
z0N0ZaY7eD
<Seen>k__BackingField
Ggy0KrqO4V
<BranchID>k__BackingField
Xyo0yXXNpv
<CreatedBy>k__BackingField
IMq0v8duUw
<CreatedTime>k__BackingField
tRy02SS6QA
<Branch>k__BackingField
iBU08Z7RIf
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Agent
AppTech.MSMS.Domain.Models.Agent
Agent
Agent
wjZ03ImbgB
<ID>k__BackingField
Sdq0bxl17O
<RowVersion>k__BackingField
yIF0EoyaMi
<Number>k__BackingField
aVS05XcKVi
<Name>k__BackingField
MP20BVagO3
<AccountID>k__BackingField
UHF0l2LDpg
<PhoneNumber>k__BackingField
agO0TV6mio
<ContactNumber>k__BackingField
jKU0CdXV4Y
<Address>k__BackingField
pE00gYjsgc
<Note>k__BackingField
dUV0cGyWq0
<Email>k__BackingField
Cyr0JSlTdE
<CardType>k__BackingField
WCw01ca91W
<CardNumber>k__BackingField
mdK0rNrPBQ
<CardIssuePlace>k__BackingField
cfk0Qa0rJF
<CardIssueDate>k__BackingField
heL0FqVWk5
<ImageName>k__BackingField
x7g0VsSKeG
<CreatedBy>k__BackingField
SwO0RhBA14
<BranchID>k__BackingField
jR60fNMYju
<CreatedTime>k__BackingField
oW60e8CRO5
<Type>k__BackingField
l210xQaZW6
<Status>k__BackingField
gEs0sXO83w
<SyncAccountID>k__BackingField
pyn0OwYHnZ
<RefNumber>k__BackingField
N910hlPGNU
<Extra>k__BackingField
cDN060vELk
<Account>k__BackingField
RGc043OZAP
<Branch>k__BackingField
ueS0m8I8o4
<UserInfo>k__BackingField
Kf307yJREc
<AgentPoints>k__BackingField
arC0WRghhC
<CashDeposits>k__BackingField
lxJ0nXByAe
<CashWithdraws>k__BackingField
nfd0zLUqG8
<Clients>k__BackingField
hHnqGWZBhf
<Distributors>k__BackingField
OGmqA6uiOt
<RemittanceIns>k__BackingField
GGeqUhNAIK
<RemittanceOuts>k__BackingField
hU2qpif7lv
<RiyalMobiles>k__BackingField
zpCq9DZIfJ
<Topups>k__BackingField
uCFqijDBr3
<Username>k__BackingField
hNTqILvxpk
<Password>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentPoint
AppTech.MSMS.Domain.Models.AgentPoint
AgentPoint
AgentPoint
YlTqHBmQUE
<ID>k__BackingField
l3Pquwyr77
<RowVersion>k__BackingField
M94qNMB8VM
<AgentID>k__BackingField
tEqqDEd3xU
<RemittancePointID>k__BackingField
ittqteOtE0
<Primary>k__BackingField
iZAqdXlgbK
<CreatedBy>k__BackingField
jAfqSECGNn
<BranchID>k__BackingField
JmeqkbaKOo
<CreatedTime>k__BackingField
Ih2qPca4pK
<Agent>k__BackingField
GF0qjR6N1A
<Branch>k__BackingField
RPjqaq6yOy
<RemittancePoint>k__BackingField
RAuqY1fpXM
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentPointUser
AppTech.MSMS.Domain.Models.AgentPointUser
AgentPointUser
AgentPointUser
VC4qwXyN3a
<ID>k__BackingField
V4eq0dYsg4
<RowVersion>k__BackingField
DDxqqkhcqn
<UserID>k__BackingField
A3JqMAGYE6
<RemittancePointID>k__BackingField
rLSqXMRNQ3
<CreatedBy>k__BackingField
AptqokOBLZ
<BranchID>k__BackingField
sFfqLuLbi8
<CreatedTime>k__BackingField
tLWqZXK36J
<Branch>k__BackingField
XYrqK6BWlW
<RemittancePoint>k__BackingField
zeQqyrfpJg
<UserInfo>k__BackingField
vKfqvZj65j
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AuditLog
AppTech.MSMS.Domain.Models.AuditLog
AuditLog
AuditLog
N0Dq2yV40S
<ID>k__BackingField
XCiq87TDex
<UserID>k__BackingField
pw0q3BBR5Y
<PageName>k__BackingField
JcDqbxbO7R
<Action>k__BackingField
n9hqExuF2w
<AduitDate>k__BackingField
tfeq5f7M0F
<TableName>k__BackingField
wVUqBJsOgW
<RecordId>k__BackingField
hoIqlv3AfY
<OriginalData>k__BackingField
wnFqTYcoZU
<NewData>k__BackingField
PBbqCtDXHE
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Bagat
AppTech.MSMS.Domain.Models.Bagat
Bagat
Bagat
TXdqgM4QkM
<ID>k__BackingField
G2FqcGXCR3
<RowVersion>k__BackingField
HZPqJV6ZcB
<Name>k__BackingField
wyoq1lKbEf
<Code>k__BackingField
kZIqrrDWRi
<LineType>k__BackingField
Pg8qQW7uS3
<Mode>k__BackingField
QlLqFfQDB4
<Price>k__BackingField
CUmqVr2H73
<CreatedBy>k__BackingField
HuUqR7UyYj
<BranchID>k__BackingField
QtOqf8xAwC
<CreatedTime>k__BackingField
AKwqeWnJDj
<SimType>k__BackingField
RZlqxxRye8
<ServiceID>k__BackingField
A7wqsq18cv
<TelPrice>k__BackingField
YPwqOJIC0b
<ProviderPrice>k__BackingField
ECmqhmlgU8
<PersonnalPrice>k__BackingField
Rgsq6G8DBo
<OrderNo>k__BackingField
iOtq4FqMlw
<Number>k__BackingField
RXRqmkkAXT
<Quantity>k__BackingField
Nxmq74SPJL
<Units>k__BackingField
vgQqWm1PM0
<Active>k__BackingField
vB1qnuQnNZ
<Balance>k__BackingField
UIGqzULZRp
<Command>k__BackingField
wMVMGmH72T
<Description>k__BackingField
SBEMAt34I7
<CategoryID>k__BackingField
TuFMUJsS4R
<ProviderID>k__BackingField
Uh3MpfiYDH
<ByProvider>k__BackingField
CnYM9d8D1Q
<Branch>k__BackingField
I1vMijeGom
<ServiceInfo>k__BackingField
CHYMIQYPEQ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BagatPayment
AppTech.MSMS.Domain.Models.BagatPayment
BagatPayment
BagatPayment
wPrMHFPO7D
<ID>k__BackingField
BdcMu9fNyp
<RowVersion>k__BackingField
VhZMN8faQy
<SubscriberNumber>k__BackingField
YFxMDwCEAK
<LineType>k__BackingField
RdFMt7hmXv
<SimType>k__BackingField
GYsMdJvJJl
<Date>k__BackingField
rqyMSXgsR9
<Note>k__BackingField
t95MkfHLCb
<Channel>k__BackingField
hAJMPU9N2s
<ServiceEntryID>k__BackingField
QOHMjIFOW6
<CreatedBy>k__BackingField
MmgMau3M0u
<BranchID>k__BackingField
bDoMYveDRS
<CreatedTime>k__BackingField
Eh7MwAPT5B
<IncludePay>k__BackingField
WNKM0BwsWc
<ActionType>k__BackingField
HRMMqFvhIr
<OfferID>k__BackingField
yIHMMYRY5R
<ServiceID>k__BackingField
VT6MXiD4Qk
<ProviderID>k__BackingField
XcFMog1D15
<Amount>k__BackingField
uGBML9o5qW
<HasLoan>k__BackingField
QlfMZ5m66a
<OfferCode>k__BackingField
NDLMKCpAcy
<OfferName>k__BackingField
b8IMyfIPo5
<OfferAction>k__BackingField
zf2Mv0kORc
<AccountID>k__BackingField
SjaM25bAtV
<Number>k__BackingField
VYyM8wSA1E
<RefNumber>k__BackingField
wMOM3ZwYJZ
<TransactionID>k__BackingField
pTgMb2wt2B
<ProviderRM>k__BackingField
WJWMExx2SP
<ProviderPrice>k__BackingField
ubSM57rkjg
<SubNote>k__BackingField
pHiMBFel9k
<Datestamb>k__BackingField
vSeMlM0Zig
<Status>k__BackingField
a7wMTTOt3k
<EntryID>k__BackingField
FduMCsJ5TY
<UniqueNo>k__BackingField
aF1MgGaoZc
<Quantity>k__BackingField
ERVMc4jdDg
<UnitPrice>k__BackingField
DXVMJwVfFe
<UnitCost>k__BackingField
u1hM1gtuqv
<CostAmount>k__BackingField
DunMrdBKqi
<DifferentialAmount>k__BackingField
i8tMQQQFoN
<CommissionAmount>k__BackingField
LdBMFQk7GX
<Discount>k__BackingField
vG8MVcnitq
<TotalCost>k__BackingField
IrrMR3g6jd
<TotalAmount>k__BackingField
IEcMf91Uxt
<Profits>k__BackingField
CaZMehnxHG
<ExCode>k__BackingField
jtsMx6UViH
<RequestInfo>k__BackingField
DQSMsC6C2G
<ResponseTime>k__BackingField
oS6MORX7YL
<ResponseStatus>k__BackingField
LGrMhRI4IM
<ExecutionPeroid>k__BackingField
qjlM6hthgb
<Debited>k__BackingField
o0BM4TlKa7
<StateClass>k__BackingField
rpOMmsKsK7
<BillState>k__BackingField
ooRM7LyFNo
<State>k__BackingField
I0PMWvNXSc
<ByChild>k__BackingField
MjUMnF2bAm
<ParentAccountID>k__BackingField
PCSMz7KU6a
<TransNumber>k__BackingField
i9BXG6DRWs
<Identifier>k__BackingField
HLLXAXCdMk
<AdminNote>k__BackingField
hjwXUwxNo7
<Cured>k__BackingField
FodXpSFhyh
<CuredBy>k__BackingField
r9rX9BlGAc
<CuredInfo>k__BackingField
XFTXiubVCL
<InspectInfo>k__BackingField
mUKXIEWuLS
<Flag>k__BackingField
stRXH3sabC
<Channeling>k__BackingField
HCRXuxuPsy
<Account>k__BackingField
k0YXNZkUs7
<Branch>k__BackingField
eFrXDfXCMT
<UserInfo>k__BackingField
laTXtBjYJu
<Offers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Bank
AppTech.MSMS.Domain.Models.Bank
Bank
Bank
xdNXdg7xpp
<ID>k__BackingField
PMPXSHWMBA
<Name>k__BackingField
MasXkWhhX0
<AccountID>k__BackingField
H1rXPrf2Do
<Note>k__BackingField
aYSXjJ4aA5
<CreatedBy>k__BackingField
SxZXaApibA
<CreatedTime>k__BackingField
RA7XYFSbwg
<RowVersion>k__BackingField
h1NXwJe2Ul
<BranchID>k__BackingField
FIqX09dDUr
<Account>k__BackingField
QSeXqSXP4U
<Branch>k__BackingField
HiEXMLUYJ8
<UserInfo>k__BackingField
srDXXhdVWr
<Cheques>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BankDeposit
AppTech.MSMS.Domain.Models.BankDeposit
BankDeposit
BankDeposit
J1vXosHB1G
<ID>k__BackingField
cfTXLP2s99
<RowVersion>k__BackingField
FBoXZTrjF8
<ServiceID>k__BackingField
m3PXKhIKRU
<AccountID>k__BackingField
RuBXy6JeHS
<Amount>k__BackingField
PcsXvlkXZ3
<CurrencyID>k__BackingField
CvmX2mBuZe
<CountryID>k__BackingField
H9DX8E5DCS
<ProvinceID>k__BackingField
yeHX3XRLmd
<BankName>k__BackingField
sXCXbB49Am
<ExchangeCurrencyID>k__BackingField
z6yXEYILEA
<SourceName>k__BackingField
CaNX5MNWLK
<TargetName>k__BackingField
cAbXBWZLGk
<InvoiceNo>k__BackingField
V4NXlIDXHu
<Note>k__BackingField
QawXTFYGl7
<Channel>k__BackingField
GZPXCBOJZP
<ParentID>k__BackingField
uXUXgHq7gM
<Status>k__BackingField
A5FXcHU2ex
<CreatedBy>k__BackingField
cXuXJkDwb5
<BranchID>k__BackingField
JG4X1fHGJs
<CreatedTime>k__BackingField
TGBXrF3MnB
<RefNumber>k__BackingField
TuVXQK7kfV
<Extra1>k__BackingField
kSTXFG9OUD
<Extra2>k__BackingField
K1fXVJ9Ggk
<Extra3>k__BackingField
Q4NXRboQgM
<Extra4>k__BackingField
V6DXfklEXy
<Account>k__BackingField
xw6XeCWAB1
<Branch>k__BackingField
N5MXxJQ1dv
<Country>k__BackingField
Fx4XsLmFWR
<Currency>k__BackingField
cgaXOpIo3x
<Currency1>k__BackingField
MhHXh5lsDH
<OrderInfo>k__BackingField
XtPX60wHUl
<Province>k__BackingField
veeX4D2Ij9
<ServiceInfo>k__BackingField
a5BXmK4nAr
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Branch
AppTech.MSMS.Domain.Models.Branch
Branch
Branch
C74X7f1ISQ
<ID>k__BackingField
uhpXWSaRle
<RowVersion>k__BackingField
i7jXnV6Ztj
<Number>k__BackingField
jlcXzpHcDh
<Name>k__BackingField
w6RoGT5INA
<AccountID>k__BackingField
yxcoAwLUMG
<Note>k__BackingField
kaaoUctdBL
<EnglishName>k__BackingField
hALopNRUhg
<Phone>k__BackingField
L1ao9QhFTH
<Fax>k__BackingField
aQHoi4m5VU
<Address>k__BackingField
gfOoID7ncm
<CreatedBy>k__BackingField
egjoHinKIM
<BranchID>k__BackingField
FAXouOlbcG
<CreatedTime>k__BackingField
XWCoNbJeVn
<Accounts>k__BackingField
P37oDwLTB1
<AccountApis>k__BackingField
DNLotoeS5P
<AccountDocuments>k__BackingField
P7rodNL8MB
<AccountRegions>k__BackingField
ev1oS2hvLf
<AccountSlatings>k__BackingField
pJbokiNwbt
<AccountUsers>k__BackingField
JquoP04Bqk
<AdminNotifications>k__BackingField
eekojjYAYC
<Agents>k__BackingField
fVxoa6M3ab
<AgentPoints>k__BackingField
DSfoYrh575
<AgentPointUsers>k__BackingField
vJvowthxoi
<Bagats>k__BackingField
h8Eo0H0KON
<BagatPayments>k__BackingField
nZaoqGptuE
<Banks>k__BackingField
tvGoMuInlh
<BankDeposits>k__BackingField
m54oXZ11Om
<Branch1>k__BackingField
J2goo2GOxA
<Branch2>k__BackingField
GYDoLp6Qqo
<BranchTargets>k__BackingField
Jp5oZCNPKw
<Brochures>k__BackingField
Bb8oKZAv2I
<BuyCurrencies>k__BackingField
EOGoyc40R8
<Cards>k__BackingField
oEbovQ7NpU
<CardFactions>k__BackingField
lxBo26498Q
<CardOrders>k__BackingField
UDuo8EiFhr
<CardPayments>k__BackingField
mgwo3DCopV
<CardTypes>k__BackingField
agxobVK16q
<CashDeposits>k__BackingField
KlfoErbAjL
<CashIns>k__BackingField
kedo5X5m0v
<CashOuts>k__BackingField
yJAoBrIqW9
<CashWithdraws>k__BackingField
zNbolON1j2
<Cheques>k__BackingField
TIGoTR4j6A
<ClaimGroups>k__BackingField
DU0oC0NrwU
<Clients>k__BackingField
NViogurEdG
<ServiceClaims>k__BackingField
paPoc6lwf6
<SMSDispatches>k__BackingField
gEKoJWv3XY
<CashTransfers>k__BackingField
FCqo1bNgtT
<CommissionReceipts>k__BackingField
z58orvST8n
<ConsumeInvoices>k__BackingField
Mx0oQYCwpC
<Countries>k__BackingField
HSWoFm5gJj
<CoverageOrders>k__BackingField
n8AoVkKJ41
<Currencies>k__BackingField
X6ToRmpN7d
<CurrencyExchanges>k__BackingField
kP9ofAW3Aa
<CurrencyRates>k__BackingField
R5XoeuqKiY
<CurrencyRateAccounts>k__BackingField
jWFoxe7q9O
<DbBackups>k__BackingField
oegosFkaHq
<DepositOrders>k__BackingField
JmLoOku3Kg
<Devices>k__BackingField
WjOohRLSri
<Distributors>k__BackingField
q5so685NPa
<Exchangers>k__BackingField
QMjo4CWKrk
<ExchangerTargets>k__BackingField
NNGom5euoY
<ExternalBranches>k__BackingField
wDdo7EwBkw
<Factions>k__BackingField
mJZoWFiXbO
<Feedbacks>k__BackingField
D0xonCRALD
<Funds>k__BackingField
nxpozZfk95
<FundUsers>k__BackingField
x0WLGE2mkv
<GeneralInfoes>k__BackingField
GytLACvfHT
<GroupItems>k__BackingField
zY0LU0GdjD
<Gsms>k__BackingField
JatLpyLt4T
<Instructions>k__BackingField
W8SL9QgGJj
<Items>k__BackingField
yjTLil8sOO
<ItemCosts>k__BackingField
q6dLIWgSnq
<Journals>k__BackingField
upyLHJHOUS
<LiveTopups>k__BackingField
xGPLu9V4bF
<LoanOrders>k__BackingField
m0rLN4QSmY
<Merchants>k__BackingField
E8WLDNfpLW
<MerchantCategories>k__BackingField
EPoLtoVqYe
<MerchantPayments>k__BackingField
h2ULd8UfRy
<MoneyMasters>k__BackingField
kqKLS34cXH
<OfferOrders>k__BackingField
MhMLkujiVt
<OpeningBalances>k__BackingField
k1SLPmCyGj
<OrderInfoes>k__BackingField
S4TLjRvgAo
<OrderSatelliteQuotas>k__BackingField
EP4La1ZFU3
<Parties>k__BackingField
OukLYR8vTX
<PartyGroups>k__BackingField
ET3Lwb18YP
<PaymentCommissions>k__BackingField
sJDL0mGLUf
<Payments>k__BackingField
bqQLqIJpat
<People>k__BackingField
eDTLMTr095
<Products>k__BackingField
SEALXl92PN
<ProductCategories>k__BackingField
s5ELoIShsX
<ProductImages>k__BackingField
gjfLLLxZFU
<Provinces>k__BackingField
VmBLZrTNJQ
<PurchaseInvoices>k__BackingField
o5WLK2xlp2
<PurchaseInvoiceLines>k__BackingField
VbKLy9n5hU
<Quotations>k__BackingField
t2FLvhUCYg
<ReceiptCreditors>k__BackingField
U6lL2BJmo9
<ReceiptDebitors>k__BackingField
bJyL81LCgY
<Regions>k__BackingField
vn7L3Kqj2q
<RemittanceCommissions>k__BackingField
a0sLbD2HkO
<RemittanceIns>k__BackingField
cS4LEURm8q
<RemittanceOuts>k__BackingField
pZ3L5DahDr
<RemittancePoints>k__BackingField
i8QLBPDP7S
<RemittanceRegions>k__BackingField
WKULlKHFIo
<RiyalMobiles>k__BackingField
VDYLTmJBOu
<RSSes>k__BackingField
rf2LCWXYJE
<SaleCurrencies>k__BackingField
zZkLgSlxpL
<SaleInvoices>k__BackingField
QaqLcev48Y
<SaleInvoiceLines>k__BackingField
dROLJB4g1d
<SatelliteFactions>k__BackingField
Fa9L1kYHLg
<SatellitePayments>k__BackingField
QY1Lr0HBub
<SatelliteProviders>k__BackingField
HmKLQnyIeN
<ServiceInfoes>k__BackingField
rfqLF2wBxO
<Sims>k__BackingField
zFnLVxH1FI
<SimCardOrders>k__BackingField
SXhLROj6wP
<SimInvoices>k__BackingField
livLfGVthc
<SimpleEntries>k__BackingField
m1xLehsl5p
<SimPurchases>k__BackingField
FAlLxg4tcY
<SMSMessages>k__BackingField
BUILs5VTXx
<SpecialSims>k__BackingField
Vr9LOu19XN
<Subscribers>k__BackingField
uIMLhLtUOB
<Suppliers>k__BackingField
G2qL62rIeK
<Topups>k__BackingField
bTDL4nvbbb
<TopupCommissions>k__BackingField
jmDLmqbFAj
<TopupOrders>k__BackingField
diyL7U8dLs
<TopupProviders>k__BackingField
Gf3LWx0gyX
<TrailToupCommissions>k__BackingField
ODPLn2Y6Fc
<TrailToupOrders>k__BackingField
HHBLz3YDx8
<TransferIns>k__BackingField
zLhZGJ7eDa
<TransferOrders>k__BackingField
t4KZAwXyT8
<TransferOuts>k__BackingField
D0KZUiK5jC
<Transporters>k__BackingField
qgtZpJx7Zy
<TransportOrders>k__BackingField
TOSZ9TXuHr
<UserDevices>k__BackingField
LBQZiePpab
<UserInfoes>k__BackingField
NqDZIrG55M
<Vouchers>k__BackingField
LvqZHdCPw7
<WERegions>k__BackingField
QIfZuwfnAS
<WifiCards>k__BackingField
lUhZNXayLw
<WifiFactions>k__BackingField
BWyZDoiYC7
<WifiPayments>k__BackingField
tjMZtG3inO
<WifiProviders>k__BackingField
FojZdYJbfi
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BranchTarget
AppTech.MSMS.Domain.Models.BranchTarget
BranchTarget
BranchTarget
jeYZSCAFBa
<ID>k__BackingField
ncaZk0JfId
<RowVersion>k__BackingField
UH8ZPLDGJ7
<BranchPintID>k__BackingField
tQKZjVv4nm
<RemittancePointID>k__BackingField
q5kZasIAnb
<Primary>k__BackingField
eC6ZYhGbXJ
<CreatedBy>k__BackingField
f2EZw9SXLF
<BranchID>k__BackingField
auNZ04b4ML
<CreatedTime>k__BackingField
tDdZqeAvxo
<Branch>k__BackingField
XYGZMUBaqd
<RemittancePoint>k__BackingField
rNNZXnkLis
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Brochure
AppTech.MSMS.Domain.Models.Brochure
Brochure
Brochure
a69Zokmiij
<ID>k__BackingField
JjdZLnBjjP
<RowVersion>k__BackingField
H9KZZmB1qJ
<ImageUrl>k__BackingField
ylwZKpmafu
<Description>k__BackingField
yNIZye5NC8
<Channel>k__BackingField
hAqZvbY0Tb
<BranchID>k__BackingField
OgvZ22ClKV
<CreatedBy>k__BackingField
YOfZ8fpWOp
<CreatedTime>k__BackingField
cZgZ3xTCHs
<Branch>k__BackingField
CRfZblLMpP
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Bundle
AppTech.MSMS.Domain.Models.Bundle
Bundle
Bundle
efLZEnecja
<ID>k__BackingField
iYEZ5OkqeC
<ProviderNumber>k__BackingField
cYPZB47n83
<FactionID>k__BackingField
luWZlHjLef
<FactionNumber>k__BackingField
vycZT8Kxyr
<ServiceID>k__BackingField
uYlZCIqZyZ
<Code>k__BackingField
YeaZgs1GT6
<ExtraCode>k__BackingField
mowZcjie5T
<CostPrice>k__BackingField
IZuZJcc5sU
<Type>k__BackingField
exFZ1diPQv
<BindID>k__BackingField
QmrZr4VVuG
<Active>k__BackingField
qNXZQ7TjwT
<Flag>k__BackingField
f1QZFLIZqn
<ProviderID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BuyCurrency
AppTech.MSMS.Domain.Models.BuyCurrency
BuyCurrency
BuyCurrency
MmbZVw9y37
<ID>k__BackingField
V11ZR0t0EA
<RowVersion>k__BackingField
wlYZf0IJga
<Number>k__BackingField
Jr9ZehJy2N
<Amount>k__BackingField
YjJZxttfj2
<CurrencyID>k__BackingField
hqOZsnfmp4
<ExchangePrice>k__BackingField
U1fZOS3RJ5
<ExchangeAmount>k__BackingField
DaNZhBqa8i
<ExchangeCurrencyID>k__BackingField
rhhZ6IxqEC
<CreditorAccountID>k__BackingField
pddZ4GKdeV
<DebitorAccountID>k__BackingField
KpEZmW8b81
<Date>k__BackingField
wK8Z7cr2ML
<Note>k__BackingField
vO3ZWhh3ql
<EntryID>k__BackingField
eSOZncHKrB
<Year>k__BackingField
jTqZzhDZjC
<Status>k__BackingField
MBWKGxrrUe
<Channel>k__BackingField
ewTKAQ5g00
<CreatedTime>k__BackingField
QuMKUlrKA9
<CreatedBy>k__BackingField
QTTKp9L8K5
<BranchID>k__BackingField
LBJK900K24
<RefNumber>k__BackingField
tBAKi18ydw
<CreatedDate>k__BackingField
atAKIPMCys
<HourTime>k__BackingField
lZUKH3oLMj
<MinuteTime>k__BackingField
gonKurnJSt
<TransNumber>k__BackingField
qbmKNTkKo2
<Attachments>k__BackingField
rlwKDiw4X5
<ExtraInfo>k__BackingField
N1lKtZKsDk
<ExtraID>k__BackingField
EGHKdDLRbK
<SyncID>k__BackingField
xD5KSmg637
<RefID>k__BackingField
mE1KktCKFu
<BindID>k__BackingField
PmfKPwmQV0
<Binded>k__BackingField
ejsKjwW0lI
<OrderID>k__BackingField
WLrKaFcwFA
<ByOrder>k__BackingField
iAKKYfXObL
<Account>k__BackingField
d9xKwwJxWL
<Account1>k__BackingField
XOdK0I0tx4
<Branch>k__BackingField
IqeKqOs4EA
<Currency>k__BackingField
iRAKM8HwHM
<Currency1>k__BackingField
cjGKXJD3pV
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Card
AppTech.MSMS.Domain.Models.Card
Card
Card
w21KofUWi5
<ID>k__BackingField
Ky6KL5visF
<RowVersion>k__BackingField
EpgKZLHbKp
<Number>k__BackingField
U2NKK92IFH
<Name>k__BackingField
DdZKyRg4Sr
<Password>k__BackingField
MjbKv0hUkw
<Description>k__BackingField
FkTK2peojd
<SerialNo>k__BackingField
VjSK8w60TK
<CardTypeID>k__BackingField
JVAK3T5hnA
<CardFactionID>k__BackingField
HacKb0Y7sc
<Status>k__BackingField
uo1KEhx1sD
<Note>k__BackingField
rXTK58k0vh
<Quantity>k__BackingField
YicKBh6Mbo
<Active>k__BackingField
Ft4KlffVD4
<BranchID>k__BackingField
W2iKTkwUfj
<CreatedBy>k__BackingField
qqcKCB8AYo
<CreatedTime>k__BackingField
N0OKgnFBBD
<Branch>k__BackingField
bLRKcsi3ij
<CardFaction>k__BackingField
oRKKJP1A66
<CardType>k__BackingField
OoHK1iBkxF
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardFaction
AppTech.MSMS.Domain.Models.CardFaction
CardFaction
CardFaction
UP5Kr1rcWw
<ID>k__BackingField
R7yKQgN9xc
<RowVersion>k__BackingField
bJ3KFONXgh
<Number>k__BackingField
wJfKVXqVCM
<Name>k__BackingField
LPrKRRyP3c
<CostPrice>k__BackingField
qDHKfnd7XX
<SelePrice>k__BackingField
FHqKe5hBLG
<CardTypeID>k__BackingField
rXgKx99xpH
<Active>k__BackingField
zX2KsLjR2h
<Note>k__BackingField
sw5KOPYCve
<OrderNo>k__BackingField
dNnKhWIW1j
<Description>k__BackingField
gOFK62Odky
<ProviderID>k__BackingField
XCmK4ciudI
<Status>k__BackingField
KriKmOBBC2
<BranchID>k__BackingField
YohK7YfR21
<CreatedBy>k__BackingField
VXcKWiMEL0
<CreatedTime>k__BackingField
zbEKnJ1kWj
<Branch>k__BackingField
z1IKzmuIPC
<Cards>k__BackingField
TxcyGVRnRP
<CardType>k__BackingField
ObCyA6NnsU
<UserInfo>k__BackingField
Ic8yUQl19O
<CardOrders>k__BackingField
rxnypCl5Op
<CardPayments>k__BackingField
gBOy97IPQM
<cardAll>k__BackingField
Cm0yi8NVsK
<cardNotPayed>k__BackingField
j5MyIsIXPQ
<cardPayed>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardOrder
AppTech.MSMS.Domain.Models.CardOrder
CardOrder
CardOrder
baJyH9gXDj
<ID>k__BackingField
QxZyuk6sQD
<RowVersion>k__BackingField
OkTyNJhesK
<Number>k__BackingField
C06yDV24iB
<CardFactionID>k__BackingField
cqHytJH9Ee
<CardTypeID>k__BackingField
urDydTwH7G
<Amount>k__BackingField
BPdySqEwbq
<CurrencyID>k__BackingField
QgSyknw9RC
<Date>k__BackingField
tBByPtTKXx
<Note>k__BackingField
rGVyj4T8R2
<UserID>k__BackingField
Hp2yae4MMu
<Username>k__BackingField
gsDyYoJ7nh
<Password>k__BackingField
LmJywQnl9f
<Email>k__BackingField
O2Gy0ZmEcR
<Phone>k__BackingField
aN9yqFQL8F
<Channel>k__BackingField
YUVyMT1aod
<ParentID>k__BackingField
pZsyX2NxPP
<ServiceID>k__BackingField
hGdyo6nI4y
<AccountID>k__BackingField
Y0TyLQptlh
<Status>k__BackingField
LHsyZK2RcP
<BranchID>k__BackingField
iXYyKafTsi
<CreatedBy>k__BackingField
ALXyyWRh3l
<CreatedTime>k__BackingField
INByvsGLB9
<Account>k__BackingField
cUWy2BMm2r
<Branch>k__BackingField
t00y8AoWRt
<CardFaction>k__BackingField
Snhy3UNQhq
<CardType>k__BackingField
M4tybgCqlZ
<Currency>k__BackingField
IL2yE9RMUG
<OrderInfo>k__BackingField
DRVy5en3Tp
<ServiceInfo>k__BackingField
LGDyBqwKBB
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardPayment
AppTech.MSMS.Domain.Models.CardPayment
CardPayment
CardPayment
S1KylDtbPP
<ID>k__BackingField
PSWyTBr3Lb
<RowVersion>k__BackingField
kPQyCdyHV0
<Number>k__BackingField
LARygOlZOo
<CardFactionID>k__BackingField
At4ycAQE6T
<CardTypeID>k__BackingField
UIAyJICefx
<Amount>k__BackingField
ebyy1PblKU
<CurrencyID>k__BackingField
HYmyrZ7tkI
<Date>k__BackingField
IlwyQKpy6C
<Note>k__BackingField
ftoyFVH04Q
<Username>k__BackingField
IjtyVk7tRZ
<Password>k__BackingField
t32yRWEua6
<Email>k__BackingField
UfUyf8aD2T
<Phone>k__BackingField
m6YyeHqn8I
<Channel>k__BackingField
VuSyxqiBTp
<EntryID>k__BackingField
TQUysG75WD
<CreditorAccountID>k__BackingField
JlNyObp33H
<DebitorAccountID>k__BackingField
SCgyhG40I1
<Year>k__BackingField
zWYy6XQFai
<ParentID>k__BackingField
GYmy4PyLKP
<ServiceID>k__BackingField
eHjymXEVxN
<AccountID>k__BackingField
bk1y75wYJe
<Status>k__BackingField
VYMyWKy30C
<BranchID>k__BackingField
M9DynVSEli
<CreatedBy>k__BackingField
tC1yzOTLHb
<CreatedTime>k__BackingField
VxqvGsoFuC
<Account>k__BackingField
zYTvAITK4M
<Branch>k__BackingField
DIUvUGBRTt
<CardFaction>k__BackingField
EQ1vpl5Q8V
<CardType>k__BackingField
Sxev9pJte9
<Currency>k__BackingField
Lnivi8B51o
<OrderInfo>k__BackingField
lV2vI7XjyV
<ServiceInfo>k__BackingField
S9fvHq8VPn
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardType
AppTech.MSMS.Domain.Models.CardType
CardType
CardType
HFJvuQqyAp
<ID>k__BackingField
fSSvNy5GMv
<RowVersion>k__BackingField
zfTvDOeSbe
<Number>k__BackingField
OyBvtEr5yq
<Name>k__BackingField
MGAvdcKmMO
<Image>k__BackingField
it0vSlDjst
<Active>k__BackingField
TeZvkvQdob
<Note>k__BackingField
vgmvPVQs19
<Description>k__BackingField
PmnvjWfdLq
<Type>k__BackingField
Axlva8dF0i
<AccountID>k__BackingField
EqnvY4ALjr
<BranchID>k__BackingField
EshvwWmhb3
<CreatedBy>k__BackingField
QKtv022cPk
<CreatedTime>k__BackingField
lXXvqATYIu
<Account>k__BackingField
iTUvMDtmEX
<Branch>k__BackingField
kBlvX27plI
<Cards>k__BackingField
vvRvoeXjjl
<CardFactions>k__BackingField
oGPvL0FhvN
<CardOrders>k__BackingField
MDKvZG0JnK
<CardPayments>k__BackingField
W1JvKFTLBE
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashDeposit
AppTech.MSMS.Domain.Models.CashDeposit
CashDeposit
CashDeposit
eAnvyHkRPl
<ID>k__BackingField
ibMvviJtMl
<RowVersion>k__BackingField
QCev2onS9x
<Number>k__BackingField
wh4v8Cx3oU
<Date>k__BackingField
NPKv3IGB4Q
<Amount>k__BackingField
PcGvbeKgbY
<CurrencyID>k__BackingField
V9YvEs57Qq
<CreditorAccountID>k__BackingField
tJZv5eIBLo
<DebitorAccountID>k__BackingField
lCovBvytbe
<AgentID>k__BackingField
KD7vlsQ10r
<Note>k__BackingField
AlJvTlkYGx
<Prints>k__BackingField
FK8vCltPxh
<EntryID>k__BackingField
L2ovgIfxNT
<IsDebited>k__BackingField
CEavci4KrR
<RefNumber>k__BackingField
leIvJtyjlu
<AttachmentNumbers>k__BackingField
Dvbv1WRaY8
<CreatedBy>k__BackingField
nk1vrYAdoM
<BranchID>k__BackingField
oN3vQymopg
<CreatedTime>k__BackingField
zEwvF5F0lo
<Depositor>k__BackingField
aVSvVqQYL9
<Year>k__BackingField
qYhvRsRvDn
<Status>k__BackingField
L5ZvfM2MpY
<CreatedDate>k__BackingField
XgdveeTU2M
<HourTime>k__BackingField
I8cvx2md8s
<MinuteTime>k__BackingField
YqovsWWI5V
<TransNumber>k__BackingField
lKVvOUjVah
<Channel>k__BackingField
JFQvhGZILo
<Attachments>k__BackingField
LrYv6w9sBF
<ExtraInfo>k__BackingField
A33v40pYaZ
<ExtraID>k__BackingField
NVVvmMBTTd
<SyncID>k__BackingField
bCov7sMfNK
<SyncEntryID>k__BackingField
LJ6vWcCCxs
<RefID>k__BackingField
tMUvnGBTV3
<BindID>k__BackingField
G5mvzAtF31
<Binded>k__BackingField
u482GiWDgg
<OrderID>k__BackingField
RL62AOKtjZ
<ByOrder>k__BackingField
Hmm2UWAJAe
<IsSync>k__BackingField
mv12pdbMg8
<Type>k__BackingField
e6k29tCKTw
<Account>k__BackingField
SFU2iHa3aF
<Account1>k__BackingField
Fin2Igpuyr
<Agent>k__BackingField
i2s2H1cnOY
<Branch>k__BackingField
veL2uv5hh9
<Currency>k__BackingField
Me22NE9Vat
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashIn
AppTech.MSMS.Domain.Models.CashIn
CashIn
CashIn
alx2D3eMh2
<ID>k__BackingField
IDH2tpQHYU
<RowVersion>k__BackingField
Pkj2d1t7dc
<Number>k__BackingField
GGw2SvhhWT
<Date>k__BackingField
brm2kIsFaj
<Method>k__BackingField
PMZ2PK4kou
<Amount>k__BackingField
Vpb2jS5hFq
<CurrencyID>k__BackingField
xox2adYeD6
<CreditorAccountID>k__BackingField
KPv2YaxD1s
<DCAmount>k__BackingField
qoj2wkT3O3
<DebitorAccountID>k__BackingField
pHJ20N8pUf
<Note>k__BackingField
v5E2qi9lWT
<Delivery>k__BackingField
HVk2MAehqd
<ChequeID>k__BackingField
ieL2XQ92kv
<EntryID>k__BackingField
yqc2oKqJYo
<RefNumber>k__BackingField
hXJ2Lq5OV9
<AttachmentNumbers>k__BackingField
V422ZGErFK
<CreatedTime>k__BackingField
rM82K1ay17
<CreatedBy>k__BackingField
n8V2ybHpAg
<BranchID>k__BackingField
JOw2vLONnC
<Year>k__BackingField
uBO22VqNyc
<Status>k__BackingField
c3W287oPbv
<CreatedDate>k__BackingField
V8M23HS34g
<HourTime>k__BackingField
FQR2bidiae
<MinuteTime>k__BackingField
Lsv2EmdBsw
<TransNumber>k__BackingField
EME25YMZjg
<Channel>k__BackingField
LUV2BbNU4f
<Attachments>k__BackingField
zMP2ltl3rZ
<ExtraInfo>k__BackingField
g6f2TwHPrv
<ExtraID>k__BackingField
xEI2CAljLQ
<SyncID>k__BackingField
ny12gXWfQN
<RefID>k__BackingField
Jcs2coMyLb
<BindID>k__BackingField
lZW2Jf2hRr
<Account>k__BackingField
VKP21Gg49g
<Account1>k__BackingField
h9O2r3eIkt
<Branch>k__BackingField
OF02QHd3G6
<Cheque>k__BackingField
GBw2FE0MeJ
<Currency>k__BackingField
vUt2V4jkAP
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashOut
AppTech.MSMS.Domain.Models.CashOut
CashOut
CashOut
H5u2Rhjd6k
<ID>k__BackingField
kgw2fZFGKh
<RowVersion>k__BackingField
JBP2e00uHo
<Number>k__BackingField
wmA2xNM0NY
<Date>k__BackingField
N9R2ss6rxx
<Method>k__BackingField
VeW2OKpiUq
<Amount>k__BackingField
oAw2h8495p
<CurrencyID>k__BackingField
yH426Nn6Qg
<CreditorAccountID>k__BackingField
LeD24QByZ4
<DCAmount>k__BackingField
hgG2m1ldwj
<DebitorAccountID>k__BackingField
wpj27SrVWu
<Note>k__BackingField
f0u2WHhK0E
<Delivery>k__BackingField
Pjp2nmGJa9
<ChequeID>k__BackingField
sF12zmxs2u
<EntryID>k__BackingField
c0c8GSAc7a
<RefNumber>k__BackingField
MGl8A9bLJo
<AttachmentNumbers>k__BackingField
Va48U6Wfys
<CreatedBy>k__BackingField
fEn8phTx85
<BranchID>k__BackingField
h6N89us70Q
<CreatedTime>k__BackingField
CIP8iloJTA
<Year>k__BackingField
npg8IxOEiH
<Status>k__BackingField
IAK8HZbKL1
<CreatedDate>k__BackingField
ecQ8uVsNbe
<HourTime>k__BackingField
FWp8Nlwjww
<MinuteTime>k__BackingField
v9l8Dkxemu
<TransNumber>k__BackingField
o6W8tciv6t
<Channel>k__BackingField
tFn8dqhKUs
<Attachments>k__BackingField
r6t8SETWs1
<ExtraInfo>k__BackingField
sPk8k1JN1b
<ExtraID>k__BackingField
t2e8PiP5Cl
<SyncID>k__BackingField
Hyq8j9vmfO
<RefID>k__BackingField
KSw8a4LXiV
<BindID>k__BackingField
T6O8Yd1eJP
<Binded>k__BackingField
G4W8w1udxa
<Account>k__BackingField
Elw805nbPd
<Account1>k__BackingField
Cvi8qDb1s2
<Branch>k__BackingField
WU08MVwKTJ
<Cheque>k__BackingField
FT28Xv0DVL
<Currency>k__BackingField
cia8oXlXNQ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashTransfer
AppTech.MSMS.Domain.Models.CashTransfer
CashTransfer
CashTransfer
TZn8LrAaOp
<ID>k__BackingField
eGJ8ZaL0fn
<RowVersion>k__BackingField
giw8K4q5vt
<Number>k__BackingField
gdH8yOWCKa
<Amount>k__BackingField
IOX8vfKZqM
<CurrencyID>k__BackingField
Sff82x4l2W
<CreditorAccountID>k__BackingField
T4i88PsTF3
<DebitorAccountID>k__BackingField
UEw83bBZX3
<Date>k__BackingField
qXB8byLaob
<Note>k__BackingField
WTh8EilrrU
<EntryID>k__BackingField
taM85B0lJo
<RefNumber>k__BackingField
WuC8BP6oIB
<CreatedTime>k__BackingField
YtR8ldgjZC
<CreatedBy>k__BackingField
g8E8Tr7LF3
<BranchID>k__BackingField
Yy28C5vq89
<Year>k__BackingField
D598gMwROi
<SenderID>k__BackingField
tlI8cxRS5y
<BeneficiaryID>k__BackingField
ziD8JBySXg
<Status>k__BackingField
vib81Y0dH7
<Channel>k__BackingField
WuN8r0j7Sm
<CreatedDate>k__BackingField
iXh8QBSIuU
<HourTime>k__BackingField
c8h8F9vEp1
<MinuteTime>k__BackingField
c5G8VrlOiq
<TransNumber>k__BackingField
xZU8Rc0pRM
<Attachments>k__BackingField
WYE8fK1tLd
<ExtraInfo>k__BackingField
bsc8eSE5QE
<ExtraID>k__BackingField
JJt8xpmd6Q
<SyncID>k__BackingField
Iur8slEiUm
<RefID>k__BackingField
M968OyNknb
<BindID>k__BackingField
Wfd8hEwVlD
<Binded>k__BackingField
yKt86pM6jh
<Account>k__BackingField
aYq84PqF80
<Account1>k__BackingField
aXL8mAJiY8
<Branch>k__BackingField
iGR87bK9dW
<Currency>k__BackingField
O0G8WwULjf
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashWithdraw
AppTech.MSMS.Domain.Models.CashWithdraw
CashWithdraw
CashWithdraw
Mx38n22tvc
<ID>k__BackingField
mPZ8z6FYVK
<RowVersion>k__BackingField
Net3GaMPZB
<Number>k__BackingField
Ghd3AZ0SbS
<Date>k__BackingField
f1I3UH8Lds
<Amount>k__BackingField
fob3pR1y5y
<CurrencyID>k__BackingField
rBx397EoVT
<CreditorAccountID>k__BackingField
sYf3iy1628
<DebitorAccountID>k__BackingField
iXg3I6gYHH
<AgentID>k__BackingField
R263HvTHbB
<Note>k__BackingField
LUT3uYVE4V
<Delivery>k__BackingField
mqD3N3fumQ
<Prints>k__BackingField
QrJ3Dpi7M8
<EntryID>k__BackingField
ysP3t4duCc
<IsDebited>k__BackingField
RUN3dRtgYC
<RefNumber>k__BackingField
nMn3SwUNcS
<AttachmentNumbers>k__BackingField
TW03knXCaB
<CreatedBy>k__BackingField
ztT3PCYiuO
<BranchID>k__BackingField
V6x3jAAVaW
<CreatedTime>k__BackingField
m8Z3aHbGWn
<Year>k__BackingField
zAM3YofIoK
<Status>k__BackingField
hOI3wrLym7
<CreatedDate>k__BackingField
tX230uvvHx
<HourTime>k__BackingField
cDb3qQjYBD
<MinuteTime>k__BackingField
Xth3M9JON3
<TransNumber>k__BackingField
tQN3XpHHin
<Channel>k__BackingField
EXS3oaBxDO
<Attachments>k__BackingField
ltV3Lx3ms8
<ExtraInfo>k__BackingField
IjZ3ZKcGWf
<ExtraID>k__BackingField
XxO3Kb3btd
<SyncID>k__BackingField
nDU3yrgd0H
<RefID>k__BackingField
b9F3vx7R9A
<BindID>k__BackingField
KmH32I147L
<Binded>k__BackingField
Vww38IgbE2
<Account>k__BackingField
lM133CUCyR
<Account1>k__BackingField
g4n3beLGZJ
<Agent>k__BackingField
IMt3ENhvH9
<Branch>k__BackingField
Kkb351KfZp
<Currency>k__BackingField
SWC3BD1XKU
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Cheque
AppTech.MSMS.Domain.Models.Cheque
Cheque
Cheque
L2w3l9osei
<ID>k__BackingField
rYu3TQVwqD
<ChequeNumber>k__BackingField
JmS3CPZWZq
<ChequeDate>k__BackingField
U0d3gnIOJA
<BankID>k__BackingField
B9v3c5ZHiR
<AccountID>k__BackingField
HbH3Jxvo8D
<ExchangeAccountID>k__BackingField
f7n31QeZps
<Amount>k__BackingField
NH63rog5C6
<CurrencyID>k__BackingField
T0J3QJJukC
<Date>k__BackingField
VUr3FA8C2t
<VoucherID>k__BackingField
sfl3VHQn70
<ChequeType>k__BackingField
SPX3Rgg5EX
<CreatedBy>k__BackingField
gDX3fIOIBP
<BranchID>k__BackingField
AtH3eZh1OR
<CreatedTime>k__BackingField
CDV3xN0aHu
<IsDebited>k__BackingField
H393sqSlXW
<Year>k__BackingField
FnQ3OCsaks
<Account>k__BackingField
JNV3hVTIQt
<Account1>k__BackingField
ysC360dC2f
<Bank>k__BackingField
pgh34ADPre
<Branch>k__BackingField
SMM3mIpoll
<CashIns>k__BackingField
XXY37GaT7h
<CashOuts>k__BackingField
xUj3WwhUot
<Currency>k__BackingField
GSq3ndoZMu
<UserInfo>k__BackingField
kyF3zLklgA
<Voucher>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClaimGroup
AppTech.MSMS.Domain.Models.ClaimGroup
ClaimGroup
ClaimGroup
unFbGqjOKB
<ID>k__BackingField
FYUbAIGAlX
<RowVersion>k__BackingField
yqfbUNrKPq
<Name>k__BackingField
NfFbpRcdKw
<Note>k__BackingField
B3Rb9Vlds9
<BranchID>k__BackingField
N7ybiEJL8q
<CreatedBy>k__BackingField
G3MbIJjURv
<CreatedTime>k__BackingField
XI3bHYsqFV
<Branch>k__BackingField
z86bunqFos
<UserInfo>k__BackingField
wFUbNqSp6T
<UserInfoes>k__BackingField
YKhbDP7eZQ
<UserPermissions>k__BackingField
ynYbtscpw7
<SelectedServices>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Client
AppTech.MSMS.Domain.Models.Client
Client
Client
RCZbdi7qPq
<ID>k__BackingField
iZRbSnXxek
<RowVersion>k__BackingField
lysbkbVr7a
<Number>k__BackingField
OVobPMS4W0
<Name>k__BackingField
NdnbjoXgJn
<AccountID>k__BackingField
RAVbaEGCeb
<Username>k__BackingField
AdZbYBmxQm
<Password>k__BackingField
NxcbwnkY1O
<ShopName>k__BackingField
Cd2b0nRxsv
<PhoneNumber>k__BackingField
poobqSHypl
<Address>k__BackingField
REQbMj7bTK
<IsActive>k__BackingField
DXKbXdUhuy
<CreatedBy>k__BackingField
oh1boFZqjO
<BranchID>k__BackingField
bWybLPbTxV
<CreatedTime>k__BackingField
EqTbZ16OHW
<BirthDate>k__BackingField
sfybKMnOW1
<ContactNumber>k__BackingField
N02byelluT
<Email>k__BackingField
iDybv0TjCy
<CardType>k__BackingField
gqMb2lPoWL
<CardNumber>k__BackingField
koeb8cOQ1r
<CardIssuePlace>k__BackingField
Epqb3LTMr9
<CardIssueDate>k__BackingField
zQwbbCK7ls
<ImageName>k__BackingField
uW3bEKVow0
<ActivateBy>k__BackingField
tukb5UcIxw
<Channel>k__BackingField
PrabBWpShP
<DeviceID>k__BackingField
McIbldOSXn
<Type>k__BackingField
VghbTQq3Qr
<Note>k__BackingField
WO2bCvpuxW
<Status>k__BackingField
eOUbgnIBPq
<PhoneNumberConfirmed>k__BackingField
L5nbc3Nvfx
<TwoFactorEnabled>k__BackingField
nyObJXJmTn
<AgentID>k__BackingField
QhYb1tGRxl
<SyncAccountID>k__BackingField
nPgbrQMib1
<LastSyncedTime>k__BackingField
G66bQRPuA2
<DistributorID>k__BackingField
wYvbFnERDg
<ParentAccountID>k__BackingField
JNdbVNAep5
<ParentType>k__BackingField
vqSbRTJLlg
<GroupID>k__BackingField
NX2bfDxAnO
<Token>k__BackingField
uYobeKrJC9
<Account>k__BackingField
Y08bxntQAV
<Agent>k__BackingField
JNbbsVeEsG
<Branch>k__BackingField
L23bO9Lp9Y
<UserInfo>k__BackingField
pAObhAHu9V
<SMSDispatches>k__BackingField
upXb6XwKwv
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CommissionReceipt
AppTech.MSMS.Domain.Models.CommissionReceipt
CommissionReceipt
CommissionReceipt
tRNb4VmyGZ
<ID>k__BackingField
w7hbm6b4XI
<RowVersion>k__BackingField
qbeb7o9nc6
<Number>k__BackingField
PIEbW67LhL
<StartDate>k__BackingField
zFKbnMCe9y
<EndDate>k__BackingField
OwobzOowif
<OpsCount>k__BackingField
DpDEGPSJWP
<Percentage>k__BackingField
PclEA7FfrZ
<Amount>k__BackingField
bjvEU2CSKC
<CurrencyID>k__BackingField
j8yEp2ugWR
<AccountID>k__BackingField
VeOE9xWjo2
<AccountGroupID>k__BackingField
iecEiyUbAy
<ServiceState>k__BackingField
EpQEIO8ZBK
<ServiceGroupID>k__BackingField
GyyEHY2I7Y
<ServiceID>k__BackingField
yB9EuCti96
<EntryID>k__BackingField
JLbENPNKpV
<Note>k__BackingField
gAKEDCPFuI
<RefNumber>k__BackingField
hwyEtW7he2
<Date>k__BackingField
Eo0EdQowG9
<Channel>k__BackingField
O5uESbEYHI
<ExtraInfo>k__BackingField
uEXEkjKCSn
<Status>k__BackingField
SLpEPO9pHS
<Year>k__BackingField
BGMEjJIYJZ
<BranchID>k__BackingField
FGBEa3jAVQ
<CreatedBy>k__BackingField
Y4nEYMoGTn
<CreatedTime>k__BackingField
wEVEwShIIP
<Account>k__BackingField
K4pE0CstDN
<Branch>k__BackingField
Am0EqpRisw
<Currency>k__BackingField
bFyEMJOu0Q
<Journal>k__BackingField
dL3EXOHdWn
<ServiceInfo>k__BackingField
wwSEopB7PU
<UserInfo>k__BackingField
QuMELo8Abv
<CommissionReceiptLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CommissionReceiptLine
AppTech.MSMS.Domain.Models.CommissionReceiptLine
CommissionReceiptLine
CommissionReceiptLine
FkXEZjwSIR
<ID>k__BackingField
HiuEKthvHp
<ParentID>k__BackingField
x1IEyShpSB
<AccountID>k__BackingField
z4KEvRNt5M
<TotalTopup>k__BackingField
wxME2LgVud
<Amount>k__BackingField
sXNE8OGR9G
<Note>k__BackingField
yiiE3O64VB
<Account>k__BackingField
qApEbPZrVK
<CommissionReceipt>k__BackingField
z9cEEm0tRA
<AccountName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Company
AppTech.MSMS.Domain.Models.Company
Company
Company
Qp9E5YhbYe
<ID>k__BackingField
i7NEBeo3W9
<Code>k__BackingField
nPlEl4JScj
<Name>k__BackingField
hU2ETTpene
<ShortName>k__BackingField
oTVECEcOa7
<Address>k__BackingField
mCPEgltaE2
<Logo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ConsumeInvoice
AppTech.MSMS.Domain.Models.ConsumeInvoice
ConsumeInvoice
ConsumeInvoice
OTyEcp5hgv
<ID>k__BackingField
L0XEJRAEt0
<RowVersion>k__BackingField
j54E1GPcHi
<Number>k__BackingField
pW2ErhIsqW
<CreditorAccountID>k__BackingField
IeQEQCneWO
<DebitorAccountID>k__BackingField
nqQEF4AHXP
<SubscriberID>k__BackingField
Sn4EVSF6Lo
<InvoiceNo>k__BackingField
eUVERBVRAP
<Date>k__BackingField
GFLEfD0FDG
<StartDate>k__BackingField
TmLEeFK6qK
<EndDate>k__BackingField
m2CEx3sC5c
<CurrencyID>k__BackingField
SSDEsDOlDZ
<UnitPrice>k__BackingField
qoQEOvKGt8
<Quantity>k__BackingField
jYTEhoIGWA
<Amount>k__BackingField
jnfE6cSAq7
<LateAmount>k__BackingField
ST6E4oplus
<ExtraAmount>k__BackingField
gCHEmDlZdx
<Discount>k__BackingField
CsqE7KNFjK
<TotalAmount>k__BackingField
gn2EWIfG6i
<Note>k__BackingField
dIIEn5UQg4
<EntryID>k__BackingField
ssJEzpJ7q4
<RefNumber>k__BackingField
uLk5GdNlkI
<Extra>k__BackingField
JkB5AE3dVr
<AttachmentNumbers>k__BackingField
dGu5U74xce
<Footer>k__BackingField
LIg5pciYqK
<Year>k__BackingField
kKF59qjiOE
<Status>k__BackingField
yqn5igwGTs
<CreatedTime>k__BackingField
Npo5ImGjL8
<CreatedBy>k__BackingField
pPd5HftDyG
<BranchID>k__BackingField
qmD5ufXEEX
<FineAmount>k__BackingField
cYL5NJpuRl
<FineNote>k__BackingField
IVb5DUL0sg
<SubscriptionFee>k__BackingField
DeY5tGTJl8
<Account>k__BackingField
bRd5dobJOE
<Account1>k__BackingField
kWJ5SbaYmE
<Branch>k__BackingField
TjJ5kEQSqI
<Currency>k__BackingField
YLL5P9KYqu
<Journal>k__BackingField
ILO5jG1qRS
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Country
AppTech.MSMS.Domain.Models.Country
Country
Country
Irb5amP96X
<ID>k__BackingField
VsY5YkjiRT
<RowVersion>k__BackingField
m9E5wwJ8yc
<Name>k__BackingField
Xc650V27um
<CreatedBy>k__BackingField
eQZ5qUnI6n
<BranchID>k__BackingField
SYi5MFJXgC
<CreatedTime>k__BackingField
aXj5XNJSwy
<BankDeposits>k__BackingField
rOd5o18H2V
<Branch>k__BackingField
x9j5LZD24N
<UserInfo>k__BackingField
Mpq5ZIcAIG
<Provinces>k__BackingField
wP25KNwVBP
<TransferOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CoverageOrder
AppTech.MSMS.Domain.Models.CoverageOrder
CoverageOrder
CoverageOrder
Jy35ygmhwN
<ID>k__BackingField
XYP5vnW7V4
<RowVersion>k__BackingField
YwS52QWJxW
<Number>k__BackingField
iFB58wOdM0
<ParentID>k__BackingField
JTA53fYFV6
<ServiceID>k__BackingField
ik75brEG2D
<AccountID>k__BackingField
vZg5EV0C0i
<ExchangeAccountID>k__BackingField
ALD55udjF3
<Amount>k__BackingField
FvJ5B1Ii7p
<CurrencyID>k__BackingField
Ana5lEd5jZ
<AccountNumber>k__BackingField
rZI5TPV711
<EntryID>k__BackingField
m065CgRsVf
<Date>k__BackingField
dvC5gQ7uAh
<Type>k__BackingField
TTW5cHCuDO
<Status>k__BackingField
txI5JD0wc7
<IsDirect>k__BackingField
MB951kODON
<Purpose>k__BackingField
S9q5rmnGpp
<ResponseMessage>k__BackingField
crh5QJOwCj
<ResponseInfo>k__BackingField
eTR5FO1eLs
<RefNumber>k__BackingField
Ooy5VPIlIS
<Note>k__BackingField
OxG5Rr6tHJ
<Channel>k__BackingField
C695fTcYGM
<CreatedBy>k__BackingField
GF15eUFMX2
<BranchID>k__BackingField
b5M5xA2o0V
<CreatedTime>k__BackingField
lra5s1Aecp
<Account>k__BackingField
OD35OVHtCS
<Account1>k__BackingField
BmY5hDA3bm
<Branch>k__BackingField
E2e56tGXVW
<Currency>k__BackingField
Lwy54Cuss2
<OrderInfo>k__BackingField
wX35mp3i2B
<ServiceInfo>k__BackingField
f5T57xc79e
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Currency
AppTech.MSMS.Domain.Models.Currency
Currency
Currency
GJK5WNjSwu
<ID>k__BackingField
Dwe5n8TDDH
<Name>k__BackingField
KF25zjZMGa
<Symbol>k__BackingField
KpeBGhbn3D
<Note>k__BackingField
AuVBATSDSA
<CreatedBy>k__BackingField
RKdBUwnsTl
<CreatedTime>k__BackingField
tJhBpg6mPJ
<RowVersion>k__BackingField
cqDB9rvJ9s
<BranchID>k__BackingField
HVcBiSTdfb
<IsDefault>k__BackingField
b6RBI8goJG
<SyncCurrencyID>k__BackingField
MnMBHdn8D6
<AccountSlatings>k__BackingField
U1uBuCURRd
<BankDeposits>k__BackingField
UvABNiGXEA
<BankDeposits1>k__BackingField
j4JBDeMmcU
<Branch>k__BackingField
oWqBtcxES0
<BuyCurrencies>k__BackingField
dAcBdjotkb
<BuyCurrencies1>k__BackingField
v9nBS9o03c
<CardOrders>k__BackingField
uISBk1TJIb
<CardPayments>k__BackingField
OyQBP9n0oV
<CashDeposits>k__BackingField
gtIBjLsNi7
<CashIns>k__BackingField
acZBacdhB9
<CashOuts>k__BackingField
IOXBY7hITr
<CashTransfers>k__BackingField
JGmBwklr65
<CashWithdraws>k__BackingField
uAJB0mI3ay
<Cheques>k__BackingField
uGJBqxHrMk
<CommissionReceipts>k__BackingField
B9EBMqfxE7
<ConsumeInvoices>k__BackingField
vQKBXySRTc
<CoverageOrders>k__BackingField
PuYBo43xoe
<UserInfo>k__BackingField
WFCBLn7TKe
<CurrencyExchanges>k__BackingField
hWrBZKkVAx
<CurrencyRates>k__BackingField
wKLBKICmko
<CurrencyRateAccounts>k__BackingField
WOUByJoraH
<CurrencyRateAccounts1>k__BackingField
oerBvedXm8
<DepositOrders>k__BackingField
SgOB2Hbm1T
<JournalEntries>k__BackingField
aQLB806wyb
<LoanOrders>k__BackingField
BdXB3F99HI
<MerchantPayments>k__BackingField
F5xBb8aLca
<OpeningBalances>k__BackingField
mE5BEJtKcH
<OrderSatelliteQuotas>k__BackingField
RQZB5ybryO
<Payments>k__BackingField
F4LBBwYclr
<PaymentCommissions>k__BackingField
KaVBl32LsQ
<Products>k__BackingField
APiBTCtZSx
<PurchaseInvoices>k__BackingField
Bi8BCAQfbq
<ReceiptCreditors>k__BackingField
HkqBgfQX3k
<ReceiptDebitors>k__BackingField
OlxBcfitMu
<RemittanceCommissions>k__BackingField
hTKBJkYB4O
<RemittanceIns>k__BackingField
byGB1DXE5f
<RemittanceOuts>k__BackingField
L2OBr3QeE5
<RiyalMobiles>k__BackingField
VdmBQZqa0Y
<SaleCurrencies>k__BackingField
H4BBFxoLYM
<SaleCurrencies1>k__BackingField
GCaBVujRwt
<SaleInvoices>k__BackingField
CgLBRTyB40
<SatellitePayments>k__BackingField
mqVBfXtVhm
<SimInvoices>k__BackingField
SP1BeZjnHd
<SimpleEntries>k__BackingField
gQhBxZLv5X
<SimPurchases>k__BackingField
CuoBsPubKj
<CurrencyExchanges1>k__BackingField
kkyBOq4bgN
<CurrencyRates1>k__BackingField
zErBhfjK5X
<UserInfo1>k__BackingField
jsLB6c3O09
<RemittanceIns1>k__BackingField
ooIB4pjut8
<Topups>k__BackingField
rYuBmaYpeA
<TopupCommissions>k__BackingField
xZyB7fPTce
<TransferIns>k__BackingField
FX0BWqcnpN
<TransferIns1>k__BackingField
gfwBntad7c
<TransferOrders>k__BackingField
VZ2BzffoX5
<TransferOrders1>k__BackingField
cTvlGsxpwH
<TransferOuts>k__BackingField
sl8lAKatZp
<TransferOuts1>k__BackingField
VnHlUCRQY5
<TransportOrders>k__BackingField
SJUlpNd1VW
<WifiPayments>k__BackingField
PhBl98nPbK
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyExchange
AppTech.MSMS.Domain.Models.CurrencyExchange
CurrencyExchange
CurrencyExchange
GUdliTd4yq
<ID>k__BackingField
vQNlImUgsb
<RowVersion>k__BackingField
hJIlH27BCe
<SourceCurrencyID>k__BackingField
sxRluJk1iT
<TargetCurrencyID>k__BackingField
AwGlNF8sLP
<Amount>k__BackingField
WGKlDRfJ9n
<ExchangePrice>k__BackingField
DYqltaQs6i
<Note>k__BackingField
GWMld73y1y
<CreatedBy>k__BackingField
URrlSwu5nO
<BranchID>k__BackingField
mDFlko7wfD
<CreatedTime>k__BackingField
vk1lPKjYAm
<ExchangeAmount>k__BackingField
Mg7ljPj8in
<Status>k__BackingField
p8jlaw9mba
<Number>k__BackingField
OkWlY4eOOh
<Type>k__BackingField
SMblw3FyDV
<EntryID>k__BackingField
zr7l0GcCY9
<Channel>k__BackingField
fTvlqsZplt
<AccountID>k__BackingField
UA4lMIuCYL
<ParentID>k__BackingField
OFblXe450Z
<ServiceID>k__BackingField
EPAlogYnk2
<Account>k__BackingField
DtSlLf72xb
<Branch>k__BackingField
p7plZhWeP3
<Currency>k__BackingField
zHLlKS3nff
<Currency1>k__BackingField
MaYlyhuBOu
<OrderInfo>k__BackingField
HXglv7iIat
<ServiceInfo>k__BackingField
gT1l2gorDm
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRate
AppTech.MSMS.Domain.Models.CurrencyRate
CurrencyRate
CurrencyRate
qUrl8RCNR1
<ID>k__BackingField
hqWl3HBbxG
<SourceCurrencyID>k__BackingField
DiElbV3djL
<TargetCurrencyID>k__BackingField
BZflETF7pa
<ExchangeRate>k__BackingField
lUZl56cto6
<BuyPrice>k__BackingField
KeTlB4SHYC
<SellPrice>k__BackingField
GDTllhH3pC
<Note>k__BackingField
NQQlT4BvZb
<CreatedBy>k__BackingField
swqlCM9qUo
<CreatedTime>k__BackingField
g9elgJt5AL
<RowVersion>k__BackingField
denlcV3ylE
<BranchID>k__BackingField
cu6lJYZb02
<UpDown>k__BackingField
Nrpl1IrTUM
<ModifiedDate>k__BackingField
ixJlr1KJTU
<ExBuyPrice>k__BackingField
CM1lQj26aO
<ExSalePrice>k__BackingField
jAKlFtN4Fx
<Branch>k__BackingField
YWDlVMWv0q
<Currency>k__BackingField
hnblRfG9IG
<Currency1>k__BackingField
SCalfT7htg
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRateAccount
AppTech.MSMS.Domain.Models.CurrencyRateAccount
CurrencyRateAccount
CurrencyRateAccount
ymDle7F4dD
<ID>k__BackingField
AEWlxQ3T4t
<RowVersion>k__BackingField
mrolsuTMqK
<SourceCurrencyID>k__BackingField
aJRlOiZ5G2
<TargetCurrencyID>k__BackingField
zm7lhlMQwU
<AccountState>k__BackingField
Blsl6WdutZ
<AccountID>k__BackingField
fyMl4YYxUH
<AccountGroupID>k__BackingField
zY8lmLG7eY
<ExchangeRate>k__BackingField
piil7ila8r
<BuyPrice>k__BackingField
t6xlW0hmxx
<SellPrice>k__BackingField
G8NlnJoHNg
<Note>k__BackingField
D7ulz9bM6T
<ModifiedDate>k__BackingField
Tn0TGn56wA
<BranchID>k__BackingField
R3TTAAqRoT
<CreatedBy>k__BackingField
yISTU7oZyd
<CreatedTime>k__BackingField
JqtTpvRh6I
<Branch>k__BackingField
tR5T9ExE9M
<Currency>k__BackingField
eKwTiokpAg
<Currency1>k__BackingField
j4XTIhGIDs
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DbBackup
AppTech.MSMS.Domain.Models.DbBackup
DbBackup
DbBackup
xUeTHGIjBx
<ID>k__BackingField
XVZTuVbdeg
<RowVersion>k__BackingField
xXGTNhQaSk
<Number>k__BackingField
RBrTDOQJuP
<Name>k__BackingField
paKTtQiXGe
<Path>k__BackingField
XIjTd3sVNw
<Keyname>k__BackingField
gxLTSkZiNk
<Note>k__BackingField
Il8TkueUjA
<BranchID>k__BackingField
foBTPVOJVU
<CreatedBy>k__BackingField
gjxTjcIPgu
<CreatedTime>k__BackingField
DfVTaCRO6N
<Branch>k__BackingField
TbHTYq8hhu
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DbScript
AppTech.MSMS.Domain.Models.DbScript
DbScript
DbScript
P1UTwHGGCj
<ID>k__BackingField
nMRT0ycjfK
<Number>k__BackingField
dEuTqF0ky5
<Name>k__BackingField
AkwTMBr35E
<ServerVersion>k__BackingField
eWOTXb3IjH
<Description>k__BackingField
gUNToaFJkC
<Note>k__BackingField
uduTLe23cu
<Status>k__BackingField
c6VTZ9NXqd
<CreatedTime>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DepositOrder
AppTech.MSMS.Domain.Models.DepositOrder
DepositOrder
DepositOrder
yR8TKo96Gy
<ID>k__BackingField
ck2Ty0GxjA
<RowVersion>k__BackingField
XQeTvkE7hV
<CreatedBy>k__BackingField
qPNT20H2bQ
<BranchID>k__BackingField
kw3T8CRpl1
<CreatedTime>k__BackingField
v5aT3aSKr1
<Amount>k__BackingField
LYtTbv8R9n
<CurrencyID>k__BackingField
A9uTEAiIuc
<MCAmount>k__BackingField
KKST5fgcpd
<Note>k__BackingField
whTTBf0wuX
<Channel>k__BackingField
WInTlraFE7
<ImageName>k__BackingField
HlrTTWtHIM
<ParentID>k__BackingField
dqwTCBmdlY
<ServiceID>k__BackingField
MLmTgY3QT9
<AccountID>k__BackingField
tr6Tc6dZLK
<Account>k__BackingField
ssVTJQAFgN
<Branch>k__BackingField
oWtT1fAwnZ
<Currency>k__BackingField
EfRTrrbcDV
<OrderInfo>k__BackingField
Ld0TQZwUPQ
<ServiceInfo>k__BackingField
kgpTFNmEob
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Device
AppTech.MSMS.Domain.Models.Device
Device
Device
v5jTVDplio
<ID>k__BackingField
jd0TRgvRc4
<RowVersion>k__BackingField
VIxTfR6asZ
<Number>k__BackingField
ihiTeegaAE
<AccountID>k__BackingField
CIyTxy7SD2
<Active>k__BackingField
cmfTsR9wAc
<Note>k__BackingField
W71TO6BkJk
<BranchID>k__BackingField
wbsThnULS7
<CreatedBy>k__BackingField
hEDT64q9q7
<CreatedTime>k__BackingField
evOT43F7vg
<Account>k__BackingField
CKmTml1RD2
<Branch>k__BackingField
LnwT76fXho
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Distributor
AppTech.MSMS.Domain.Models.Distributor
Distributor
Distributor
fpdTWpuHyM
<ID>k__BackingField
CkRTnQJkLv
<RowVersion>k__BackingField
wW2TzcX6Ok
<Number>k__BackingField
g6tCGfrdlU
<Name>k__BackingField
feSCAjLQO3
<AccountID>k__BackingField
c45CUjSRJH
<Major>k__BackingField
pvvCpkNuqr
<PhoneNumber>k__BackingField
K4IC9eKdn6
<Address>k__BackingField
SYKCiLbZ4T
<IsActive>k__BackingField
kQLCI9tR1A
<BirthDate>k__BackingField
RenCHwp5X9
<ContactNumber>k__BackingField
gSgCuFwjrA
<Email>k__BackingField
VVJCNCENuY
<CardType>k__BackingField
sORCDYXWMK
<CardNumber>k__BackingField
yVeCtRB0lL
<CardIssuePlace>k__BackingField
tfpCdJ7lRr
<CardIssueDate>k__BackingField
ou7CS6L41O
<ImageName>k__BackingField
nKqCkfCdxG
<ActivateBy>k__BackingField
rQICPoUFB9
<Channel>k__BackingField
WWNCjakTME
<DeviceID>k__BackingField
KApCaLVK1w
<Type>k__BackingField
XE1CYMYB9v
<Note>k__BackingField
pEaCwkcvel
<Status>k__BackingField
MyYC0sR2J6
<PhoneNumberConfirmed>k__BackingField
OMRCqACbT2
<TwoFactorEnabled>k__BackingField
DbPCM4Lwra
<AgentID>k__BackingField
jptCXeFCBE
<SyncAccountID>k__BackingField
an6Co81fpu
<LastSyncedTime>k__BackingField
fxhCLNx1B4
<CreatedBy>k__BackingField
qnoCZTeYQc
<BranchID>k__BackingField
Tv5CK4w11e
<CreatedTime>k__BackingField
GlZCyvOIuS
<Account>k__BackingField
R7XCv915Un
<Agent>k__BackingField
r4KC2oA2lE
<Branch>k__BackingField
zvvC8vJofH
<UserInfo>k__BackingField
RcGC3m1jvN
<Username>k__BackingField
PXmCblbeu0
<Password>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ELMAH_Error
AppTech.MSMS.Domain.Models.ELMAH_Error
ELMAH_Error
ELMAH_Error
zIRCEuEm0K
<ErrorId>k__BackingField
gukC5UjQB9
<Application>k__BackingField
QyICBeqLYL
<Host>k__BackingField
q6UCl0GRRe
<Type>k__BackingField
E9CCTtYSVB
<Source>k__BackingField
mEBCChqy3r
<Message>k__BackingField
JyBCgjyirH
<User>k__BackingField
xQ3CctnulE
<StatusCode>k__BackingField
SE7CJcaMBQ
<TimeUtc>k__BackingField
gtjC1qSEGC
<Sequence>k__BackingField
qkGCrVXECq
<AllXml>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Error
AppTech.MSMS.Domain.Models.Error
Error
Error
oFrCQoxolb
<ID>k__BackingField
sawCFQ0jw9
<Channel>k__BackingField
gyLCV8ges4
<UserID>k__BackingField
lMxCRrrDB5
<Date>k__BackingField
UJJCfP2uDg
<Source>k__BackingField
XUfCekEGTC
<Type>k__BackingField
bdgCxeCSjf
<IpAddress>k__BackingField
ea8CsqnyFf
<UserAgent>k__BackingField
r8lCOhy00i
<Message>k__BackingField
MotChbG2p0
<Exception>k__BackingField
dJyC63xj80
<InnerException>k__BackingField
RJaC4uwmg1
<Extra>k__BackingField
Pq0CmRw1lf
<Stacktrace>k__BackingField
T2RC7OX786
<HttpReferer>k__BackingField
aDiCWDLij8
<PathAndQuery>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Exchanger
AppTech.MSMS.Domain.Models.Exchanger
Exchanger
Exchanger
ydHCnom7bN
<ID>k__BackingField
KdHCzmZ8e6
<RowVersion>k__BackingField
RcWgGoMS0n
<Number>k__BackingField
Q0TgA0a0KC
<Name>k__BackingField
x6QgUKjg8k
<CreatedBy>k__BackingField
zFTgpDme5r
<BranchID>k__BackingField
Gsmg9KUxU0
<CreatedTime>k__BackingField
TKLgiXigLA
<AccountID>k__BackingField
EWvgI5H7LD
<IsExternal>k__BackingField
WNVgHFSrve
<Account>k__BackingField
rmygujVleD
<Branch>k__BackingField
bQxgNkQxJR
<UserInfo>k__BackingField
H9hgDMLKS8
<ExchangerTargets>k__BackingField
oeEgtAC0Qt
<TransferIns>k__BackingField
ywigdg2wPi
<TransferOrders>k__BackingField
LnCgSxoyA2
<TransferOuts>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExchangerTarget
AppTech.MSMS.Domain.Models.ExchangerTarget
ExchangerTarget
ExchangerTarget
KwkgkeM08t
<ID>k__BackingField
DKTgP6CqKp
<RowVersion>k__BackingField
XICgj0Nc3f
<ExchangerID>k__BackingField
rwjga5MXmS
<RemittancePointID>k__BackingField
SYvgYtBAqj
<ProviderID>k__BackingField
GwwgwmYwL1
<ExtraID>k__BackingField
AbJg0aZjTB
<BindID>k__BackingField
PtNgqsEmwU
<SyncID>k__BackingField
xdSgM8icki
<ParentID>k__BackingField
P7MgX1ZVU6
<Primary>k__BackingField
XEygoqK9IF
<Binded>k__BackingField
K9CgLliK9i
<Active>k__BackingField
WLUgZqWfGa
<Synced>k__BackingField
XC9gKwuPUT
<Status>k__BackingField
tY2gyyTJVE
<Type>k__BackingField
FHWgvNI0JN
<Binding>k__BackingField
xAwg23HYIi
<Note>k__BackingField
Y2Kg8plEV4
<ExtraInfo>k__BackingField
eOOg3UfpEW
<Token>k__BackingField
qqkgb6bbJS
<CreatedBy>k__BackingField
THZgEJoZlZ
<BranchID>k__BackingField
qUgg5gUarH
<CreatedTime>k__BackingField
VgZgBc1Hdr
<Branch>k__BackingField
zZVglbKbrb
<Exchanger>k__BackingField
aX6gTA3iYl
<RemittancePoint>k__BackingField
P8ggCQAOix
<TopupProvider>k__BackingField
i0RggX2YBS
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExternalBranch
AppTech.MSMS.Domain.Models.ExternalBranch
ExternalBranch
ExternalBranch
PLhgcQXJMR
<ID>k__BackingField
IotgJVCpPo
<RowVersion>k__BackingField
AIhg1y69cp
<Number>k__BackingField
NUQgristLc
<Name>k__BackingField
VpggQPBf23
<AccountID>k__BackingField
B3ygFA9809
<PhoneNumber>k__BackingField
Y36gVCSw47
<Address>k__BackingField
wk9gRlNkgT
<Fax>k__BackingField
n1cgfUlcNv
<Note>k__BackingField
ydcgewxIWO
<Key>k__BackingField
naDgxh7Mvy
<CreatedBy>k__BackingField
yh4gspigaZ
<BranchID>k__BackingField
mQKgOcYE59
<CreatedTime>k__BackingField
mQMghmkhNw
<Status>k__BackingField
AVdg6DGKCV
<ImageName>k__BackingField
TPUg49gZca
<IsExternal>k__BackingField
n6dgm9B6gS
<IsSpecial>k__BackingField
rxZg7qPfI3
<BaseUrl>k__BackingField
uGNgWcduoD
<Type>k__BackingField
RufgnTE6jF
<Token>k__BackingField
ByYgze0H1m
<AppName>k__BackingField
QvbcGjg2Sn
<ContactNumbers>k__BackingField
uRdcAhBgVx
<ClaimID>k__BackingField
FNGcU2Oj7Z
<MainBranchID>k__BackingField
X5McpCP1l2
<Keyname>k__BackingField
wUjc9oSZVq
<ExtraInfo>k__BackingField
NxUciuopfR
<ExtraID>k__BackingField
wUAcIG2CHe
<SyncID>k__BackingField
oPbcHGEw6y
<SyncAccountID>k__BackingField
hs1cu3FJvm
<Account>k__BackingField
T6McNcdFR4
<Branch>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Faction
AppTech.MSMS.Domain.Models.Faction
Faction
Faction
E5lcDkOrsa
<ID>k__BackingField
tJCctpNAis
<RowVersion>k__BackingField
Oy5cdjoDsR
<Name>k__BackingField
fdjcSxCIKn
<Price>k__BackingField
o8Tckdf5QC
<Type>k__BackingField
Gq2cPigTRj
<CreatedBy>k__BackingField
Uw1cjxGcvy
<BranchID>k__BackingField
JHxcaw7Gv3
<CreatedTime>k__BackingField
Bb1cYgQun7
<ServiceID>k__BackingField
buCcwav2Mk
<ProviderPrice>k__BackingField
ILbc0MIu1N
<PersonnalPrice>k__BackingField
YC0cqbghrv
<OrderNo>k__BackingField
JmwcMNF5nq
<ProviderCode>k__BackingField
XnKcXJKHX9
<RefID>k__BackingField
BllcoNhgbx
<Note>k__BackingField
xBCcLBhgDG
<Number>k__BackingField
kCjcZ8SCMG
<CurrencyID>k__BackingField
KXdcKn9raO
<CategoryID>k__BackingField
kdycy16qi9
<ProviderID>k__BackingField
QCTcv0DIaw
<Description>k__BackingField
XbBc2ChqEj
<Status>k__BackingField
Hvgc8lvUbN
<ClassType>k__BackingField
wntc3grsGo
<RefNumber>k__BackingField
wEXcb6Zhsg
<LineType>k__BackingField
RCtcETeL0E
<Quantity>k__BackingField
u0Ac5wjATC
<Units>k__BackingField
X58cBn4fWu
<Active>k__BackingField
rBwclymSa5
<Balance>k__BackingField
CrgcTAw6Sb
<Command>k__BackingField
h6DcChGFFJ
<Code>k__BackingField
kHUcgSNyrB
<Branch>k__BackingField
MwJccyGEAA
<UserInfo>k__BackingField
VuecJpumHL
<PurchaseInvoiceLines>k__BackingField
RHsc13u8pt
<SaleInvoiceLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Feedback
AppTech.MSMS.Domain.Models.Feedback
Feedback
Feedback
OK9crOHHHc
<ID>k__BackingField
FcocQn2xFL
<RowVersion>k__BackingField
VXEcFO6a2k
<AccountID>k__BackingField
WAkcV41SX7
<ServiceName>k__BackingField
jH8cRkhm3J
<Feed>k__BackingField
BjUcfhay97
<Type>k__BackingField
E5hcepYMDK
<Note>k__BackingField
RCFcxp3UtV
<BranchID>k__BackingField
B10csMUC8K
<CreatedBy>k__BackingField
eAkcO9VSsL
<CreatedTime>k__BackingField
XtNchdJ7vP
<Account>k__BackingField
H6oc63N2d1
<Branch>k__BackingField
VZ8c4WCuXw
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Fund
AppTech.MSMS.Domain.Models.Fund
Fund
Fund
z2PcmJFSTt
<ID>k__BackingField
A9Jc7speMB
<RowVersion>k__BackingField
juUcWxM88w
<Number>k__BackingField
WAWcnYiFja
<Name>k__BackingField
WIVczoZ8eT
<AccountID>k__BackingField
E7XJGOO0fc
<UserID>k__BackingField
W4jJAe0H7N
<Note>k__BackingField
rPgJUN0ctw
<CreatedBy>k__BackingField
vd8JpTwj4e
<BranchID>k__BackingField
EQqJ9DmrGk
<CreatedTime>k__BackingField
puTJiYah4S
<Account>k__BackingField
YWXJIYXLDl
<Branch>k__BackingField
e7SJHihcNA
<UserInfo>k__BackingField
i1GJuT9XNP
<FundUsers>k__BackingField
LDfJN6YJtS
<UserInfo1>k__BackingField
PRKJDh4p5v
<Extra>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FundUser
AppTech.MSMS.Domain.Models.FundUser
FundUser
FundUser
WkCJt7P3S0
<ID>k__BackingField
FSaJduBvnF
<RowVersion>k__BackingField
L5BJSDR350
<FundID>k__BackingField
pLNJkJHRlL
<UserID>k__BackingField
Yd7JPLXKOS
<Note>k__BackingField
GhgJjN1KF6
<CreatedBy>k__BackingField
viPJa7dZuU
<BranchID>k__BackingField
nLgJYDo9kd
<CreatedTime>k__BackingField
z9ZJwlAWeP
<Branch>k__BackingField
RyMJ0220k8
<Fund>k__BackingField
i1jJq2pDv1
<UserInfo>k__BackingField
KbrJMaaS0B
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GeneralInfo
AppTech.MSMS.Domain.Models.GeneralInfo
GeneralInfo
GeneralInfo
mYoJXbHI2K
<ID>k__BackingField
jo0JoyAxMu
<RowVersion>k__BackingField
ECjJL1U9EK
<Title>k__BackingField
avjJZY1BgL
<Description>k__BackingField
grfJKnepqo
<CreatedBy>k__BackingField
sJAJyENduL
<BranchID>k__BackingField
AG8JvXyVBm
<CreatedTime>k__BackingField
dcrJ2X15vt
<Branch>k__BackingField
TKNJ8UpESJ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GroupItem
AppTech.MSMS.Domain.Models.GroupItem
GroupItem
GroupItem
pW2J3hB9Pn
<ID>k__BackingField
wFYJbffPOe
<GroupID>k__BackingField
PIVJE3YvKS
<ItemID>k__BackingField
AoTJ5f4gn1
<Type>k__BackingField
jnpJByBXV7
<BranchID>k__BackingField
L0EJlPXW1O
<CreatedBy>k__BackingField
XGHJTZRVMv
<CreatedTime>k__BackingField
P5sJCmRrsf
<Branch>k__BackingField
pWCJgV0Dys
<PartyGroup>k__BackingField
FIVJcsFtGH
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Gsm
AppTech.MSMS.Domain.Models.Gsm
Gsm
Gsm
wspJJr0Kag
<ID>k__BackingField
zDvJ1SJvQV
<RowVersion>k__BackingField
vKFJrF518K
<Number>k__BackingField
GRpJQiFhNm
<Status>k__BackingField
RoBJFX1PCR
<Status_chng_date>k__BackingField
QUYJVRhuWY
<Account_code>k__BackingField
ec0JRqdPH1
<Name>k__BackingField
ujjJf47DT0
<Bills>k__BackingField
sMJJeFgavp
<Limit>k__BackingField
f4yJxDR9vg
<Deposit>k__BackingField
lrZJsD1peW
<Invoice_date>k__BackingField
NvcJO0yc3y
<On>k__BackingField
UhCJhcvJ7P
<BN>k__BackingField
DofJ6NXN8t
<GroupID>k__BackingField
MLHJ47owYx
<GroupName>k__BackingField
b19Jmlmer5
<BranchID>k__BackingField
TGcJ7OoXRQ
<CreatedBy>k__BackingField
NgJJWUPRqH
<CreatedTime>k__BackingField
BDsJnRnFby
<Branch>k__BackingField
b8mJzGjuWX
<UserInfo>k__BackingField
FHe1GOJEm7
<ViaExcel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Instruction
AppTech.MSMS.Domain.Models.Instruction
Instruction
Instruction
UXZ1Axh9Pe
<ID>k__BackingField
KaT1UrnQ6d
<RowVersion>k__BackingField
gN21pIvEad
<Number>k__BackingField
YQM19Q4H0v
<Text>k__BackingField
Pcw1iE0iNf
<CreatedBy>k__BackingField
u5L1Ig9OFo
<BranchID>k__BackingField
wye1Hmudac
<CreatedTime>k__BackingField
uIR1uFD90o
<Branch>k__BackingField
Wyc1NKdcD5
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Item
AppTech.MSMS.Domain.Models.Item
Item
Item
sM41DBNbXC
<ID>k__BackingField
WtS1tpRJRZ
<RowVersion>k__BackingField
yrn1dRsPmm
<Number>k__BackingField
nJv1SHI0n7
<Name>k__BackingField
fgo1klIxG4
<ServiceID>k__BackingField
F2r1P3Wst4
<ItemID>k__BackingField
qW21j1vXG5
<Type>k__BackingField
uMm1agurMI
<ChildID>k__BackingField
n5C1YENhp8
<ChildType>k__BackingField
D8J1wZGrME
<CostPrice>k__BackingField
LBt10aEEdn
<SalePrice>k__BackingField
rMd1qdmsm0
<PersonnalPrice>k__BackingField
V211M7FGC4
<AgentPrice>k__BackingField
VBb1XqUv5J
<BranchPrice>k__BackingField
m461oMBMuh
<OriginalPrice>k__BackingField
OyT1LvgETE
<StartAmount>k__BackingField
Tsx1Zav7wG
<EndAmount>k__BackingField
XMG1Kw3cPU
<InRange>k__BackingField
Q6Y1y9ooMK
<ByPercentage>k__BackingField
cof1v7JUvc
<RayalMobile>k__BackingField
Cn412Po2pJ
<ByItemID>k__BackingField
dsy18Acvu0
<Flag>k__BackingField
M1X13IkufL
<OrderNo>k__BackingField
ifM1btQQlg
<Code>k__BackingField
fWe1EWTPB4
<RefID>k__BackingField
EOj153Ymnb
<Note>k__BackingField
dA21BuLxia
<CategoryID>k__BackingField
Cd61l8tmL3
<ProviderID>k__BackingField
KV51TvDPaH
<CreatedBy>k__BackingField
mft1ChxMul
<BranchID>k__BackingField
XmQ1gsy9AB
<CreatedTime>k__BackingField
NQw1ctFisf
<Description>k__BackingField
idM1JQh8sL
<Status>k__BackingField
wPS11PTdRK
<ClassType>k__BackingField
M1A1rlHSW6
<RefNumber>k__BackingField
qhr1Q840YT
<Branch>k__BackingField
sKU1FBXQJD
<ServiceInfo>k__BackingField
tKg1VLVRtK
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ItemCost
AppTech.MSMS.Domain.Models.ItemCost
ItemCost
ItemCost
O3w1RXpr3R
<ID>k__BackingField
FxS1fcbaSo
<RowVersion>k__BackingField
zkI1e6KiRo
<ServiceID>k__BackingField
oVu1xl83l0
<ProviderID>k__BackingField
Qne1sbjtZ9
<Price>k__BackingField
xAB1OoMNrt
<Type>k__BackingField
mk51hfRO4t
<Note>k__BackingField
CEs161AgpW
<CreatedBy>k__BackingField
BWs14CekNJ
<BranchID>k__BackingField
JPl1mk8OOV
<CreatedTime>k__BackingField
ByH17jdLuL
<Branch>k__BackingField
tZ71WoLhQV
<TopupProvider>k__BackingField
ljH1nb2pay
<ServiceInfo>k__BackingField
soh1z8ZgAT
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Journal
AppTech.MSMS.Domain.Models.Journal
Journal
Journal
OmVrGTobNo
<ID>k__BackingField
Rt3rAB2f8U
<VoucherID>k__BackingField
yyVrU4fYMp
<Number>k__BackingField
OCFrpAL9qq
<EntryID>k__BackingField
A9ir9hwtey
<Date>k__BackingField
kkqriraYrb
<CreatedBy>k__BackingField
hO2rIjhsLa
<Debited>k__BackingField
w4nrHsWCsq
<CreatedTime>k__BackingField
On9rucmXdX
<BranchID>k__BackingField
H9lrN0IiHO
<Year>k__BackingField
R3yrDH7q9B
<Status>k__BackingField
iRprtnCk8x
<TotalAmount>k__BackingField
fO6rdf9uCF
<SyncJournalID>k__BackingField
V2brS5D9Vx
<datestamb>k__BackingField
VOmrkHRatr
<EntrySource>k__BackingField
jlwrP9uT31
<Depended>k__BackingField
UHyrjyuL7o
<RefNumber>k__BackingField
MuHra4MQ7i
<CurrencyID>k__BackingField
iXRrYf5x9w
<Branch>k__BackingField
FJbrwnQfr7
<CommissionReceipts>k__BackingField
dOGr025iDC
<ConsumeInvoices>k__BackingField
ftjrqREsAW
<JournalEntries>k__BackingField
Sg1rMp3E64
<UserInfo>k__BackingField
wUfrXbXGA9
<Voucher>k__BackingField
Hv2roXibVI
<OrderInfoes>k__BackingField
CwVrLpr2gM
<PurchaseInvoices>k__BackingField
QlmrZwyflP
<RemittanceIns>k__BackingField
esBrKc85PK
<RemittanceOuts>k__BackingField
e9GrycVUkA
<RiyalMobiles>k__BackingField
kRurvM4e9g
<SaleInvoices>k__BackingField
SP2r2wsOyj
<Topups>k__BackingField
awMr8CLImG
<TopupCommissions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.JournalEntry
AppTech.MSMS.Domain.Models.JournalEntry
JournalEntry
JournalEntry
zyBr3pn6Sq
<ID>k__BackingField
FmTrbNZcGm
<ParentID>k__BackingField
f1GrEmiFU3
<Amount>k__BackingField
V0tr5iYXSr
<CurrencyID>k__BackingField
QRprBbDYxM
<DCAmount>k__BackingField
UcQrlExsTW
<AccountID>k__BackingField
UlprTe7pkb
<CostCenterID>k__BackingField
rnGrCc2RQq
<Note>k__BackingField
uBArgPOPZ5
<Datestamp>k__BackingField
PG3rcSrBPT
<ExchangeRate>k__BackingField
oprrJmOxcm
<SyncEntryID>k__BackingField
oker1NohHg
<Account>k__BackingField
vX9rrvU90x
<Currency>k__BackingField
GynrQSpNfE
<Journal>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LiveTopup
AppTech.MSMS.Domain.Models.LiveTopup
LiveTopup
LiveTopup
wtRrF8rhRY
<ID>k__BackingField
LAZrVOOZtX
<RowVersion>k__BackingField
UQ9rR46OK5
<ProviderID>k__BackingField
xbVrfZStZQ
<ServiceID>k__BackingField
RMtreCXTvZ
<ServiceCode>k__BackingField
fRxrx0dhtB
<Active>k__BackingField
K2drs87952
<CreatedBy>k__BackingField
eHrrOIxmYS
<BranchID>k__BackingField
MHUrhOYSqS
<CreatedTime>k__BackingField
ngtr6xsPov
<ProviderCommission>k__BackingField
iSYr4ixqQe
<BranchCommission>k__BackingField
sWIrmTlWyo
<AccountID>k__BackingField
MJnr74qKPN
<ActiveForAccount>k__BackingField
t3frWKbVp3
<Type>k__BackingField
TLkrnG66pU
<Status>k__BackingField
UuorzHQOO6
<Flag>k__BackingField
CEjQGq9lmV
<Bundling>k__BackingField
zpPQAFSRRw
<ProcessMethod>k__BackingField
cMBQUFa6Il
<ConnectMethod>k__BackingField
NCvQpFpmnp
<AccountState>k__BackingField
tMEQ9mGcPX
<AccountGroupID>k__BackingField
Kp6QiQGovw
<Account>k__BackingField
OfBQITrM0B
<Branch>k__BackingField
B9uQH9hH9J
<ServiceInfo>k__BackingField
cjiQu6DIqI
<TopupProvider>k__BackingField
bsLQNCRAOV
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LoanOrder
AppTech.MSMS.Domain.Models.LoanOrder
LoanOrder
LoanOrder
NlNQDO6PAE
<ID>k__BackingField
xFdQth7NtI
<RowVersion>k__BackingField
BwVQdT8CWp
<ServiceID>k__BackingField
pdRQSYHEmb
<AccountID>k__BackingField
O9kQkPNLE6
<Amount>k__BackingField
T2ZQPx6Lcf
<CurrencyID>k__BackingField
JCqQj4R7vN
<Note>k__BackingField
qVbQaG38ed
<Channel>k__BackingField
iglQY8J2OZ
<ParentID>k__BackingField
Hl8QwOseA4
<Status>k__BackingField
yjIQ0GnRWp
<CreatedBy>k__BackingField
BAjQq6OX5O
<BranchID>k__BackingField
QXLQM5Z4TO
<CreatedTime>k__BackingField
MCQQXfh9f3
<Account>k__BackingField
IMwQo4ab21
<Branch>k__BackingField
kElQLHGBxq
<Currency>k__BackingField
b0MQZfctXi
<OrderInfo>k__BackingField
MHCQKstmdT
<ServiceInfo>k__BackingField
K0OQyljnnw
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Log
AppTech.MSMS.Domain.Models.Log
Log
Log
phoQvwEmkZ
<ID>k__BackingField
Kt3Q2sVGVH
<TimeStamp>k__BackingField
jDfQ8TFpMg
<Level>k__BackingField
wG8Q3MtZcr
<Type>k__BackingField
qGKQbj9d9Q
<Logger>k__BackingField
miJQEYZPET
<Message>k__BackingField
LxPQ5kS9Vp
<Username>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Membership
AppTech.MSMS.Domain.Models.Membership
Membership
Membership
pIuQB5RNXd
<ID>k__BackingField
wZwQlb6uQg
<UserID>k__BackingField
oRlQTAc7Hy
<CreateDate>k__BackingField
hhaQCrw5CY
<ConfirmationToken>k__BackingField
TRrQgoAsbx
<IsConfirmed>k__BackingField
amGQcIwJZ2
<LastPasswordFailureDate>k__BackingField
FqJQJ7gEQi
<PasswordFailuresSinceLastSuccess>k__BackingField
vA1Q10UXfr
<Password>k__BackingField
ReSQrKOXEN
<PasswordChangedDate>k__BackingField
KKpQQE8vdN
<PasswordSalt>k__BackingField
AhxQFSTenp
<PasswordVerificationToken>k__BackingField
ufVQVu1K9h
<PasswordVerificationTokenExpirationDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Merchant
AppTech.MSMS.Domain.Models.Merchant
Merchant
Merchant
l8RQRQbH4U
<ID>k__BackingField
VnQQfDuWax
<RowVersion>k__BackingField
Un2Qe4jkj5
<Number>k__BackingField
NxYQxQDkr0
<Name>k__BackingField
tMRQsZ8a5A
<CategoryID>k__BackingField
HhGQObJRu7
<Description>k__BackingField
UCgQh4PGAE
<OwnerName>k__BackingField
W9QQ6lMYVU
<AccountID>k__BackingField
GkkQ4Z3oom
<PhoneNumber>k__BackingField
W2qQmqIdvf
<ContactNumber>k__BackingField
M6XQ7DiPKj
<Address>k__BackingField
gTkQWuLF0l
<Note>k__BackingField
BWJQn0lBgD
<Email>k__BackingField
BudQzbZX58
<CreatedBy>k__BackingField
xDfFG4gSvl
<BranchID>k__BackingField
NsMFAAGxH6
<CreatedTime>k__BackingField
KQ2FU3yrMx
<ImageName>k__BackingField
Cy9FphPlY9
<Status>k__BackingField
yOJF94REd0
<InvoiceImage>k__BackingField
wBdFiXEhdp
<SyncAccountID>k__BackingField
JdYFI2Qrg5
<RefNumber>k__BackingField
b1kFHDKErS
<Extra>k__BackingField
VTIFuVtYnD
<Account>k__BackingField
TDuFNhYspT
<Branch>k__BackingField
rNaFDoa0Hp
<UserInfo>k__BackingField
qV1FtXe4J0
<MerchantPayments>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantCategory
AppTech.MSMS.Domain.Models.MerchantCategory
MerchantCategory
MerchantCategory
tAaFd3ucoW
<ID>k__BackingField
s95FSDaUWO
<RowVersion>k__BackingField
uxtFkvX2iA
<Name>k__BackingField
PqRFP2HwwY
<CreatedBy>k__BackingField
HvrFjWWcI1
<BranchID>k__BackingField
ahtFaMVLyN
<CreatedTime>k__BackingField
uujFYnaDcn
<ImageName>k__BackingField
LixFw38dPm
<Branch>k__BackingField
L4JF0TIThD
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantPayment
AppTech.MSMS.Domain.Models.MerchantPayment
MerchantPayment
MerchantPayment
CMpFqcNMSG
<ID>k__BackingField
U6mFMPZcMD
<RowVersion>k__BackingField
NjHFXHXEac
<Number>k__BackingField
hhVFo1Rmnr
<Amount>k__BackingField
bmCFL5vCbu
<CurrencyID>k__BackingField
MkCFZueSWT
<CreditorAccountID>k__BackingField
jMGFKxSRuq
<DebitorAccountID>k__BackingField
lm8FybQIFC
<MerchantID>k__BackingField
TBHFv2pQYZ
<Date>k__BackingField
yXAF2VNH0C
<Note>k__BackingField
yvNF8WfL49
<EntryID>k__BackingField
R1wF39wl3c
<IsDebited>k__BackingField
bhDFbNLXIv
<RefNumber>k__BackingField
tgaFEMsAVR
<CreatedBy>k__BackingField
p8yF5L4cp7
<BranchID>k__BackingField
TSgFB6A1mr
<CreatedTime>k__BackingField
xbdFl5Gaci
<InvoiceNumber>k__BackingField
ywqFTocntd
<Year>k__BackingField
REeFCpctp4
<Status>k__BackingField
S9KFgpe1XL
<InvoiceImage>k__BackingField
njmFc4n3dN
<Extra>k__BackingField
bwjFJ6xl6h
<Channel>k__BackingField
THJF1QapPt
<CreatedDate>k__BackingField
jGwFrY6uGW
<HourTime>k__BackingField
MPWFQnPAju
<MinuteTime>k__BackingField
qyBFFDGSW7
<TransNumber>k__BackingField
baoFVCt2V6
<Attachments>k__BackingField
nOcFREIifI
<ExtraInfo>k__BackingField
DVoFf6Qgqg
<ExtraID>k__BackingField
Q5XFeIqrTq
<SyncID>k__BackingField
FmYFxWWFtF
<RefID>k__BackingField
EseFsnvJCU
<BindID>k__BackingField
i0HFOJPX07
<Binded>k__BackingField
t1SFh1nDC5
<Account>k__BackingField
aaAF6gOMlT
<Account1>k__BackingField
CARF4dQWY5
<Branch>k__BackingField
RXPFmr5lI9
<Currency>k__BackingField
WOoF7bkkTj
<Merchant>k__BackingField
xqyFWNCAKb
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Entities
AppTech.MSMS.Domain.Models.Entities
Entities
Entities
LggFnOq4Ft
<Accounts>k__BackingField
UhkFzTade2
<AccountApis>k__BackingField
N37VGuLMOt
<AccountDocuments>k__BackingField
rDJVA6rJHv
<AccountLedgers>k__BackingField
IwKVUHdcyM
<AccountNotifications>k__BackingField
NrfVpBp01b
<AccountRegions>k__BackingField
GNqV9g11ug
<AccountSlatings>k__BackingField
gAaVioflQj
<AccountUsers>k__BackingField
n2xVIyMafd
<Addresses>k__BackingField
YyeVHIaOfU
<AdminNotifications>k__BackingField
l0MVuDJxw6
<Agents>k__BackingField
QMWVN42Kya
<AgentPoints>k__BackingField
CZSVDNRhdw
<AgentPointUsers>k__BackingField
zPQVt1BnVq
<AuditLogs>k__BackingField
QjWVdud8hx
<Bagats>k__BackingField
KEqVSE3OQw
<BagatPayments>k__BackingField
ilFVkPNhai
<Banks>k__BackingField
HNDVPYZv0F
<BankDeposits>k__BackingField
YbwVjOS9kU
<Branches>k__BackingField
K5hVapkl2D
<BranchTargets>k__BackingField
EHYVYaQTWL
<Brochures>k__BackingField
djSVwflMLG
<Bundles>k__BackingField
GavV0MBjUc
<BuyCurrencies>k__BackingField
dSvVqSt74T
<Cards>k__BackingField
YKcVMxW4yW
<CardFactions>k__BackingField
ETnVXq2A1x
<CardOrders>k__BackingField
h1VVoW6EQ1
<CardPayments>k__BackingField
cRVVLWfZOC
<CardTypes>k__BackingField
z4FVZHnINw
<CashDeposits>k__BackingField
b6jVKoZGiT
<CashIns>k__BackingField
peMVyFFRSU
<CashOuts>k__BackingField
x15Vvb3VuF
<CashTransfers>k__BackingField
AWLV2Sew6o
<CashWithdraws>k__BackingField
XZOV86RLV2
<Cheques>k__BackingField
EbuV3uscyd
<ClaimGroups>k__BackingField
I7VVbK8ZLY
<Clients>k__BackingField
GRGVEBSHE8
<CommissionReceipts>k__BackingField
uCUV549qsZ
<CommissionReceiptLines>k__BackingField
kRcVBAQ9vT
<Companies>k__BackingField
ExcVlOusDV
<ConsumeInvoices>k__BackingField
DhUVTBFmcy
<Countries>k__BackingField
UflVCk8kFe
<CoverageOrders>k__BackingField
E40VgtGCcB
<Currencies>k__BackingField
hi2VcGrOp5
<CurrencyExchanges>k__BackingField
f4fVJwrtLH
<CurrencyRates>k__BackingField
dTEV1Gr9YW
<CurrencyRateAccounts>k__BackingField
W8wVrZgk0G
<DbBackups>k__BackingField
UhqVQoIYpx
<DepositOrders>k__BackingField
QNWVF3U3fF
<Devices>k__BackingField
mRQVV2ICGk
<Distributors>k__BackingField
rS5VR2IlXw
<ELMAH_Error>k__BackingField
VDuVfKAWw6
<Errors>k__BackingField
TcJVeGWwAE
<Exchangers>k__BackingField
j4MVxHjZ0n
<ExchangerTargets>k__BackingField
CwiVsyHH6X
<ExternalBranches>k__BackingField
nEjVORpjZ6
<Factions>k__BackingField
aC0VhhYVeI
<Feedbacks>k__BackingField
La7V6ryjS4
<Funds>k__BackingField
c29V4DWpnV
<FundUsers>k__BackingField
hp2VmCZWba
<GeneralInfoes>k__BackingField
dTVV7DTwOb
<GroupItems>k__BackingField
gUIVWC1aHT
<Gsms>k__BackingField
xaJVnKmlNJ
<Instructions>k__BackingField
U6PVzJxcSu
<Items>k__BackingField
g2eRGtXJA7
<ItemCosts>k__BackingField
USoRAplcqy
<Journals>k__BackingField
zSERUbcwQp
<JournalEntries>k__BackingField
ERLRp90Yi9
<LiveTopups>k__BackingField
qwtR99vdlD
<LoanOrders>k__BackingField
jWGRiArq6w
<Logs>k__BackingField
aLyRICbVDh
<Memberships>k__BackingField
oudRHaIVfB
<Merchants>k__BackingField
rwRRu0sxnl
<MerchantCategories>k__BackingField
Dt2RNKy3GS
<MerchantPayments>k__BackingField
Ir6RD4xPAc
<MoneyMasters>k__BackingField
EeSRtk9Uek
<Numberings>k__BackingField
DhGRdqEtat
<OfferOrders>k__BackingField
QdsRS9Wdyt
<OpeningBalances>k__BackingField
EYjRkhy2Cv
<OrderInfoes>k__BackingField
dYSRPMm3r2
<OrderSatelliteQuotas>k__BackingField
cRCRjv7rSN
<PageActions>k__BackingField
p6mRaOIwdh
<PageInfoes>k__BackingField
qeHRYkm0Nk
<Parties>k__BackingField
zZORwgt5M2
<PartyGroups>k__BackingField
RdvR0oghkw
<Payments>k__BackingField
xHmRqBOARm
<PaymentCommissions>k__BackingField
sCeRMQI0Id
<People>k__BackingField
GD9RXhmTlm
<Products>k__BackingField
vVBRoPRvHa
<ProductCategories>k__BackingField
M1JRLvdRU3
<ProductImages>k__BackingField
bjsRZnnrvO
<Provinces>k__BackingField
gdBRKZpWrJ
<PurchaseInvoices>k__BackingField
ocERy5NDGc
<PurchaseInvoiceLines>k__BackingField
EUeRvlkQXb
<Quotations>k__BackingField
aqLR2hkkqd
<ReceiptCreditors>k__BackingField
i4NR89ONmf
<ReceiptDebitors>k__BackingField
LEPR3tXh86
<Regions>k__BackingField
LYHRbT2sbd
<RemittanceCommissions>k__BackingField
LowRELmDfs
<RemittanceIns>k__BackingField
dfOR5EtMoU
<RemittanceNumbers>k__BackingField
eO3RBUrhog
<RemittanceOuts>k__BackingField
hqURlJHHWd
<RemittancePoints>k__BackingField
I0ZRTbP29w
<RemittanceRegions>k__BackingField
Kp9RCyY8VB
<RiyalMobiles>k__BackingField
TvhRg0p682
<RoleClaims>k__BackingField
UMERca1xYU
<RoleInfoes>k__BackingField
TdfRJK7RBN
<RSSes>k__BackingField
PBeR1pyNV4
<SaleCurrencies>k__BackingField
nTiRrMfmlx
<SaleInvoices>k__BackingField
uNqRQo2Bjs
<SaleInvoiceLines>k__BackingField
UTvRF303Dw
<SatelliteFactions>k__BackingField
fLHRVANjQM
<SatellitePayments>k__BackingField
ypgRRSinSh
<SatelliteProviders>k__BackingField
WBbRfTp3T6
<ServiceClaims>k__BackingField
Sl5Req0lCD
<ServiceInfoes>k__BackingField
KWMRxb11QH
<Settings>k__BackingField
F3qRsa0NBo
<Sims>k__BackingField
noXROJKgll
<SimCardOrders>k__BackingField
x2wRhBjd9W
<SimInvoices>k__BackingField
cg3R6BLrjL
<SimpleEntries>k__BackingField
iWsR4AF5SC
<SimPurchases>k__BackingField
GseRmP8joH
<SMSDispatches>k__BackingField
eT8R7vSXNF
<SMSLogs>k__BackingField
l0FRWeDaM0
<SMSMessages>k__BackingField
h1jRnrNT3u
<SpecialSims>k__BackingField
KCARzRwpiB
<Subscribers>k__BackingField
gcffGHIsE5
<Suppliers>k__BackingField
hmHfApOcb9
<SuspendTopups>k__BackingField
RbWfU1KeAt
<Topups>k__BackingField
f9EfpdVUWc
<TopupClosures>k__BackingField
t4gf94TE7s
<TopupCommissions>k__BackingField
sHpfiroU9H
<TopupNetworks>k__BackingField
Ey3fIbLdi1
<TopupOrders>k__BackingField
hK8fHLbAIU
<TopupProviders>k__BackingField
G7nfunuDi6
<TrailToupCommissions>k__BackingField
pchfNUp9Ei
<TrailToupOrders>k__BackingField
IELfDxXfid
<TransferIns>k__BackingField
X6Ofts05cF
<TransferOrders>k__BackingField
AxEfd7VsHi
<TransferOuts>k__BackingField
v60fS7vKoW
<Transporters>k__BackingField
MHPfki0LxY
<TransportOrders>k__BackingField
v92fPx6Mw8
<UserClaims>k__BackingField
fOVfjbF112
<UserDevices>k__BackingField
uDefa51Irp
<UserInfoes>k__BackingField
Tr0fYkT1IB
<UserPages>k__BackingField
qwFfwTQVwp
<UserPermissions>k__BackingField
o88f0rHfpi
<UserRoles>k__BackingField
qNRfqhhVFi
<UserTokens>k__BackingField
pSsfMdfHJ8
<Vouchers>k__BackingField
FZhfXt9McC
<WERegions>k__BackingField
KAtfoDbf4C
<WifiCards>k__BackingField
YsafL7E86t
<WifiFactions>k__BackingField
w7GfZdpYWR
<WifiPayments>k__BackingField
xfAfKJMn1x
<WifiProviders>k__BackingField
ADGfyKnci8
<WithdrawOrders>k__BackingField
kJlfvfoEc0
<DbScripts>k__BackingField
KQCf2FyjgO
<PaymentStatus>k__BackingField
FSvf8W6sTO
<Parties1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MoneyMaster
AppTech.MSMS.Domain.Models.MoneyMaster
MoneyMaster
MoneyMaster
WkJf3qwXru
<ID>k__BackingField
fDbfbMhjyN
<RowVersion>k__BackingField
ixQfER89nq
<Name>k__BackingField
i7af5K6OXA
<CreatedBy>k__BackingField
EJDfBtT2vl
<BranchID>k__BackingField
wBqflrDSdo
<CreatedTime>k__BackingField
Me2fTsx0qo
<ObjectName>k__BackingField
MoCfCU5kje
<Branch>k__BackingField
zXhfgeaQQy
<UserInfo>k__BackingField
Wh4fcEad3R
<OrderInfoes>k__BackingField
LuhfJRjIv8
<ServiceInfoes>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Numbering
AppTech.MSMS.Domain.Models.Numbering
Numbering
Numbering
cdRf1jsbWW
<ID>k__BackingField
TOSfrSycqD
<Number>k__BackingField
lwGfQn92In
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OfferOrder
AppTech.MSMS.Domain.Models.OfferOrder
OfferOrder
OfferOrder
Dd8fFBTQl4
<ID>k__BackingField
XT7fVgFX9c
<RowVersion>k__BackingField
IrPfRBklj8
<ParentID>k__BackingField
v7Sff6DhdU
<ServiceID>k__BackingField
KULfelMv09
<AccountID>k__BackingField
rlFfxnpce1
<SubscriberNo>k__BackingField
f3Sfsy1279
<Amount>k__BackingField
vOhfOF912d
<Description>k__BackingField
aSsfhmTW8B
<NetworkID>k__BackingField
WANf6Gi9cH
<OfferType>k__BackingField
m6bf4Y60i1
<SimType>k__BackingField
SbLfmDHJVv
<Note>k__BackingField
OYGf78ah8e
<Channel>k__BackingField
amtfWpHsEu
<ImageName>k__BackingField
ahGfnVBLDk
<CreatedBy>k__BackingField
LEFfzNZ3lH
<BranchID>k__BackingField
PrueG6wlxn
<CreatedTime>k__BackingField
zYoeAMHyxm
<Account>k__BackingField
EhIeUuIfOM
<Branch>k__BackingField
Dt7epU2NBY
<OrderInfo>k__BackingField
VdUe9itmnx
<ServiceInfo>k__BackingField
qmaeiKEAUQ
<TopupNetwork>k__BackingField
URieIrbPIi
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OpeningBalance
AppTech.MSMS.Domain.Models.OpeningBalance
OpeningBalance
OpeningBalance
HAFeH4XbcO
<ID>k__BackingField
srLeuBEyNV
<RowVersion>k__BackingField
KrKeNhkyFp
<Number>k__BackingField
XrqeDxTXav
<AccountID>k__BackingField
yN9etuFxat
<Amount>k__BackingField
V2Leds9lF3
<CurrencyID>k__BackingField
cU2eS3ThZO
<EntryID>k__BackingField
iEeek69byQ
<Date>k__BackingField
IDpePKp14C
<Note>k__BackingField
N4UejfnZAD
<CreatedBy>k__BackingField
ekrea5QFk7
<BranchID>k__BackingField
bjfeYN0VuK
<CreatedTime>k__BackingField
M5EewkLnAR
<Account>k__BackingField
BnDe0yVJVQ
<Branch>k__BackingField
VIQeqaVEyR
<Currency>k__BackingField
j0WeMtLhiI
<UserInfo>k__BackingField
oDWeX4V36K
<ExchangeAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OrderInfo
AppTech.MSMS.Domain.Models.OrderInfo
OrderInfo
OrderInfo
FHIeocw2AZ
<ID>k__BackingField
f4leLhE2l0
<RowVersion>k__BackingField
GVteZvVj9t
<Number>k__BackingField
PXGeKK3ECD
<ServiceCategoryID>k__BackingField
YXGeylKH6i
<ServiceID>k__BackingField
Ji7evyjdbE
<OrderType>k__BackingField
FaTe2XFgEq
<Amount>k__BackingField
TX3e8JuvQ6
<CurrencyID>k__BackingField
GfPe3FY3Do
<CreditorAccountID>k__BackingField
YNdebfeOta
<MCAmount>k__BackingField
m1UeEtC3NC
<DebitorAccountID>k__BackingField
ws1e5kiMGA
<Description>k__BackingField
vgCeBs8X1h
<Date>k__BackingField
bYcelIFRoV
<Channel>k__BackingField
WAoeTp8THW
<Note>k__BackingField
imleCNOojP
<ExtraAmount>k__BackingField
zEJegcoYfS
<RefNumber>k__BackingField
hrRecGqJ0S
<IsAmountModified>k__BackingField
pwneJTctW0
<AmountModifiedBy>k__BackingField
fdIe1GiB4y
<State>k__BackingField
fyjerPOqOa
<InUse>k__BackingField
qb8eQhxMEJ
<UsageTime>k__BackingField
Yc0eFdGOmM
<BindBy>k__BackingField
sZaeV0MJAo
<IsDebited>k__BackingField
lQWeRMbgvE
<DebitedBy>k__BackingField
U7yefnWSyy
<IsRejected>k__BackingField
fI4eeffiBE
<RejectReason>k__BackingField
e0DexVi4f8
<RejectedBy>k__BackingField
L4yesLqenc
<EntryID>k__BackingField
d5weOhs88q
<ServiceEntryID>k__BackingField
DmZehGFffL
<CreatedBy>k__BackingField
mjSe6uKxGn
<BranchID>k__BackingField
Wt5e48bTLL
<CreatedTime>k__BackingField
FZUemwo56x
<Year>k__BackingField
uYCe7bY1V8
<Status>k__BackingField
BNteWS5dHo
<AccountID>k__BackingField
fJ8enrrUim
<SubNote>k__BackingField
hSwezA5eFp
<CommissionAmount>k__BackingField
WNJxG9Gmd9
<CommissionCurrencyID>k__BackingField
BxcxAIQhC3
<Account>k__BackingField
GZExUy6hwP
<BankDeposits>k__BackingField
vw5xpIggXR
<Branch>k__BackingField
Iu7x9MlhqE
<CardOrders>k__BackingField
C3cxi5kYGj
<CardPayments>k__BackingField
q2JxIfOCfH
<CoverageOrders>k__BackingField
mnMxHwQtxX
<CurrencyExchanges>k__BackingField
z0Mxu5jSFL
<DepositOrders>k__BackingField
egcxNbyVEN
<Journal>k__BackingField
v64xD2nE47
<LoanOrders>k__BackingField
Y6Kxt4dHLx
<MoneyMaster>k__BackingField
vCjxdXxTVu
<OfferOrders>k__BackingField
YuyxSHLV8j
<OrderInfo1>k__BackingField
J46xkI0ePG
<OrderInfo2>k__BackingField
w7nxPbrZMY
<Payment>k__BackingField
w10xjesy7S
<ServiceInfo>k__BackingField
oT2xaqxCfV
<UserInfo>k__BackingField
x5MxYLNP6E
<SimCardOrders>k__BackingField
N40xwqnTox
<OrderInfo11>k__BackingField
IBtx0Mbety
<OrderInfo3>k__BackingField
j3dxq5Hphx
<UserInfo1>k__BackingField
IRexMRS619
<UserInfo2>k__BackingField
BCHxXuqTld
<TopupOrders>k__BackingField
wcFxo7fGNg
<TrailToupOrders>k__BackingField
jbdxLLfDtk
<TransferOrders>k__BackingField
hsjxZ2GIgq
<TransportOrders>k__BackingField
mKjxKdjRhV
<ExchangerAccountID>k__BackingField
rOJxyE3VxO
<TransactionID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PageAction
AppTech.MSMS.Domain.Models.PageAction
PageAction
PageAction
eBFxv8eNI5
<ID>k__BackingField
oTCx2ois3R
<ControlName>k__BackingField
Le1x8vvBcp
<Title>k__BackingField
nhVx3ZsjEu
<PageType>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PageInfo
AppTech.MSMS.Domain.Models.PageInfo
PageInfo
PageInfo
yl4xbrmbJs
<ID>k__BackingField
gigxEh7MRY
<PageName>k__BackingField
u25x5etmPN
<Title>k__BackingField
FiOxBVMU6M
<Assembly>k__BackingField
XqDxlNxmNK
<PageType>k__BackingField
StDxTEZKWA
<Visible>k__BackingField
VAdxCuMZRF
<Description>k__BackingField
eQhxgSjVKd
<Prefix>k__BackingField
JQpxcGHZ8R
<Class>k__BackingField
y4GxJTHk5N
<OrderNumber>k__BackingField
nLHx1ej9f4
<Role>k__BackingField
qBBxrZb9FV
<Module>k__BackingField
P1RxQrgwXs
<ModuleTitle>k__BackingField
fZmxFdN4Vy
<Version>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Party
AppTech.MSMS.Domain.Models.Party
Party
Party
rqpxVBfPhf
<ID>k__BackingField
o8QxRCRps2
<RowVersion>k__BackingField
g0exfwlUl3
<AccountID>k__BackingField
WYoxep7gNi
<PhoneNumber>k__BackingField
Cn4xxYqshF
<ClaimGroupID>k__BackingField
LFYxs8OUNb
<Status>k__BackingField
tWRxO9XUa3
<Token>k__BackingField
RTVxhBRCyv
<Note>k__BackingField
cucx6Ou4YP
<SyncAccountID>k__BackingField
QfIx4cCpL0
<CreatedBy>k__BackingField
lhRxmDxkOh
<BranchID>k__BackingField
NWax7wZ9Ot
<CreatedTime>k__BackingField
WDUxWJJh1N
<Account>k__BackingField
X4rxn9XQot
<Branch>k__BackingField
hxoxzbVm0Q
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Party1
AppTech.MSMS.Domain.Models.Party1
Party1
Party1
dsrsGMWRHx
<ID>k__BackingField
kYYsAhY9bt
<Number>k__BackingField
snIsUhAY2m
<Name>k__BackingField
o0Nsp7vEmo
<PhoneNumber>k__BackingField
Etxs9AIQ9f
<AccountID>k__BackingField
pLhsiTwkqV
<Address>k__BackingField
mFUsIiTCo3
<Type>k__BackingField
vBqsHmrgna
<UserType>k__BackingField
xR3sus8Uds
<BranchID>k__BackingField
HCLsNZ4dB5
<SyncAccountID>k__BackingField
bi8sDErgY9
<Status>k__BackingField
ikhstexPiK
<AgentID>k__BackingField
SZusdkuH3H
<DistributorID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PartyGroup
AppTech.MSMS.Domain.Models.PartyGroup
PartyGroup
PartyGroup
SKKsSxwDO6
<ID>k__BackingField
s5DskJWyQo
<RowVersion>k__BackingField
QJtsPLvK6W
<Name>k__BackingField
HIwsjZ4vXA
<Type>k__BackingField
La5saWutH1
<KeyName>k__BackingField
BRPsYWT2OC
<Description>k__BackingField
Vd6swt3M4w
<BranchID>k__BackingField
bqfs02gYCV
<CreatedBy>k__BackingField
AOmsqBEQ45
<CreatedTime>k__BackingField
oErsMsPcyO
<Branch>k__BackingField
K92sXJW5t0
<GroupItems>k__BackingField
KbZsomBtMy
<UserInfo>k__BackingField
RdVsL9akMZ
<SelectedItems>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Payment
AppTech.MSMS.Domain.Models.Payment
Payment
Payment
QBGsZ4vVqR
<ID>k__BackingField
d0KsKQuPRk
<RowVersion>k__BackingField
S8dsyRfTah
<Number>k__BackingField
H5osvMiLql
<RecordID>k__BackingField
MFys2waqoj
<ServiceID>k__BackingField
z8Ts87IyMW
<Amount>k__BackingField
x0hs3DcEPW
<Debited>k__BackingField
tAWsb0IPYq
<Date>k__BackingField
tg8sELP2xI
<CreatedBy>k__BackingField
hops5fpHdB
<BranchID>k__BackingField
WqrsBlE1Dd
<CreatedTime>k__BackingField
GbkslQmg3U
<Note>k__BackingField
TGasTjwkWV
<Year>k__BackingField
AhwsC0QKvv
<Status>k__BackingField
GDRsgSDfyk
<Method>k__BackingField
dcUscubl0p
<Datestamp>k__BackingField
qQwsJ59E8W
<Channel>k__BackingField
kCis10yp3k
<CurrencyID>k__BackingField
LIZsrHkthI
<IsSynced>k__BackingField
QRssQqvhMC
<UserID>k__BackingField
V74sFEA7Vh
<CrOrDr>k__BackingField
XsnsVcmxAe
<AccountID>k__BackingField
khssR7ba2m
<Account>k__BackingField
BmlsfSV7Kj
<Branch>k__BackingField
bFJseQQH4j
<Currency>k__BackingField
QLhsxhC9Fo
<OrderInfoes>k__BackingField
ojtssseSHv
<UserInfo>k__BackingField
S4esOMdqyP
<ServiceInfo>k__BackingField
heAshA8MF2
<UserInfo1>k__BackingField
vARs6hofei
<RiyalMobiles>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PaymentCommission
AppTech.MSMS.Domain.Models.PaymentCommission
PaymentCommission
PaymentCommission
xcfs41DhLi
<ID>k__BackingField
St5smERgg8
<RowVersion>k__BackingField
BOhs74ASgs
<ServiceID>k__BackingField
MTksWYpHjq
<FromAmount>k__BackingField
PHgsnuoi81
<ToAmount>k__BackingField
Mh4szCTSMw
<TraderAmount>k__BackingField
xS3OGcmaTL
<Note>k__BackingField
DjbOAZLGuq
<CreatedBy>k__BackingField
uP3OUVwaNn
<BranchID>k__BackingField
EIUOpldQVa
<CreatedTime>k__BackingField
FJoO9AB6KV
<PersonalAmount>k__BackingField
sPEOiUXYUT
<CurrencyID>k__BackingField
iBDOIamDBv
<CommissionCurrencyID>k__BackingField
QlQOHguiuR
<AccountState>k__BackingField
AiLOumRJyG
<AccountID>k__BackingField
NEuONwDtjC
<AccountGroupID>k__BackingField
sNgODkr3iG
<CommissionType>k__BackingField
nH1OtfGSK2
<CurrencyState>k__BackingField
mK1OdUv9hQ
<IsAgainst>k__BackingField
jw0OSaqtEB
<Branch>k__BackingField
dcXOk1SpPo
<Currency>k__BackingField
MthOPULr5I
<ServiceInfo>k__BackingField
KuYOjbm0ET
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PaymentStatu
AppTech.MSMS.Domain.Models.PaymentStatu
PaymentStatu
PaymentStatu
pJiOaDsrDG
<ID>k__BackingField
YN6OYKPdiO
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Person
AppTech.MSMS.Domain.Models.Person
Person
Person
FnxOwuVOYP
<ID>k__BackingField
lAFO08RQXh
<RowVersion>k__BackingField
n5ROqoFSwX
<Name>k__BackingField
bQpOMlSSh8
<PhoneNumber>k__BackingField
XMuOXNHeQx
<Address>k__BackingField
hcLOoUBeJV
<Email>k__BackingField
YMhOLFGFSM
<Nationality>k__BackingField
lrAOZgu0RT
<CardType>k__BackingField
ylQOKLgaWj
<CardNumber>k__BackingField
lcYOyhy0gA
<CardIssuePlace>k__BackingField
etaOvblk8o
<CardIssueDate>k__BackingField
r40O2GDtD8
<CardExpireDate>k__BackingField
YDAO8OXUoY
<FrontImage>k__BackingField
yyQO3YGvSy
<BackImage>k__BackingField
JupObCgeBX
<CreatedBy>k__BackingField
oNfOElWo8U
<BranchID>k__BackingField
xuAO54HxYD
<CreatedTime>k__BackingField
NsKOBVs8kX
<Type>k__BackingField
kuIOlLLlCx
<Branch>k__BackingField
E4NOTNSuEa
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Product
AppTech.MSMS.Domain.Models.Product
Product
Product
EJJOCLvbsN
<ID>k__BackingField
sUKOglxYIp
<RowVersion>k__BackingField
mVKOcncmTN
<Number>k__BackingField
EBqOJThtSn
<Name>k__BackingField
NRFO1Q5DPV
<Title>k__BackingField
k8yOrsWJx7
<Description>k__BackingField
es3OQOHjUL
<Type>k__BackingField
amKOFnAiG9
<ClassType>k__BackingField
nTAOVvawbX
<CategoryID>k__BackingField
xMgORFg2t2
<Status>k__BackingField
aigOfeyPRC
<Active>k__BackingField
XkMOekJSqK
<CostPrice>k__BackingField
oMOOxYt59W
<Price>k__BackingField
JTkOsBTYu7
<PersonnalPrice>k__BackingField
WQoOOfigZi
<CurrencyID>k__BackingField
LTVOhWwiL1
<UintID>k__BackingField
Yj8O6gJcfw
<ImageName>k__BackingField
vagO4GCwGD
<Flag>k__BackingField
d94OmCrtqd
<SoldOut>k__BackingField
WfwO7jpy62
<IsNew>k__BackingField
BoVOWB3rYS
<Limited>k__BackingField
fGEOnV8JWJ
<IsSpecail>k__BackingField
XHtOz8hvwH
<HasDiscount>k__BackingField
drPhG50yM2
<Discount>k__BackingField
PUQhA3VxRw
<OrderNo>k__BackingField
WrohUgJGht
<Code>k__BackingField
RAZhp99o42
<Tag>k__BackingField
zCSh9S0lAP
<RefID>k__BackingField
MAxhi2Imkx
<Note>k__BackingField
SN7hIAdNaV
<ExtraInfo>k__BackingField
PB0hHJS4tB
<ExtraID>k__BackingField
t3uhuXuPkb
<Dispatcher>k__BackingField
G1XhNAXUKD
<IsSynce>k__BackingField
NJChDseR7C
<Direct>k__BackingField
EoYhtgMqJ8
<Deleted>k__BackingField
gOOhdGyQG2
<Seen>k__BackingField
nf2hS9Hoi9
<Channel>k__BackingField
pvRhk2DY0K
<SyncID>k__BackingField
ANohPGftyM
<BindID>k__BackingField
M5RhjS5LwK
<CreatedBy>k__BackingField
GWWhacdH19
<BranchID>k__BackingField
NTShYr3IVr
<CreatedTime>k__BackingField
WVthwrt07G
<Branch>k__BackingField
DeVh0ZAnrA
<Currency>k__BackingField
n1ghqfFYS4
<ProductCategory>k__BackingField
XwVhM6ANIK
<UserInfo>k__BackingField
aIQhXWf5cl
<ProductImages>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ProductCategory
AppTech.MSMS.Domain.Models.ProductCategory
ProductCategory
ProductCategory
tlahoXaD8L
<ID>k__BackingField
KeIhL2W1an
<RowVersion>k__BackingField
C4ShZ6g48b
<Number>k__BackingField
vVjhK62GyW
<Name>k__BackingField
sgrhyJRFUw
<Note>k__BackingField
acNhvwIT0i
<BranchID>k__BackingField
rwah2k2xZr
<CreatedBy>k__BackingField
sp3h8RTnoT
<CreatedTime>k__BackingField
efZh3fIOU8
<Branch>k__BackingField
N3GhbnoDPg
<Products>k__BackingField
JDBhExb5Fw
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ProductImage
AppTech.MSMS.Domain.Models.ProductImage
ProductImage
ProductImage
iSah5Dnnm5
<ID>k__BackingField
oDDhBAoT7G
<RowVersion>k__BackingField
USGhlIWZLP
<ProductID>k__BackingField
pEYhTmm8gu
<ImageName>k__BackingField
X33hC5kSi4
<Title>k__BackingField
C8mhgdv5Nq
<Description>k__BackingField
cKEhcrlJTM
<Extainfo>k__BackingField
QlshJpRVSf
<BranchID>k__BackingField
uURh1Fqb1Q
<CreatedBy>k__BackingField
VythrtZ5sp
<CreatedTime>k__BackingField
sywhQ2OqlR
<Branch>k__BackingField
kpshFZdDud
<Product>k__BackingField
DM6hVR6riH
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Province
AppTech.MSMS.Domain.Models.Province
Province
Province
G5whRWMOtg
<ID>k__BackingField
X0YhfItJwr
<RowVersion>k__BackingField
Ft4heL8HfB
<Name>k__BackingField
NRdhxoqWSK
<CountryID>k__BackingField
vIWhs4blow
<CreatedBy>k__BackingField
LV1hORoLjn
<BranchID>k__BackingField
gq6hhQHgKn
<CreatedTime>k__BackingField
Bu8h6LBm9Z
<BankDeposits>k__BackingField
oX2h4l9bx8
<Branch>k__BackingField
Xo8hmvDsTC
<Country>k__BackingField
HlZh7NB5Gx
<UserInfo>k__BackingField
J74hWkAbIy
<Regions>k__BackingField
rlrhn7CeWb
<RemittanceRegions>k__BackingField
iDdhzdKjan
<TransferOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PurchaseInvoice
AppTech.MSMS.Domain.Models.PurchaseInvoice
PurchaseInvoice
PurchaseInvoice
yma6Grohb1
<ID>k__BackingField
R8R6AjZSf0
<RowVersion>k__BackingField
sMa6UPI3ed
<Number>k__BackingField
cA86pB7a40
<Amount>k__BackingField
NtB69IkU6Q
<CurrencyID>k__BackingField
fn16ieYTyY
<CreditorAccountID>k__BackingField
ECM6I2vWoK
<DebitorAccountID>k__BackingField
aBy6HxmjU4
<SupplierID>k__BackingField
oHp6u2WUCh
<InvoiceSupplierNo>k__BackingField
oOd6NCFUwL
<Discount>k__BackingField
uDY6DEjGrG
<Subtotal>k__BackingField
l6t6t3sHnf
<Date>k__BackingField
R7s6dkd409
<DueDate>k__BackingField
V066SIG7Sm
<Note>k__BackingField
naZ6k4l6Up
<EntryID>k__BackingField
uBO6PmYAWf
<InventoryID>k__BackingField
aX26jORGHA
<RefNumber>k__BackingField
Uua6ap2Two
<Extra>k__BackingField
VGE6YU0SHe
<AttachmentNumbers>k__BackingField
VFU6w6AFnK
<Footer>k__BackingField
b6M60Xigvl
<Year>k__BackingField
MOW6q61p46
<Status>k__BackingField
q1a6MiE0FC
<CreatedTime>k__BackingField
nto6X1wx4U
<CreatedBy>k__BackingField
GiI6o4EVZF
<BranchID>k__BackingField
iHS6L3eaxL
<Account>k__BackingField
wrj6ZAQdRP
<Account1>k__BackingField
aaC6KdqjEf
<Branch>k__BackingField
jjA6yO3m1n
<Currency>k__BackingField
N346vCROit
<Journal>k__BackingField
kx262HfbAT
<UserInfo>k__BackingField
Fui68L9HtC
<PurchaseInvoiceLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PurchaseInvoiceLine
AppTech.MSMS.Domain.Models.PurchaseInvoiceLine
PurchaseInvoiceLine
PurchaseInvoiceLine
eb263HwroH
<ID>k__BackingField
RvI6belWp2
<RowVersion>k__BackingField
vVX6E34osJ
<ParentID>k__BackingField
cts65Wh2ww
<SerialNumber>k__BackingField
sPx6BQRpKa
<ProductID>k__BackingField
ECP6lIvSyj
<Quantity>k__BackingField
cBb6TmR0Gx
<UnitPrice>k__BackingField
Ijk6C3PClI
<Discount>k__BackingField
fA86gE5SxZ
<SubTotal>k__BackingField
los6cVbHp4
<TotalAmount>k__BackingField
DQF6JSvpmc
<InventoryLineID>k__BackingField
dYT61M77uW
<Note>k__BackingField
fEe6rD9CP9
<CreatedTime>k__BackingField
vGa6QRvjm1
<CreatedBy>k__BackingField
OXP6F8CJyN
<BranchID>k__BackingField
EgI6VijA0Y
<Branch>k__BackingField
P6l6RpnAkS
<Faction>k__BackingField
krf6fnPjrB
<PurchaseInvoice>k__BackingField
HGE6eSj2nB
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Quotation
AppTech.MSMS.Domain.Models.Quotation
Quotation
Quotation
l1p6xnpZN4
<ID>k__BackingField
i8i6s20SCK
<RowVersion>k__BackingField
sYn6O5PNWQ
<ServiceID>k__BackingField
u9L6h7irkO
<AccountState>k__BackingField
VT266m0FM4
<AccountID>k__BackingField
hYt64af11I
<AccountGroupID>k__BackingField
Cg86mWYdi8
<Price>k__BackingField
D7Z67DHZDk
<Price2>k__BackingField
qJo6WCDtqv
<Type>k__BackingField
kCk6nDh8jB
<Note>k__BackingField
fWE6zwrnk6
<CreatedBy>k__BackingField
GIN4Gb7CjC
<BranchID>k__BackingField
Jcq4AeFEZR
<CreatedTime>k__BackingField
KN54UGMTiq
<Account>k__BackingField
NKK4pjmIeK
<Branch>k__BackingField
cEr49OLgjH
<ServiceInfo>k__BackingField
PJp4i0NYhk
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptCreditor
AppTech.MSMS.Domain.Models.ReceiptCreditor
ReceiptCreditor
ReceiptCreditor
jQy4IsKt64
<ID>k__BackingField
fQ14HoxQL3
<RowVersion>k__BackingField
nEL4uXMkxk
<Number>k__BackingField
QK94N9rPxu
<Amount>k__BackingField
Oh24DvrAgY
<CurrencyID>k__BackingField
Ww04tichXn
<CreditorAccountID>k__BackingField
NHB4dcfyTi
<DebitorAccountID>k__BackingField
Da84SdB6Z9
<Date>k__BackingField
TIZ4kVYRO5
<Note>k__BackingField
GkH4PZfO63
<EntryID>k__BackingField
ygG4jNbF9O
<RefNumber>k__BackingField
Vy04afmoxf
<AttachmentNumbers>k__BackingField
cMZ4YIIQqF
<IsDebited>k__BackingField
doB4w2SUlG
<CreatedBy>k__BackingField
vqy408sqek
<Prints>k__BackingField
igO4q22xB2
<CreatedTime>k__BackingField
gai4M2inbB
<BranchID>k__BackingField
HxC4XIYn1t
<Year>k__BackingField
Otq4o29iqe
<Status>k__BackingField
vpU4LXXXRV
<CreatedDate>k__BackingField
PMu4ZChN1Z
<HourTime>k__BackingField
y714KPOcv9
<MinuteTime>k__BackingField
Qwu4yZDF9g
<TransNumber>k__BackingField
Ct64vXB8ke
<Channel>k__BackingField
V8N42BWKZj
<Attachments>k__BackingField
LiA48C16dB
<ExtraInfo>k__BackingField
NFr439AkG7
<ExtraID>k__BackingField
U9d4bCULnN
<SyncID>k__BackingField
vAB4ErDTAP
<RefID>k__BackingField
m0p45bm2Wr
<BindID>k__BackingField
fUg4BjwrkC
<Binded>k__BackingField
eXs4lbipJQ
<Account>k__BackingField
jRU4TTAYt7
<Account1>k__BackingField
nbi4CKa7Jv
<Branch>k__BackingField
YAg4gFPLt8
<Currency>k__BackingField
etD4cBjjBL
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptDebitor
AppTech.MSMS.Domain.Models.ReceiptDebitor
ReceiptDebitor
ReceiptDebitor
WnG4JLDuAt
<ID>k__BackingField
LZ2412Ltcn
<RowVersion>k__BackingField
STL4rtuebv
<Number>k__BackingField
K1f4QwWIqV
<Amount>k__BackingField
VIe4FpVJ5X
<CurrencyID>k__BackingField
NZ34VgCNfL
<CreditorAccountID>k__BackingField
Cwe4RHJhlM
<DebitorAccountID>k__BackingField
aSm4f1Cu2m
<Date>k__BackingField
BK34eUcr7P
<Note>k__BackingField
jNy4xR9fEC
<EntryID>k__BackingField
bWa4s27JCv
<RefNumber>k__BackingField
kVp4On7hBN
<AttachmentNumbers>k__BackingField
TUi4hRsTC3
<IsDebited>k__BackingField
r0s46cW02j
<CreatedBy>k__BackingField
qnf44Q090s
<Prints>k__BackingField
YjK4m8NbaP
<CreatedTime>k__BackingField
Vro47Y7EOy
<BranchID>k__BackingField
xrQ4W8eVdx
<Year>k__BackingField
Qkc4nXE8rm
<Status>k__BackingField
Dw94zRuKP3
<CreatedDate>k__BackingField
r9WmGLk8in
<HourTime>k__BackingField
JPTmAufJHC
<MinuteTime>k__BackingField
M4umUQ0Ocg
<TransNumber>k__BackingField
yfjmpU7Wo9
<Channel>k__BackingField
kEQm9LwBLD
<Attachments>k__BackingField
tdOmiBKAT1
<ExtraInfo>k__BackingField
w1nmIF1wIm
<ExtraID>k__BackingField
KUxmHXiH5P
<SyncID>k__BackingField
edQmu3f53h
<RefID>k__BackingField
Ho2mNeVDUP
<BindID>k__BackingField
GnnmD1VQQc
<Binded>k__BackingField
GbVmtfutxb
<Account>k__BackingField
FUOmdBZskG
<Account1>k__BackingField
UIFmSunPDx
<Branch>k__BackingField
hUWmkdfYZs
<Currency>k__BackingField
VM6mPmnp2n
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Region
AppTech.MSMS.Domain.Models.Region
Region
Region
KuJmjkca2N
<ID>k__BackingField
yNhma4NI8K
<RowVersion>k__BackingField
KGBmYsyKNT
<Number>k__BackingField
nqwmwd4ZBP
<Name>k__BackingField
J1um07QC17
<Note>k__BackingField
QEmmq98sFM
<ProvinceID>k__BackingField
sSDmMiPIdj
<BranchID>k__BackingField
cQumX3UlYM
<CreatedBy>k__BackingField
lvRmoc2N6e
<CreatedTime>k__BackingField
K0EmL8fBoL
<AccountRegions>k__BackingField
VkqmZlsjpQ
<Branch>k__BackingField
VjlmKhbBKB
<Province>k__BackingField
YmumyJyUj4
<UserInfo>k__BackingField
TKOmvdgVn9
<SatelliteProviders>k__BackingField
KZlm2psuDC
<WifiProviders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceCommission
AppTech.MSMS.Domain.Models.RemittanceCommission
RemittanceCommission
RemittanceCommission
Mhym80kOsT
<ID>k__BackingField
AlMm3X1QJt
<RowVersion>k__BackingField
PScmbpcrUF
<RemittanceType>k__BackingField
c3jmEhX2xm
<CurrencyID>k__BackingField
Snum519slM
<StartAmount>k__BackingField
JGAmBGVie7
<EndAmount>k__BackingField
wjJml2OBpU
<CommmissionType>k__BackingField
W9QmTMFObN
<CenterCommission>k__BackingField
vC7mCNhtp0
<PointCommission>k__BackingField
fs7mg17E77
<CreatedBy>k__BackingField
Oo1mcSoGkM
<BranchID>k__BackingField
EfVmJye7wL
<CreatedTime>k__BackingField
bxCm1iCSV6
<CommissionCurrencyID>k__BackingField
e4fmrIBpco
<IsExpress>k__BackingField
Qs1mQoWmeI
<TargetState>k__BackingField
ONdmFEKJyU
<TargetID>k__BackingField
udXmVjgbFZ
<TargetGroupID>k__BackingField
SkjmRnTJNJ
<AccountState>k__BackingField
f7rmfOurf6
<AccountID>k__BackingField
oapme1sg0N
<AccountGroupID>k__BackingField
nlCmxOiJwN
<IsAgainst>k__BackingField
hlPmsXCVqZ
<VoucherType>k__BackingField
UVtmO48wYu
<CurrencyState>k__BackingField
U99mhyjfnU
<AmountState>k__BackingField
LIjm6fghXW
<EntryType>k__BackingField
rjym45bIHc
<SourceState>k__BackingField
ds7mmEQImC
<SourceID>k__BackingField
h4ym7VythC
<SourceGroupID>k__BackingField
hgsmWlmLAS
<Branch>k__BackingField
dI5mnGsZev
<Currency>k__BackingField
wu7mzewDCF
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceIn
AppTech.MSMS.Domain.Models.RemittanceIn
RemittanceIn
RemittanceIn
Gxm7GFLjOt
<ID>k__BackingField
lnE7AoRBgl
<RowVersion>k__BackingField
em97UIJZk1
<Number>k__BackingField
Y1E7pYtOb2
<RemittanceNumber>k__BackingField
AjS79jmn4G
<Amount>k__BackingField
He97iCeSDg
<CurrencyID>k__BackingField
Em17Iq2Ce9
<CreditorAccountID>k__BackingField
JJv7H8LCLk
<DebitorAccountID>k__BackingField
pDV7uxNsBm
<BeneficiaryName>k__BackingField
zKA7NvvcRV
<BeneficiaryPhone>k__BackingField
apT7DKsvJk
<SenderName>k__BackingField
FqE7tY07j2
<SenderPhone>k__BackingField
fnI7dtodGj
<SourcePointID>k__BackingField
j9M7SG3Z0y
<TargetPointID>k__BackingField
FgY7kPY7ue
<AgentID>k__BackingField
wJW7PxpBf2
<BranchID>k__BackingField
OJp7jHci9G
<SourceRegionID>k__BackingField
iED7agynHc
<TargetRegionID>k__BackingField
dXj7YDiYfF
<Date>k__BackingField
b4f7wqysgU
<Note>k__BackingField
AeD70fBsUG
<Purpose>k__BackingField
O3q7q2FIVA
<Prints>k__BackingField
FYn7MLK3CA
<Status>k__BackingField
w6J7XjxKFp
<EntryID>k__BackingField
j4T7oAwO9f
<CommissionAmount>k__BackingField
CYY7L5PD7b
<CommissionCurrencyID>k__BackingField
vh87ZtQoPc
<RefNumber>k__BackingField
ex97KMbQqD
<SenderCardID>k__BackingField
MkQ7ydDya5
<BeneficiaryCardID>k__BackingField
BeV7vyUHuU
<SearchOnlyByNumber>k__BackingField
G3T72HDLIU
<CreatedBy>k__BackingField
UIw78J4dLJ
<CreatedTime>k__BackingField
XBW733WUNi
<Year>k__BackingField
lxx7b0ilDc
<CenterCommission>k__BackingField
zsW7EFTbvH
<PointCommission>k__BackingField
Wn475fFYIo
<CommissionType>k__BackingField
DO27BU1bNw
<QueriedBy>k__BackingField
KXh7l0sOfi
<IsSync>k__BackingField
o9N7TKLBA6
<SyncRemittanceID>k__BackingField
tkd7ChNCBk
<SyncNumber>k__BackingField
jCU7ggGBXg
<Imported>k__BackingField
TRI7ciIKP7
<NetworkTarget>k__BackingField
Q107J16O1n
<ExchangeAmount>k__BackingField
bAe71mEFXv
<ExchangeCurrencyID>k__BackingField
XG17rZyCSa
<Channel>k__BackingField
Gxv7Q3mr4B
<Delivered>k__BackingField
DtC7FQ5M3g
<CreatedDate>k__BackingField
QyK7V1aLZe
<HourTime>k__BackingField
URA7RYfQXe
<MinuteTime>k__BackingField
Yak7fvOQIS
<TransNumber>k__BackingField
Nug7eDu4uV
<Attachments>k__BackingField
ICu7xeQlGD
<ExtraNote>k__BackingField
OmS7sct7cH
<RequetInfo>k__BackingField
loS7Oulal6
<ResponseInfo>k__BackingField
gWU7hDH0Gw
<RequestNote>k__BackingField
Y8776FeNQG
<ExtraInfo>k__BackingField
mb774Mf5Z0
<ProviderRef>k__BackingField
Iof7ma3M0g
<TransactionId>k__BackingField
zUd7710bQx
<UniqueNo>k__BackingField
f4V7WmZSYb
<ExtraID>k__BackingField
hYE7nxf07v
<SyncID>k__BackingField
kw07z1BuoY
<RefID>k__BackingField
c8CWGK9HvZ
<BindID>k__BackingField
llqWAVwV5F
<AutoExport>k__BackingField
ojwWUPx15p
<SyncEntyID>k__BackingField
uafWpxXh4e
<EntryNumber>k__BackingField
nVfW9xX3iP
<Binded>k__BackingField
ys8Wie5MDb
<TransferNumber>k__BackingField
jxKWICgrEi
<BillNumber>k__BackingField
JisWHnYsfq
<Method>k__BackingField
GRuWusOKkI
<State>k__BackingField
FuWWN7LMD1
<Suspended>k__BackingField
TvQWDjANO7
<Depended>k__BackingField
q5gWt2A7RK
<ExpressSync>k__BackingField
qMiWdf51eJ
<ExpressReference>k__BackingField
ETAWSso16K
<SyncTime>k__BackingField
O7SWkUX0C0
<SyncMethod>k__BackingField
CYsWPlgGXy
<AdminNote>k__BackingField
qVRWjCZsoJ
<AccountNote>k__BackingField
g17WahOqa4
<ByOrder>k__BackingField
G3KWYxnVha
<OrderID>k__BackingField
ndHWwSOP4T
<Account>k__BackingField
AbKW0pUIR6
<Account1>k__BackingField
IZiWqd829c
<Agent>k__BackingField
oc4WMhGjPV
<Branch>k__BackingField
ynVWXEuXjm
<Currency>k__BackingField
uMSWoeDTEQ
<Currency1>k__BackingField
ikJWLHkhYc
<Journal>k__BackingField
OjfWZSDL45
<RemittancePoint>k__BackingField
LdTWKgs18S
<UserInfo>k__BackingField
odZWyHRCij
<RemittanceOuts>k__BackingField
B3XWvDAB2d
<RemittancePoint1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceNumber
AppTech.MSMS.Domain.Models.RemittanceNumber
RemittanceNumber
RemittanceNumber
lp3W2XySpY
<ID>k__BackingField
b4uW8gVDKR
<Number>k__BackingField
NE0W3BCmYP
<Key>k__BackingField
j2VWbb5Nxl
<CreatedTime>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceOut
AppTech.MSMS.Domain.Models.RemittanceOut
RemittanceOut
RemittanceOut
kiZWERhdVK
<ID>k__BackingField
zHVW5XnRph
<RowVersion>k__BackingField
HCZWBJO4VG
<Number>k__BackingField
vbMWlUP2Vy
<RemittanceID>k__BackingField
tXnWTnNukN
<RemittanceNumber>k__BackingField
lniWCOuO1L
<Amount>k__BackingField
xEhWgtZ4kN
<CurrencyID>k__BackingField
W0sWcTI8Os
<CreditorAccountID>k__BackingField
UNCWJLg1kq
<DebitorAccountID>k__BackingField
vUDW1lUhHU
<RemittancePointID>k__BackingField
ouGWr0DTa9
<AgentID>k__BackingField
sBeWQqYnRY
<BranchID>k__BackingField
n59WFq0pNB
<RegionID>k__BackingField
IChWVODmUc
<Date>k__BackingField
cKOWRfOX5J
<Note>k__BackingField
jvvWffAW45
<Prints>k__BackingField
IxPWeX5tUE
<CommissionAmount>k__BackingField
VtvWxuSFNJ
<CommissionCurrencyID>k__BackingField
hvvWswJedb
<Delivered>k__BackingField
pDAWOMFi19
<EntryID>k__BackingField
wPqWhfOJjB
<BeneficiaryCardID>k__BackingField
sEqW6Unv9N
<CreatedBy>k__BackingField
aLbW4V6GtJ
<CreatedTime>k__BackingField
w9PWmLRYUg
<RefNumber>k__BackingField
zHjW7aVIls
<Year>k__BackingField
PNMWWyjkbf
<CenterCommission>k__BackingField
L98WnThTed
<PointCommission>k__BackingField
wGUWzE2Frf
<CommissionType>k__BackingField
mrnnGYNUPd
<BenficiaryID>k__BackingField
bVInACdLWq
<BenficiaryCard>k__BackingField
v9UnUK1Ivj
<Channel>k__BackingField
aabnpVnL6h
<Status>k__BackingField
a2cn9iTZud
<CreatedDate>k__BackingField
qy2niN9YtA
<HourTime>k__BackingField
W2PnI7CR1S
<MinuteTime>k__BackingField
Hl2nHP9dYB
<TransNumber>k__BackingField
mWLnuPO5ek
<Attachments>k__BackingField
AN0nNvGvvo
<ExtraInfo>k__BackingField
zIxnDj4vMq
<ExtraID>k__BackingField
q63nt9yN47
<SyncID>k__BackingField
g0MndL6qUF
<RefID>k__BackingField
UR2nS6ut6D
<BindID>k__BackingField
p70nkutk7d
<Binded>k__BackingField
LJYnPKQbRW
<Method>k__BackingField
bv2njic3Vy
<TransferNumber>k__BackingField
KoRna4EmOS
<BillNumber>k__BackingField
FSynYIWLKn
<State>k__BackingField
Lianwf3UIg
<Exported>k__BackingField
Fjsn0HntAk
<Depended>k__BackingField
RhQnqlxVhw
<ByOrder>k__BackingField
ssunMEpuWh
<OrderID>k__BackingField
VfRnXsJlAn
<Account>k__BackingField
XEynoU3fqe
<Account1>k__BackingField
KLNnLqDDsp
<Agent>k__BackingField
l2qnZO9Zkw
<Branch>k__BackingField
rH5nKXsL03
<Currency>k__BackingField
bCZnyN51Fn
<Journal>k__BackingField
Oa8nvKtaZj
<RemittanceIn>k__BackingField
mJQn2mBTI5
<RemittancePoint>k__BackingField
tXqn8x89xd
<RemittanceRegion>k__BackingField
d9Qn3W861l
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittancePoint
AppTech.MSMS.Domain.Models.RemittancePoint
RemittancePoint
RemittancePoint
CiVnbRM7SP
<ID>k__BackingField
CM2nECIlqB
<RowVersion>k__BackingField
rrxn5JkgL0
<Number>k__BackingField
yamnBtKa6y
<Name>k__BackingField
caNnldOSo3
<RegionID>k__BackingField
TfunTRtmIY
<Description>k__BackingField
zpLnCdHd7I
<Type>k__BackingField
zlMngapxZt
<Phone>k__BackingField
uT3ncdnS7c
<Address>k__BackingField
IPFnJQYw0J
<Fax>k__BackingField
qrgn1D3c25
<Note>k__BackingField
KkBnrZGtHa
<Active>k__BackingField
qqhnQJuyJR
<CreatedBy>k__BackingField
JuxnFsUjjX
<BranchID>k__BackingField
NAVnVkGHZP
<CreatedTime>k__BackingField
KVWnR80RBZ
<AccountID>k__BackingField
YxJnfDa2ke
<AutoExport>k__BackingField
prsneqOeRl
<Class>k__BackingField
KuZnxJd9YE
<SyncID>k__BackingField
nvpnsGqYHi
<Status>k__BackingField
x3mnOVkeNZ
<IsDirect>k__BackingField
PQGnhdNP2j
<AgentPoints>k__BackingField
irVn6H7TXI
<AgentPointUsers>k__BackingField
Ejrn4Zi8co
<Branch>k__BackingField
YmknmQgv91
<BranchTargets>k__BackingField
Gqsn7J9DGM
<ExchangerTargets>k__BackingField
NZKnWyVkqU
<RemittanceIns>k__BackingField
ThPnnnmcQJ
<RemittanceIns1>k__BackingField
f3nnzabW1j
<RemittanceOuts>k__BackingField
UlOzG7ApEo
<RemittanceRegion>k__BackingField
KTkzAoWyQN
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceRegion
AppTech.MSMS.Domain.Models.RemittanceRegion
RemittanceRegion
RemittanceRegion
sCvzUXSVcJ
<ID>k__BackingField
XojzpjXyuk
<RowVersion>k__BackingField
N2Tz92GdbE
<Name>k__BackingField
OrAziMQLP8
<ProvinceID>k__BackingField
KyqzI5kBwF
<CreatedBy>k__BackingField
XJ5zHgTo8d
<BranchID>k__BackingField
T99zujvLrU
<CreatedTime>k__BackingField
JBJzNsTN2j
<Branch>k__BackingField
SkpzDmxlYi
<Province>k__BackingField
E7jztelEZF
<RemittanceOuts>k__BackingField
g2mzdXcc3D
<RemittancePoints>k__BackingField
G1wzSQcRXg
<UserInfo>k__BackingField
vk9zkxIayu
<TransferOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RiyalMobile
AppTech.MSMS.Domain.Models.RiyalMobile
RiyalMobile
RiyalMobile
silzP67eik
<ID>k__BackingField
mNvzjEiBQf
<Number>k__BackingField
hZKzaJaAha
<ServiceID>k__BackingField
pyMzY25YRC
<NetworkID>k__BackingField
sAIzwH1Qel
<SubscriberNumber>k__BackingField
teUz0SNv42
<Amount>k__BackingField
WC2zqf8I0c
<FactionID>k__BackingField
up8zMUAymn
<RegionID>k__BackingField
JhYzX4ybhq
<LineType>k__BackingField
vrkzox8WtO
<Date>k__BackingField
fCFzLXPvZJ
<Year>k__BackingField
PJjzZ1Z0mf
<Status>k__BackingField
XhYzKc3pIF
<Note>k__BackingField
HrdzyKYHm5
<CreditorAccountID>k__BackingField
FKCzvNN0St
<CurrencyID>k__BackingField
B83z2r0HGb
<DebitorAccountID>k__BackingField
bNLz8K55Rg
<AgentID>k__BackingField
I2Dz3Rw9IS
<RefNumber>k__BackingField
NvPzbRreoi
<TransactionID>k__BackingField
TvtzEm98Gx
<ProviderID>k__BackingField
WbXz5yRrVA
<EntryID>k__BackingField
nanzBKm5DP
<PaymentEntryID>k__BackingField
JCxzlm9Hog
<Channel>k__BackingField
sk6zTOf28H
<CreatedBy>k__BackingField
vrrzCsSMOj
<BranchBy>k__BackingField
wyizgihq2X
<CreatedTime>k__BackingField
wN9zccyyIo
<RowVersion>k__BackingField
yEczJAgrv9
<BranchID>k__BackingField
BBQz1Goigu
<ProviderRM>k__BackingField
yyVzrOC346
<ProviderPrice>k__BackingField
hy6zQwWIdf
<SubNote>k__BackingField
zjJzFPRrnW
<Datestamb>k__BackingField
KT0zVtE74m
<UniqueNo>k__BackingField
QQQzRMNSwg
<OrderID>k__BackingField
weuzfdtvgb
<ExtraID>k__BackingField
nZEzeLIUj3
<SyncID>k__BackingField
R9szxaJPr3
<SyncEntryID>k__BackingField
ecmzsJZeMj
<SyncRecordID>k__BackingField
hL4zOb7DXw
<ProviderRecordID>k__BackingField
d2Qzhmu1AS
<Commission>k__BackingField
LrIz6eo28l
<Credit>k__BackingField
fKVz4iRIq0
<Percentage>k__BackingField
lNczm4xNTr
<ByOrder>k__BackingField
GUBz7ZuTJK
<IsDirect>k__BackingField
wohzWXK1Zt
<Flag>k__BackingField
kCTznXBHCU
<Verified>k__BackingField
RZfzztDLiI
<StatusChecked>k__BackingField
cLaAGG73YOc
<StatusUpdated>k__BackingField
A4rAGAfPsgA
<Type>k__BackingField
CePAGUcjOa0
<State>k__BackingField
bobAGpS4kNZ
<ByApi>k__BackingField
rN1AG92Cl2T
<ExternalApi>k__BackingField
LTRAGit7uMF
<Callback>k__BackingField
hFWAGIvRZcr
<ApiUpdated>k__BackingField
qHKAGH79EE6
<IsDebited>k__BackingField
UsoAGurimjm
<UpdatedByProvider>k__BackingField
OvVAGNwhkjC
<Tag>k__BackingField
d07AGDpP6jW
<Token>k__BackingField
rfLAGtwPPm0
<FactionName>k__BackingField
xkmAGdrmG7B
<Description>k__BackingField
wiSAGS8OrEC
<DeviceID>k__BackingField
ynuAGkgOrqu
<CallbackNote>k__BackingField
yFmAGPwK5JV
<CallbackResponse>k__BackingField
SAYAGjE7Rki
<ApiMessage>k__BackingField
LcqAGadKTJZ
<ApiInfo>k__BackingField
afkAGYZ1BIl
<ApiNote>k__BackingField
aiOAGw1U9he
<Reason>k__BackingField
qTyAG0S9ltv
<StateNote>k__BackingField
g2aAGqGmOGL
<RequestInfo>k__BackingField
b8iAGM9Qvag
<ExtraInfo>k__BackingField
UwQAGXI32pH
<ProcessNote>k__BackingField
uEXAGomVoSg
<AccountNote>k__BackingField
UfDAGLl9Me9
<Account>k__BackingField
M4IAGZWf8t1
<Account1>k__BackingField
MuvAGKJQclS
<Agent>k__BackingField
QQMAGy48awZ
<Branch>k__BackingField
L7gAGveaj44
<Currency>k__BackingField
bCdAG2O6d1s
<Journal>k__BackingField
J2GAG8ehawZ
<Payment>k__BackingField
pGiAG3ZJIcU
<TopupNetwork>k__BackingField
UPPAGba5yRo
<RiyalMobile1>k__BackingField
KmeAGEoM7VC
<RiyalMobile2>k__BackingField
TvVAG5qm682
<ServiceInfo>k__BackingField
U0dAGBrlb83
<TopupProvider>k__BackingField
mwbAGl6Gjxp
<UserInfo>k__BackingField
DXeAGTkqUjx
<WERegion>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RoleClaim
AppTech.MSMS.Domain.Models.RoleClaim
RoleClaim
RoleClaim
F4VAGC9BfZL
<ID>k__BackingField
AtrAGgQp0jO
<ClaimType>k__BackingField
bWLAGcYcmnc
<ClaimValue>k__BackingField
eB4AGJ5Taj4
<RoleID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RoleInfo
AppTech.MSMS.Domain.Models.RoleInfo
RoleInfo
RoleInfo
QLKAG1liTwv
<ID>k__BackingField
jrGAGrakPSi
<Name>k__BackingField
rkAAGQKL2fb
<AccountUsers>k__BackingField
Wn9AGFryKMN
<UserRoles>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RSS
AppTech.MSMS.Domain.Models.RSS
RSS
RSS
ldeAGVN10Nk
<ID>k__BackingField
th5AGRAXB4G
<RowVersion>k__BackingField
zDbAGfb8YQb
<Feed>k__BackingField
MRgAGe76Gwc
<Active>k__BackingField
pD8AGxfLd3M
<CreatedBy>k__BackingField
oYtAGs1aet9
<BranchID>k__BackingField
fQ8AGOUF0Bo
<CreatedTime>k__BackingField
WfsAGhaAOsV
<Branch>k__BackingField
pPVAG6QAg8J
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SaleCurrency
AppTech.MSMS.Domain.Models.SaleCurrency
SaleCurrency
SaleCurrency
T6DAG4WvgV6
<ID>k__BackingField
GQIAGmMXX6w
<RowVersion>k__BackingField
pVMAG7endAM
<Number>k__BackingField
PrgAGWyumoV
<Amount>k__BackingField
VtlAGn8CFji
<CurrencyID>k__BackingField
WV1AGzd52XD
<ExchangePrice>k__BackingField
mdPAAGLBUnO
<ExchangeAmount>k__BackingField
f1eAAACE3N4
<ExchangeCurrencyID>k__BackingField
qqmAAUCJPVb
<CreditorAccountID>k__BackingField
GySAApsQ95T
<DebitorAccountID>k__BackingField
OdoAA9hJ4oF
<Date>k__BackingField
yf4AAirakH4
<Note>k__BackingField
MUsAAI2BP9F
<EntryID>k__BackingField
igZAAHH7RGu
<Year>k__BackingField
r7SAAuwCCGK
<Status>k__BackingField
bxbAANGZfhN
<Channel>k__BackingField
g9vAADQ8ShK
<CreatedTime>k__BackingField
WMwAAtkUKio
<CreatedBy>k__BackingField
tHVAAd7LEUN
<BranchID>k__BackingField
Sg6AASBfhp6
<RefNumber>k__BackingField
MTRAAkptk2b
<CreatedDate>k__BackingField
M3wAAPBBKGE
<HourTime>k__BackingField
MkoAAjLJ91Z
<MinuteTime>k__BackingField
iOcAAabu0pR
<TransNumber>k__BackingField
yj0AAYmejtP
<Attachments>k__BackingField
fS8AAw30HXr
<ExtraInfo>k__BackingField
LUyAA0MFT5c
<ExtraID>k__BackingField
NL6AAq4YOr7
<SyncID>k__BackingField
exAAAM0VGNW
<RefID>k__BackingField
UqhAAXInemT
<BindID>k__BackingField
Y5DAAoHZtmI
<Binded>k__BackingField
Kn9AALipU90
<OrderID>k__BackingField
VaeAAZ9ZF99
<ByOrder>k__BackingField
iowAAKbxh0J
<Account>k__BackingField
Lg7AAyWlx7E
<Account1>k__BackingField
nxyAAvPiYWw
<Branch>k__BackingField
oTKAA2IrUtP
<Currency>k__BackingField
HS5AA8Z6tqC
<Currency1>k__BackingField
RRpAA3m4wEh
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SaleInvoice
AppTech.MSMS.Domain.Models.SaleInvoice
SaleInvoice
SaleInvoice
RxJAAboXA5b
<ID>k__BackingField
IWOAAE0PQt4
<RowVersion>k__BackingField
LFIAA5v0P9T
<Number>k__BackingField
BeJAABWU8sI
<Amount>k__BackingField
TPwAAlA3S05
<CurrencyID>k__BackingField
E1kAATFEcdf
<CreditorAccountID>k__BackingField
TpBAAC7rphb
<DebitorAccountID>k__BackingField
X5VAAgfivGB
<CustomerID>k__BackingField
Y0bAAcZPxYh
<PurchaseOrderNo>k__BackingField
lphAAJt49VP
<Discount>k__BackingField
OmFAA121MMu
<Subtotal>k__BackingField
PZOAArs850t
<Date>k__BackingField
WpsAAQMW80q
<DueDate>k__BackingField
nwKAAFZ74Ow
<Note>k__BackingField
oM7AAVRgii1
<EntryID>k__BackingField
PQ1AARBkipi
<InventoryID>k__BackingField
oeXAAf1TBJP
<RefNumber>k__BackingField
MsDAAeeG1o8
<Extra>k__BackingField
afOAAxboJB8
<AttachmentNumbers>k__BackingField
e3dAAsnCgeF
<Footer>k__BackingField
h9JAAO8wRIl
<Year>k__BackingField
HeFAAhavKB0
<Status>k__BackingField
LehAA6xK6GN
<CreatedTime>k__BackingField
mTeAA4qA5c6
<CreatedBy>k__BackingField
EtHAAmU9Wsa
<BranchID>k__BackingField
MUkAA7w2RGc
<Account>k__BackingField
Wk1AAWUCipq
<Account1>k__BackingField
FO2AAniy7H2
<Branch>k__BackingField
s01AAz4YHDQ
<Currency>k__BackingField
AD4AUGyRH26
<Journal>k__BackingField
y7rAUAGgfn8
<UserInfo>k__BackingField
xd9AUUnMLjD
<SaleInvoiceLines>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SaleInvoiceLine
AppTech.MSMS.Domain.Models.SaleInvoiceLine
SaleInvoiceLine
SaleInvoiceLine
NqdAUppJJlT
<ID>k__BackingField
suEAU9mYRbP
<RowVersion>k__BackingField
SLTAUiPkoQC
<ParentID>k__BackingField
cB5AUIhVOCj
<SerialNumber>k__BackingField
JjTAUHLrZZ3
<ProductID>k__BackingField
ImbAUuKDr8c
<Quantity>k__BackingField
bdPAUN5E2X8
<UnitPrice>k__BackingField
gDlAUDJOma8
<Discount>k__BackingField
DKDAUtNyQIK
<SubTotal>k__BackingField
bc3AUdw6B1G
<TotalAmount>k__BackingField
aQxAUSPgsWp
<InventoryLineID>k__BackingField
U98AUkmNsyw
<Note>k__BackingField
I8MAUPAkdvF
<CreatedTime>k__BackingField
vCuAUjeEnDx
<CreatedBy>k__BackingField
xPFAUaDHYYe
<BranchID>k__BackingField
mLTAUYJkuIj
<Branch>k__BackingField
TXdAUw1MdZI
<Faction>k__BackingField
A62AU05F44A
<SaleInvoice>k__BackingField
pfDAUqrpq4t
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteFaction
AppTech.MSMS.Domain.Models.SatelliteFaction
SatelliteFaction
SatelliteFaction
ttXAUMHXHoM
<ID>k__BackingField
z70AUXxcRwY
<RowVersion>k__BackingField
I9HAUo4xwXe
<Number>k__BackingField
F4IAULNJLqC
<Name>k__BackingField
wHSAUZON8YS
<Description>k__BackingField
OVgAUK3qCq0
<Price>k__BackingField
fZAAUyWCZYv
<Note>k__BackingField
V7fAUvetQPo
<SatelliteProviderID>k__BackingField
YNFAU2H5Yhc
<BranchID>k__BackingField
yKkAU88kPOs
<CreatedBy>k__BackingField
dnQAU3je4cV
<CreatedTime>k__BackingField
PGNAUbKoI8n
<Branch>k__BackingField
hDZAUEIrvu4
<SatelliteProvider>k__BackingField
FSHAU5QLCUq
<UserInfo>k__BackingField
y0WAUBluXMy
<SatellitePayments>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatellitePayment
AppTech.MSMS.Domain.Models.SatellitePayment
SatellitePayment
SatellitePayment
bcKAUlSYLWr
<ID>k__BackingField
usiAUTKQERW
<RowVersion>k__BackingField
dLUAUCm6632
<Number>k__BackingField
gIMAUg2lFMQ
<ProviderID>k__BackingField
IDRAUc2JvS8
<FactionID>k__BackingField
Oi7AUJyeJ2u
<SubscriptionTerm>k__BackingField
VDUAU1dBGn3
<SubscriptionNumber>k__BackingField
HswAUr0SiLP
<ProfitAmount>k__BackingField
KVqAUQ1vBe5
<ProviderAmount>k__BackingField
g8sAUFNkcng
<Amount>k__BackingField
FmEAUVTgdF8
<CurrencyID>k__BackingField
oGQAURd7UJZ
<CreditorAccountID>k__BackingField
KXAAUfQsphC
<DebitorAccountID>k__BackingField
lVCAUehwpOm
<Date>k__BackingField
wHlAUx8bQ2R
<Note>k__BackingField
KoXAUshxKq0
<EntryID>k__BackingField
DnHAUOEP6MR
<RefNumber>k__BackingField
YYZAUhMm1lg
<IsDebited>k__BackingField
GwBAU6JR251
<Year>k__BackingField
YRVAU4R1A5P
<CreatedBy>k__BackingField
qNEAUm07xMU
<CreatedTime>k__BackingField
VBFAU7RQpZ0
<ProccessTime>k__BackingField
f7IAUWJibdw
<BranchID>k__BackingField
xA9AUnTiwms
<Status>k__BackingField
cNdAUzyFGsV
<ServiceID>k__BackingField
tBYApGimxS8
<Account>k__BackingField
HkiApAS632s
<Account1>k__BackingField
rwyApUwV2uB
<Branch>k__BackingField
AbEApp7TjIp
<Currency>k__BackingField
A1RAp9mjEqL
<SatelliteFaction>k__BackingField
BCFApi2n6QX
<SatelliteProvider>k__BackingField
PduApI6LRS5
<ServiceInfo>k__BackingField
q4qApHqU4jZ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteProvider
AppTech.MSMS.Domain.Models.SatelliteProvider
SatelliteProvider
SatelliteProvider
yl3ApuykoCp
<ID>k__BackingField
KOsApNR7riO
<RowVersion>k__BackingField
uQhApDGF5GN
<Number>k__BackingField
UTKAptNyT3Q
<Name>k__BackingField
tZlApd2A6Kj
<Phone>k__BackingField
MQaApSZVn6K
<Note>k__BackingField
Fl2ApkZI5AQ
<PrafitStatus>k__BackingField
qe0ApPsAdsn
<PrafitAmount>k__BackingField
LQfApjkZHFx
<AccountID>k__BackingField
mTjApaUQlNL
<RegionID>k__BackingField
BD5ApYMj30Y
<MinSubscribe>k__BackingField
UhZApw8ZtPf
<BranchID>k__BackingField
aO3Ap0YdIgA
<CreatedBy>k__BackingField
iuYApq3KboH
<CreatedTime>k__BackingField
kjgApMEJNFx
<Account>k__BackingField
Cu2ApXnLsdm
<Branch>k__BackingField
ofEApoCQ0tE
<OrderSatelliteQuotas>k__BackingField
zYFApLKlZSo
<Region>k__BackingField
u3bApZckAOn
<SatelliteFactions>k__BackingField
YgsApKprnrI
<SatellitePayments>k__BackingField
tJhApyS8sZI
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OrderSatelliteQuota
AppTech.MSMS.Domain.Models.OrderSatelliteQuota
OrderSatelliteQuota
OrderSatelliteQuota
v9jApvlOnfP
<ID>k__BackingField
kSmAp2DiebF
<RowVersion>k__BackingField
pnPAp83tDyu
<Number>k__BackingField
bhsAp3pUPAS
<Amount>k__BackingField
nh2ApbHVPom
<CurrencyID>k__BackingField
VMTApE0QA30
<ProviderID>k__BackingField
joDAp5BmDLO
<CreditorAccountID>k__BackingField
PtyApBO3305
<DebitorAccountID>k__BackingField
whnApluhRo8
<Description>k__BackingField
JF0ApTfiscq
<ProviderNote>k__BackingField
yBGApCagQ4P
<QuotaAmount>k__BackingField
PiiApglhWYo
<QuotatedTime>k__BackingField
xBiApc3dAqQ
<Commission>k__BackingField
RU9ApJQYI8E
<Status>k__BackingField
g9VAp1tb9Gr
<ParentID>k__BackingField
T4xAprg3Fak
<Date>k__BackingField
LPAApQnYlVc
<Note>k__BackingField
VtOApFdNBfj
<EntryID>k__BackingField
rlNApVZEZpj
<IsDebited>k__BackingField
pCsApRyYoBM
<Channel>k__BackingField
FxdApfQ3QN2
<Year>k__BackingField
j0cApe8ObaA
<ExtraInfo>k__BackingField
uj8ApxfxVXd
<CreatedBy>k__BackingField
CPAApsn2EKc
<BranchID>k__BackingField
quEApOuh6NP
<CreatedTime>k__BackingField
RkcAphRhPCD
<Account>k__BackingField
K5xAp6RRGXn
<Account1>k__BackingField
YCSAp4CHWAw
<Branch>k__BackingField
TNZApm59udn
<Currency>k__BackingField
tmfAp7lAxH1
<SatelliteProvider>k__BackingField
UVFApWmIHeV
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceClaim
AppTech.MSMS.Domain.Models.ServiceClaim
ServiceClaim
ServiceClaim
FnYApnn0pOx
<ID>k__BackingField
U4qApz2KguI
<RowVersion>k__BackingField
CxNA9GNaJXS
<ServiceID>k__BackingField
nb9A9A4ZnQS
<CreatedBy>k__BackingField
rdaA9UOJj6V
<BranchID>k__BackingField
cYyA9pOkTfg
<CreatedTime>k__BackingField
UIeA99eGiJW
<AccountID>k__BackingField
WD3A9iKKB8I
<AccountGroupID>k__BackingField
OP9A9ISCFj1
<AccountState>k__BackingField
PHpA9HejAfX
<Account>k__BackingField
U1cA9ueoCoP
<Branch>k__BackingField
YkPA9NHO14Q
<ServiceInfo>k__BackingField
JqSA9De4iiY
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceInfo
AppTech.MSMS.Domain.Models.ServiceInfo
ServiceInfo
ServiceInfo
CNZA9t2jm8N
<ID>k__BackingField
K8TA9d0FxNc
<RowVersion>k__BackingField
gX7A9S6V4ML
<Number>k__BackingField
XKlA9kYqDOW
<Name>k__BackingField
zxLA9PRbqOU
<Note>k__BackingField
LGGA9jnOnTK
<CreatedBy>k__BackingField
ylSA9agSmFb
<BranchID>k__BackingField
J98A9YkTU7p
<CreatedTime>k__BackingField
funA9w0cdhQ
<FixedPrice>k__BackingField
Ha5A908Dqtd
<Prices>k__BackingField
y3FA9qOPN4i
<Description>k__BackingField
cl1A9MkA4WU
<CategoryID>k__BackingField
MZAA9XXYWVT
<IsActive>k__BackingField
r0YA9onVinR
<Type>k__BackingField
BfjA9LF4dot
<KeyName>k__BackingField
E6kA9ZnM777
<VoucherID>k__BackingField
VkRA9Kyp5GO
<Module>k__BackingField
AV1A9yt07tL
<HadProduct>k__BackingField
fMJA9vuHJAP
<PartyTarget>k__BackingField
s3MA92eiBeb
<RefNumber>k__BackingField
a4pA98wMdHI
<Extra>k__BackingField
nTcA9327S8H
<Bagats>k__BackingField
TgEA9bdxi7g
<BankDeposits>k__BackingField
JPfA9EMyImq
<Branch>k__BackingField
g7NA95sGLt0
<CardOrders>k__BackingField
w76A9BqgjXP
<CardPayments>k__BackingField
ItcA9lv695b
<CommissionReceipts>k__BackingField
nK7A9TOPL9J
<CoverageOrders>k__BackingField
kU5A9CBxnOA
<CurrencyExchanges>k__BackingField
sDMA9g0v4Eu
<DepositOrders>k__BackingField
ptQA9cHFJ0f
<Items>k__BackingField
Dd1A9JeAjwg
<ItemCosts>k__BackingField
KEYA911LQrL
<LiveTopups>k__BackingField
FHrA9rqRttp
<LoanOrders>k__BackingField
QeqA9Qg0Cww
<MoneyMaster>k__BackingField
iD7A9FvW5eV
<OfferOrders>k__BackingField
i54A9Vr0U98
<OrderInfoes>k__BackingField
VBLA9RlnJPF
<Payments>k__BackingField
RQmA9fg5dNK
<PaymentCommissions>k__BackingField
HxHA9e2IugF
<Quotations>k__BackingField
rG8A9x7ExBY
<RiyalMobiles>k__BackingField
HR4A9sbHomf
<SatellitePayments>k__BackingField
ywgA9OaDRMb
<ServiceClaims>k__BackingField
pfEA9hco7cB
<SMSDispatches>k__BackingField
JgeA96NIa25
<UserInfo>k__BackingField
AdvA94TED7k
<SimCardOrders>k__BackingField
nIcA9mBn8Il
<Subscribers>k__BackingField
rZdA97MYMuY
<Topups>k__BackingField
z9ZA9WhRU6S
<TopupCommissions>k__BackingField
p4iA9nW4FBL
<TopupOrders>k__BackingField
VatA9z55jST
<TrailToupOrders>k__BackingField
RJFAiGUXVa5
<TransferOrders>k__BackingField
wxUAiAhRIXR
<TransportOrders>k__BackingField
T7DAiUHYFDh
<WERegions>k__BackingField
lWWAipbxUFD
<WifiPayments>k__BackingField
niOAi9FMMtk
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Setting
AppTech.MSMS.Domain.Models.Setting
Setting
Setting
rItAiibTWEJ
<ID>k__BackingField
tNmAiI3jBx1
<KeyName>k__BackingField
Yj9AiHwxh7g
<Value>k__BackingField
lr9AiuGLBIo
<Level>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Sim
AppTech.MSMS.Domain.Models.Sim
Sim
Sim
gFeAiN8B3I9
<ID>k__BackingField
gvAAiDJPHlY
<RowVersion>k__BackingField
HnmAitojdW7
<InvoiceID>k__BackingField
xhRAidvnDW1
<OperatorID>k__BackingField
EFgAiSrimJv
<Number>k__BackingField
vEqAik615Pi
<AccountID>k__BackingField
YZxAiPntqca
<Price>k__BackingField
hGPAijOlhub
<ExtraPrice>k__BackingField
ggJAia1KGsi
<Total>k__BackingField
h12AiYfpbIJ
<DateIn>k__BackingField
PIkAiwO9OCE
<DateOut>k__BackingField
us8Ai0gpqBl
<Type>k__BackingField
xxEAiq6pgX9
<Status>k__BackingField
JhqAiMrtja4
<OrderID>k__BackingField
B1aAiXskrX5
<OrderNumber>k__BackingField
OsgAio36lmg
<ActionType>k__BackingField
rZnAiLUKcOT
<ActionID>k__BackingField
ramAiZCBCXA
<SubscriberName>k__BackingField
D50AiKuxx6F
<SubscriberNumber>k__BackingField
KI8Aiyle4da
<Active>k__BackingField
S8aAiv6dDeL
<ActivatedBy>k__BackingField
vf8Ai2hiMKi
<RefNumber>k__BackingField
KayAi842YZv
<Note>k__BackingField
wJyAi3hHCuT
<BranchID>k__BackingField
PZDAibC8BNX
<CreatedBy>k__BackingField
RJuAiEr82dI
<CreatedTime>k__BackingField
OmvAi5KmN0o
<InvoiceType>k__BackingField
NbbAiBP7PoW
<Sold>k__BackingField
eoDAilDNkes
<CostPrice>k__BackingField
zGVAiTYxHLi
<SupplierAccountID>k__BackingField
iLvAiCCCn4y
<ExchangeAccountID>k__BackingField
GImAigQpEe9
<Account>k__BackingField
NgLAic1wRfA
<Branch>k__BackingField
OD5AiJepdkR
<SimInvoice>k__BackingField
C8bAi1RxShO
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimCardOrder
AppTech.MSMS.Domain.Models.SimCardOrder
SimCardOrder
SimCardOrder
DnnAireC9AB
<ID>k__BackingField
iJMAiQ517Uj
<RowVersion>k__BackingField
t6UAiFKXLYq
<SimNumber>k__BackingField
Kh3AiV9yD0q
<SimType>k__BackingField
iOwAiRIvXgk
<ContractNumber>k__BackingField
OtiAifX3ZJl
<ESDN>k__BackingField
BJrAieMEWct
<MSISDN>k__BackingField
nEDAixSkqPK
<PersonalCardType>k__BackingField
XRIAisCOwQg
<IssueDate>k__BackingField
Xb4AiOp7HYk
<BirthDate>k__BackingField
UMcAihSDSuH
<CustomerName>k__BackingField
mHeAi67rb6N
<CustomerAddress>k__BackingField
FHPAi4i29M9
<CreatedBy>k__BackingField
oTJAimgD5F4
<BranchID>k__BackingField
nMyAi7nAA5A
<CreatedTime>k__BackingField
VwvAiW9ZPpD
<Amount>k__BackingField
UvDAincM5r2
<SubscriberNumber>k__BackingField
OK3AizYEXTl
<Note>k__BackingField
mg7AIG0stax
<ActionType>k__BackingField
OWJAIAllIZo
<Channel>k__BackingField
quoAIUpjKyy
<FrontCardImage>k__BackingField
XBPAIpXnvrx
<BackCardImage>k__BackingField
WsyAI9MO14D
<ExpireDate>k__BackingField
rvyAIiltSqQ
<CardNumber>k__BackingField
XWcAII4vvOo
<ServiceID>k__BackingField
G1iAIHu1UhE
<ParentID>k__BackingField
akRAIuKUATH
<CardIssuePlace>k__BackingField
kKaAINipvIq
<LineType>k__BackingField
tf4AIDNluh7
<NetworkID>k__BackingField
lyHAItSTQYG
<SimStatus>k__BackingField
tZkAId0E1gq
<SimAction>k__BackingField
EOHAISu1Cue
<AccountID>k__BackingField
zJwAIkjRJOQ
<Account>k__BackingField
JRqAIPZRecg
<Branch>k__BackingField
eTaAIjMTjK3
<OrderInfo>k__BackingField
PpGAIa6cfqr
<ServiceInfo>k__BackingField
lfVAIY9RxIB
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimInvoice
AppTech.MSMS.Domain.Models.SimInvoice
SimInvoice
SimInvoice
IkcAIwXIxSU
<ID>k__BackingField
by1AI0Sujky
<RowVersion>k__BackingField
w5qAIqd5Maw
<Number>k__BackingField
xF6AIMMqo5C
<NetworkID>k__BackingField
tIaAIXiJMrw
<StartNumber>k__BackingField
SIbAIoGuWFX
<EndNumber>k__BackingField
w3jAIL8O7YW
<UnitPrice>k__BackingField
ftTAIZnqIHH
<TotalUnits>k__BackingField
JeiAIKNwwqj
<Amount>k__BackingField
D3nAIyHRnLO
<CurrencyID>k__BackingField
qNZAIvBDvOZ
<CreditorAccountID>k__BackingField
LQdAI2u1jUT
<DebitorAccountID>k__BackingField
h4DAI8Nnqap
<EntryID>k__BackingField
It8AI3HiUoi
<Note>k__BackingField
xvCAIbxHS5K
<RefNumber>k__BackingField
k4DAIE79Aoo
<Date>k__BackingField
kf1AI5yXxqS
<Status>k__BackingField
iqbAIBlqHIV
<Channel>k__BackingField
p55AIlUwWko
<Year>k__BackingField
qksAITNpdl5
<BranchID>k__BackingField
m8YAIC4Dxbs
<CreatedBy>k__BackingField
NikAIgiIZOZ
<CreatedTime>k__BackingField
yUvAIcjHq00
<CreatedDate>k__BackingField
bDuAIJh5m1O
<HourTime>k__BackingField
NOEAI1oZ3mx
<MinuteTime>k__BackingField
bT2AIr9ao8Z
<TransNumber>k__BackingField
Ap4AIQsSj6X
<SimType>k__BackingField
Iw4AIFCgrxU
<ViaExcel>k__BackingField
F4yAIV9wqmi
<Attachments>k__BackingField
iCwAIRL071u
<ExtraInfo>k__BackingField
JNDAIfaXNHU
<ExtraID>k__BackingField
psfAIe2TEl8
<SyncID>k__BackingField
GOlAIxdIIXj
<RefID>k__BackingField
SwEAIscbWLV
<BindID>k__BackingField
M5aAIOvTvno
<Binded>k__BackingField
NawAIhYAUk6
<OrderID>k__BackingField
irOAI6rBLHa
<ByOrder>k__BackingField
RlCAI45Nake
<LineType>k__BackingField
wFcAImVAUwK
<Type>k__BackingField
JsgAI7gLHuc
<Method>k__BackingField
GkrAIWj1Li4
<SourceType>k__BackingField
gXJAInjZp6k
<Account>k__BackingField
aLbAIzqMi6D
<Account1>k__BackingField
BXTAHGVGx2N
<Branch>k__BackingField
W8mAHAGAnml
<Currency>k__BackingField
W99AHUlomrk
<Sims>k__BackingField
ul5AHptmQ58
<TopupNetwork>k__BackingField
GEuAH9srGBs
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimpleEntry
AppTech.MSMS.Domain.Models.SimpleEntry
SimpleEntry
SimpleEntry
TCyAHiI7iAf
<ID>k__BackingField
xXiAHIJiZpE
<RowVersion>k__BackingField
dHMAHHm6TTO
<Number>k__BackingField
dkxAHuO4uUS
<Amount>k__BackingField
S7wAHNSPMkF
<CurrencyID>k__BackingField
t5rAHD2YCk0
<CreditorAccountID>k__BackingField
SaMAHttZgNf
<DebitorAccountID>k__BackingField
pBVAHdg6p5y
<Date>k__BackingField
J2YAHSvUeCI
<Note>k__BackingField
XVwAHkCNXhd
<EntryID>k__BackingField
BCbAHPX07Hs
<RefNumber>k__BackingField
Hf0AHjiPgOS
<AttachmentNumbers>k__BackingField
exhAHahM1iO
<IsDebited>k__BackingField
XerAHYw5qCq
<CreatedBy>k__BackingField
VMLAHwuEsjp
<Prints>k__BackingField
PWGAH0racqh
<CreatedTime>k__BackingField
qr6AHqSqJZt
<BranchID>k__BackingField
LsVAHMkUhs0
<Year>k__BackingField
MyQAHXGHv8H
<Status>k__BackingField
s2VAHoVjs2s
<CreatedDate>k__BackingField
mNSAHL1PHiL
<HourTime>k__BackingField
bVGAHZ7JKLw
<MinuteTime>k__BackingField
V9lAHKDhQOp
<TransNumber>k__BackingField
QnDAHyf7qmY
<Channel>k__BackingField
OLQAHvW2SgI
<Attachments>k__BackingField
BM0AH2pnwee
<ExtraInfo>k__BackingField
isHAH83yH4Q
<ExtraID>k__BackingField
BcQAH3MSMQq
<SyncID>k__BackingField
GBJAHbc7iuK
<RefID>k__BackingField
OdrAHEtiOmM
<BindID>k__BackingField
PEbAH5tJ1HX
<Binded>k__BackingField
qjGAHBmMEej
<Account>k__BackingField
yMiAHlHlAn2
<Account1>k__BackingField
sYeAHTL2HCV
<Branch>k__BackingField
avKAHCfosLG
<Currency>k__BackingField
unMAHg99cbb
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimPurchase
AppTech.MSMS.Domain.Models.SimPurchase
SimPurchase
SimPurchase
kxYAHcMAH8D
<ID>k__BackingField
kp7AHJuXGPa
<RowVersion>k__BackingField
kVHAH1dtvvK
<Number>k__BackingField
G8yAHrh70KM
<NetworkID>k__BackingField
b4mAHQ2vOaU
<StartNumber>k__BackingField
uAMAHFkKcpB
<EndNumber>k__BackingField
onlAHVN3ZZp
<UnitPrice>k__BackingField
woKAHR2DQLx
<TotalUnits>k__BackingField
WVPAHfrPoTF
<Amount>k__BackingField
QavAHecQOjl
<CurrencyID>k__BackingField
oj4AHxtv9qk
<CreditorAccountID>k__BackingField
DTNAHs7j6eN
<DebitorAccountID>k__BackingField
a2GAHOMDr8H
<EntryID>k__BackingField
JRMAHhfhQeN
<Note>k__BackingField
BL7AH64lnkX
<RefNumber>k__BackingField
FI6AH4iJUuF
<Date>k__BackingField
UjjAHmh07WL
<Status>k__BackingField
gidAH7h9JVT
<Channel>k__BackingField
zMHAHWHV5jw
<Year>k__BackingField
hsUAHn5d9s2
<BranchID>k__BackingField
IDrAHzkqMlo
<CreatedBy>k__BackingField
H3OAuGoWexb
<CreatedTime>k__BackingField
f2pAuAxy89G
<CreatedDate>k__BackingField
vMqAuUNrhUu
<HourTime>k__BackingField
FaSAup0asoW
<MinuteTime>k__BackingField
H0fAu9tWSIJ
<TransNumber>k__BackingField
Gd3AuiMtj86
<SimType>k__BackingField
jh3AuIE2Kbr
<ViaExcel>k__BackingField
yI4AuH6axUW
<Attachments>k__BackingField
TpuAuuAQwPs
<ExtraInfo>k__BackingField
P4ZAuNIdsGK
<ExtraID>k__BackingField
uKkAuDLwdOw
<SyncID>k__BackingField
HbsAutA19PW
<RefID>k__BackingField
bl9Aud3MAnG
<BindID>k__BackingField
S6eAuSHHjrI
<Binded>k__BackingField
z5UAukcEihW
<OrderID>k__BackingField
y24AuPJaOsO
<ByOrder>k__BackingField
CgLAujExLri
<LineType>k__BackingField
fIeAua2f8K8
<Account>k__BackingField
iXrAuYY7Obx
<Account1>k__BackingField
lnlAuw2tFjs
<Branch>k__BackingField
KkwAu0OSony
<Currency>k__BackingField
nctAuq11fsF
<SimPurchase1>k__BackingField
w32AuMvFrkr
<SimPurchase2>k__BackingField
yL9AuX8Nujy
<TopupNetwork>k__BackingField
XEyAuoDQpBG
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSDispatch
AppTech.MSMS.Domain.Models.SMSDispatch
SMSDispatch
SMSDispatch
LRSAuLLK7pR
<ID>k__BackingField
VQJAuZ1KtP4
<RowVersion>k__BackingField
iQdAuKxXip0
<ServiceID>k__BackingField
dRHAuyor92Y
<ClientID>k__BackingField
M79Auv2Xh76
<Active>k__BackingField
lu8Au2Ng63o
<BranchID>k__BackingField
SnUAu8SYpfn
<CreatedBy>k__BackingField
PRvAu3rJly4
<CreatedTime>k__BackingField
rUxAubnVgYx
<Branch>k__BackingField
xDcAuE2GC6B
<Client>k__BackingField
nsdAu5ekFQs
<ServiceInfo>k__BackingField
smPAuBKsfCl
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSLog
AppTech.MSMS.Domain.Models.SMSLog
SMSLog
SMSLog
ffBAulf3ryV
<ID>k__BackingField
XRuAuTyopbV
<UserID>k__BackingField
maHAuCZE0wa
<AccountID>k__BackingField
BnkAugr34OH
<Stamp>k__BackingField
lLcAuc6xCrD
<PhoneNumber>k__BackingField
YJ8AuJOxsVu
<Message>k__BackingField
MhOAu1tjkKL
<Note>k__BackingField
kYSAur7b4gt
<Extra>k__BackingField
Du7AuQLTZ4c
<RequestMsg>k__BackingField
xqlAuFqprwY
<ResponseMsg>k__BackingField
DWKAuVXteRl
<Status>k__BackingField
ovFAuRr6Y61
<CreatedTime>k__BackingField
OvCAuf3BRIC
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSMessage
AppTech.MSMS.Domain.Models.SMSMessage
SMSMessage
SMSMessage
hoDAueacDJL
<ID>k__BackingField
x2HAuxQtfMn
<RowVersion>k__BackingField
FcNAuslUUYv
<PhoneNumber>k__BackingField
WgWAuOxUpJU
<Message>k__BackingField
qvlAuhAKuEk
<Status>k__BackingField
hRHAu6GlAdq
<CreatedBy>k__BackingField
eoxAu4JexbK
<BranchID>k__BackingField
jnxAumhy6JG
<CreatedTime>k__BackingField
uYxAu7yuJv4
<Branch>k__BackingField
pJNAuWRMJVT
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SpecialSim
AppTech.MSMS.Domain.Models.SpecialSim
SpecialSim
SpecialSim
Qd7AunNjqa7
<ID>k__BackingField
hLOAuzdKq15
<RowVersion>k__BackingField
Ij9ANGqexB3
<Number>k__BackingField
b0wANAXmyHk
<Type>k__BackingField
E79ANUiJnan
<Price>k__BackingField
NEvANpbkief
<CostPrice>k__BackingField
B7KAN9MEoyH
<OperatorID>k__BackingField
nFbANi7KhDD
<Status>k__BackingField
tQiANIfmwO7
<Active>k__BackingField
RAjANHXeguZ
<Note>k__BackingField
kMfANu8pIcA
<BranchID>k__BackingField
G7ZANNkv92h
<CreatedBy>k__BackingField
QugANDwl6u6
<CreatedTime>k__BackingField
f6GANtA3M6R
<Branch>k__BackingField
BGWANdd3sZp
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Subscriber
AppTech.MSMS.Domain.Models.Subscriber
Subscriber
Subscriber
gjEANSMx0Ri
<ID>k__BackingField
BmRANkIV3M4
<RowVersion>k__BackingField
njlANPP101Z
<Number>k__BackingField
j6VANjpXs6i
<Name>k__BackingField
V6oANaZZdqW
<SubscriberNo>k__BackingField
qtXANYUM7IJ
<AccountID>k__BackingField
xOMANwkxYuN
<ProviderID>k__BackingField
DYKAN0DvuCL
<ServiceID>k__BackingField
yq3ANqhOPWT
<Code>k__BackingField
fJCANM47N5u
<Ref>k__BackingField
Gf5ANXcbN14
<Extra>k__BackingField
k4sANoLPGOy
<Address>k__BackingField
pxaANLW2c3Q
<Note>k__BackingField
eo1ANZAKHmd
<BranchID>k__BackingField
OQsANKISFPM
<CreatedBy>k__BackingField
rNwANyBWEZY
<CreatedTime>k__BackingField
J65ANvDj2rT
<PhoneNumber>k__BackingField
EU6AN2Pyfg4
<OpeningBalance>k__BackingField
ed9AN8nrBfq
<Account>k__BackingField
a9CAN3tcrj7
<Branch>k__BackingField
IIlANbWrblA
<ServiceInfo>k__BackingField
mXLANEOFmWN
<TopupProvider>k__BackingField
eJDAN50sAex
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Supplier
AppTech.MSMS.Domain.Models.Supplier
Supplier
Supplier
RqSANBf5mMt
<ID>k__BackingField
ODJANlojHch
<RowVersion>k__BackingField
Oy5ANTX96KC
<Number>k__BackingField
V61ANCEwDvl
<Name>k__BackingField
zbQANgJ4DBA
<AccountID>k__BackingField
xjdANcmrbyM
<PhoneNumber>k__BackingField
KKCANJGBCf2
<ContactNumber>k__BackingField
EaRAN1OX3A1
<Address>k__BackingField
RreANreTyrZ
<Note>k__BackingField
axDANQABsco
<Email>k__BackingField
gjbANFHPRph
<CardType>k__BackingField
e1iANVSoxnR
<CardNumber>k__BackingField
Jq1ANRohBAY
<CardIssuePlace>k__BackingField
spfANfPOEAk
<CardIssueDate>k__BackingField
IsnANeBaMt9
<ImageName>k__BackingField
KgiANx4gxDu
<CreatedBy>k__BackingField
KcoANsXZFsB
<BranchID>k__BackingField
tJ6ANOFCT30
<CreatedTime>k__BackingField
CNYANh6frOT
<Type>k__BackingField
un0AN6EPJ3A
<Status>k__BackingField
J1lAN4GTh4q
<SyncAccountID>k__BackingField
I7NANmF86Ht
<RefNumber>k__BackingField
H0hAN7jFPEG
<Extra>k__BackingField
dcgANW4NoOy
<Account>k__BackingField
df6ANn2D24M
<Branch>k__BackingField
CSIANzF2RKM
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SuspendTopup
AppTech.MSMS.Domain.Models.SuspendTopup
SuspendTopup
SuspendTopup
YlQADG8NTvH
<ID>k__BackingField
SwkADAydTlZ
<RecordID>k__BackingField
T3VADUfohcP
<ProviderID>k__BackingField
AZ9ADpPFm2b
<Type>k__BackingField
FtWAD9e4W0Y
<Status>k__BackingField
V0oADi7ju67
<Date>k__BackingField
yShADIlEMdo
<CuredTime>k__BackingField
tfEADHChkSq
<CuredBy>k__BackingField
vnoADuidvsr
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Topup
AppTech.MSMS.Domain.Models.Topup
Topup
Topup
lp3ADNtF7lC
<ID>k__BackingField
TteADDHEVnE
<Number>k__BackingField
VGAADt7brPj
<ServiceID>k__BackingField
t8XADdTMKbB
<NetworkID>k__BackingField
fj3ADSJgPle
<SubscriberNumber>k__BackingField
nlNADkpZtjX
<Amount>k__BackingField
BpCADPBR9ir
<FactionID>k__BackingField
EL9ADjEv496
<RegionID>k__BackingField
wA4ADaEaSG0
<LineType>k__BackingField
CuyADYM9Aas
<Date>k__BackingField
qxDADwTIPm3
<Year>k__BackingField
NM3AD0Alvbr
<Status>k__BackingField
h8yADqZRkno
<Note>k__BackingField
utKADMsx6Cq
<CreditorAccountID>k__BackingField
wPuADXastLf
<CurrencyID>k__BackingField
wwiADoM8v7d
<DebitorAccountID>k__BackingField
hlxADLEAMnv
<AgentID>k__BackingField
UTZADZKfdjX
<RefNumber>k__BackingField
fquADKJ5ALq
<TransactionID>k__BackingField
wF0ADyiQ05j
<ProviderID>k__BackingField
bsPADviQlTE
<EntryID>k__BackingField
hKSAD20EeBC
<PaymentEntryID>k__BackingField
oYhAD8DXXB2
<Channel>k__BackingField
J7xAD3DuBTb
<CreatedBy>k__BackingField
HPBADbPT7oT
<BranchBy>k__BackingField
cJ0ADEmYBHd
<CreatedTime>k__BackingField
PMMAD5EsufF
<RowVersion>k__BackingField
EnvADBdusjU
<BranchID>k__BackingField
P9XADlTAICw
<ProviderRM>k__BackingField
t6fADTeDbLA
<ProviderPrice>k__BackingField
nCbADCg30aq
<SubNote>k__BackingField
hYqADgq855j
<Datestamb>k__BackingField
xkFADcJmGVZ
<UniqueNo>k__BackingField
zhUADJr4ZDh
<Quantity>k__BackingField
YBOAD1pSTGy
<UnitPrice>k__BackingField
qxFADrF4ae5
<UnitCost>k__BackingField
H2fADQ1h7kO
<CostAmount>k__BackingField
VDVADFFFf6L
<DifferentialAmount>k__BackingField
FpvADVltT0v
<CommissionAmount>k__BackingField
Fm7ADRltGCg
<Discount>k__BackingField
EnRADftkopi
<TotalCost>k__BackingField
SsSADeZ4BtC
<TotalAmount>k__BackingField
FEsADxQ87XI
<Profits>k__BackingField
bihADs91yyX
<Method>k__BackingField
Sv7ADOhInyi
<Type>k__BackingField
bsBADhQ26hW
<Class>k__BackingField
yVfAD6OdGEP
<LType>k__BackingField
BwBAD4GDcGd
<OperatorID>k__BackingField
gEdADmnQmqs
<AppTechApi>k__BackingField
flgAD7qJPXb
<BillNumber>k__BackingField
dfUADWRS03C
<BillState>k__BackingField
z9MADnDhtls
<Debited>k__BackingField
MhUADzevvyC
<ByChild>k__BackingField
Bh6AtGRhKvN
<IsDirect>k__BackingField
fqDAtAbf381
<BundleName>k__BackingField
Wr5AtU8s7nC
<BundleCode>k__BackingField
HTrAtpRvG3s
<ExCode>k__BackingField
qHZAt9oMITu
<TransNumber>k__BackingField
SgbAtitIXAQ
<OperationID>k__BackingField
hKWAtIbowBn
<AccountID>k__BackingField
w4LAtHQ1k7f
<State>k__BackingField
PtOAtuPX8ep
<StateClass>k__BackingField
rjLAtNOPid1
<Identifier>k__BackingField
wKpAtDfjPIO
<AdminNote>k__BackingField
RfQAttOM0Te
<AccountNote>k__BackingField
vt4AtdZHYbH
<Description>k__BackingField
yS3AtSWa0Yp
<Responded>k__BackingField
SlMAtkQrqXg
<RequestInfo>k__BackingField
v4gAtP6ZiaT
<ResponseInfo>k__BackingField
BiVAtje2XWv
<ResponseTime>k__BackingField
k6wAtaXAaJU
<ResponseStatus>k__BackingField
QC4AtYJXfdE
<ResponseReference>k__BackingField
yUWAtwfC9ib
<ExecutionPeroid>k__BackingField
BI7At0UfjRl
<FaildRequest>k__BackingField
DixAtq6StdK
<FailedReason>k__BackingField
BgGAtMBTpYM
<FailedType>k__BackingField
RMCAtXSWU7Q
<Cured>k__BackingField
nPdAtorK4ju
<CuredBy>k__BackingField
ndKAtLVKITw
<CuredInfo>k__BackingField
yj7AtZZFqE2
<InspectInfo>k__BackingField
UAGAtKTAqDt
<Flag>k__BackingField
hS1AtyAxMpI
<Action>k__BackingField
MyUAtvs6QTJ
<QuotaionID>k__BackingField
Xn2At2aixU6
<SyncID>k__BackingField
KqfAt8pg2du
<Account>k__BackingField
JpjAt3KwSjr
<Account1>k__BackingField
Y87Atb6QWLe
<Agent>k__BackingField
q9cAtESFULo
<Branch>k__BackingField
NuWAt5w1fqk
<Currency>k__BackingField
bYlAtBhpya6
<Journal>k__BackingField
pofAtlur1lX
<ServiceInfo>k__BackingField
YA8AtTWhYeb
<TopupProvider>k__BackingField
FIiAtCCRUOS
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupClosure
AppTech.MSMS.Domain.Models.TopupClosure
TopupClosure
TopupClosure
auHAtg5G588
<ID>k__BackingField
crHAtcSHclf
<RowVersion>k__BackingField
nb7AtJE6J2K
<Number>k__BackingField
xbSAt16IuGI
<ProviderID>k__BackingField
oRBAtrYDl39
<Date>k__BackingField
rk0AtQw1xEo
<TopupBalance>k__BackingField
U1PAtF3JyVy
<CurrentBalance>k__BackingField
ppCAtVRTqbv
<RemoteBalance>k__BackingField
OjHAtRR8X7b
<DifferBalance>k__BackingField
bQLAtfEMGJ7
<IsMatched>k__BackingField
N4WAteCcmpj
<DebitedTotal>k__BackingField
H02Atx2QXeZ
<FailedTotal>k__BackingField
LR2AtsR68wJ
<Type>k__BackingField
sYOAtOvnxFr
<Status>k__BackingField
dWOAth4myaW
<Flag>k__BackingField
KA2At6p7343
<BalStatus>k__BackingField
n95At43WXIE
<Approved>k__BackingField
UDlAtm9On3m
<Info>k__BackingField
uwHAt76GsK5
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupCommission
AppTech.MSMS.Domain.Models.TopupCommission
TopupCommission
TopupCommission
rvEAtWXEIcv
<ID>k__BackingField
mpOAtnR4TRb
<RowVersion>k__BackingField
EMBAtzdS5qE
<Number>k__BackingField
kdTAdG6miYu
<ServiceID>k__BackingField
JHgAdAxtZrD
<StartDate>k__BackingField
SYxAdUqcuoU
<EndDate>k__BackingField
SM3AdpvtgHD
<OpsCount>k__BackingField
f5pAd9AT27J
<Percentage>k__BackingField
mcmAdiO0yUb
<Amount>k__BackingField
f41AdIEsY5i
<CurrencyID>k__BackingField
mDQAdHVlsAl
<CreditorAccountID>k__BackingField
LGfAduSNpFl
<DebitorAccountID>k__BackingField
R3bAdNpxBl7
<Credited>k__BackingField
axiAdD8HmWk
<EntryID>k__BackingField
IvYAdtLqNBU
<Note>k__BackingField
DjiAddlZM3m
<RefNumber>k__BackingField
IBmAdSdhjat
<LineType>k__BackingField
mwUAdk7AONE
<ProviderID>k__BackingField
tR8AdPlrkZx
<NetworkID>k__BackingField
y3RAdjn4ZFP
<Date>k__BackingField
e1HAdaykjWU
<Status>k__BackingField
bLTAdYJ5Mn6
<Year>k__BackingField
yvSAdwgETdx
<BranchID>k__BackingField
bTvAd0l37UH
<CreatedBy>k__BackingField
NmgAdqU4ZFq
<CreatedTime>k__BackingField
uekAdM4rj06
<CreatedDate>k__BackingField
lkuAdXDEfeB
<HourTime>k__BackingField
R46AdohEEAy
<MinuteTime>k__BackingField
I6fAdLdEGQy
<TransNumber>k__BackingField
UmyAdZdVDln
<Channel>k__BackingField
NcAAdKxxtsU
<Attachments>k__BackingField
MgFAdyidyuo
<ExtraInfo>k__BackingField
ICrAdvlbLQq
<ExtraID>k__BackingField
CK8Ad2S46CP
<SyncID>k__BackingField
S1uAd8WMfLF
<RefID>k__BackingField
bSNAd391AiA
<BindID>k__BackingField
G9ZAdbKcBxm
<Binded>k__BackingField
Vo6AdEYne2O
<TotalTopup>k__BackingField
G0vAd5XsWp2
<Account>k__BackingField
ob3AdBHQ2Kg
<Account1>k__BackingField
HUFAdlnrfh9
<Branch>k__BackingField
gNAAdTVSlSZ
<Currency>k__BackingField
glkAdCYBJwp
<Journal>k__BackingField
gqkAdgji9dd
<ServiceInfo>k__BackingField
Io4AdcRVrKZ
<TopupNetwork>k__BackingField
g6LAdJUfgVo
<TopupProvider>k__BackingField
KZmAd16c9sW
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupNetwork
AppTech.MSMS.Domain.Models.TopupNetwork
TopupNetwork
TopupNetwork
OxhAdrbt0li
<ID>k__BackingField
bFYAdQYAbot
<Name>k__BackingField
m4SAdFaWfYG
<Type>k__BackingField
K0xAdVbv3BH
<OfferOrders>k__BackingField
UJqAdRAK2M1
<RiyalMobiles>k__BackingField
JKhAdf2SdlT
<SimInvoices>k__BackingField
zVrAdeNfSXy
<SimPurchases>k__BackingField
fpkAdxiQyZD
<TopupCommissions>k__BackingField
GMjAdsRVQh8
<TrailToupCommissions>k__BackingField
yawAdO8KFHm
<TrailToupOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupOrder
AppTech.MSMS.Domain.Models.TopupOrder
TopupOrder
TopupOrder
hE4Adhn5NGe
<ID>k__BackingField
ggcAd6wQloe
<RowVersion>k__BackingField
dwJAd4mqc9F
<ParentID>k__BackingField
gtUAdmsCZ4K
<ServiceID>k__BackingField
lfBAd7pdkMv
<SubscriberNumber>k__BackingField
qXOAdWAfo4Z
<Amount>k__BackingField
xAuAdnv6WQ5
<Description>k__BackingField
xsQAdz7IZa4
<Note>k__BackingField
xEMASGjGiq6
<Channel>k__BackingField
Qa2ASAuhjbs
<ImageName>k__BackingField
z9mASUyG85c
<CreatedBy>k__BackingField
SiOASpCWDvQ
<BranchID>k__BackingField
i8iAS9h8EXN
<CreatedTime>k__BackingField
U8FASiuX22Q
<FactionID>k__BackingField
wj7ASIFu3Rs
<AccountID>k__BackingField
Q3CASHTDhmS
<Account>k__BackingField
nUTASuWTT5n
<Branch>k__BackingField
g0dASNyt6yG
<OrderInfo>k__BackingField
yXAASDMbcGw
<ServiceInfo>k__BackingField
AryAStghtBX
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupProvider
AppTech.MSMS.Domain.Models.TopupProvider
TopupProvider
TopupProvider
ioQASdrIiCe
<ID>k__BackingField
QmEASSUOc1r
<RowVersion>k__BackingField
HCjASk4v9Wr
<Number>k__BackingField
DUNASPg2nWY
<Name>k__BackingField
lQCASj52xa1
<Note>k__BackingField
EPrASaFHI6E
<CreatedBy>k__BackingField
Dw3ASYAgbFn
<BranchID>k__BackingField
sqnASwCxFCN
<CreatedTime>k__BackingField
YSUAS0LHPBG
<AccountID>k__BackingField
y2gASqe5X8y
<ApiName>k__BackingField
mOKASM6d9WS
<Username>k__BackingField
LVCASX4aY4W
<Password>k__BackingField
pI9ASohA7kk
<Token>k__BackingField
y0mASLMOOSx
<UserId>k__BackingField
NtCASZv2I82
<BaseUrl>k__BackingField
yvYASKJ2bYR
<Type>k__BackingField
wgLASybExV2
<SyncAccountID>k__BackingField
jwKASvsRXYg
<Balance>k__BackingField
oEfAS2Y3IcK
<BalanceText>k__BackingField
DhWAS8oymw4
<IsAppTechApi>k__BackingField
NvkAS3kpGJb
<ApiVC>k__BackingField
f5AASbwGmXU
<IsDirect>k__BackingField
wB7ASEmRLH3
<BalanceUpdatedTime>k__BackingField
BCnAS5qEAG9
<AutoBalance>k__BackingField
CVCASBIsF1X
<NotifyBalOut>k__BackingField
Q00ASlkyp22
<IsSoap>k__BackingField
oeZASTYJvBH
<Port>k__BackingField
Jx0ASCXZWJn
<Mode>k__BackingField
BgnASghv3qg
<Bundling>k__BackingField
RoBAScFrotn
<Status>k__BackingField
Ux7ASJlBst6
<ApiType>k__BackingField
efYAS1NxcDU
<Flag>k__BackingField
EEhASrGPZh5
<Warning>k__BackingField
aUNASQGh9BJ
<WarningNote>k__BackingField
JZiASFqys81
<ExtraNo>k__BackingField
AAtASVdQwiw
<Token2>k__BackingField
nRGASRQbJG9
<ExtraInfo>k__BackingField
LGyASfMgZj9
<MiniBalance>k__BackingField
EjPASeUebim
<AutoInspect>k__BackingField
y4BASxBbhQb
<Account>k__BackingField
CF2ASsOV4R6
<Branch>k__BackingField
JUMASOXNBFH
<ExchangerTargets>k__BackingField
mP1ASh0s8vR
<ItemCosts>k__BackingField
gESAS6m6nYU
<LiveTopups>k__BackingField
ps8AS45DxAU
<RiyalMobiles>k__BackingField
cgFASmMADQf
<Subscribers>k__BackingField
MphAS7LqoZN
<Topups>k__BackingField
vsVASW7VJY6
<TopupCommissions>k__BackingField
TxAASnsV61w
<UserInfo>k__BackingField
bXuASzKSe6f
<TransportOrders>k__BackingField
VigAkGIYuXw
<External>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupCommission
AppTech.MSMS.Domain.Models.TrailToupCommission
TrailToupCommission
TrailToupCommission
KxFAkAD1CXo
<ID>k__BackingField
wa8AkUSFOZa
<RowVersion>k__BackingField
tdWAkpiT2or
<MobileNetworkID>k__BackingField
EcRAk9Kgv8X
<FromAmount>k__BackingField
AHyAkiPiabN
<ToAmount>k__BackingField
YyXAkIAgk7S
<Percentage>k__BackingField
TDlAkHHtFK5
<Note>k__BackingField
xPYAkuLNiq3
<CreatedBy>k__BackingField
TstAkNXWcMD
<CreatedTime>k__BackingField
O3iAkDDkGFA
<BranchID>k__BackingField
UNiAktS3UNb
<PersonnalPrice>k__BackingField
OvuAkd1Wohk
<Branch>k__BackingField
HAiAkSjjtvv
<TopupNetwork>k__BackingField
kxAAkkDpvom
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupOrder
AppTech.MSMS.Domain.Models.TrailToupOrder
TrailToupOrder
TrailToupOrder
mKZAkP5reOl
<ID>k__BackingField
swPAkjDGOxi
<RowVersion>k__BackingField
RZ6Aka00RKH
<MobileNetworkID>k__BackingField
LddAkY2LQM8
<SubscriberNumber>k__BackingField
QX2AkwUCkyg
<Amount>k__BackingField
Y6sAk0nfU2q
<Percentage>k__BackingField
IneAkqOSr2e
<ExchangeAmount>k__BackingField
wlrAkMMJemC
<Note>k__BackingField
cBQAkXKERRG
<Channel>k__BackingField
FVtAkoTRN4W
<ParentID>k__BackingField
z98AkLQUq5G
<ServiceID>k__BackingField
M6iAkZsYmRN
<CreatedBy>k__BackingField
wtDAkKplBCp
<BranchID>k__BackingField
StTAkyHTTTe
<CreatedTime>k__BackingField
yBvAkvhlwUV
<AccountID>k__BackingField
ciAAk2BDXov
<Account>k__BackingField
SiXAk8KhiVm
<Branch>k__BackingField
s8pAk36Mywn
<OrderInfo>k__BackingField
DGkAkbaj0OG
<ServiceInfo>k__BackingField
xVrAkEt2dpp
<TopupNetwork>k__BackingField
Ej9Ak5Ym5eZ
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferIn
AppTech.MSMS.Domain.Models.TransferIn
TransferIn
TransferIn
AnSAkBsSIJ6
<ID>k__BackingField
u5NAkl3Yw65
<RowVersion>k__BackingField
YylAkTK1b64
<Number>k__BackingField
pj7AkCKRCkw
<TransferNumber>k__BackingField
NaVAkgRybuc
<Amount>k__BackingField
V8bAkccsUiq
<CurrencyID>k__BackingField
e5qAkJCwaYF
<ExchangerID>k__BackingField
xm0Ak1JMpHO
<AccountID>k__BackingField
ze2AkrhL3V6
<BeneficiaryName>k__BackingField
KoaAkQEVZNN
<BeneficiaryPhone>k__BackingField
LaJAkFjCpxG
<SenderName>k__BackingField
VZiAkVD2142
<SenderPhone>k__BackingField
G6tAkRDySYt
<CommissionCurrencyID>k__BackingField
mh4AkfrFaAI
<CommissionAmount>k__BackingField
JMoAke0N7Ch
<EntryID>k__BackingField
IbQAkxdMLYl
<ExtraInfo>k__BackingField
NtKAksJm1nK
<ExtraID>k__BackingField
iToAkO0q6im
<RefNumber>k__BackingField
oXXAkht0pmd
<Status>k__BackingField
BwWAk6qggI8
<Delivered>k__BackingField
tlYAk41MS5x
<Date>k__BackingField
qQwAkmSG02T
<Year>k__BackingField
uUNAk7Vyo18
<Note>k__BackingField
JXqAkW6Lv0y
<Channel>k__BackingField
iOfAkncjXGF
<CreatedBy>k__BackingField
fpfAkzY60jr
<BranchID>k__BackingField
Q2JAPGypash
<CreatedTime>k__BackingField
TowAPAHhUDg
<CreatedDate>k__BackingField
dHNAPUbPSiA
<HourTime>k__BackingField
ktXAPpHbOhu
<MinuteTime>k__BackingField
VyKAP957kn5
<TransNumber>k__BackingField
gT7APi8TNYu
<Attachments>k__BackingField
PBiAPIXol5Q
<SyncID>k__BackingField
Eh3APHMgCY2
<RefID>k__BackingField
pqOAPu28Dpt
<BindID>k__BackingField
mhtAPNlFnLw
<Binded>k__BackingField
egLAPDt2JpS
<OrderID>k__BackingField
TpVAPtwuXFU
<ByOrder>k__BackingField
SbYAPdpUdgL
<IsSync>k__BackingField
S7vAPS11SfO
<Type>k__BackingField
WtfAPk6rXmZ
<Account>k__BackingField
hkTAPPaFht2
<Branch>k__BackingField
cXcAPjV2IhJ
<Currency>k__BackingField
zsVAPajbP4b
<Currency1>k__BackingField
YYOAPYp6Gtv
<Exchanger>k__BackingField
i9OAPw8sRZq
<UserInfo>k__BackingField
s9yAP06EJ38
<ExchangerAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOrder
AppTech.MSMS.Domain.Models.TransferOrder
TransferOrder
TransferOrder
PNdAPq8ebno
<ID>k__BackingField
lisAPMhWn9h
<RowVersion>k__BackingField
CCVAPXt7ErB
<TransferNumber>k__BackingField
FidAPo2OVBg
<ServiceID>k__BackingField
cLWAPLf1lW8
<IsIncoming>k__BackingField
CNcAPZofghJ
<Amount>k__BackingField
sevAPKhpsW6
<CurrencyID>k__BackingField
G3LAPyhFfCl
<ReceiverName>k__BackingField
nduAPv5q3Hr
<ReceiverMobile>k__BackingField
F9FAP2SUEGZ
<ReceiverCardNo>k__BackingField
yJlAP8y68tM
<ReceiverCardIssuerPlace>k__BackingField
yoMAP3V3wCP
<ReceiverCardIssuerDate>k__BackingField
cbiAPbGj66A
<SenderName>k__BackingField
dTEAPEF0Thj
<SenderMobile>k__BackingField
JDXAP5BUxM2
<Note>k__BackingField
CMNAPBNTKyi
<Channel>k__BackingField
oo5APleJwR8
<ImageName>k__BackingField
pYrAPTwRKbd
<CreatedBy>k__BackingField
PhSAPCjQdKJ
<BranchID>k__BackingField
BGCAPgbrYhk
<CreatedTime>k__BackingField
R3eAPcJbRBm
<ExchangerID>k__BackingField
dAqAPJWLdQE
<ParentID>k__BackingField
wnSAP1w7POD
<ReceiverCardType>k__BackingField
CgkAPrQErRE
<AccountID>k__BackingField
vNfAPQrsrJk
<CommissionCurrencyID>k__BackingField
XuoAPF1kb3l
<Commission>k__BackingField
gtyAPVdXixh
<Status>k__BackingField
roTAPRQj1fN
<ReceiverImage>k__BackingField
C1dAPfuwIWQ
<SenderImage>k__BackingField
d6DAPeTkGmn
<Date>k__BackingField
To8APx3mR8K
<CountryID>k__BackingField
WaeAPsPdNY6
<ProvinceID>k__BackingField
XgEAPOKYWdH
<RegionID>k__BackingField
L2xAPhbaaeB
<RefNumber>k__BackingField
QbjAP69w48L
<Extra>k__BackingField
BfYAP4fjU9D
<Account>k__BackingField
hkpAPm4jNC0
<Branch>k__BackingField
bZ4AP7rDSOV
<Country>k__BackingField
M3mAPWy9mSb
<Currency>k__BackingField
AqSAPnqHbH4
<Currency1>k__BackingField
WX9APzdI1nX
<Exchanger>k__BackingField
ap3AjGpiRdm
<OrderInfo>k__BackingField
yHmAjAYDdob
<Province>k__BackingField
V82AjUMXcQf
<RemittanceRegion>k__BackingField
WelAjpfApGL
<ServiceInfo>k__BackingField
nBYAj9ZyK4v
<TransferOrder1>k__BackingField
u6NAjiH9mTy
<TransferOrder2>k__BackingField
RSTAjIFMX8d
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOut
AppTech.MSMS.Domain.Models.TransferOut
TransferOut
TransferOut
J5PAjHRRBDY
<ID>k__BackingField
CUFAjuyYGp9
<RowVersion>k__BackingField
oZ4AjNnb6I2
<Number>k__BackingField
v7dAjDXqXoX
<TransferNumber>k__BackingField
l9ZAjttxJPb
<Amount>k__BackingField
lRgAjdW1nKE
<CurrencyID>k__BackingField
TA2AjSFpVyP
<ExchangerID>k__BackingField
NjiAjkukxmP
<AccountID>k__BackingField
xdAAjPwux3w
<BeneficiaryName>k__BackingField
BRaAjjWmt5a
<BeneficiaryPhone>k__BackingField
kbbAja7eNXA
<SenderName>k__BackingField
MQJAjYLPkNa
<SenderPhone>k__BackingField
jHbAjwLjqbF
<CommissionCurrencyID>k__BackingField
E69Aj08oHbG
<CommissionAmount>k__BackingField
mwYAjqyCsjt
<ExchangerCommission>k__BackingField
LxFAjMIkZe9
<EntryID>k__BackingField
YhJAjXfFLg5
<ExtraInfo>k__BackingField
IiAAjonDwiv
<ExtraID>k__BackingField
P1RAjLHXJ8E
<RefNumber>k__BackingField
lBgAjZgBWyx
<Status>k__BackingField
RfqAjK7N02C
<Date>k__BackingField
eJ0AjyA7ClD
<Year>k__BackingField
gg6AjvBY2sX
<Note>k__BackingField
WWqAj2stxi8
<Channel>k__BackingField
xKAAj8lChkp
<CreatedBy>k__BackingField
bOcAj3nJXgX
<BranchID>k__BackingField
yU3Ajb1nfqb
<CreatedTime>k__BackingField
DERAjEjJCsA
<CreatedDate>k__BackingField
xo1Aj5O5Vh4
<HourTime>k__BackingField
ukaAjBZotGU
<MinuteTime>k__BackingField
R07AjlnkV4f
<TransNumber>k__BackingField
VZQAjTkaDVI
<Attachments>k__BackingField
qBWAjCXYcvV
<SyncID>k__BackingField
PeBAjgTbyTl
<RefID>k__BackingField
KKUAjcg2Sc4
<BindID>k__BackingField
ir1AjJOhd70
<Binded>k__BackingField
rwLAj1howPY
<OrderID>k__BackingField
rbYAjrNGw7Z
<ByOrder>k__BackingField
ecrAjQETZP8
<IsSync>k__BackingField
aglAjFbLE2Y
<Type>k__BackingField
aBRAjVTGOCT
<Account>k__BackingField
RQfAjRCLIWU
<Branch>k__BackingField
v7SAjff4kAv
<Currency>k__BackingField
fI9Ajepmn5M
<Currency1>k__BackingField
HTNAjxCbARA
<Exchanger>k__BackingField
ujLAjsNRYar
<UserInfo>k__BackingField
lEEAjOcn1qj
<ExchangerAccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Transporter
AppTech.MSMS.Domain.Models.Transporter
Transporter
Transporter
CjkAjhOCKFd
<ID>k__BackingField
c2LAj68WuCu
<RowVersion>k__BackingField
qXBAj4d3H5D
<Number>k__BackingField
bkdAjmEnKqa
<Name>k__BackingField
CpZAj7gxMGq
<AccountID>k__BackingField
PAsAjW1uqXt
<Description>k__BackingField
AWRAjnSP6Gy
<Note>k__BackingField
vYwAjzY753U
<Status>k__BackingField
t3aAaGwbcJw
<BranchID>k__BackingField
zW3AaA2uGmn
<CreatedBy>k__BackingField
FBFAaU31Bbm
<CreatedTime>k__BackingField
v7gAaplTVif
<ImageName>k__BackingField
L6oAa9NCfha
<Account>k__BackingField
baKAaiiO9Ll
<Branch>k__BackingField
xA1AaIQ0Na1
<UserInfo>k__BackingField
WwAAaH36Dhf
<TransportOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransportOrder
AppTech.MSMS.Domain.Models.TransportOrder
TransportOrder
TransportOrder
NtmAau9NI9f
<ID>k__BackingField
apMAaNOivaA
<RowVersion>k__BackingField
We7AaDmGBZO
<ServiceID>k__BackingField
bKfAat3L6Uf
<AccountID>k__BackingField
oDWAad2mFvN
<SourceRegionID>k__BackingField
evAAaSBHfuS
<TargetRegionID>k__BackingField
Si5AakPEdTc
<Date>k__BackingField
x4LAaPR8GLs
<Time>k__BackingField
f2EAajduGYm
<FullName>k__BackingField
eWIAaa33VbB
<TicketNumber>k__BackingField
IetAaY1lHih
<IsChild>k__BackingField
p3yAawdJSZ5
<ProviderID>k__BackingField
nYnAa0aFNWm
<ImageName>k__BackingField
uuNAaqvxumQ
<ContactNumber>k__BackingField
EAmAaMeEkkS
<RefNumber>k__BackingField
zArAaXY6aOQ
<Amount>k__BackingField
iUXAaoS61cR
<CurrencyID>k__BackingField
UH9AaLyF0Gm
<Note>k__BackingField
HI6AaZQCv5c
<Channel>k__BackingField
JOgAaK4aXID
<ParentID>k__BackingField
SkTAayeGFZy
<TransType>k__BackingField
FUCAavUMF1w
<CreatedBy>k__BackingField
NHVAa2FpeSg
<BranchID>k__BackingField
zdXAa8nLxKb
<CreatedTime>k__BackingField
keaAa3clph3
<TransporterID>k__BackingField
kulAabjYg0w
<Account>k__BackingField
XXqAaEtM6og
<Branch>k__BackingField
oMAAa5XhXL9
<Currency>k__BackingField
LHCAaBkPlZU
<OrderInfo>k__BackingField
LiKAalEustK
<ServiceInfo>k__BackingField
O9QAaTxjAdR
<TopupProvider>k__BackingField
dhEAaCl91t9
<Transporter>k__BackingField
hbJAag6ja7g
<WERegion>k__BackingField
H6jAac2Ev4x
<WERegion1>k__BackingField
rJ7AaJbjahY
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserClaim
AppTech.MSMS.Domain.Models.UserClaim
UserClaim
UserClaim
oLsAa1vCB6y
<ID>k__BackingField
BiPAarXhF5J
<ClaimType>k__BackingField
F22AaQ7Y89b
<ClaimValue>k__BackingField
RBVAaFUFmA9
<UserID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserDevice
AppTech.MSMS.Domain.Models.UserDevice
UserDevice
UserDevice
SNeAaVoCnXB
<ID>k__BackingField
jbtAaRkJlNf
<RowVersion>k__BackingField
v6sAafPLhqr
<UserID>k__BackingField
c9lAae3R74e
<Identifier>k__BackingField
SK2AaxfZEIs
<Permitted>k__BackingField
ayvAasqFH9W
<IsExpired>k__BackingField
NUZAaO8LdeJ
<ExpireDate>k__BackingField
VUtAahLuGUB
<ExtraInfo>k__BackingField
WSeAa6Z17Od
<Keyname>k__BackingField
eaTAa4cbRBm
<Note>k__BackingField
W7bAamum6BG
<BranchID>k__BackingField
u36Aa7Mnv0F
<CreatedBy>k__BackingField
WomAaWchdkk
<CreatedTime>k__BackingField
bxMAanSRgZk
<Branch>k__BackingField
iG9AazRdskU
<UserInfo>k__BackingField
jjAAYGiwOyi
<UserInfo1>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserInfo
AppTech.MSMS.Domain.Models.UserInfo
UserInfo
UserInfo
neRAYAIAwan
<ID>k__BackingField
hAgAYUng7wc
<RowVersion>k__BackingField
lbvAYp3pwQE
<Number>k__BackingField
gDAAY9j0vrW
<UserName>k__BackingField
sBqAYi40fT2
<Note>k__BackingField
SsIAYIXNmLE
<Password>k__BackingField
THmAYHFUlg3
<Type>k__BackingField
d8jAYum36vX
<CreatedBy>k__BackingField
eadAYNA8qEY
<BranchID>k__BackingField
SmAAYDcWEOg
<CreatedTime>k__BackingField
NpKAYtWJcUR
<LastLoginDate>k__BackingField
venAYdgLXpD
<Status>k__BackingField
ONcAYSuLW8j
<AccessFailedCount>k__BackingField
qiKAYkvKl9i
<ConcurrencyStamp>k__BackingField
MdCAYPAmiQi
<SecurityStamp>k__BackingField
UQcAYjyKtui
<TwoFactorEnabled>k__BackingField
I73AYaoyv8r
<ClaimGroupID>k__BackingField
IcgAYYOQQv4
<Accounts>k__BackingField
qy6AYwna2wn
<AccountApis>k__BackingField
wDmAY09dRCs
<AccountDocuments>k__BackingField
C0gAYqXlZhl
<AccountRegions>k__BackingField
vvKAYMtwrJP
<AccountSlatings>k__BackingField
XUQAYXDZ8tY
<AccountUsers>k__BackingField
kmhAYoLo72U
<AccountUsers1>k__BackingField
yjiAYLslL5V
<AdminNotifications>k__BackingField
XIMAYZ0eqTD
<Agents>k__BackingField
OilAYKBCcPt
<AgentPoints>k__BackingField
px4AYyfHPrK
<AgentPointUsers>k__BackingField
NKnAYv59FNh
<AgentPointUsers1>k__BackingField
iuaAY2gIi1I
<Bagats>k__BackingField
thVAY8qcEbJ
<BagatPayments>k__BackingField
KjcAY3o49rR
<Banks>k__BackingField
TN3AYbBTsw4
<BankDeposits>k__BackingField
zSmAYEqGydD
<Branch>k__BackingField
ItVAY5TUqUl
<BranchTargets>k__BackingField
aeAAYBoT9Qe
<Brochures>k__BackingField
CphAYlp68cy
<BuyCurrencies>k__BackingField
z0rAYTGJvB8
<Cards>k__BackingField
D9iAYCT2pVe
<CardFactions>k__BackingField
nIoAYgXpQWE
<CardOrders>k__BackingField
BH8AYcg2R3D
<CardPayments>k__BackingField
wm5AYJW7D8o
<CardTypes>k__BackingField
fFXAY1HfnNt
<CashDeposits>k__BackingField
vK1AYrC1IRb
<CashIns>k__BackingField
tywAYQfoLNC
<CashOuts>k__BackingField
xRpAYFolZwi
<CashTransfers>k__BackingField
PD5AYV5OAFp
<CashWithdraws>k__BackingField
IHRAYR4s1JY
<Cheques>k__BackingField
oHMAYfrrN5f
<ClaimGroups>k__BackingField
eQKAYeMg7rc
<ClaimGroup>k__BackingField
LTHAYxCYt0y
<Clients>k__BackingField
N2aAYsFlmgZ
<CommissionReceipts>k__BackingField
RTVAYO006es
<ConsumeInvoices>k__BackingField
wKvAYh2aFlP
<Countries>k__BackingField
H4hAY6dkWPp
<CoverageOrders>k__BackingField
uGvAY4jiRV0
<Currencies>k__BackingField
JxJAYmfETlA
<Currencies1>k__BackingField
EsJAY7j3mSo
<CurrencyExchanges>k__BackingField
Sa6AYWWEex3
<CurrencyRates>k__BackingField
WKEAYnQAxFF
<CurrencyRateAccounts>k__BackingField
ONGAYzXZeDJ
<DbBackups>k__BackingField
wqiAwGEVRlJ
<DepositOrders>k__BackingField
LsJAwAFb9Me
<Devices>k__BackingField
xNNAwUZgUr4
<Distributors>k__BackingField
qLnAwpdMenF
<Exchangers>k__BackingField
BcHAw9vtTst
<ExchangerTargets>k__BackingField
Dg4AwiTGQHS
<Factions>k__BackingField
K9hAwINHkpf
<Feedbacks>k__BackingField
WLSAwHMTboX
<Funds>k__BackingField
NGtAwuyLCZb
<Funds1>k__BackingField
hubAwNjsSx4
<FundUsers>k__BackingField
AoNAwDpAjGn
<FundUsers1>k__BackingField
LwUAwt0dwty
<GeneralInfoes>k__BackingField
GSfAwdTuwSE
<GroupItems>k__BackingField
CfAAwS6IQRw
<Gsms>k__BackingField
GFlAwkODS2b
<Instructions>k__BackingField
BGZAwPYFYwF
<Items>k__BackingField
rhDAwjhgaxD
<ItemCosts>k__BackingField
NEmAwaG0wbv
<Journals>k__BackingField
w6rAwYL5ZAL
<LiveTopups>k__BackingField
EUrAww2aYyM
<LoanOrders>k__BackingField
PiVAw0pTPns
<Merchants>k__BackingField
AqUAwqc7ayG
<MerchantCategories>k__BackingField
LWiAwMY7YIN
<MerchantPayments>k__BackingField
XssAwXK6nfZ
<MoneyMasters>k__BackingField
kT2Awo3PxUf
<OfferOrders>k__BackingField
sRVAwLdUebf
<OpeningBalances>k__BackingField
dLsAwZH2RfR
<OrderInfoes>k__BackingField
K3QAwK6ow60
<OrderInfoes1>k__BackingField
C5BAwyv5Tpo
<OrderInfoes2>k__BackingField
UGCAwvlNgkV
<OrderSatelliteQuotas>k__BackingField
zIxAw2dD3d7
<Parties>k__BackingField
O6LAw801XlU
<PartyGroups>k__BackingField
cVrAw3f8F82
<Payments>k__BackingField
hL2AwboJk64
<Payments1>k__BackingField
AO7AwEKRysI
<PaymentCommissions>k__BackingField
DBPAw5YyW0K
<People>k__BackingField
KlkAwBUT5p1
<Products>k__BackingField
kORAwlH4JQh
<ProductCategories>k__BackingField
TR8AwTljLpR
<ProductImages>k__BackingField
mqbAwC5pT0u
<Provinces>k__BackingField
O4QAwgNtvfQ
<PurchaseInvoices>k__BackingField
gSDAwc4ipPt
<PurchaseInvoiceLines>k__BackingField
uMoAwJAESm5
<Quotations>k__BackingField
uJBAw1ZN9Ah
<ReceiptCreditors>k__BackingField
v0QAwra3xLh
<ReceiptDebitors>k__BackingField
xt6AwQvcFSF
<Regions>k__BackingField
OUiAwFCTwcR
<RemittanceCommissions>k__BackingField
VCJAwVf0VYu
<RemittanceIns>k__BackingField
GmRAwR22mDd
<RemittanceOuts>k__BackingField
FmsAwflTV8F
<RemittancePoints>k__BackingField
dJLAweNKDDi
<RemittanceRegions>k__BackingField
zHbAwxMFGXk
<RiyalMobiles>k__BackingField
q7bAwsfukLH
<RSSes>k__BackingField
On4AwOBhJkF
<SaleCurrencies>k__BackingField
WF0AwhxQLZf
<SaleInvoices>k__BackingField
VlNAw6Ri9GU
<SaleInvoiceLines>k__BackingField
OdLAw4KYoOt
<SatelliteFactions>k__BackingField
cWtAwmnUHuf
<SatellitePayments>k__BackingField
LKMAw7HpbG2
<SatelliteProviders>k__BackingField
h6sAwWHuEVi
<ServiceClaims>k__BackingField
guHAwnoyDHM
<ServiceInfoes>k__BackingField
VRrAwzbuXn7
<Sims>k__BackingField
e5tA0G3EMyK
<SimCardOrders>k__BackingField
qwEA0Adh4Cq
<SimInvoices>k__BackingField
z3DA0U71jDM
<SimpleEntries>k__BackingField
IGhA0p76Wdo
<SimPurchases>k__BackingField
rtYA09bKyH4
<SMSDispatches>k__BackingField
QUUA0icU8ED
<SMSLogs>k__BackingField
MenA0IddRkO
<SMSMessages>k__BackingField
AkeA0HsVL22
<SpecialSims>k__BackingField
l6lA0uE7Lo6
<Subscribers>k__BackingField
HrIA0NlJeTw
<Suppliers>k__BackingField
pwFA0DerhXH
<Topups>k__BackingField
kQqA0t6FOKw
<TopupCommissions>k__BackingField
AkyA0dVIQfT
<TopupOrders>k__BackingField
oB3A0SUddx6
<TopupProviders>k__BackingField
yBsA0kkffO4
<TrailToupCommissions>k__BackingField
NEFA0PH8irP
<TrailToupOrders>k__BackingField
lZ6A0jFSfUh
<TransferIns>k__BackingField
J41A0a260nJ
<TransferOrders>k__BackingField
YpFA0YheASw
<TransferOuts>k__BackingField
jprA0w9Fn6N
<Transporters>k__BackingField
zp9A00EHhiG
<TransportOrders>k__BackingField
ok5A0qSuC7x
<UserDevices>k__BackingField
KjRA0MBZQdk
<UserDevices1>k__BackingField
luMA0XJAk1h
<UserRoles>k__BackingField
URvA0oeePl0
<WERegions>k__BackingField
afsA0LScNFc
<WifiCards>k__BackingField
tqTA0ZvJd9O
<WifiFactions>k__BackingField
ICxA0K6LDZC
<WifiPayments>k__BackingField
oH4A0yhNU86
<WifiProviders>k__BackingField
E1sA0vwxgSU
<WithdrawOrders>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserPage
AppTech.MSMS.Domain.Models.UserPage
UserPage
UserPage
A4SA024UCNE
<ID>k__BackingField
aF1A08ZKjM4
<UserID>k__BackingField
zYPA03r632l
<PageID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserPermission
AppTech.MSMS.Domain.Models.UserPermission
UserPermission
UserPermission
fJNA0bVdWnr
<ID>k__BackingField
T8KA0E0b9W8
<UserID>k__BackingField
UeaA053nC8q
<PageID>k__BackingField
N8aA0BcceDF
<ActionID>k__BackingField
msZA0liPO5b
<ClaimGroupID>k__BackingField
aoVA0TraNWi
<ClaimGroup>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserRole
AppTech.MSMS.Domain.Models.UserRole
UserRole
UserRole
ovCA0Ct0t9s
<ID>k__BackingField
i1xA0g30Qyu
<UserID>k__BackingField
LmLA0cCMBFF
<RoleID>k__BackingField
hS6A0JIUpDP
<RoleInfo>k__BackingField
NvkA01w6O6g
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserToken
AppTech.MSMS.Domain.Models.UserToken
UserToken
UserToken
EVkA0rsXtlB
<UserID>k__BackingField
XGCA0Q4RIEv
<Provider>k__BackingField
t19A0FMTLhW
<Name>k__BackingField
BhcA0VHCNIU
<Value>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Voucher
AppTech.MSMS.Domain.Models.Voucher
Voucher
Voucher
MPhA0Rkj4Is
<ID>k__BackingField
QuQA0fVdFc0
<Name>k__BackingField
NlFA0eJ0C5l
<Entity>k__BackingField
WykA0xOnS9Z
<Note>k__BackingField
DpDA0sGTVRQ
<CreatedBy>k__BackingField
EkiA0OUFlwD
<BranchID>k__BackingField
V94A0hgAtqN
<CreatedTime>k__BackingField
YOXA06TDoRd
<Active>k__BackingField
hG2A04eY1pg
<CrOrDr>k__BackingField
FM3A0mLfY2V
<Module>k__BackingField
ibsA075s70V
<Branch>k__BackingField
P6KA0WPHwMW
<Cheques>k__BackingField
BojA0nCKNaF
<Journals>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WERegion
AppTech.MSMS.Domain.Models.WERegion
WERegion
WERegion
NHkA0z6X2hC
<ID>k__BackingField
CclAqGcD4Gv
<RowVersion>k__BackingField
MP1AqAUnX6T
<Name>k__BackingField
leWAqUm6MUi
<ServiceID>k__BackingField
priAqpiHpSZ
<CreatedBy>k__BackingField
sxeAq9eo7mT
<BranchID>k__BackingField
it9Aqi9udIE
<CreatedTime>k__BackingField
k9AAqIMH8YT
<Code>k__BackingField
MdmAqHMJetx
<ServiceKeyName>k__BackingField
nFEAqulYGxo
<Branch>k__BackingField
H8WAqNxhjGI
<RiyalMobiles>k__BackingField
b2MAqDHIxh1
<ServiceInfo>k__BackingField
rp7Aqtba9FQ
<TransportOrders>k__BackingField
JEsAqdbyl6o
<TransportOrders1>k__BackingField
ajxAqSafSNt
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiCard
AppTech.MSMS.Domain.Models.WifiCard
WifiCard
WifiCard
EtKAqkfIMsr
<ID>k__BackingField
o8IAqP0oQr5
<RowVersion>k__BackingField
nbcAqj7Vhvv
<Password>k__BackingField
DxEAqaB5yIq
<Username>k__BackingField
gVcAqYHu0Hl
<FactionID>k__BackingField
voFAqwvqCnc
<Active>k__BackingField
HiOAq0qSIYA
<SerialNo>k__BackingField
LRYAqqu1U15
<CostPrice>k__BackingField
c5XAqM4Bd30
<SalePrice>k__BackingField
GOGAqX7DHru
<SalePrice2>k__BackingField
JVBAqo5Rtgu
<Status>k__BackingField
uXnAqLdtEGn
<Note>k__BackingField
HD4AqZCujNF
<Number>k__BackingField
CKHAqKoBNde
<Peroid>k__BackingField
mAhAqyeQ4L2
<Quantity>k__BackingField
iaDAqvDqljB
<AccountID>k__BackingField
Ga1Aq20CwQs
<IsExternal>k__BackingField
yUwAq88DE20
<Description>k__BackingField
DLXAq3yafCm
<OwnerType>k__BackingField
eTnAqb9PIZF
<ProviderID>k__BackingField
gyrAqE2XeJZ
<BranchID>k__BackingField
ci2Aq5sGKvr
<CreatedBy>k__BackingField
UilAqB7d7Nd
<CreatedTime>k__BackingField
usXAqlvgJSU
<Branch>k__BackingField
KgUAqTZBkok
<UserInfo>k__BackingField
P01AqCCKj86
<ViaExcel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiFaction
AppTech.MSMS.Domain.Models.WifiFaction
WifiFaction
WifiFaction
M5SAqgkDdH8
<ID>k__BackingField
pSjAqckB09J
<RowVersion>k__BackingField
ip3AqJol4JP
<Number>k__BackingField
F6HAq15RSPf
<OrderNO>k__BackingField
cmTAqrua0p7
<Name>k__BackingField
TvmAqQEGngU
<Description>k__BackingField
GsxAqForDOS
<Price>k__BackingField
j6PAqV6VW80
<Note>k__BackingField
qTGAqRc9rIE
<WifiProviderID>k__BackingField
k74AqfNuqmU
<BranchID>k__BackingField
wgeAqeDdvkF
<CreatedBy>k__BackingField
X4TAqxCkT0X
<CreatedTime>k__BackingField
Lt6AqsOFede
<Branch>k__BackingField
TFUAqOQRQxQ
<UserInfo>k__BackingField
HAiAqhpZ5CY
<WifiProvider>k__BackingField
naEAq6Xq6YF
<TotalAll>k__BackingField
kuxAq4gCOGb
<TotalSold>k__BackingField
xcuAqmXuoFF
<TotalRemian>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiPayment
AppTech.MSMS.Domain.Models.WifiPayment
WifiPayment
WifiPayment
wmWAq7Zcnb5
<ID>k__BackingField
RsbAqWSAaDe
<RowVersion>k__BackingField
WA5AqndUNkc
<Number>k__BackingField
HSWAqz42TJC
<ProviderID>k__BackingField
dDCAMGJJ5Cd
<FactionID>k__BackingField
iNPAMA9GxqD
<WifiCardID>k__BackingField
BbuAMUYJD18
<WifiCardIDs>k__BackingField
tuBAMpRlYXu
<ProfitAmount>k__BackingField
fOPAM9C5Rjj
<ProviderAmount>k__BackingField
Ra4AMiJol8O
<Amount>k__BackingField
VDyAMIs7FhG
<Quantity>k__BackingField
BsoAMHqcVjb
<CurrencyID>k__BackingField
rHMAMuatWsJ
<CreditorAccountID>k__BackingField
l60AMN3J5wu
<DebitorAccountID>k__BackingField
Q7gAMDr8V7J
<Date>k__BackingField
VxOAMt3XNJP
<Note>k__BackingField
avGAMdZo1Qp
<EntryID>k__BackingField
oUTAMSAKD2O
<RefNumber>k__BackingField
PKhAMk5ADjj
<IsDebited>k__BackingField
lRHAMPTHDDE
<Year>k__BackingField
kKtAMjnlbLc
<CreatedBy>k__BackingField
z37AMaaOFmr
<CreatedTime>k__BackingField
te6AMYGRENj
<BranchID>k__BackingField
sOmAMwivwQ9
<Status>k__BackingField
DdRAM0ggVT6
<ServiceID>k__BackingField
UORAMqxUOi9
<Account>k__BackingField
pfJAMMwX1g9
<Account1>k__BackingField
wcmAMX06O0S
<Branch>k__BackingField
WdXAMoiSXSI
<Currency>k__BackingField
drDAMLGsYWn
<ServiceInfo>k__BackingField
PURAMZ90Xdf
<UserInfo>k__BackingField
jYgAMKkIV8u
<WifiNumber>k__BackingField
Q0aAMyTaON4
<PhoneNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiProvider
AppTech.MSMS.Domain.Models.WifiProvider
WifiProvider
WifiProvider
MWpAMvciIK8
<ID>k__BackingField
f8lAM2dR6ju
<RowVersion>k__BackingField
OopAM8aFEXS
<Number>k__BackingField
kFZAM30jj92
<Name>k__BackingField
cGVAMbdOVwW
<Image>k__BackingField
DGgAMEefnrt
<Address>k__BackingField
mfFAM5IobcE
<URL>k__BackingField
IGDAMBTFAq3
<Phone>k__BackingField
BZjAMlEA0hW
<Note>k__BackingField
ihQAMToVXB6
<PrafitStatus>k__BackingField
tOGAMCYe4GT
<PrafitAmount>k__BackingField
KNsAMgA6ffI
<AccountID>k__BackingField
ADXAMcNl398
<RegionID>k__BackingField
SViAMJk6iMP
<BranchID>k__BackingField
PIZAM1MwhAA
<CreatedBy>k__BackingField
NKYAMrRcWPB
<CreatedTime>k__BackingField
hoYAMQhKGFe
<Account>k__BackingField
AasAMFGyUJi
<Branch>k__BackingField
hO9AMVJ9klI
<Region>k__BackingField
tkJAMRIDiIp
<UserInfo>k__BackingField
wKjAMfwCY5R
<WifiFactions>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WithdrawOrder
AppTech.MSMS.Domain.Models.WithdrawOrder
WithdrawOrder
WithdrawOrder
RdiAMealkaW
<ID>k__BackingField
RWwAMxQZBlt
<RowVersion>k__BackingField
NcuAMs2XYVj
<ClientID>k__BackingField
Q7kAMOnTKLs
<AgentID>k__BackingField
RP9AMhcmAkX
<Amount>k__BackingField
FPiAM6w0NN8
<CurrencyID>k__BackingField
k6sAM4nn9Vh
<SecretCode>k__BackingField
n00AMm6a55h
<State>k__BackingField
wewAM7jRwZs
<Debited>k__BackingField
C5wAMWRAKSy
<Cancelled>k__BackingField
lCoAMnVB7Z4
<Date>k__BackingField
rWUAMzpvXmP
<Note>k__BackingField
fo0AXGM9tpP
<Channel>k__BackingField
B3OAXADtWjH
<ParentID>k__BackingField
WXsAXU39BnW
<ServiceID>k__BackingField
jFJAXpdIMus
<CreatedBy>k__BackingField
WLAAX9iwalt
<BranchID>k__BackingField
AJCAXin5jqK
<CreatedTime>k__BackingField
kd4AXIHh8qT
<Status>k__BackingField
A6qAXHUPsci
<Branch>k__BackingField
XhrAXurdJZT
<Client>k__BackingField
KVnAXNipJ7e
<Currency>k__BackingField
qdrAXDd07Pl
<ServiceInfo>k__BackingField
K72AXtRVimC
<UserInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.Dashboard
AppTech.MSMS.Domain.Models.Dashboard
Dashboard
Dashboard
LF7AXdv4uGh
Init
dRmAXSRKNSQ
_accountId
nGdAXkK9rJp
<PieChartData>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PieChart
AppTech.MSMS.Domain.Models.PieChart
PieChart
PieChart
IlsAXPvcLr9
<Vouchers>k__BackingField
UffAXjKVATw
<Values>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.IMsAuditable
AppTech.MSMS.Domain.Models.IMsAuditable
IMsAuditable
IMsAuditable
<<type>>
AppTech.MSMS.Domain.Models.SpecialSimMetadata
AppTech.MSMS.Domain.Models.SpecialSimMetadata
SpecialSimMetadata
SpecialSimMetadata
xiqAXadIwkX
<Number>k__BackingField
XIyAXY1SseS
<Type>k__BackingField
EV2AXwNWi30
<Price>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ItemMetadata
AppTech.MSMS.Domain.Models.ItemMetadata
ItemMetadata
ItemMetadata
cDuAX0MJ25x
<ServiceID>k__BackingField
mX6AXqVriaF
<CostPrice>k__BackingField
t53AXMVM9E5
<SalePrice>k__BackingField
CWOAXXTrvJa
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExchangerTargetMetadata
AppTech.MSMS.Domain.Models.ExchangerTargetMetadata
ExchangerTargetMetadata
ExchangerTargetMetadata
ybvAXoGNQid
<ExchangerID>k__BackingField
AJsAXLyDEUg
<RemittancePointID>k__BackingField
jajAXZ2v0uu
<ProviderID>k__BackingField
BCNAXKqd4E3
<SyncID>k__BackingField
ULjAXyZsSuM
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountApiMetadata
AppTech.MSMS.Domain.Models.AccountApiMetadata
AccountApiMetadata
AccountApiMetadata
pB7AXveYtMn
<Number>k__BackingField
LwXAX2j5QyQ
<AccountId>k__BackingField
xemAX8wu6JZ
<IsAllowed>k__BackingField
urDAX3cBCK6
<IpAddress>k__BackingField
zSuAXbFZxDp
<Binded>k__BackingField
IexAXEjxwyG
<Binding>k__BackingField
hR2AX5vFtxG
<PublicKey>k__BackingField
k7cAXBnETJh
<Permitted>k__BackingField
TwRAXlIou4Z
<Primary>k__BackingField
zjKAXT7Evxx
<Synced>k__BackingField
eAtAXC7y2Dj
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BrochureMetadata
AppTech.MSMS.Domain.Models.BrochureMetadata
BrochureMetadata
BrochureMetadata
HtoAXgaS24n
<ImageUrl>k__BackingField
l0TAXcBkKVD
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PersonalInfoMetadata
AppTech.MSMS.Domain.Models.PersonalInfoMetadata
PersonalInfoMetadata
PersonalInfoMetadata
<<type>>
AppTech.MSMS.Domain.Models.RemittanceRegionMetadata
AppTech.MSMS.Domain.Models.RemittanceRegionMetadata
RemittanceRegionMetadata
RemittanceRegionMetadata
JfcAXJhe0CG
<Name>k__BackingField
TCHAX1PWu0O
<ProvinceID>k__BackingField
ETwAXrmjjtn
<CreatedBy>k__BackingField
pgqAXQBNy9a
<BranchID>k__BackingField
McNAXFoS04b
<CreatedTime>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittancePointMetadata
AppTech.MSMS.Domain.Models.RemittancePointMetadata
RemittancePointMetadata
RemittancePointMetadata
HUCAXVKwSDl
<Number>k__BackingField
NsLAXRsoguk
<Name>k__BackingField
U7RAXfCQ7mt
<RegionID>k__BackingField
kGLAXeCPg54
<Description>k__BackingField
jIJAXxEyuxh
<Type>k__BackingField
yoBAXsokyoA
<Phone>k__BackingField
rKbAXOiigID
<Active>k__BackingField
iJlAXh5GBo5
<Address>k__BackingField
qccAX6xmV4y
<Fax>k__BackingField
waWAX4n9fOX
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceOutMetadata
AppTech.MSMS.Domain.Models.RemittanceOutMetadata
RemittanceOutMetadata
RemittanceOutMetadata
oHpAXmFRIf9
<Number>k__BackingField
GmLAX7R2H0v
<RemittanceNumber>k__BackingField
SM8AXWFAA79
<RemittanceID>k__BackingField
HJ7AXn3CXoo
<Amount>k__BackingField
VfJAXzvSV1C
<CurrencyID>k__BackingField
zD0AoGCLiDa
<CreditorAccountID>k__BackingField
ydwAoAmqjCs
<RemittancePointID>k__BackingField
T5MAoUe0HVt
<AgentID>k__BackingField
dViAoplV9Q2
<RegionID>k__BackingField
Y9pAo9QkXUy
<Date>k__BackingField
DHjAoiN8Ls2
<Note>k__BackingField
HwhAoIkS1kV
<Prints>k__BackingField
uBHAoHaehDp
<CommissionAmount>k__BackingField
Q6cAouE4Fon
<CommissionCurrencyID>k__BackingField
FakAoNIL2Pb
<BeneficiaryCardID>k__BackingField
FJnAoDjHu23
<RefNumber>k__BackingField
lp1AotByC9P
<CenterCommission>k__BackingField
lQZAodWGcui
<PointCommission>k__BackingField
h4eAoSClZ8k
<CommissionType>k__BackingField
AlXAokKJ99p
<BenficiaryID>k__BackingField
xNfAoPXLycW
<BenficiaryCard>k__BackingField
cPAAojFPkhe
<Channel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceInMetadata
AppTech.MSMS.Domain.Models.RemittanceInMetadata
RemittanceInMetadata
RemittanceInMetadata
PG5Aoa8IHVb
<Number>k__BackingField
JcPAoYqVKpI
<SyncNumber>k__BackingField
SVdAowylICa
<RemittanceNumber>k__BackingField
SXOAo0DiSlV
<Amount>k__BackingField
wlnAoqi37sN
<CurrencyID>k__BackingField
eTjAoM7k6cO
<DebitorAccountID>k__BackingField
tRfAoXKbbTb
<BeneficiaryName>k__BackingField
SyVAooZPYhf
<BeneficiaryPhone>k__BackingField
hP5AoLvx49i
<SenderName>k__BackingField
NZAAoZIPtXI
<SenderPhone>k__BackingField
dC6AoKws8lS
<TargetPointID>k__BackingField
f2mAoyl02AF
<AgentID>k__BackingField
MLEAovcrSbD
<SourceRegionID>k__BackingField
UfJAo2cQFTN
<TargetRegionID>k__BackingField
mxBAo8kRZNE
<Date>k__BackingField
g0MAo3ZWfVv
<Note>k__BackingField
vN0Aob00p29
<Purpose>k__BackingField
VIkAoEAuquL
<Prints>k__BackingField
IPnAo5P23E6
<Status>k__BackingField
YJPAoBZQMUP
<CommissionAmount>k__BackingField
oMwAolLOKii
<CommissionCurrencyID>k__BackingField
bXrAoTqET9T
<RefNumber>k__BackingField
wrWAoCuk6nX
<CenterCommission>k__BackingField
Nb2AogmHcmy
<PointCommission>k__BackingField
jU8AocsuatZ
<CommissionType>k__BackingField
tbYAoJy2FoD
<QueriedBy>k__BackingField
E8CAo1XTMEV
<IsSync>k__BackingField
abBAor6aEWu
<SyncRemittanceID>k__BackingField
goEAoQUg85Y
<Imported>k__BackingField
FrpAoF3QSCs
<NetworkTarget>k__BackingField
oVsAoV8pFQs
<ExchangeAmount>k__BackingField
F17AoRcY6O3
<ExchangeCurrencyID>k__BackingField
XDsAofm88o8
<Channel>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferInMetadata
AppTech.MSMS.Domain.Models.TransferInMetadata
TransferInMetadata
TransferInMetadata
HXvAoe1jYas
<Number>k__BackingField
mPWAoxw0D2C
<TransferNumber>k__BackingField
fnTAosb8XC6
<Amount>k__BackingField
Yn9AoOKi52p
<CurrencyID>k__BackingField
gm8AohenLEn
<AccountID>k__BackingField
yIOAo6vB43Z
<BeneficiaryName>k__BackingField
TNXAo4T2Frf
<BeneficiaryPhone>k__BackingField
BBxAombIuo6
<SenderName>k__BackingField
ycCAo7qF5eb
<SenderPhone>k__BackingField
eeWAoW6Gg0h
<ExchangerID>k__BackingField
Kt5AonALx6p
<Date>k__BackingField
QkEAozvnt2i
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOutMetadata
AppTech.MSMS.Domain.Models.TransferOutMetadata
TransferOutMetadata
TransferOutMetadata
LsWALGeY4Eq
<Number>k__BackingField
xLwALAXt721
<TransferNumber>k__BackingField
xsIALUxAj57
<Amount>k__BackingField
pMnALpvHWuh
<CurrencyID>k__BackingField
yBmAL942USH
<AccountID>k__BackingField
M67ALiilFdk
<BeneficiaryName>k__BackingField
WlxALIKUekd
<BeneficiaryPhone>k__BackingField
lnjALHsWJuc
<SenderName>k__BackingField
YQHALu8bRS1
<SenderPhone>k__BackingField
kOAALNfKQlc
<ExchangerID>k__BackingField
YwLALD9pIFS
<Date>k__BackingField
KvJALt1ltbt
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentPointMetadata
AppTech.MSMS.Domain.Models.AgentPointMetadata
AgentPointMetadata
AgentPointMetadata
xLFALdTljm2
<AgentID>k__BackingField
eYCALSGFe8k
<RemittancePointID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BranchTargetMetadata
AppTech.MSMS.Domain.Models.BranchTargetMetadata
BranchTargetMetadata
BranchTargetMetadata
uERALkyD4ef
<BranchPintID>k__BackingField
Eo9ALPyEiKb
<RemittancePointID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.InvoiceMetadata
AppTech.MSMS.Domain.Models.InvoiceMetadata
InvoiceMetadata
InvoiceMetadata
k1WALjOAwuV
<Date>k__BackingField
ibZALaEjDyo
<Amount>k__BackingField
xPKALYuWfZk
<CurrencyID>k__BackingField
heJALwdkwKV
<DebitorAccountID>k__BackingField
LueAL0kqbjT
<CreditorAccountID>k__BackingField
PpxALq8s7xx
<Note>k__BackingField
plQALMs7vtt
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ConsumeInvoiceMetadata
AppTech.MSMS.Domain.Models.ConsumeInvoiceMetadata
ConsumeInvoiceMetadata
ConsumeInvoiceMetadata
gZXALXG1k3g
<StartDate>k__BackingField
YpHALol9bPA
<EndDate>k__BackingField
rfNALLQfnxn
<UnitPrice>k__BackingField
rq9ALZksWjE
<Quantity>k__BackingField
G9aALKoiUnw
<Amount>k__BackingField
ip7ALypSvYH
<TotalAmount>k__BackingField
rWmALv5tsSk
<FineAmount>k__BackingField
tasAL2JIwMo
<LateAmount>k__BackingField
fIrAL8QkYaE
<ExtraAmount>k__BackingField
ua6AL34UBjp
<SubscriptionFee>k__BackingField
fFNALbdNpx0
<FineNote>k__BackingField
tiRALENxfVd
<CurrencyID>k__BackingField
BkvAL5xNAn1
<DebitorAccountID>k__BackingField
AQJALBtKR25
<Note>k__BackingField
xl1ALlVekDE
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SubscriberMetadata
AppTech.MSMS.Domain.Models.SubscriberMetadata
SubscriberMetadata
SubscriberMetadata
v5MALTsxylU
<Name>k__BackingField
lnqALCJ3idK
<SubscriberNo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PartyGroupMetadata
AppTech.MSMS.Domain.Models.PartyGroupMetadata
PartyGroupMetadata
PartyGroupMetadata
PfaALg7gYju
<Name>k__BackingField
pQuALcg2X5H
<Description>k__BackingField
AqJALJtM0Bq
<SelectedItems>k__BackingField
IwhAL1evS1w
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClaimGroupMetadata
AppTech.MSMS.Domain.Models.ClaimGroupMetadata
ClaimGroupMetadata
ClaimGroupMetadata
mW3ALrI5c6c
<Name>k__BackingField
n0WALQvqPFa
<Note>k__BackingField
pgTALFsskyI
<SelectedServices>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GroupItemrMetadata
AppTech.MSMS.Domain.Models.GroupItemrMetadata
GroupItemrMetadata
GroupItemrMetadata
a3RALVIMGHY
<Name>k__BackingField
PWmALRWigjp
<SubscriberNo>k__BackingField
APGALfH2bAE
<PhoneNumber>k__BackingField
b2eALeho0rw
<OpeningBalance>k__BackingField
Y9XALxdByHK
<ProviderID>k__BackingField
DawALsuiLUD
<ServiceID>k__BackingField
XI6ALO026wa
<Address>k__BackingField
fXFALhaUr9p
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiCardMetadata
AppTech.MSMS.Domain.Models.WifiCardMetadata
WifiCardMetadata
WifiCardMetadata
IatAL6AF2IX
<Number>k__BackingField
o88AL4xEeJs
<FactionID>k__BackingField
q5ZALmx6HA4
<ProviderID>k__BackingField
pLCAL70RU8G
<SerialNo>k__BackingField
ig4ALWTDVOf
<Username>k__BackingField
hVeALnwWovr
<Password>k__BackingField
cZlALzHYT8A
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BuyCurrencyMetadata
AppTech.MSMS.Domain.Models.BuyCurrencyMetadata
BuyCurrencyMetadata
BuyCurrencyMetadata
cCQAZGU3BoE
<Number>k__BackingField
CQMAZAlCdfV
<Date>k__BackingField
PiRAZUCwINm
<Amount>k__BackingField
btiAZpkE91U
<ExchangeAmount>k__BackingField
b8wAZ97UfQI
<ExchangePrice>k__BackingField
DGxAZiYOeZF
<CurrencyID>k__BackingField
kXFAZIg6XYW
<DebitorAccountID>k__BackingField
gybAZHsX70d
<CreditorAccountID>k__BackingField
JYwAZuCsUQn
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClientSMSMetadata
AppTech.MSMS.Domain.Models.ClientSMSMetadata
ClientSMSMetadata
ClientSMSMetadata
DZoAZNl18SK
<ServiceID>k__BackingField
jjAAZDPChXW
<ClientID>k__BackingField
IkeAZtj2Cfi
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountMetadata
AppTech.MSMS.Domain.Models.AccountMetadata
AccountMetadata
AccountMetadata
rytAZdCbF5w
<Name>k__BackingField
qFVAZSKTs3s
<ParentNumber>k__BackingField
XWJAZk40wTe
<Number>k__BackingField
K3nAZPWrW7C
<Type>k__BackingField
r5mAZjpkuuJ
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DistributorMetadata
AppTech.MSMS.Domain.Models.DistributorMetadata
DistributorMetadata
DistributorMetadata
HGsAZalFJBn
<Name>k__BackingField
RSTAZYZysq9
<PhoneNumber>k__BackingField
kOyAZwRKpVw
<ContactNumber>k__BackingField
Y1eAZ0W1SM4
<Address>k__BackingField
sUuAZqbGYRw
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentMetadata
AppTech.MSMS.Domain.Models.AgentMetadata
AgentMetadata
AgentMetadata
aDeAZMlHyN1
<Name>k__BackingField
NfLAZXNgxQs
<PhoneNumber>k__BackingField
RfkAZo99hOX
<ContactNumber>k__BackingField
GiiAZLWIpZd
<Address>k__BackingField
wTHAZZkcsAx
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ExternalBranchMetadata
AppTech.MSMS.Domain.Models.ExternalBranchMetadata
ExternalBranchMetadata
ExternalBranchMetadata
IbuAZKxnu3J
<Name>k__BackingField
sUHAZywVFLA
<PhoneNumber>k__BackingField
gvVAZvLQCU4
<Fax>k__BackingField
FBnAZ2VVaK0
<Address>k__BackingField
Pm9AZ8gFvVT
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AgentUsersMetadata
AppTech.MSMS.Domain.Models.AgentUsersMetadata
AgentUsersMetadata
AgentUsersMetadata
<<type>>
AppTech.MSMS.Domain.Models.BranchMetadata
AppTech.MSMS.Domain.Models.BranchMetadata
BranchMetadata
BranchMetadata
<<type>>
AppTech.MSMS.Domain.Models.CashDepositMetadata
AppTech.MSMS.Domain.Models.CashDepositMetadata
CashDepositMetadata
CashDepositMetadata
VIRAZ3JXHyQ
<Number>k__BackingField
M1XAZbtbEwG
<Date>k__BackingField
ax2AZEQ9dso
<Amount>k__BackingField
keZAZ5jITjN
<CurrencyID>k__BackingField
a9IAZBSb5ns
<Depositor>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashWithdrawMetadata
AppTech.MSMS.Domain.Models.CashWithdrawMetadata
CashWithdrawMetadata
CashWithdrawMetadata
DIyAZlDtW9H
<Date>k__BackingField
KMiAZTMcSPH
<Amount>k__BackingField
LVQAZCw5r6Q
<CurrencyID>k__BackingField
afVAZgdep22
<Delivery>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ChequeMetadata
AppTech.MSMS.Domain.Models.ChequeMetadata
ChequeMetadata
ChequeMetadata
<<type>>
AppTech.MSMS.Domain.Models.ClientMetadata
AppTech.MSMS.Domain.Models.ClientMetadata
ClientMetadata
ClientMetadata
rtPAZcr36Vu
<Name>k__BackingField
z3LAZJfDple
<Username>k__BackingField
sr1AZ1coPLu
<Password>k__BackingField
mxAAZrRsCWq
<ShopName>k__BackingField
D54AZQtoIhl
<PhoneNumber>k__BackingField
fLhAZFlw2l2
<Address>k__BackingField
e9bAZViXFvT
<BirthDate>k__BackingField
eOSAZRiHms6
<ContactNumber>k__BackingField
rE8AZf3nnkM
<Email>k__BackingField
zkaAZeUPYEH
<CardType>k__BackingField
N5DAZxwNyeC
<CardNumber>k__BackingField
kRtAZsfYui5
<CardIssuePlace>k__BackingField
qVYAZOLIW2p
<CardIssueDate>k__BackingField
OuaAZhd1s50
<ImageName>k__BackingField
C3kAZ69MBNV
<Note>k__BackingField
LxtAZ4EYUWQ
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountNotificationMetadata
AppTech.MSMS.Domain.Models.AccountNotificationMetadata
AccountNotificationMetadata
AccountNotificationMetadata
NmOAZmqQEZV
<AccountID>k__BackingField
GbTAZ7m9r7a
<Message>k__BackingField
m87AZWGUcaN
<Title>k__BackingField
l1rAZnHR2S5
<RealTime>k__BackingField
ysQAZzAkaXK
<AccountState>k__BackingField
HFhAKGGkD3v
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClientPermissionMetadata
AppTech.MSMS.Domain.Models.ClientPermissionMetadata
ClientPermissionMetadata
ClientPermissionMetadata
<<type>>
AppTech.MSMS.Domain.Models.AccountSlatingMetadata
AppTech.MSMS.Domain.Models.AccountSlatingMetadata
AccountSlatingMetadata
AccountSlatingMetadata
hByAKAmXhPC
<AccountID>k__BackingField
v3rAKUuyrBq
<Amount>k__BackingField
e4iAKpkdlyq
<CurrencyID>k__BackingField
b1XAK961yM6
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ClientUsersMetadata
AppTech.MSMS.Domain.Models.ClientUsersMetadata
ClientUsersMetadata
ClientUsersMetadata
<<type>>
AppTech.MSMS.Domain.Models.CurrencyMetadata
AppTech.MSMS.Domain.Models.CurrencyMetadata
CurrencyMetadata
CurrencyMetadata
TlnAKiI0GMQ
<Name>k__BackingField
RjUAKIoKvga
<Symbol>k__BackingField
JhhAKHxBCfP
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyExchangeMetadata
AppTech.MSMS.Domain.Models.CurrencyExchangeMetadata
CurrencyExchangeMetadata
CurrencyExchangeMetadata
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRateMetadata
AppTech.MSMS.Domain.Models.CurrencyRateMetadata
CurrencyRateMetadata
CurrencyRateMetadata
ip2AKuwk12v
<SourceCurrencyID>k__BackingField
GoPAKNPlIR9
<ExchangeRate>k__BackingField
RByAKDMGD1w
<BuyPrice>k__BackingField
VowAKthwmOE
<SellPrice>k__BackingField
prIAKdNLaNd
<ExBuyPrice>k__BackingField
JK2AKSrk5lD
<ExSalePrice>k__BackingField
d8dAKk43mk7
<UpDown>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CurrencyRateAccountMetadata
AppTech.MSMS.Domain.Models.CurrencyRateAccountMetadata
CurrencyRateAccountMetadata
CurrencyRateAccountMetadata
LmhAKPtvWtq
<SourceCurrencyID>k__BackingField
mA9AKjeemmw
<TargetCurrencyID>k__BackingField
UQSAKaeifHB
<AccountState>k__BackingField
DJJAKYpjhrm
<AccountID>k__BackingField
chnAKw0wN11
<AccountGroupID>k__BackingField
T71AK0iDFH1
<ExchangeRate>k__BackingField
TuZAKqdaHty
<BuyPrice>k__BackingField
R80AKMqMWTf
<SellPrice>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DepositOrderMetadata
AppTech.MSMS.Domain.Models.DepositOrderMetadata
DepositOrderMetadata
DepositOrderMetadata
bASAKXm88HZ
<Amount>k__BackingField
xTMAKoDUAqm
<CurrencyID>k__BackingField
PvTAKLhKG3G
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DocumentMetadata
AppTech.MSMS.Domain.Models.DocumentMetadata
DocumentMetadata
DocumentMetadata
<<type>>
AppTech.MSMS.Domain.Models.EntryMetadata
AppTech.MSMS.Domain.Models.EntryMetadata
EntryMetadata
EntryMetadata
<<type>>
AppTech.MSMS.Domain.Models.EntryDetailMetadata
AppTech.MSMS.Domain.Models.EntryDetailMetadata
EntryDetailMetadata
EntryDetailMetadata
<<type>>
AppTech.MSMS.Domain.Models.ExchangerMetadata
AppTech.MSMS.Domain.Models.ExchangerMetadata
ExchangerMetadata
ExchangerMetadata
adXAKZ3HZ8M
<Number>k__BackingField
EigAKKU1dbK
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FactionMetadata
AppTech.MSMS.Domain.Models.FactionMetadata
FactionMetadata
FactionMetadata
j1FAKy0wvYt
<Name>k__BackingField
uy9AKvuDa8j
<Number>k__BackingField
tw8AK2Jhj5A
<Quantity>k__BackingField
WVMAK8RCnw8
<Price>k__BackingField
GJJAK3KMYTC
<PersonnalPrice>k__BackingField
JIEAKbRpdqU
<ServiceID>k__BackingField
JpKAKE2RDOd
<ProviderPrice>k__BackingField
QWXAK5jOs2A
<OrderNo>k__BackingField
jXaAKBdXkKs
<ProviderCode>k__BackingField
jHkAKlecid7
<Note>k__BackingField
bpaAKTV8jgl
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DeviceMetadata
AppTech.MSMS.Domain.Models.DeviceMetadata
DeviceMetadata
DeviceMetadata
PklAKCspOVk
<Number>k__BackingField
Yg0AKgVXQFj
<AccountID>k__BackingField
MIqAKcpUXGF
<Active>k__BackingField
Tj5AKJcSCpW
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserDeviceMetadata
AppTech.MSMS.Domain.Models.UserDeviceMetadata
UserDeviceMetadata
UserDeviceMetadata
xbZAK1KVomu
<Identifier>k__BackingField
I6vAKrgLpck
<UserID>k__BackingField
Fq4AKQvnBra
<Permitted>k__BackingField
GfJAKFRZeYk
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.DbBackupMetadata
AppTech.MSMS.Domain.Models.DbBackupMetadata
DbBackupMetadata
DbBackupMetadata
anKAKVY9yoq
<Name>k__BackingField
t7lAKREbfii
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BankMetadata
AppTech.MSMS.Domain.Models.BankMetadata
BankMetadata
BankMetadata
fhdAKfri9mg
<Name>k__BackingField
f3VAKecOc23
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FundMetadata
AppTech.MSMS.Domain.Models.FundMetadata
FundMetadata
FundMetadata
wx9AKxDmhuC
<Number>k__BackingField
dZvAKs2NdCa
<Name>k__BackingField
B44AKO4I30S
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.FundUserMetadata
AppTech.MSMS.Domain.Models.FundUserMetadata
FundUserMetadata
FundUserMetadata
uZaAKhUTHDC
<FundID>k__BackingField
JgkAK6qLaBy
<UserID>k__BackingField
HbbAK4eM7f1
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.GeneralInfoMetadata
AppTech.MSMS.Domain.Models.GeneralInfoMetadata
GeneralInfoMetadata
GeneralInfoMetadata
NJxAKmtdCDv
<Title>k__BackingField
CZFAK78AlAk
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.InstructionMetadata
AppTech.MSMS.Domain.Models.InstructionMetadata
InstructionMetadata
InstructionMetadata
axKAKW6wVlD
<Number>k__BackingField
oFDAKnF9wtj
<Text>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantMetadata
AppTech.MSMS.Domain.Models.MerchantMetadata
MerchantMetadata
MerchantMetadata
r3vAKzsFsWw
<Name>k__BackingField
rxSAyG4Afdw
<CategoryID>k__BackingField
XSMAyA6QYCS
<Description>k__BackingField
r6MAyUJGiN5
<OwnerName>k__BackingField
CNHAypP6Lgj
<PhoneNumber>k__BackingField
FDRAy9GGG0j
<ContactNumber>k__BackingField
Tk0AyibW3Iw
<Address>k__BackingField
iETAyILp542
<Note>k__BackingField
WShAyHeTIZ6
<ImageName>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantCategoryMetadata
AppTech.MSMS.Domain.Models.MerchantCategoryMetadata
MerchantCategoryMetadata
MerchantCategoryMetadata
h9BAyuC4TEp
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.MerchantPaymentMetadata
AppTech.MSMS.Domain.Models.MerchantPaymentMetadata
MerchantPaymentMetadata
MerchantPaymentMetadata
c5UAyNXOOAQ
<MerchantID>k__BackingField
NBaAyDaWHCS
<Amount>k__BackingField
DFpAytV5HsM
<CurrencyID>k__BackingField
vfLAydPuM7Z
<InvoiceNumber>k__BackingField
oRTAyShtP6K
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OfferPaymentMetadata
AppTech.MSMS.Domain.Models.OfferPaymentMetadata
OfferPaymentMetadata
OfferPaymentMetadata
<<type>>
AppTech.MSMS.Domain.Models.TopupMetadata
AppTech.MSMS.Domain.Models.TopupMetadata
TopupMetadata
TopupMetadata
olfAykwpeKO
get_Date
VKtAyP5Kgcs
set_Date
nDHAyjWkLxY
<Amount>k__BackingField
ugEAyaFdq5B
<SubscriberNumber>k__BackingField
LM3AyYoMaSN
<Note>k__BackingField
reSAywX89nI
<LineType>k__BackingField
HxuAy0WZ0Tq
<ServiceID>k__BackingField
O7yAyqPaDBL
<FactionID>k__BackingField
JE0AyMYKaqc
<Number>k__BackingField
arVAyXt7ue4
<RefNumber>k__BackingField
GNpAyoC5qeG
<TransNumber>k__BackingField
XbLAyLtZs1M
<TransactionID>k__BackingField
Qa9AyZrseDA
<Status>k__BackingField
PntAyKYiRiw
<StateClass>k__BackingField
ppSAyyj56G8
<ProviderRM>k__BackingField
utRAyvmF1ZX
<Date>k__BackingField
gnpAy2Y8qlM
<Quantity>k__BackingField
bntAy8rpAcP
<UnitPrice>k__BackingField
qhpAy3YgGMY
<DifferentialAmount>k__BackingField
XEqAyboOlwn
<TotalAmount>k__BackingField
h0kAyEesJsT
<UnitCost>k__BackingField
Mv8Ay5ZlJfd
<CostAmount>k__BackingField
EgtAyB2PBCf
<TotalCost>k__BackingField
AmxAylmNxeW
<CommissionAmount>k__BackingField
urhAyTGFSl5
<Discount>k__BackingField
a8pAyCyAgPv
<Profits>k__BackingField
WeTAygY43Ea
<Description>k__BackingField
t4SAycAksiL
<QuotaionID>k__BackingField
Ru7AyJY7UZn
<RequestInfo>k__BackingField
nhVAy1C764Q
<ResponseInfo>k__BackingField
fiHAyrqMQ6N
<ResponseTime>k__BackingField
yUiAyQrNDmy
<ExecutionPeroid>k__BackingField
LOQAyFs6nWr
<Responded>k__BackingField
XdFAyVgWTpj
<FaildRequest>k__BackingField
dLGAyRfvhXx
<FailedReason>k__BackingField
hbsAyfoKTA0
<FailedType>k__BackingField
p7nAyeUXGOb
<AppTechApi>k__BackingField
vL7Ayxc6gRt
<ResponseStatus>k__BackingField
A2AAysCjMvP
<Cured>k__BackingField
VExAyOKObdN
<CuredBy>k__BackingField
AfUAyhJexfC
<InspectInfo>k__BackingField
ePBAy6UKIuw
<Debited>k__BackingField
qXnAy4wtSxY
<IsDirect>k__BackingField
oQSAymwps95
<OperatorID>k__BackingField
ehEAy7xZFQm
<BillNumber>k__BackingField
GxpAyWBLehq
<Channel>k__BackingField
M63AynEHylk
<Method>k__BackingField
qV2AyzfQLBu
<Type>k__BackingField
xKMAvGMrUau
<Identifier>k__BackingField
QjkAvAqnVLw
<BundleCode>k__BackingField
GOvAvUasUVj
<BundleName>k__BackingField
TfZAvpXy4RT
<AdminNote>k__BackingField
j8dAv9iXJyM
<AccountNote>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.PaymentOrderMetadata
AppTech.MSMS.Domain.Models.PaymentOrderMetadata
PaymentOrderMetadata
PaymentOrderMetadata
nClAviHoAhb
<ServiceID>k__BackingField
RTvAvIkW3Ds
<SubscriberNumber>k__BackingField
pWsAvH4tc4k
<Amount>k__BackingField
QnMAvuKnwEe
<Description>k__BackingField
b0FAvNoJ8oC
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupProviderMetadata
AppTech.MSMS.Domain.Models.TopupProviderMetadata
TopupProviderMetadata
TopupProviderMetadata
B6YAvD2NKeW
<AutoBalance>k__BackingField
hJSAvtiOgAo
<Number>k__BackingField
NTkAvdgvruh
<Name>k__BackingField
dF9AvSsIJ3G
<Type>k__BackingField
lwYAvk1h9sI
<Note>k__BackingField
LkqAvPkmOe2
<Username>k__BackingField
egFAvj6FKy6
<Password>k__BackingField
aUAAvanNTtU
<BaseUrl>k__BackingField
koMAvYU0Ad1
<Token>k__BackingField
EgaAvwU3gui
<UserId>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LoanOrderMetadata
AppTech.MSMS.Domain.Models.LoanOrderMetadata
LoanOrderMetadata
LoanOrderMetadata
<<type>>
AppTech.MSMS.Domain.Models.OfferOrderMetadata
AppTech.MSMS.Domain.Models.OfferOrderMetadata
OfferOrderMetadata
OfferOrderMetadata
<<type>>
AppTech.MSMS.Domain.Models.SimInvoiceMetadata
AppTech.MSMS.Domain.Models.SimInvoiceMetadata
SimInvoiceMetadata
SimInvoiceMetadata
SpVAv0O2qCD
<NetworkID>k__BackingField
Tg4AvqEAFAL
<Date>k__BackingField
CKUAvM4Z56c
<StartNumber>k__BackingField
Vx6AvXQWGGr
<EndNumber>k__BackingField
vvcAvoncAUA
<UnitPrice>k__BackingField
waqAvLqJjcj
<TotalUnits>k__BackingField
pD4AvZB7Ka2
<Amount>k__BackingField
OsvAvKL5lss
<CreditorAccountID>k__BackingField
eqdAvyRqwrd
<DebitorAccountID>k__BackingField
wsrAvvBXZeD
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TopupCommissionMetadata
AppTech.MSMS.Domain.Models.TopupCommissionMetadata
TopupCommissionMetadata
TopupCommissionMetadata
KseAv2k34q7
<ServiceID>k__BackingField
EBJAv88utdB
<CreditorAccountID>k__BackingField
RbdAv3W1DNv
<Percentage>k__BackingField
oB1AvbvItqs
<StartDate>k__BackingField
Sl8AvEKJxiq
<EndDate>k__BackingField
m69Av5vogiq
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.LiveTopupMetadata
AppTech.MSMS.Domain.Models.LiveTopupMetadata
LiveTopupMetadata
LiveTopupMetadata
KgAAvBdy7rj
<ProviderID>k__BackingField
qThAvlgbqb6
<ServiceID>k__BackingField
CNpAvTUvbTQ
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.QuotationMetadata
AppTech.MSMS.Domain.Models.QuotationMetadata
QuotationMetadata
QuotationMetadata
TgcAvCalZw3
<ServiceID>k__BackingField
tVnAvgEv3Cq
<Note>k__BackingField
RDdAvcowgrq
<Price>k__BackingField
BWuAvJx1a6N
<AccountState>k__BackingField
pZ6Av1EeUxC
<AccountID>k__BackingField
KToAvrYH5aS
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ItemCostMetadata
AppTech.MSMS.Domain.Models.ItemCostMetadata
ItemCostMetadata
ItemCostMetadata
xrUAvQIiofi
<ProviderID>k__BackingField
QFWAvFl5g4j
<ServiceID>k__BackingField
fAiAvViNtNn
<Price>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashInMetadata
AppTech.MSMS.Domain.Models.CashInMetadata
CashInMetadata
CashInMetadata
VocAvRmgpWh
<Date>k__BackingField
rwhAvf7HFV4
<Method>k__BackingField
bUhAveuwgOh
<Amount>k__BackingField
LXqAvxDEi3M
<CurrencyID>k__BackingField
GvKAvsf5Zq3
<CreditorAccountID>k__BackingField
oX7AvONGk5X
<DebitorAccountID>k__BackingField
xyPAvhvdWFJ
<Note>k__BackingField
pf2Av6sc7rF
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RegionMetadata
AppTech.MSMS.Domain.Models.RegionMetadata
RegionMetadata
RegionMetadata
H8rAv4VWb29
<Name>k__BackingField
NOCAvmlPVid
<ServiceID>k__BackingField
fCtAv7SfACY
<Code>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RoleMetadata
AppTech.MSMS.Domain.Models.RoleMetadata
RoleMetadata
RoleMetadata
<<type>>
AppTech.MSMS.Domain.Models.RSSMetadata
AppTech.MSMS.Domain.Models.RSSMetadata
RSSMetadata
RSSMetadata
gxqAvWTGT7e
<Feed>k__BackingField
WiCAvnKCClL
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceMetadata
AppTech.MSMS.Domain.Models.ServiceMetadata
ServiceMetadata
ServiceMetadata
<<type>>
AppTech.MSMS.Domain.Models.ServiceCategoryMetadata
AppTech.MSMS.Domain.Models.ServiceCategoryMetadata
ServiceCategoryMetadata
ServiceCategoryMetadata
<<type>>
AppTech.MSMS.Domain.Models.PaymentCommissionMetadata
AppTech.MSMS.Domain.Models.PaymentCommissionMetadata
PaymentCommissionMetadata
PaymentCommissionMetadata
acKAvz2RD6c
<ServiceID>k__BackingField
AdpA2GdliGB
<CurrencyID>k__BackingField
ATMA2AKxfBS
<CommissionCurrencyID>k__BackingField
kj4A2UOBNPv
<FromAmount>k__BackingField
kBqA2pxGy8Q
<ToAmount>k__BackingField
bntA2936fNS
<TraderAmount>k__BackingField
PUMA2ib3iCN
<Note>k__BackingField
dDHA2IXAp79
<PersonalAmount>k__BackingField
uXGA2HhOAVS
<IsAgainst>k__BackingField
po6A2uylpHU
<AccountState>k__BackingField
iMrA2NAP7WP
<AccountID>k__BackingField
CNlA2DRA3tW
<AccountGroupID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ServiceEntryMetadata
AppTech.MSMS.Domain.Models.ServiceEntryMetadata
ServiceEntryMetadata
ServiceEntryMetadata
<<type>>
AppTech.MSMS.Domain.Models.SIMCardOrderMetadata
AppTech.MSMS.Domain.Models.SIMCardOrderMetadata
SIMCardOrderMetadata
SIMCardOrderMetadata
Xv8A2tIyGRE
<SimNumber>k__BackingField
TbGA2d81HRs
<CardIssuePlace>k__BackingField
crmA2S6rKn8
<SimType>k__BackingField
yTaA2kHnKgp
<ContractNumber>k__BackingField
gioA2POM1GD
<ESDN>k__BackingField
GU0A2jKuhSP
<MSISDN>k__BackingField
kp2A2aF0aS5
<PersonalCardType>k__BackingField
MYDA2Y2y4PN
<IssueDate>k__BackingField
e8KA2w4MVJD
<BirthDate>k__BackingField
kmLA20r4kQp
<CustomerName>k__BackingField
nO1A2q6xoWA
<CustomerAddress>k__BackingField
P3TA2MC3i8c
<SubscriberNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SimpleEntryMetadata
AppTech.MSMS.Domain.Models.SimpleEntryMetadata
SimpleEntryMetadata
SimpleEntryMetadata
We6A2XYnyJL
<Amount>k__BackingField
LRPA2owFDg9
<Date>k__BackingField
HVMA2LPqeXI
<CurrencyID>k__BackingField
jkIA2ZKODCe
<CreditorAccountID>k__BackingField
F8FA2KNSSWf
<DebitorAccountID>k__BackingField
oH6A2yADwLQ
<Note>k__BackingField
sKlA2viplE0
<RefNumber>k__BackingField
tpIA223wD6f
<AttachmentNumbers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OpeningBalanceMetadata
AppTech.MSMS.Domain.Models.OpeningBalanceMetadata
OpeningBalanceMetadata
OpeningBalanceMetadata
JIsA28v4aVQ
<Amount>k__BackingField
QWfA234X3jB
<CurrencyID>k__BackingField
WdGA2bAGePI
<AccountID>k__BackingField
QkjA2EkoaJk
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SMSMetadata
AppTech.MSMS.Domain.Models.SMSMetadata
SMSMetadata
SMSMetadata
lSEA25H6Ycp
<PhoneNumber>k__BackingField
xOyA2BdOJqL
<Message>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashOutMetadata
AppTech.MSMS.Domain.Models.CashOutMetadata
CashOutMetadata
CashOutMetadata
m6vA2ltH3yk
<Date>k__BackingField
Ud8A2TlXC7N
<Method>k__BackingField
jBUA2CYHWZV
<Amount>k__BackingField
FFIA2g9itGT
<CurrencyID>k__BackingField
UYvA2ciDIWE
<CreditorAccountID>k__BackingField
D2hA2JrYcCL
<DebitorAccountID>k__BackingField
yNmA21Z1gvu
<Note>k__BackingField
YqVA2rF68fu
<Delivery>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupCommissionMetadata
AppTech.MSMS.Domain.Models.TrailToupCommissionMetadata
TrailToupCommissionMetadata
TrailToupCommissionMetadata
UVoA2QCNXUC
<MobileNetworkID>k__BackingField
D6DA2FRQiJm
<FromAmount>k__BackingField
AkgA2VZrLk7
<ToAmount>k__BackingField
GV9A2R18E1h
<Percentage>k__BackingField
HZ4A2ftPFeX
<PersonnalPrice>k__BackingField
R6jA2eYW9iX
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TrailToupOrderMetadata
AppTech.MSMS.Domain.Models.TrailToupOrderMetadata
TrailToupOrderMetadata
TrailToupOrderMetadata
c0QA2xUylTo
<MobileNetworkID>k__BackingField
Ou0A2suViSe
<SubscriberNumber>k__BackingField
l2kA2Oey2KY
<Amount>k__BackingField
D0dA2houQFN
<Percentage>k__BackingField
z0oA26KJCEL
<ExchangeAmount>k__BackingField
kFKA24IfeV8
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CashTransferMetadata
AppTech.MSMS.Domain.Models.CashTransferMetadata
CashTransferMetadata
CashTransferMetadata
X2EA2mxnBlk
<Amount>k__BackingField
sI2A27l3dbp
<CurrencyID>k__BackingField
abCA2WikgZD
<CreditorAccountID>k__BackingField
SuDA2nADNvk
<DebitorAccountID>k__BackingField
tRQA2zTJnVO
<Note>k__BackingField
wCJA8GDAWH0
<RefNumber>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransferOrderMetadata
AppTech.MSMS.Domain.Models.TransferOrderMetadata
TransferOrderMetadata
TransferOrderMetadata
bloA8AJ20Yx
<TransferNumber>k__BackingField
ijlA8UqU0MC
<ServiceID>k__BackingField
VKRA8pXlGbj
<IsIncoming>k__BackingField
H15A89ftT7C
<Amount>k__BackingField
V9fA8i7W474
<CurrencyID>k__BackingField
Yr1A8Ih1I71
<ReceiverName>k__BackingField
mjCA8HL5TPD
<ReceiverMobile>k__BackingField
t88A8u6T1Yb
<ReceiverCardNo>k__BackingField
qRCA8NafqET
<ReceiverCardIssuerPlace>k__BackingField
x2KA8DFU3HA
<ReceiverCardIssuerDate>k__BackingField
uRgA8t0FiUt
<SenderName>k__BackingField
u8FA8dIk5Bi
<SenderMobile>k__BackingField
z84A8SfRgZV
<Note>k__BackingField
nwiA8kvQss1
<ImageName>k__BackingField
CcOA8PNsJm3
<ExchangerID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserMetadata
AppTech.MSMS.Domain.Models.UserMetadata
UserMetadata
UserMetadata
RnBA8j04611
<UserName>k__BackingField
eo0A8aD5x8H
<Password>k__BackingField
ujWA8YbO7jI
<Status>k__BackingField
kjOA8wmtGP9
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WithdrawOrderMetadata
AppTech.MSMS.Domain.Models.WithdrawOrderMetadata
WithdrawOrderMetadata
WithdrawOrderMetadata
<<type>>
AppTech.MSMS.Domain.Models.BagatMetadata
AppTech.MSMS.Domain.Models.BagatMetadata
BagatMetadata
BagatMetadata
Kr1A80YowKY
<Name>k__BackingField
AA8A8qADOC6
<Number>k__BackingField
KVfA8MOgMS1
<Code>k__BackingField
mL9A8XDFjkW
<LineType>k__BackingField
G0uA8ognfOv
<OrderNo>k__BackingField
bfcA8Lj7lmA
<Quantity>k__BackingField
aCFA8ZvIomg
<ProviderPrice>k__BackingField
GaoA8KQbWMQ
<TelPrice>k__BackingField
TrtA8yuJWgT
<Price>k__BackingField
pg5A8vaToB6
<PersonnalPrice>k__BackingField
wRrA82m04ZP
<Description>k__BackingField
xFwA88q4QGC
<Mode>k__BackingField
zqLA83e1gek
<ByProvider>k__BackingField
runA8b6HvtB
<ProviderID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.BagatPaymentMetadata
AppTech.MSMS.Domain.Models.BagatPaymentMetadata
BagatPaymentMetadata
BagatPaymentMetadata
ssnA8EqD3ak
<SubscriberNumber>k__BackingField
AG1A85RXbQE
<LineType>k__BackingField
YbxA8BEs7gu
<SimType>k__BackingField
rStA8lrw8jH
<Note>k__BackingField
iJGA8Tn2w8h
<Channel>k__BackingField
qKaA8C0ripa
<ServiceEntryID>k__BackingField
rt0A8gxX9gR
<IncludePay>k__BackingField
voTA8cnFIED
<ActionType>k__BackingField
jUsA8JcIURG
<OfferID>k__BackingField
tMuA81qnktF
<ServiceID>k__BackingField
JxhA8rq1Ex1
<ProviderID>k__BackingField
PrmA8Q2dLZw
<Amount>k__BackingField
gZeA8F1Fwdu
<HasLoan>k__BackingField
UcZA8VKWYjs
<OfferCode>k__BackingField
HcrA8R6cUkV
<OfferName>k__BackingField
v8DA8f69cdf
<OfferAction>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.UserTargetMetadata
AppTech.MSMS.Domain.Models.UserTargetMetadata
UserTargetMetadata
UserTargetMetadata
hEoA8eQWmix
<CreatedTime>k__BackingField
pLrA8xjVPuM
<UserID>k__BackingField
swwA8sR8BRW
<PrimaryUser>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RemittanceCommissionMetadata
AppTech.MSMS.Domain.Models.RemittanceCommissionMetadata
RemittanceCommissionMetadata
RemittanceCommissionMetadata
fY9A8O7EyLq
<RemittanceType>k__BackingField
UDoA8hYjNwN
<CurrencyID>k__BackingField
NNWA860yHOa
<CommissionCurrencyID>k__BackingField
yTNA849AZra
<StartAmount>k__BackingField
AerA8m3dUKD
<EndAmount>k__BackingField
LovA87K6lXd
<PointCommission>k__BackingField
FBLA8WlbyHN
<CenterCommission>k__BackingField
M6GA8ns0dEG
<TargetState>k__BackingField
ajUA8zdWw9I
<TargetID>k__BackingField
iEtA3GEBJsv
<TargetGroupID>k__BackingField
Q96A3A5bk7J
<IsAgainst>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CountryMetadata
AppTech.MSMS.Domain.Models.CountryMetadata
CountryMetadata
CountryMetadata
<<type>>
AppTech.MSMS.Domain.Models.ProvinceMetadata
AppTech.MSMS.Domain.Models.ProvinceMetadata
ProvinceMetadata
ProvinceMetadata
<<type>>
AppTech.MSMS.Domain.Models.CommissionReceiptMetadata
AppTech.MSMS.Domain.Models.CommissionReceiptMetadata
CommissionReceiptMetadata
CommissionReceiptMetadata
Y0HA3UpAdMs
<ServiceID>k__BackingField
SkLA3pUCQUD
<AccountGroupID>k__BackingField
EEuA39aQl32
<Percentage>k__BackingField
slEA3ibrHHS
<StartDate>k__BackingField
AwVA3IvX615
<EndDate>k__BackingField
d2yA3HtMMFK
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardMetadata
AppTech.MSMS.Domain.Models.CardMetadata
CardMetadata
CardMetadata
wHTA3u1wif5
<Number>k__BackingField
euKA3Nf8tKm
<Name>k__BackingField
D69A3D9fjag
<Password>k__BackingField
hjLA3tqeZjY
<CardTypeID>k__BackingField
zB0A3dLStDw
<CardFactionID>k__BackingField
SGbA3S1VEPo
<SerialNo>k__BackingField
uNVA3kv6IeX
<Description>k__BackingField
UufA3PmENd1
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardTypeMetadata
AppTech.MSMS.Domain.Models.CardTypeMetadata
CardTypeMetadata
CardTypeMetadata
rIEA3jouFqH
<Number>k__BackingField
FHcA3ajyYwD
<Name>k__BackingField
i6EA3Y0ilCN
<AccountID>k__BackingField
HlkA3w78vPs
<Image>k__BackingField
FOxA30E9UYA
<Type>k__BackingField
GKoA3q4UP8m
<Description>k__BackingField
FX9A3MABPIp
<Note>k__BackingField
PstA3XrSnVW
<Active>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardFactionMetadata
AppTech.MSMS.Domain.Models.CardFactionMetadata
CardFactionMetadata
CardFactionMetadata
mgVA3olYhxE
<Number>k__BackingField
R5lA3LPsPyd
<Name>k__BackingField
qMZA3ZBKtgD
<CostPrice>k__BackingField
Qa7A3K9hB1u
<SelePrice>k__BackingField
IbbA3ydCUtE
<CardTypeID>k__BackingField
zEsA3vF3ESt
<Active>k__BackingField
AsuA32iVxA4
<Status>k__BackingField
VEnA38FN0vI
<Description>k__BackingField
ePjA33t5qaX
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardOrderMetadata
AppTech.MSMS.Domain.Models.CardOrderMetadata
CardOrderMetadata
CardOrderMetadata
x7CA3bWcsH5
<Number>k__BackingField
zSVA3E6Tgsh
<CardFactionID>k__BackingField
d9BA35VPIhx
<CardTypeID>k__BackingField
m1FA3BSSqkD
<Amount>k__BackingField
eOKA3l9daCw
<Username>k__BackingField
VyjA3ToOaec
<Password>k__BackingField
NpkA3CUNLoZ
<Email>k__BackingField
J2oA3ggSyB8
<Phone>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CardPaymentMetadata
AppTech.MSMS.Domain.Models.CardPaymentMetadata
CardPaymentMetadata
CardPaymentMetadata
HR7A3cOU3qm
<Number>k__BackingField
psAA3JypOwE
<Name>k__BackingField
PJ0A315HHRH
<CardFactionID>k__BackingField
FnqA3r46UNc
<CardTypeID>k__BackingField
z21A3QW9l6J
<Amount>k__BackingField
TIJA3FBm9cB
<Username>k__BackingField
lkEA3VSN9Py
<Password>k__BackingField
uJiA3RKuM8h
<Email>k__BackingField
I3aA3fuvbo8
<Phone>k__BackingField
oefA3e3L10h
<Description>k__BackingField
cKEA3xAjFYh
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.RegionsMetadata
AppTech.MSMS.Domain.Models.RegionsMetadata
RegionsMetadata
RegionsMetadata
UoZA3sZ5JJx
<Number>k__BackingField
D33A3OfvBk4
<Name>k__BackingField
PQNA3hViy5J
<ProvinceID>k__BackingField
vlQA36jfHW7
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.AccountRegionMetadata
AppTech.MSMS.Domain.Models.AccountRegionMetadata
AccountRegionMetadata
AccountRegionMetadata
TfOA34Nqo4m
<Number>k__BackingField
rQlA3mwV4jv
<RegionID>k__BackingField
WAHA375TIdL
<AccountID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiProviderMetadata
AppTech.MSMS.Domain.Models.WifiProviderMetadata
WifiProviderMetadata
WifiProviderMetadata
DimA3WKJt9r
<Number>k__BackingField
X4WA3nks8no
<AccountID>k__BackingField
guLA3zxJrqB
<Name>k__BackingField
UrcAbGotu6j
<RegionID>k__BackingField
EdDAbASBocV
<Image>k__BackingField
OEUAbU9kun9
<Address>k__BackingField
zsLAbpXOSWM
<URL>k__BackingField
pQtAb9WBkOJ
<Phone>k__BackingField
j8DAbiHeFpC
<PrafitAmount>k__BackingField
WeNAbItwYVR
<PrafitStatus>k__BackingField
EplAbHm2U2U
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.WifiFactionMetadata
AppTech.MSMS.Domain.Models.WifiFactionMetadata
WifiFactionMetadata
WifiFactionMetadata
TOmAbuyYjFl
<Number>k__BackingField
QylAbNEH6K3
<Name>k__BackingField
DyaAbDSuarW
<OrderNO>k__BackingField
AG4Abt8ZguV
<WifiProviderID>k__BackingField
jiSAbd7f502
<Note>k__BackingField
prtAbS4Wj05
<Price>k__BackingField
PdEAbkmqWjM
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.TransporterMetadata
AppTech.MSMS.Domain.Models.TransporterMetadata
TransporterMetadata
TransporterMetadata
KUIAbPhZUgc
<Number>k__BackingField
paKAbjpFHbd
<Name>k__BackingField
i61AbaVrngE
<ImageName>k__BackingField
r1lAbY3jh3N
<AccountID>k__BackingField
qxwAbwODO9x
<Note>k__BackingField
GLGAb0PlgMx
<Description>k__BackingField
HXLAbq4OYQV
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.CoverageOrderMetadata
AppTech.MSMS.Domain.Models.CoverageOrderMetadata
CoverageOrderMetadata
CoverageOrderMetadata
TiEAbMnkw8R
<AccountID>k__BackingField
vK2AbXhNSqv
<ExchangeAccountID>k__BackingField
OxgAbo6XPMP
<Amount>k__BackingField
OlyAbLAxLRG
<Purpose>k__BackingField
RPHAbZMfVC9
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptCreditorMetadata
AppTech.MSMS.Domain.Models.ReceiptCreditorMetadata
ReceiptCreditorMetadata
ReceiptCreditorMetadata
vfeAbKuSQix
<Amount>k__BackingField
rq4AbymLuiB
<Date>k__BackingField
pL9AbvVsopA
<CurrencyID>k__BackingField
mXVAb2vH2hL
<CreditorAccountID>k__BackingField
q3IAb8HIgSs
<DebitorAccountID>k__BackingField
UfMAb3G0PCr
<Note>k__BackingField
KGnAbbsmcb6
<RefNumber>k__BackingField
OF7AbE1ZvSQ
<AttachmentNumbers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.ReceiptDebitorMetadata
AppTech.MSMS.Domain.Models.ReceiptDebitorMetadata
ReceiptDebitorMetadata
ReceiptDebitorMetadata
aXvAb5YQGpU
<Amount>k__BackingField
yPWAbB59Fxc
<Date>k__BackingField
I1uAblUHqdK
<CurrencyID>k__BackingField
XEVAbTNwKKM
<CreditorAccountID>k__BackingField
dIhAbC94Q3a
<DebitorAccountID>k__BackingField
TInAbgP5BN6
<Note>k__BackingField
NUnAbcmQfrS
<RefNumber>k__BackingField
Ay2AbJUnT0x
<AttachmentNumbers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteProviderMetadata
AppTech.MSMS.Domain.Models.SatelliteProviderMetadata
SatelliteProviderMetadata
SatelliteProviderMetadata
R1VAb11iZbE
<Number>k__BackingField
cD6AbrvXnip
<AccountID>k__BackingField
KaSAbQR7ZXk
<Name>k__BackingField
kQ2AbFXt9nI
<MinSubscribe>k__BackingField
eV6AbV8vNLU
<RegionID>k__BackingField
eHQAbRhkdLv
<Phone>k__BackingField
vYaAbftESJU
<PrafitAmount>k__BackingField
AZUAbeaUTZE
<PrafitStatus>k__BackingField
kqCAbx9wbj9
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.SatelliteFactionMetadata
AppTech.MSMS.Domain.Models.SatelliteFactionMetadata
SatelliteFactionMetadata
SatelliteFactionMetadata
pDwAbsT1Moh
<Number>k__BackingField
kiqAbOZhsn9
<Name>k__BackingField
F7OAbhJeXXP
<SatelliteProviderID>k__BackingField
A7aAb6rKR3e
<Note>k__BackingField
Mp9Ab4608X1
<Price>k__BackingField
PcJAbmbuE4s
<Description>k__BackingField
<<type>>
AppTech.MSMS.Domain.Models.OrderSatelliteQuotaMetadata
AppTech.MSMS.Domain.Models.OrderSatelliteQuotaMetadata
OrderSatelliteQuotaMetadata
OrderSatelliteQuotaMetadata
QwrAb7uInE3
<Description>k__BackingField
dRGAbW0ekSl
<ProviderID>k__BackingField
KS0AbnsnUQ0
<Note>k__BackingField
T3XAbznrdXA
<ProviderNote>k__BackingField
TKEAEGILbCU
<Status>k__BackingField
<<type>>
AppTech.MSMS.Domain.Security.FrecnhiLogin
AppTech.MSMS.Domain.Security.FrecnhiLogin
FrecnhiLogin
FrecnhiLogin
<<type>>
AppTech.MSMS.Domain.Security.IpAddressManager
AppTech.MSMS.Domain.Security.IpAddressManager
IpAddressManager
IpAddressManager
<<type>>
AppTech.MSMS.Domain.Security.RijndaelHelper
AppTech.MSMS.Domain.Security.RijndaelHelper
RijndaelHelper
RijndaelHelper
FwHAEAFBEbR
DecryptStringFromBytes
DcPAEUyNE7c
EncryptStringToBytes
<<type>>
AppTech.MSMS.Domain.Security.Models.LicenseSetting
AppTech.MSMS.Domain.Security.Models.LicenseSetting
LicenseSetting
LicenseSetting
MKNAEpURJq7
<Distributor>k__BackingField
dAVAE9J0ZuZ
<Branch>k__BackingField
QD0AEiO13de
<SmsNo>k__BackingField
bQEAEIbWlmS
<DT>k__BackingField
Q80AEHlvX0Z
<SrvNo>k__BackingField
dhAAEuteV9q
<CEAU>k__BackingField
<<type>>
AppTech.MSMS.Domain.Helpers.BalanceClosure
AppTech.MSMS.Domain.Helpers.BalanceClosure
BalanceClosure
BalanceClosure
TKbAENnB1jK
CloseAccountName
RTnAEDUwIVx
CopyBalanceDate
CKJAEtd19ae
copyBalanceDate
IRQAEdaXHBM
TargetDBName
VwhAESdsAKE
SourceDbName
<<type>>
AppTech.MSMS.Domain.Helpers.ExcelHelper
AppTech.MSMS.Domain.Helpers.ExcelHelper
ExcelHelper
ExcelHelper
y2yAEkhZmnW
_path
<<type>>
AppTech.MSMS.Domain.Helpers.JsonHelper
AppTech.MSMS.Domain.Helpers.JsonHelper
JsonHelper
JsonHelper
<<type>>
AppTech.MSMS.Domain.Helpers.Pdf
AppTech.MSMS.Domain.Helpers.Pdf
Pdf
Pdf
xWRAEPwu61P
doc
<<type>>
AppTech.MSMS.Domain.Helpers.DbBackupHelper
AppTech.MSMS.Domain.Helpers.DbBackupHelper
DbBackupHelper
DbBackupHelper
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy
ExpressRemittanceProxy
ExpressRemittanceProxy
IYCAEjXfshT
GetCurrencyId
Md0AEahbwWr
Log
FjRAEYqltsX
Log
dDZAEwxaVI1
Log
<<type>>
AppTech.MSMS.Domain.Helpers.FrenchiClient
AppTech.MSMS.Domain.Helpers.FrenchiClient
FrenchiClient
FrenchiClient
ydWAE0SRNuR
_baseUrl
<<type>>
AppTech.MSMS.Domain.Helpers.PhoneNumber
AppTech.MSMS.Domain.Helpers.PhoneNumber
PhoneNumber
PhoneNumber
<<type>>
AppTech.MSMS.Domain.Entities.Balance
AppTech.MSMS.Domain.Entities.Balance
Balance
Balance
NsIAEqtdikg
<CurrencyID>k__BackingField
vL8AEMdMF9w
<VoucherName>k__BackingField
vl9AEXcPROi
<CurrencyName>k__BackingField
RM4AEorBUZs
<AccountName>k__BackingField
TXPAELuASIC
<CurrencySymbol>k__BackingField
u1FAEZE5lLp
<Amount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Commission
AppTech.MSMS.Domain.Entities.Commission
Commission
Commission
lL6AEKuht1m
<ParentAccountID>k__BackingField
krKAEyR4yGS
<IsChild>k__BackingField
r9gAEvncZme
<Amount>k__BackingField
OamAE2kkhRd
<DifferentailAmount>k__BackingField
Yl4AE8lq58r
<CurrencyID>k__BackingField
IDRAE3PHQQj
<CurrencyName>k__BackingField
P5MAEbFhH5y
<InPercentage>k__BackingField
acSAEEN0eXw
<ExtraInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Entity
AppTech.MSMS.Domain.Entities.Entity
Entity
Entity
CbnAE552G4H
<ID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.FcmToken
AppTech.MSMS.Domain.Entities.FcmToken
FcmToken
FcmToken
hO4AEBr9mKp
<Token>k__BackingField
YVqAElWg0nb
<AccountID>k__BackingField
dMLAETiFwgM
<Key>k__BackingField
TIVAECpGC4m
<DeviceID>k__BackingField
s7xAEgqmirx
<UserAgent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.FmsCacheResponse
AppTech.MSMS.Domain.Entities.FmsCacheResponse
FmsCacheResponse
FmsCacheResponse
eEEAEcIkoy4
<GroupID>k__BackingField
PjiAEJJskrt
<LSetting>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.IItem
AppTech.MSMS.Domain.Entities.IItem
IItem
IItem
<<type>>
AppTech.MSMS.Domain.Entities.IPerson
AppTech.MSMS.Domain.Entities.IPerson
IPerson
IPerson
<<type>>
AppTech.MSMS.Domain.Entities.Parent
AppTech.MSMS.Domain.Entities.Parent
Parent
Parent
jd9AE1TiuhP
<ID>k__BackingField
Nj9AEr4tEyM
<Number>k__BackingField
OjEAEQF9ojr
<Name>k__BackingField
XUGAEFYGo9f
<AccountID>k__BackingField
AxQAEVHQNo4
<PhoneNumber>k__BackingField
UiLAERpGRBJ
<ContactNumber>k__BackingField
hZHAEfV9OEh
<Address>k__BackingField
BltAEeVpbFg
<Note>k__BackingField
lm5AExQi958
<BranchID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.RemittancePointInfo
AppTech.MSMS.Domain.Entities.RemittancePointInfo
RemittancePointInfo
RemittancePointInfo
jCoAEsG60mr
<ID>k__BackingField
CiIAEOCppor
<Name>k__BackingField
rgcAEhZ7Z7h
<RegionID>k__BackingField
R7QAE6ufQFw
<Type>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.SourceInfo
AppTech.MSMS.Domain.Entities.SourceInfo
SourceInfo
SourceInfo
zehAE4yuDDt
<Host>k__BackingField
cMDAEmZ3awK
<IpAddress>k__BackingField
XarAE7Frf3Y
<UserAgent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.RequestAuth
AppTech.MSMS.Domain.Entities.RequestAuth
RequestAuth
RequestAuth
hrLAEWtmEyU
<VC>k__BackingField
IlyAEnoMqWa
<VERSION_CODE>k__BackingField
b2KAEz9kRl4
<SessionID>k__BackingField
Bl4A5GGWJq2
<DeviceID>k__BackingField
w9gA5AGIm0T
<TransactionID>k__BackingField
OYiA5UmUueW
<DeviceInfo>k__BackingField
mOCA5p9qBac
<Location>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TableColumn
AppTech.MSMS.Domain.Entities.TableColumn
TableColumn
TableColumn
WWWA59Tkbt0
<Type>k__BackingField
G6fA5iiAmJ5
<Name>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.GomalaCommission
AppTech.MSMS.Domain.Entities.GomalaCommission
GomalaCommission
GomalaCommission
d4KA5IRI29H
<ID>k__BackingField
l7wA5H3eqp0
<CostID>k__BackingField
j4oA5uBZLS7
<SalePrice>k__BackingField
bHNA5NbCnu9
<ProviderPrice>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupQuery
AppTech.MSMS.Domain.Entities.TopupQuery
TopupQuery
TopupQuery
gOcA5DPSQK8
<QueryType>k__BackingField
VyyA5tAvuvS
<SID>k__BackingField
oWxA5d450Rf
<SNO>k__BackingField
OqkA5SL6IDo
<Number>k__BackingField
FkhA5kcgUgo
<Type>k__BackingField
vZfA5PGpFWx
<RID>k__BackingField
vRGA5jWwuW9
<UID>k__BackingField
zgUA5a5YABL
<IsApi>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.ITopup
AppTech.MSMS.Domain.Entities.ITopup
ITopup
ITopup
<<type>>
AppTech.MSMS.Domain.Entities.TopupModel
AppTech.MSMS.Domain.Entities.TopupModel
TopupModel
TopupModel
KUeA5YCnfxy
<ID>k__BackingField
w09A5wuCaQS
<Number>k__BackingField
iUiA50SdlFj
<ServiceID>k__BackingField
wm0A5q5B4XO
<SubscriberNumber>k__BackingField
YxKA5MVVdPe
<RefNumber>k__BackingField
sE4A5XRRMC2
<TransactionID>k__BackingField
XBQA5oOK5Ob
<Status>k__BackingField
hasA5LAgxj2
<EntryID>k__BackingField
eYYA5ZbZa5A
<UniqueNo>k__BackingField
etsA5KpD18K
<Note>k__BackingField
xtvA5ylM8on
<ProviderRM>k__BackingField
LeEA5v2RpyX
<SubNote>k__BackingField
z5SA52eocYl
<Quantity>k__BackingField
L9xA58kpyFC
<UnitPrice>k__BackingField
omQA530fPC2
<CostAmount>k__BackingField
PRQA5bTUKHg
<AppTech.MSMS.Domain.Entities.ITopup.Date>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupRequest
AppTech.MSMS.Domain.Entities.TopupRequest
TopupRequest
TopupRequest
VFVA5Ef2U2B
<UN>k__BackingField
iskA55FbVVg
<PSS>k__BackingField
R22A5B2IlgI
<TKN>k__BackingField
COjA5lI3l5n
<Note>k__BackingField
qj7A5TVBLwF
<SID>k__BackingField
QCGA5C3VaFD
<SNO>k__BackingField
VHoA5g542Ev
<AMT>k__BackingField
nc3A5copDve
<Cost>k__BackingField
A6UA5Juf2oS
<FID>k__BackingField
BDOA51eDIfo
<RID>k__BackingField
GTcA5rWr1Fo
<LType>k__BackingField
Gw9A5QE5WOV
<CType>k__BackingField
lAlA5FCPURn
<Ref>k__BackingField
d8eA5VFWWgO
<Channel>k__BackingField
nxfA5RZj5LL
<Identifier>k__BackingField
ntMA5fHOYEm
<Action>k__BackingField
WSxA5eOW2cV
<Type>k__BackingField
b0mA5xGbS4g
<State>k__BackingField
fyGA5sMS7PK
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupRespose
AppTech.MSMS.Domain.Entities.TopupRespose
TopupRespose
TopupRespose
ftLA5OBZfBI
<Success>k__BackingField
fyIA5hXCaZ5
<Status>k__BackingField
uVtA56IMEYg
<MSG>k__BackingField
tIOA54x4JT1
<TNO>k__BackingField
dHiA5mTHh9W
<ID>k__BackingField
rXUA57kraVc
<Quantity>k__BackingField
LttA5WwKuqo
<UnitCost>k__BackingField
zxJA5nl0pZu
<CostAmount>k__BackingField
HD8A5zGI2au
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupRequestInfo
AppTech.MSMS.Domain.Entities.TopupRequestInfo
TopupRequestInfo
TopupRequestInfo
U2CABGB7nGq
<TransactionID>k__BackingField
BuqABAyIjMG
<ServiceID>k__BackingField
Ok2ABUit74F
<Number>k__BackingField
ngtABpPI1Kt
<Amount>k__BackingField
LjfAB93JMhL
<BundleID>k__BackingField
IsSABifkXEo
<LineType>k__BackingField
llhABIppcbD
<Type>k__BackingField
X7JABHiqCDb
<Action>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupResponseInfo
AppTech.MSMS.Domain.Entities.TopupResponseInfo
TopupResponseInfo
TopupResponseInfo
hCNABunDeXm
<Status>k__BackingField
oO4ABNLLF8q
<PaymentID>k__BackingField
OMSABDSAS01
<Message>k__BackingField
GZAABtbwp0B
<CostAmount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.StatusInfo
AppTech.MSMS.Domain.Entities.StatusInfo
StatusInfo
StatusInfo
Qq9ABdMgFgs
<Status>k__BackingField
LarABSFu0PH
<Success>k__BackingField
tc1ABkojvXe
<Message>k__BackingField
uBYABPX524T
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupStatusInfo
AppTech.MSMS.Domain.Entities.TopupStatusInfo
TopupStatusInfo
TopupStatusInfo
FRcABjbal5D
<Status>k__BackingField
GmeABa0VloD
<Success>k__BackingField
NI3ABYccNNT
<Message>k__BackingField
jbjABwlNrxn
<RequestInfo>k__BackingField
D4jAB0wFpMZ
<Note>k__BackingField
k1AABqeZeOe
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.TopupStatusRequest
AppTech.MSMS.Domain.Entities.TopupStatusRequest
TopupStatusRequest
TopupStatusRequest
S78ABMvXnyV
<UN>k__BackingField
O93ABXXSYD1
<PSS>k__BackingField
moQABo5aMRr
<TKN>k__BackingField
N44ABL1kDac
<SID>k__BackingField
kA0ABZ9y5AB
<SNO>k__BackingField
HtBABKAK6Qd
<TID>k__BackingField
bHEAByWrRHq
<REF>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.WifiInventoryModel
AppTech.MSMS.Domain.Entities.WifiInventoryModel
WifiInventoryModel
WifiInventoryModel
e2uABvgKNqG
<ID>k__BackingField
S7NAB2KDjeO
<Number>k__BackingField
Hi4AB8WpKxa
<Name>k__BackingField
jMOAB3tZ3DU
<OrderNO>k__BackingField
xATABboWi5R
<Description>k__BackingField
xZvABEyEkwX
<Price>k__BackingField
oc0AB5bjEip
<TotalAll>k__BackingField
usHABBMQIhw
<TotalSold>k__BackingField
UBVABlmxn3G
<TotalRemain>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Password
AppTech.MSMS.Domain.Entities.Password
Password
Password
cFjABTRwr7B
<CurrentPasswrod>k__BackingField
R1rABClLgEU
<NewPasswrod>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.ServiceCode
AppTech.MSMS.Domain.Entities.ServiceCode
ServiceCode
ServiceCode
<<type>>
AppTech.MSMS.Domain.Entities.AlconPaymentRequest
AppTech.MSMS.Domain.Entities.AlconPaymentRequest
AlconPaymentRequest
AlconPaymentRequest
XhjABghuyrl
<USR>k__BackingField
MIdABcYCfqD
<TKN>k__BackingField
mfhABJE4g9e
<AC>k__BackingField
BO4AB1DGWP3
<MT>k__BackingField
KKmABroN3AY
<SC>k__BackingField
w2XABQXsaDL
<SNO>k__BackingField
GrmABF3C5QX
<AMT>k__BackingField
hGYABVyA5Dl
<REF>k__BackingField
GDaABRGWkcy
<SAC>k__BackingField
XiZABf01NRu
<REM>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlconPaymentRespone
AppTech.MSMS.Domain.Entities.AlconPaymentRespone
AlconPaymentRespone
AlconPaymentRespone
B3RABeuBvWx
<RC>k__BackingField
zCXABx32sTS
<MSG>k__BackingField
vUTABsIITPw
<REF>k__BackingField
kbQABOsCd0b
<TRX>k__BackingField
DT2ABhRnJ9u
<MT>k__BackingField
XR2AB6nODtB
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConInqueryRequest
AppTech.MSMS.Domain.Entities.AlConInqueryRequest
AlConInqueryRequest
AlConInqueryRequest
g0vAB4FtWAj
<AC>k__BackingField
yjsABmhqcgB
<SC>k__BackingField
Lp6AB7sBdnq
<SNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConLoan
AppTech.MSMS.Domain.Entities.AlConLoan
AlConLoan
AlConLoan
cxkABWHJMUZ
<loanStatus>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConInqueryResponse
AppTech.MSMS.Domain.Entities.AlConInqueryResponse
AlConInqueryResponse
AlConInqueryResponse
CDQABnBNieC
<RC>k__BackingField
mDqABzyEMvb
<MSG>k__BackingField
T3HAlGXSLGj
<BAL>k__BackingField
NabAlArWns2
<MT>k__BackingField
Lc6AlUWIxsW
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.S2DStatus
AppTech.MSMS.Domain.Entities.S2DStatus
S2DStatus
S2DStatus
QafAlpOvNRZ
<Status>k__BackingField
ObtAl9sQZ3M
<Phone>k__BackingField
m5hAliNWTcE
<Rem>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConInqueryResponse2
AppTech.MSMS.Domain.Entities.AlConInqueryResponse2
AlConInqueryResponse2
AlConInqueryResponse2
tnUAlICXlEw
<RC>k__BackingField
ft4AlHMFG32
<MSG>k__BackingField
ahpAlulNn0Y
<BAL>k__BackingField
p2hAlNRcYNj
<MT>k__BackingField
SZqAlDuVmDq
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConSD
AppTech.MSMS.Domain.Entities.AlConSD
AlConSD
AlConSD
viEAltCbAc9
<BaqaAmt>k__BackingField
xT1AldN5RE1
<remain>k__BackingField
ikAAlSkR7oR
<Balance>k__BackingField
TRCAlk51ZEL
<CREDIT>k__BackingField
C1EAlPPv25W
<ExpDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.PostPaidAlConSD
AppTech.MSMS.Domain.Entities.PostPaidAlConSD
PostPaidAlConSD
PostPaidAlConSD
QLWAljQXxwk
<CREDIT>k__BackingField
RccAlay1wYO
<Balance>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOfferRequest
AppTech.MSMS.Domain.Entities.AlConOfferRequest
AlConOfferRequest
AlConOfferRequest
PmQAlYgnhrY
<USR>k__BackingField
fX5AlwodcDp
<TKN>k__BackingField
IClAl0vGvsw
<AC>k__BackingField
hUJAlqmiKb3
<SC>k__BackingField
X5tAlMUm6qP
<SNO>k__BackingField
kfXAlXrq5xG
<SAC>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOfferResponse
AppTech.MSMS.Domain.Entities.AlConOfferResponse
AlConOfferResponse
AlConOfferResponse
LrKAlolyWhO
<RC>k__BackingField
SQoAlLJ7wIo
<MSG>k__BackingField
ndrAlZ7tg6C
<SD>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOffer
AppTech.MSMS.Domain.Entities.AlConOffer
AlConOffer
AlConOffer
CESAlKuZxuO
<offer_id>k__BackingField
CJxAlyHbtfX
<offer_name>k__BackingField
AuMAlvHiwMw
<eff_date>k__BackingField
SujAl2ByMkf
<exp_date>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConBalanceRequest
AppTech.MSMS.Domain.Entities.AlConBalanceRequest
AlConBalanceRequest
AlConBalanceRequest
QJnAl8vZUxm
<AC>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConBalanceResponse
AppTech.MSMS.Domain.Entities.AlConBalanceResponse
AlConBalanceResponse
AlConBalanceResponse
ilkAl3DmVpy
<RC>k__BackingField
xBYAlb8QTLt
<MSG>k__BackingField
KvCAlEgjGOR
<BAL>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AlConOfferActionType
AppTech.MSMS.Domain.Entities.AlConOfferActionType
AlConOfferActionType
AlConOfferActionType
<<type>>
AppTech.MSMS.Domain.Entities.AlConActionType
AppTech.MSMS.Domain.Entities.AlConActionType
AlConActionType
AlConActionType
<<type>>
AppTech.MSMS.Domain.Entities.PaymentRequest
AppTech.MSMS.Domain.Entities.PaymentRequest
PaymentRequest
PaymentRequest
BqEAl5AkH5W
<PaymentType>k__BackingField
zdbAlBUqrx8
<RecordID>k__BackingField
UrTAlln14eW
<ServiceID>k__BackingField
oUyAlTfNEFl
<SNO>k__BackingField
i3GAlCuPGYF
<CostAmount>k__BackingField
AnJAlgU5ytv
<Amount>k__BackingField
TiPAlcpkrAc
<FactionID>k__BackingField
XqUAlJ2rQ6W
<BundleCode>k__BackingField
X1cAl1xhwfb
<LineType>k__BackingField
SbMAlrKcyYT
<CType>k__BackingField
hrMAlQQIrJu
<SubCode>k__BackingField
V9UAlFw3F7g
<TransactionID>k__BackingField
vemAlVjfJ9Q
<ActionType>k__BackingField
njMAlRQyQwI
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.PaymentResponse
AppTech.MSMS.Domain.Entities.PaymentResponse
PaymentResponse
PaymentResponse
P5TAlfifd17
set_RequestInfo
eMIAleTHDpp
<Status>k__BackingField
H67AlxJNK33
<Success>k__BackingField
ldKAlsIaCYw
<LineType>k__BackingField
X79AlOowRX6
<SubDetail>k__BackingField
NTmAlhaEVQj
<SubBalance>k__BackingField
V2HAl6Qn58W
<Message>k__BackingField
nDEAl4HIoPD
<ProviderResponse>k__BackingField
NxCAlmWYRLu
<RequestInfo>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.OfferQueryRequest
AppTech.MSMS.Domain.Entities.OfferQueryRequest
OfferQueryRequest
OfferQueryRequest
TiQAl7O8WPQ
<ServiceID>k__BackingField
LW7AlWcjV1A
<SNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.OfferQueryResponse
AppTech.MSMS.Domain.Entities.OfferQueryResponse
OfferQueryResponse
OfferQueryResponse
jAIAlnwPiNC
<Success>k__BackingField
gbNAlzbW0uu
<MSG>k__BackingField
Ma7ATGQdBf9
<SD>k__BackingField
wtqATABPY6l
<Offers>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.BagatRequest
AppTech.MSMS.Domain.Entities.BagatRequest
BagatRequest
BagatRequest
zfwATUpXMoc
<UN>k__BackingField
EYmATp2v77g
<PSS>k__BackingField
BeWAT9USYEs
<TKN>k__BackingField
LdnATiuOgod
<Ref>k__BackingField
oEDATIUvVIB
<SNO>k__BackingField
UimATHx83AO
<AMT>k__BackingField
cJmATuhOWgU
<SID>k__BackingField
QgwATNSjRhA
<FID>k__BackingField
ulVATDJwc1t
<LType>k__BackingField
E9IATt37Wjc
<Code>k__BackingField
iKgATdtbaSa
<Action>k__BackingField
IaZATSsX9Ej
<Channel>k__BackingField
uQ6ATknUj0x
<Note>k__BackingField
uhdATPjmfNm
<UID>k__BackingField
petATjhRd6q
<IncludePay>k__BackingField
OwHATavYVxY
<RecordID>k__BackingField
ML7ATYLdcN6
<Identifier>k__BackingField
YqKATwZnuYg
<OfferName>k__BackingField
oaUAT00MwNv
<Type>k__BackingField
II1ATqCEjiK
<State>k__BackingField
X6MATMNvIk5
<CostAmount>k__BackingField
AgDATXx6VQM
<Flag>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.BagatRespose
AppTech.MSMS.Domain.Entities.BagatRespose
BagatRespose
BagatRespose
xkUAToCgXcl
<Success>k__BackingField
lhCATL6vUI3
<Status>k__BackingField
SyEATZ8YAmK
<MSG>k__BackingField
GyJATKIx2oX
<TNO>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.OfferAction
AppTech.MSMS.Domain.Entities.OfferAction
OfferAction
OfferAction
<<type>>
AppTech.MSMS.Domain.Entities.PaymentType
AppTech.MSMS.Domain.Entities.PaymentType
PaymentType
PaymentType
<<type>>
AppTech.MSMS.Domain.Entities.QueryType
AppTech.MSMS.Domain.Entities.QueryType
QueryType
QueryType
<<type>>
AppTech.MSMS.Domain.Entities.QueryRequest
AppTech.MSMS.Domain.Entities.QueryRequest
QueryRequest
QueryRequest
QWvATymQf6H
<Query>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.QueryResponse
AppTech.MSMS.Domain.Entities.QueryResponse
QueryResponse
QueryResponse
v4fATvqcVpN
<Success>k__BackingField
KdgAT2xCcV0
<Message>k__BackingField
bnhAT8xV0Gj
<Result>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.QuotationResponse
AppTech.MSMS.Domain.Entities.QuotationResponse
QuotationResponse
QuotationResponse
TPcAT3s8K9q
Init
ICeATbLZ50n
_request
JanATEj8xJm
_session
SpAAT57a6PU
<Items>k__BackingField
hAfATBiXLnx
<Success>k__BackingField
qXgATlbgSio
<Message>k__BackingField
v02ATTLB1wl
<UnitPrice>k__BackingField
KGUATCbiBGZ
<Quantity>k__BackingField
xtrATgrr7H4
<Amount>k__BackingField
S0VATcYkTKd
<Commission>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.QuotationRequest
AppTech.MSMS.Domain.Entities.QuotationRequest
QuotationRequest
QuotationRequest
I4gATJKpXYt
<ServiceID>k__BackingField
eAVAT1RB5tv
<Type>k__BackingField
KywATrKtEaU
<FactionID>k__BackingField
ImKATQPKbSN
<Quantity>k__BackingField
dU4ATF0p8kL
<Amount>k__BackingField
EBpATVXAxIw
<Flag>k__BackingField
FoQATRErbfF
<Note>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Pair
AppTech.MSMS.Domain.Entities.Pair
Pair
Pair
cKgATff1JfD
<Key>k__BackingField
Fu2ATelCcMX
<Value>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.RegisterationInfo
AppTech.MSMS.Domain.Entities.RegisterationInfo
RegisterationInfo
RegisterationInfo
BllATxlUPoY
<Username>k__BackingField
WkqATsXF39K
<Password>k__BackingField
XrdATOKba81
<ClientName>k__BackingField
PX1AThncL9Y
<ShopName>k__BackingField
KcQAT6Kd7dA
<FcmToken>k__BackingField
FnjAT4Ocugj
<PhoneNumber>k__BackingField
iGQATmRfnNo
<Address>k__BackingField
hLLAT7Vq0AW
<Email>k__BackingField
jb0ATWE0xMY
<Image>k__BackingField
YBVATnC9LW9
<Device>k__BackingField
vZ6ATz4lvwa
<DeviceID>k__BackingField
l2ZACG7aGcJ
<Channel>k__BackingField
ainACApmKrZ
<ApiKey>k__BackingField
tQmACUV7QBl
<CardType>k__BackingField
odSACplc08Z
<CardNumber>k__BackingField
lZVAC9ienCT
<CardIssuePlace>k__BackingField
kKFACieUfri
<CardIssueDate>k__BackingField
ReEACIvtvPH
<DeviceInfo>k__BackingField
p3CACHBSCj4
<Flavor>k__BackingField
RibACuxeCCb
<ID>k__BackingField
hkMACN91oDg
<BranchID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.BalanceResponseInfo
AppTech.MSMS.Domain.Entities.BalanceResponseInfo
BalanceResponseInfo
BalanceResponseInfo
qvFACDY7FH6
<Success>k__BackingField
fYZACt54meb
<Balance>k__BackingField
RKoACdBvL2i
<Message>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.InqueryResponse
AppTech.MSMS.Domain.Entities.InqueryResponse
InqueryResponse
InqueryResponse
DkRACS5jpui
<Success>k__BackingField
TgEACklcjlG
<Message>k__BackingField
RLcACP6dryW
<Balance>k__BackingField
TTPACjoCOfd
<LineType>k__BackingField
LhJACaXaU4H
<Credit>k__BackingField
WoPACYGAMDu
<SubDetail>k__BackingField
kHrACw2kMT8
<Offers>k__BackingField
NcJAC08rOfo
<LoanStatus>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Offer
AppTech.MSMS.Domain.Entities.Offer
Offer
Offer
sawACqgaSA4
<ID>k__BackingField
dYEACMV82Pi
<Name>k__BackingField
aGUACXhNBYi
<StartDate>k__BackingField
bAcACojHMB8
<ExpireDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.AgentModel
AppTech.MSMS.Domain.Entities.AgentModel
AgentModel
AgentModel
PIOACLXPVNT
<ID>k__BackingField
S8qACZ98Q94
<Number>k__BackingField
rAEACKkyEEk
<Name>k__BackingField
CccACyhTnpZ
<AccountID>k__BackingField
PDwACvY7aOP
<PhoneNumber>k__BackingField
qPEAC2hVvya
<Address>k__BackingField
JQYAC8El8oW
<IsRemittancePoint>k__BackingField
E8wAC3QxW07
<RemittancePoint>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.Party
AppTech.MSMS.Domain.Entities.Party
Party
Party
nR6ACbgcbr2
Init
gKyACE3eOgW
ValidateClient
a3iAC5KLCDP
ValidateAgent
HbwACBDjyPl
ValidateDistributor
wh1AClKjGWC
ValidateBranch
h9AACTiaqT1
ValidateAdmin
enQACCa1EEe
ValidateMerchant
QcrACgViHE9
_unitOfWork
XDyACcydX3h
_userParty
rnVACJX6Bts
<Status>k__BackingField
HsVAC12cuRH
<ID>k__BackingField
onDACrsaY2l
<Number>k__BackingField
ScbACQjbcvw
<Name>k__BackingField
gm6ACF3TAAv
<Email>k__BackingField
xYsACVU12ql
<AccountID>k__BackingField
rKvACRkydti
<ParentAccountID>k__BackingField
cy1ACfEP1Zj
<IsChild>k__BackingField
QkoACerUdsU
<PhoneNumber>k__BackingField
Uc5ACxD8Fr6
<Address>k__BackingField
o4yACsvBjdJ
<IsPerson>k__BackingField
VgBACO77fyI
<IsWifiProvider>k__BackingField
c91AChjEX1I
<IsSatelliteProvider>k__BackingField
kGoAC6rrjt7
<IsCardProvider>k__BackingField
hMXAC45UnCd
<Parent>k__BackingField
<<type>>
AppTech.MSMS.Domain.Entities.MerchantModel
AppTech.MSMS.Domain.Entities.MerchantModel
MerchantModel
MerchantModel
kDUACmfCkA2
<ID>k__BackingField
IPEAC7Q2pT0
<Number>k__BackingField
FKsACW6gVA6
<Name>k__BackingField
SLAACnebnrL
<OwnerName>k__BackingField
pcFACzQspwa
<AccountID>k__BackingField
BIAAgGPGTAl
<PhoneNumber>k__BackingField
Y0UAgAlypGu
<Address>k__BackingField
<<type>>
AppTech.MSMS.Domain.Core.LicensedServices
AppTech.MSMS.Domain.Core.LicensedServices
LicensedServices
LicensedServices
rHEAgUEwdIs
<Sim>k__BackingField
VxdAgpR1DRg
<Gomala>k__BackingField
RoeAg9aDK8k
<Merchant>k__BackingField
TxYAgiDQQ4g
<Transfers>k__BackingField
<<type>>
AppTech.MSMS.Domain.YemenPostReference.billpaymentws
AppTech.MSMS.Domain.YemenPostReference.billpaymentws
billpaymentws
billpaymentws
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequest
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequest
ypPaymentRequest
ypPaymentRequest
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequestBody
AppTech.MSMS.Domain.YemenPostReference.ypPaymentRequestBody
ypPaymentRequestBody
ypPaymentRequestBody
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponse
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponse
ypPaymentResponse
ypPaymentResponse
<<type>>
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponseBody
AppTech.MSMS.Domain.YemenPostReference.ypPaymentResponseBody
ypPaymentResponseBody
ypPaymentResponseBody
<<type>>
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsChannel
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsChannel
billpaymentwsChannel
billpaymentwsChannel
<<type>>
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsClient
AppTech.MSMS.Domain.YemenPostReference.billpaymentwsClient
billpaymentwsClient
billpaymentwsClient
<<type>>
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.TadawulReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.TadawulReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.TadawulReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.ServiceReference1.paymenttransactionresponse
AppTech.MSMS.Domain.ServiceReference1.paymenttransactionresponse
paymenttransactionresponse
paymenttransactionresponse
usRAgIxwGFU
extensionDataField
f98AgH5CygV
response_codeField
EUvAguEgL8R
response_messageField
ufdAgNIkZGd
PropertyChanged
<<type>>
AppTech.MSMS.Domain.ServiceReference1.Ialbayanmtnclientservice
AppTech.MSMS.Domain.ServiceReference1.Ialbayanmtnclientservice
Ialbayanmtnclientservice
Ialbayanmtnclientservice
<<type>>
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceChannel
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
<<type>>
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceClient
AppTech.MSMS.Domain.ServiceReference1.IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
<<type>>
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLib
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.QulaidiServiceReference2.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.QulaidiServiceReference2.CSDServiceLinkLibClient
AppTech.MSMS.Domain.QulaidiServiceReference2.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.JuzaifaServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.JuzaifaServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.JuzaifaServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.ForMeServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.ForMeServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.ForMeServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.DerhimApiReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.DerhimApiReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.DerhimApiReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.BaAmerReference.IService
AppTech.MSMS.Domain.BaAmerReference.IService
IService
IService
<<type>>
AppTech.MSMS.Domain.BaAmerReference.IServiceChannel
AppTech.MSMS.Domain.BaAmerReference.IServiceChannel
IServiceChannel
IServiceChannel
<<type>>
AppTech.MSMS.Domain.BaAmerReference.ServiceClient
AppTech.MSMS.Domain.BaAmerReference.ServiceClient
ServiceClient
ServiceClient
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AtheerServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AtheerServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLib
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AtheerServiceReference2.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AtheerServiceReference2.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AtheerServiceReference2.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AlSareeaOnLineServiceReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.AlmutarebReference.IService
AppTech.MSMS.Domain.AlmutarebReference.IService
IService
IService
<<type>>
AppTech.MSMS.Domain.AlmutarebReference.IServiceChannel
AppTech.MSMS.Domain.AlmutarebReference.IServiceChannel
IServiceChannel
IServiceChannel
<<type>>
AppTech.MSMS.Domain.AlmutarebReference.ServiceClient
AppTech.MSMS.Domain.AlmutarebReference.ServiceClient
ServiceClient
ServiceClient
<<type>>
AppTech.MSMS.Domain.AlbayanReference.paymenttransactionresponse
AppTech.MSMS.Domain.AlbayanReference.paymenttransactionresponse
paymenttransactionresponse
paymenttransactionresponse
adxAgDroqg5
extensionDataField
lmdAgt8NcZn
response_codeField
i2qAgdqFM53
response_messageField
eYIAgSD2FK8
PropertyChanged
<<type>>
AppTech.MSMS.Domain.AlbayanReference.Ialbayanmtnclientservice
AppTech.MSMS.Domain.AlbayanReference.Ialbayanmtnclientservice
Ialbayanmtnclientservice
Ialbayanmtnclientservice
<<type>>
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceChannel
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
IalbayanmtnclientserviceChannel
<<type>>
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceClient
AppTech.MSMS.Domain.AlbayanReference.IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
IalbayanmtnclientserviceClient
<<type>>
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLib
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLib
ICSDServiceLinkLib
ICSDServiceLinkLib
<<type>>
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLibChannel
AppTech.MSMS.Domain.AbsiReference.ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
ICSDServiceLinkLibChannel
<<type>>
AppTech.MSMS.Domain.AbsiReference.CSDServiceLinkLibClient
AppTech.MSMS.Domain.AbsiReference.CSDServiceLinkLibClient
CSDServiceLinkLibClient
CSDServiceLinkLibClient
<<type>>
AppTech.MSMS.Domain.BaseModels.IEntry
AppTech.MSMS.Domain.BaseModels.IEntry
IEntry
IEntry
<<type>>
AppTech.MSMS.Domain.BaseModels.IPayment
AppTech.MSMS.Domain.BaseModels.IPayment
IPayment
IPayment
<<type>>
AppTech.MSMS.Domain.BaseModels.IRemittanceEntity
AppTech.MSMS.Domain.BaseModels.IRemittanceEntity
IRemittanceEntity
IRemittanceEntity
<<type>>
AppTech.MSMS.Domain.BaseModels.IDoubleEntry
AppTech.MSMS.Domain.BaseModels.IDoubleEntry
IDoubleEntry
IDoubleEntry
<<type>>
AppTech.MSMS.Domain.BaseModels.IAccountEntry
AppTech.MSMS.Domain.BaseModels.IAccountEntry
IAccountEntry
IAccountEntry
<<type>>
AppTech.MSMS.Domain.BaseModels.OrderDetail
AppTech.MSMS.Domain.BaseModels.OrderDetail
OrderDetail
OrderDetail
<<type>>
AppTech.MSMS.Domain.BaseModels.RemoteOrderDetail
AppTech.MSMS.Domain.BaseModels.RemoteOrderDetail
RemoteOrderDetail
RemoteOrderDetail
QVEAgkfrGUY
<TransactionID>k__BackingField
<<type>>
AppTech.MSMS.Domain.Attributes.Digits
AppTech.MSMS.Domain.Attributes.Digits
Digits
Digits
<<type>>
AppTech.MSMS.Domain.Attributes.Money
AppTech.MSMS.Domain.Attributes.Money
Money
Money
<<type>>
AppTech.MSMS.Domain.Attributes.MsDisplayNameAttribute
AppTech.MSMS.Domain.Attributes.MsDisplayNameAttribute
MsDisplayNameAttribute
MsDisplayNameAttribute
<<type>>
AppTech.MSMS.Domain.Attributes.MsRequired
AppTech.MSMS.Domain.Attributes.MsRequired
MsRequired
MsRequired
<<type>>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<PrivateImplementationDetails>
<<type>>
boSOx4oSVuWtSEF1hW.dd0IQEiPbKKjT68EHk/KpERFIBO49b2ha9WkM
Derhim.DerhimApi/DerhimServices
KpERFIBO49b2ha9WkM
DerhimServices
<<type>>
boSOx4oSVuWtSEF1hW.dd0IQEiPbKKjT68EHk/<InqueryAsync>d__19
Derhim.DerhimApi/<InqueryAsync>d__19
<InqueryAsync>d__19
<InqueryAsync>d__19
<<type>>
boSOx4oSVuWtSEF1hW.dd0IQEiPbKKjT68EHk/<MakeBagaAsyn>d__21
Derhim.DerhimApi/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
boSOx4oSVuWtSEF1hW.dd0IQEiPbKKjT68EHk/<PaymentAsync>d__23
Derhim.DerhimApi/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
boSOx4oSVuWtSEF1hW.dd0IQEiPbKKjT68EHk/<PaymentGomlaAsync>d__24
Derhim.DerhimApi/<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<<type>>
AppTech.MSMS.Domain.DomainManager/<>c
AppTech.MSMS.Domain.DomainManager/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAsync>d__3
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAsync>d__3
<ExecuteAsync>d__3
<ExecuteAsync>d__3
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteGomalaAsync>d__4
AppTech.MSMS.Domain.FrancyGateway/<ExecuteGomalaAsync>d__4
<ExecuteGomalaAsync>d__4
<ExecuteGomalaAsync>d__4
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteTopupAsync>d__5
AppTech.MSMS.Domain.FrancyGateway/<ExecuteTopupAsync>d__5
<ExecuteTopupAsync>d__5
<ExecuteTopupAsync>d__5
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteBagatAsync>d__6
AppTech.MSMS.Domain.FrancyGateway/<ExecuteBagatAsync>d__6
<ExecuteBagatAsync>d__6
<ExecuteBagatAsync>d__6
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<>c__DisplayClass7_0
AppTech.MSMS.Domain.FrancyGateway/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAdenNetAsync>d__7
AppTech.MSMS.Domain.FrancyGateway/<ExecuteAdenNetAsync>d__7
<ExecuteAdenNetAsync>d__7
<ExecuteAdenNetAsync>d__7
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalBagat>d__8
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalBagat>d__8
<ExecuteRiyalBagat>d__8
<ExecuteRiyalBagat>d__8
<<type>>
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalTopup>d__9
AppTech.MSMS.Domain.FrancyGateway/<ExecuteRiyalTopup>d__9
<ExecuteRiyalTopup>d__9
<ExecuteRiyalTopup>d__9
<<type>>
xrbAoJY5M4oL0cY2w2.d50nP9NxIDqJ0P8YUw/oAqp7drXm7KbKZNB3g
AppTech.MSMS.Domain.TestClass/TransType
oAqp7drXm7KbKZNB3g
TransType
<<type>>
xrbAoJY5M4oL0cY2w2.d50nP9NxIDqJ0P8YUw/CqlhlNwMcEC9HOZNDu
AppTech.MSMS.Domain.TestClass/Client
CqlhlNwMcEC9HOZNDu
Client
NVHAgPBEKM6
get_ID
NFFAgjHxv6R
set_ID
mE1Aga9JbsO
get_Name
UDsAgYV8LKR
set_Name
KlcAgwiXbEK
get_Code
y98Ag01Mctn
set_Code
G0eAgM092Vd
get_BaseUrl
d7pAgXT5hYt
set_BaseUrl
S7PAgLm5Unw
<ID>k__BackingField
YecAgZb8oAs
<Name>k__BackingField
T1nAgK4LYYX
<Code>k__BackingField
Cc9AgyxuOSu
<BaseUrl>k__BackingField
rOWAgqZtpft
Code
uhvAgoaZU61
BaseUrl
<<type>>
AppTech.MSMS.Domain.TestHelper/<>c__DisplayClass0_0
AppTech.MSMS.Domain.TestHelper/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_1
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_1
<>c__DisplayClass14_1
<>c__DisplayClass14_1
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_2
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_2
<>c__DisplayClass14_2
<>c__DisplayClass14_2
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_3
AppTech.MSMS.Domain.Updates.DbUpdater/<>c__DisplayClass14_3
<>c__DisplayClass14_3
<>c__DisplayClass14_3
<<type>>
AppTech.MSMS.Domain.Updates.DbUpdater/<>c
AppTech.MSMS.Domain.Updates.DbUpdater/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Topup.TopupFactory/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Topup.TopupFactory/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager/<>c
AppTech.MSMS.Domain.JournalEntries.JournalEntryManager/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_1
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c__DisplayClass17_1
<>c__DisplayClass17_1
<>c__DisplayClass17_1
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c
AppTech.MSMS.Domain.Providers.AlAssedApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<InqueryAsync>d__23
AppTech.MSMS.Domain.Providers.AlAssedApi/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Providers.AlAssedApi/<PaymentGomlaAsync>d__28
AppTech.MSMS.Domain.Providers.AlAssedApi/<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi/AlbayanServices
AppTech.MSMS.Domain.Providers.AlbayanApi/AlbayanServices
AlbayanServices
AlbayanServices
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi/<PaymentAsync>d__13
AppTech.MSMS.Domain.Providers.AlbayanApi/<PaymentAsync>d__13
<PaymentAsync>d__13
<PaymentAsync>d__13
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi/<MakeBagaAsyn>d__17
AppTech.MSMS.Domain.Providers.AlbayanApi/<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/AlbayanServices
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/AlbayanServices
AlbayanServices
AlbayanServices
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<PaymentAsync>d__12
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<PaymentAsync>d__12
<PaymentAsync>d__12
<PaymentAsync>d__12
<<type>>
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<MakeBagaAsyn>d__16
AppTech.MSMS.Domain.Providers.AlbayanApi_v2/<MakeBagaAsyn>d__16
<MakeBagaAsyn>d__16
<MakeBagaAsyn>d__16
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOffer
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOffer
AbsiOffer
AbsiOffer
JEYAgv0MP22
<offerId>k__BackingField
CtNAg2KUSqL
<offerName>k__BackingField
SawAg807vVj
<offerStartDate>k__BackingField
HsGAg3CnsLt
<offerEndDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOfferResponse
AppTech.MSMS.Domain.Providers.AlhashediApi/AbsiOfferResponse
AbsiOfferResponse
AbsiOfferResponse
Il0Agbk1T0h
<offers>k__BackingField
R47AgE8n7oQ
<resultCode>k__BackingField
Hf7Ag5slL05
<resultDesc>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentAsync>d__6
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentAsync>d__6
<PaymentAsync>d__6
<PaymentAsync>d__6
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentGomlaAsync>d__7
AppTech.MSMS.Domain.Providers.AlhashediApi/<PaymentGomlaAsync>d__7
<PaymentGomlaAsync>d__7
<PaymentGomlaAsync>d__7
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<InqueryAsync>d__18
AppTech.MSMS.Domain.Providers.AlhashediApi/<InqueryAsync>d__18
<InqueryAsync>d__18
<InqueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<MakeBagaAsyn>d__23
AppTech.MSMS.Domain.Providers.AlhashediApi/<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_0
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_0
<>c__DisplayClass29_0
<>c__DisplayClass29_0
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_1
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c__DisplayClass29_1
<>c__DisplayClass29_1
<>c__DisplayClass29_1
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c
AppTech.MSMS.Domain.Providers.AlhashediApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.AlhashediApi/<QueryYmOffersAsync>d__31
AppTech.MSMS.Domain.Providers.AlhashediApi/<QueryYmOffersAsync>d__31
<QueryYmOffersAsync>d__31
<QueryYmOffersAsync>d__31
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/EasyConnectServices
AppTech.MSMS.Domain.Providers.EasyConnectAPI/EasyConnectServices
EasyConnectServices
EasyConnectServices
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentGomlaAsync>d__18
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentGomlaAsync>d__18
<PaymentGomlaAsync>d__18
<PaymentGomlaAsync>d__18
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentAsync>d__19
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<MakeBagaAsyn>d__20
AppTech.MSMS.Domain.Providers.EasyConnectAPI/<MakeBagaAsyn>d__20
<MakeBagaAsyn>d__20
<MakeBagaAsyn>d__20
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/EasySIMServices
AppTech.MSMS.Domain.Providers.EasySIM_YRM/EasySIMServices
EasySIMServices
EasySIMServices
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentGomlaAsync>d__16
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentGomlaAsync>d__16
<PaymentGomlaAsync>d__16
<PaymentGomlaAsync>d__16
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentAsync>d__17
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<PaymentAsync>d__17
<PaymentAsync>d__17
<PaymentAsync>d__17
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<MakeBagaAsyn>d__18
AppTech.MSMS.Domain.Providers.EasySIM_YRM/<MakeBagaAsyn>d__18
<MakeBagaAsyn>d__18
<MakeBagaAsyn>d__18
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/EasySIMServices
AppTech.MSMS.Domain.Providers.GMaxApi/EasySIMServices
EasySIMServices
EasySIMServices
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentGomlaAsync>d__21
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentAsync>d__22
AppTech.MSMS.Domain.Providers.GMaxApi/<PaymentAsync>d__22
<PaymentAsync>d__22
<PaymentAsync>d__22
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<InqueryAsync>d__24
AppTech.MSMS.Domain.Providers.GMaxApi/<InqueryAsync>d__24
<InqueryAsync>d__24
<InqueryAsync>d__24
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_0
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_0
<>c__DisplayClass26_0
<>c__DisplayClass26_0
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_1
AppTech.MSMS.Domain.Providers.GMaxApi/<>c__DisplayClass26_1
<>c__DisplayClass26_1
<>c__DisplayClass26_1
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<>c
AppTech.MSMS.Domain.Providers.GMaxApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.GMaxApi/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Providers.GMaxApi/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/EasySIMServices
AppTech.MSMS.Domain.Providers.EasySIM/EasySIMServices
EasySIMServices
EasySIMServices
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentGomlaAsync>d__15
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentGomlaAsync>d__15
<PaymentGomlaAsync>d__15
<PaymentGomlaAsync>d__15
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentAsync>d__16
AppTech.MSMS.Domain.Providers.EasySIM/<PaymentAsync>d__16
<PaymentAsync>d__16
<PaymentAsync>d__16
<<type>>
AppTech.MSMS.Domain.Providers.EasySIM/<MakeBagaAsyn>d__17
AppTech.MSMS.Domain.Providers.EasySIM/<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOffer
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOffer
AbsiOffer
AbsiOffer
SubAgBWQW4e
<offerId>k__BackingField
B91AglIcFhg
<offerName>k__BackingField
cW2AgTIXZBi
<offerStartDate>k__BackingField
L9sAgCNqpFP
<offerEndDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOfferResponse
AppTech.MSMS.Domain.Providers.AbuOsamaApi/AbsiOfferResponse
AbsiOfferResponse
AbsiOfferResponse
mxQAggYSgQq
<offers>k__BackingField
zv3AgcCDIbF
<resultCode>k__BackingField
YTfAgJwhlRQ
<resultDesc>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<MakeBagaAsyn>d__22
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<PaymentAsync>d__32
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<PaymentAsync>d__32
<PaymentAsync>d__32
<PaymentAsync>d__32
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<InqueryAsync>d__33
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<InqueryAsync>d__33
<InqueryAsync>d__33
<InqueryAsync>d__33
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<BalanceQueryAsync>d__34
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<<type>>
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<QueryYmOffersAsync>d__35
AppTech.MSMS.Domain.Providers.AbuOsamaApi/<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentAsync>d__11
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentAsync>d__11
<PaymentAsync>d__11
<PaymentAsync>d__11
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<QueryAsync>d__13
AppTech.MSMS.Domain.Providers.AlConApi/<QueryAsync>d__13
<QueryAsync>d__13
<QueryAsync>d__13
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<InqueryAsync>d__18
AppTech.MSMS.Domain.Providers.AlConApi/<InqueryAsync>d__18
<InqueryAsync>d__18
<InqueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentGomlaAsync>d__21
AppTech.MSMS.Domain.Providers.AlConApi/<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<PaymentGomlaAsync>d__21
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<MakeBagaAsyn>d__22
AppTech.MSMS.Domain.Providers.AlConApi/<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_0
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_0
<>c__DisplayClass27_0
<>c__DisplayClass27_0
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_1
AppTech.MSMS.Domain.Providers.AlConApi/<>c__DisplayClass27_1
<>c__DisplayClass27_1
<>c__DisplayClass27_1
<<type>>
AppTech.MSMS.Domain.Providers.AlConApi/<>c
AppTech.MSMS.Domain.Providers.AlConApi/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI/<GetRequestResponseAsync>d__12
AppTech.MSMS.Domain.Providers.RialMobileAPI/<GetRequestResponseAsync>d__12
<GetRequestResponseAsync>d__12
<GetRequestResponseAsync>d__12
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI/<InqueryAsync>d__19
AppTech.MSMS.Domain.Providers.RialMobileAPI/<InqueryAsync>d__19
<InqueryAsync>d__19
<InqueryAsync>d__19
<<type>>
AppTech.MSMS.Domain.Providers.RialMobileAPI/<PaymentAsync>d__23
AppTech.MSMS.Domain.Providers.RialMobileAPI/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOffer
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOffer
AbsiOffer
AbsiOffer
UAZAg1v5Psb
<offerId>k__BackingField
pXIAgrKCmNT
<offerName>k__BackingField
OTsAgQxo29u
<offerStartDate>k__BackingField
QQPAgFk3asw
<offerEndDate>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOfferResponse
AppTech.MSMS.Domain.Providers.SharabiApi/AbsiOfferResponse
AbsiOfferResponse
AbsiOfferResponse
LXbAgV3pj6v
<offers>k__BackingField
Y1iAgR29fn1
<resultCode>k__BackingField
NMvAgfaFaN7
<resultDesc>k__BackingField
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<MakeBagaAsyn>d__22
AppTech.MSMS.Domain.Providers.SharabiApi/<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<MakeBagaAsyn>d__22
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<PaymentAsync>d__32
AppTech.MSMS.Domain.Providers.SharabiApi/<PaymentAsync>d__32
<PaymentAsync>d__32
<PaymentAsync>d__32
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<InqueryAsync>d__33
AppTech.MSMS.Domain.Providers.SharabiApi/<InqueryAsync>d__33
<InqueryAsync>d__33
<InqueryAsync>d__33
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<BalanceQueryAsync>d__34
AppTech.MSMS.Domain.Providers.SharabiApi/<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<BalanceQueryAsync>d__34
<<type>>
AppTech.MSMS.Domain.Providers.SharabiApi/<QueryYmOffersAsync>d__35
AppTech.MSMS.Domain.Providers.SharabiApi/<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<QueryYmOffersAsync>d__35
<<type>>
AppTech.MSMS.Domain.Providers.TopupProviderApi/<QueryAsync>d__34
AppTech.MSMS.Domain.Providers.TopupProviderApi/<QueryAsync>d__34
<QueryAsync>d__34
<QueryAsync>d__34
<<type>>
AppTech.MSMS.Domain.Providers.TopupProviderApi/<>c__DisplayClass40_0
AppTech.MSMS.Domain.Providers.TopupProviderApi/<>c__DisplayClass40_0
<>c__DisplayClass40_0
<>c__DisplayClass40_0
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakePaymentAync>d__3
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakePaymentAync>d__3
<MakePaymentAync>d__3
<MakePaymentAync>d__3
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeOfferAsync>d__5
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeOfferAsync>d__5
<MakeOfferAsync>d__5
<MakeOfferAsync>d__5
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeGomlaAync>d__6
AppTech.MSMS.Domain.Providers.TopupProccessor/<MakeGomlaAync>d__6
<MakeGomlaAync>d__6
<MakeGomlaAync>d__6
<<type>>
AppTech.MSMS.Domain.Providers.TopupProccessor/<QueryAsync>d__7
AppTech.MSMS.Domain.Providers.TopupProccessor/<QueryAsync>d__7
<QueryAsync>d__7
<QueryAsync>d__7
<<type>>
b8wfMLkWmGBXutxot0.pMZfVOuJwUk6jfHyEx/<InqueryAsync>d__15
AppTech.MSMS.Domain.Topuping.AlanwarAPI/<InqueryAsync>d__15
<InqueryAsync>d__15
<InqueryAsync>d__15
<<type>>
b8wfMLkWmGBXutxot0.pMZfVOuJwUk6jfHyEx/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.AlanwarAPI/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<PaymentAsync>d__6
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<PaymentAsync>d__6
<PaymentAsync>d__6
<PaymentAsync>d__6
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<InqueryAsync>d__12
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<InqueryAsync>d__12
<InqueryAsync>d__12
<InqueryAsync>d__12
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<MakeBagaAsyn>d__17
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<MakeBagaAsyn>d__17
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<BalanceQueryAsync>d__25
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<BalanceQueryAsync>d__25
<BalanceQueryAsync>d__25
<BalanceQueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<QueryYmOffersAsync>d__26
AppTech.MSMS.Domain.Topuping.AlGumaiiApi/<QueryYmOffersAsync>d__26
<QueryYmOffersAsync>d__26
<QueryYmOffersAsync>d__26
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/AlmoheetServices
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/AlmoheetServices
AlmoheetServices
AlmoheetServices
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PostRequestAsync>d__15
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PostRequestAsync>d__15
<PostRequestAsync>d__15
<PostRequestAsync>d__15
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<InqueryAsync>d__24
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<InqueryAsync>d__24
<InqueryAsync>d__24
<InqueryAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<MakeBagaAsyn>d__27
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<MakeBagaAsyn>d__27
<MakeBagaAsyn>d__27
<MakeBagaAsyn>d__27
<<type>>
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PaymentAsync>d__30
AppTech.MSMS.Domain.Topuping.AlmoheetAPI/<PaymentAsync>d__30
<PaymentAsync>d__30
<PaymentAsync>d__30
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<QueryAsync>d__21
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<QueryAsync>d__21
<QueryAsync>d__21
<QueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__23
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__25
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<InqueryAsync>d__25
<InqueryAsync>d__25
<InqueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Topuping.AlMutarrebApi/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<QueryAsync>d__21
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<QueryAsync>d__21
<QueryAsync>d__21
<QueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__23
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__25
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<InqueryAsync>d__25
<InqueryAsync>d__25
<InqueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Topuping.AlmutarrebAPI_v2/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi/activeSIMType_IDs
AppTech.MSMS.Domain.Topuping.AlSareeaOnLineApi/activeSIMType_IDs
activeSIMType_IDs
activeSIMType_IDs
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi_v2/<MakeBagaAsyn>d__2
AppTech.MSMS.Domain.Topuping.AppTechApi_v2/<MakeBagaAsyn>d__2
<MakeBagaAsyn>d__2
<MakeBagaAsyn>d__2
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<QueryAsync>d__18
AppTech.MSMS.Domain.Topuping.AtheerApi/<QueryAsync>d__18
<QueryAsync>d__18
<QueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<MakeBagaAsyn>d__19
AppTech.MSMS.Domain.Topuping.AtheerApi/<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentAsync>d__23
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentGomlaAsync>d__24
AppTech.MSMS.Domain.Topuping.AtheerApi/<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi/<InqueryAsync>d__28
AppTech.MSMS.Domain.Topuping.AtheerApi/<InqueryAsync>d__28
<InqueryAsync>d__28
<InqueryAsync>d__28
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_South/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<InqueryAsync>d__20
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<InqueryAsync>d__20
<InqueryAsync>d__20
<InqueryAsync>d__20
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<MakeBagaAsyn>d__21
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentAsync>d__25
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentAsync>d__25
<PaymentAsync>d__25
<PaymentAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentGomlaAsync>d__26
AppTech.MSMS.Domain.Topuping.AtheerApi_South/<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<InqueryAsync>d__20
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<InqueryAsync>d__20
<InqueryAsync>d__20
<InqueryAsync>d__20
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<MakeBagaAsyn>d__21
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentAsync>d__25
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentAsync>d__25
<PaymentAsync>d__25
<PaymentAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentGomlaAsync>d__26
AppTech.MSMS.Domain.Topuping.AtheerApi_South_v2/<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<PaymentGomlaAsync>d__26
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<InqueryAsync>d__18
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<InqueryAsync>d__18
<InqueryAsync>d__18
<InqueryAsync>d__18
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<MakeBagaAsyn>d__19
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<MakeBagaAsyn>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentAsync>d__23
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentAsync>d__23
<PaymentAsync>d__23
<PaymentAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentGomlaAsync>d__24
AppTech.MSMS.Domain.Topuping.AtheerApi_v2/<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<PaymentGomlaAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/AtheerServices
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<InqueryAsync>d__22
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<InqueryAsync>d__22
<InqueryAsync>d__22
<InqueryAsync>d__22
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<MakeBagaAsyn>d__23
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<MakeBagaAsyn>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentAsync>d__27
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentAsync>d__27
<PaymentAsync>d__27
<PaymentAsync>d__27
<<type>>
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentGomlaAsync>d__28
AppTech.MSMS.Domain.Topuping.AtheerApi_v3/<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<PaymentGomlaAsync>d__28
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Topuping.BaAmerApi/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.BaAmerApi/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<QueryAsync>d__21
AppTech.MSMS.Domain.Topuping.BaAmerApi/<QueryAsync>d__21
<QueryAsync>d__21
<QueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__23
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__23
<InqueryAsync>d__23
<InqueryAsync>d__23
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__25
AppTech.MSMS.Domain.Topuping.BaAmerApi/<InqueryAsync>d__25
<InqueryAsync>d__25
<InqueryAsync>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.BaAmerApi/<MakeBagaAsyn>d__29
AppTech.MSMS.Domain.Topuping.BaAmerApi/<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<MakeBagaAsyn>d__29
<<type>>
AppTech.MSMS.Domain.Topuping.FrenchiApi/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Topuping.FrenchiApi/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/CSDServices
AppTech.MSMS.Domain.Topuping.ForMeAPI/CSDServices
CSDServices
CSDServices
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentGomlaAsync>d__17
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentGomlaAsync>d__17
<PaymentGomlaAsync>d__17
<PaymentGomlaAsync>d__17
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentAsync>d__24
AppTech.MSMS.Domain.Topuping.ForMeAPI/<PaymentAsync>d__24
<PaymentAsync>d__24
<PaymentAsync>d__24
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<MakeBagaAsyn>d__25
AppTech.MSMS.Domain.Topuping.ForMeAPI/<MakeBagaAsyn>d__25
<MakeBagaAsyn>d__25
<MakeBagaAsyn>d__25
<<type>>
AppTech.MSMS.Domain.Topuping.ForMeAPI/<InqueryAsync>d__26
AppTech.MSMS.Domain.Topuping.ForMeAPI/<InqueryAsync>d__26
<InqueryAsync>d__26
<InqueryAsync>d__26
<<type>>
oqGKTHOyseFvcT5oIk.t6dxjs7yyR0waOpuFa/EExyGgHGpjWlr85aGp
AppTech.MSMS.Domain.Topuping.JuzaifaAPI/JuzaifaServices
EExyGgHGpjWlr85aGp
JuzaifaServices
<<type>>
oqGKTHOyseFvcT5oIk.t6dxjs7yyR0waOpuFa/<PaymentAsync>d__22
AppTech.MSMS.Domain.Topuping.JuzaifaAPI/<PaymentAsync>d__22
<PaymentAsync>d__22
<PaymentAsync>d__22
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentAsync>d__7
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentAsync>d__7
<PaymentAsync>d__7
<PaymentAsync>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentGomlaAsync>d__9
AppTech.MSMS.Domain.Topuping.MainCenterApi/<PaymentGomlaAsync>d__9
<PaymentGomlaAsync>d__9
<PaymentGomlaAsync>d__9
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<MakeBagaAsyn>d__12
AppTech.MSMS.Domain.Topuping.MainCenterApi/<MakeBagaAsyn>d__12
<MakeBagaAsyn>d__12
<MakeBagaAsyn>d__12
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<QueryAsync>d__15
AppTech.MSMS.Domain.Topuping.MainCenterApi/<QueryAsync>d__15
<QueryAsync>d__15
<QueryAsync>d__15
<<type>>
AppTech.MSMS.Domain.Topuping.MainCenterApi/<InqueryAsync>d__21
AppTech.MSMS.Domain.Topuping.MainCenterApi/<InqueryAsync>d__21
<InqueryAsync>d__21
<InqueryAsync>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentAsync>d__4
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentAsync>d__4
<PaymentAsync>d__4
<PaymentAsync>d__4
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentGomlaAsync>d__5
AppTech.MSMS.Domain.Topuping.AppTechApi/<PaymentGomlaAsync>d__5
<PaymentGomlaAsync>d__5
<PaymentGomlaAsync>d__5
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<MakeBagaAsyn>d__6
AppTech.MSMS.Domain.Topuping.AppTechApi/<MakeBagaAsyn>d__6
<MakeBagaAsyn>d__6
<MakeBagaAsyn>d__6
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<QueryAsync>d__7
AppTech.MSMS.Domain.Topuping.AppTechApi/<QueryAsync>d__7
<QueryAsync>d__7
<QueryAsync>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.AppTechApi/<InqueryAsync>d__8
AppTech.MSMS.Domain.Topuping.AppTechApi/<InqueryAsync>d__8
<InqueryAsync>d__8
<InqueryAsync>d__8
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi/AtheerServices
AppTech.MSMS.Domain.Topuping.OnsSoftApi/AtheerServices
AtheerServices
AtheerServices
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<MakeBagaAsyn>d__15
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<MakeBagaAsyn>d__15
<MakeBagaAsyn>d__15
<MakeBagaAsyn>d__15
<<type>>
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<PaymentAsync>d__17
AppTech.MSMS.Domain.Topuping.OnsSoftApi/<PaymentAsync>d__17
<PaymentAsync>d__17
<PaymentAsync>d__17
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<InqueryAsync>d__6
AppTech.MSMS.Domain.Topuping.QulaidiApi/<InqueryAsync>d__6
<InqueryAsync>d__6
<InqueryAsync>d__6
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<MakeBagaAsyn>d__7
AppTech.MSMS.Domain.Topuping.QulaidiApi/<MakeBagaAsyn>d__7
<MakeBagaAsyn>d__7
<MakeBagaAsyn>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentAsync>d__10
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentAsync>d__10
<PaymentAsync>d__10
<PaymentAsync>d__10
<<type>>
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentGomlaAsync>d__11
AppTech.MSMS.Domain.Topuping.QulaidiApi/<PaymentGomlaAsync>d__11
<PaymentGomlaAsync>d__11
<PaymentGomlaAsync>d__11
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulApi/<PaymentAsync>d__19
AppTech.MSMS.Domain.Topuping.TadawulApi/<PaymentAsync>d__19
<PaymentAsync>d__19
<PaymentAsync>d__19
<<type>>
AppTech.MSMS.Domain.Topuping.TadawulApi/<MakeBagaAsyn>d__21
AppTech.MSMS.Domain.Topuping.TadawulApi/<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<MakeBagaAsyn>d__21
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryAsync>d__5
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryAsync>d__5
<QueryAsync>d__5
<QueryAsync>d__5
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryYmBalAndOffers>d__7
AppTech.MSMS.Domain.Topuping.TopupHelper/<QueryYmBalAndOffers>d__7
<QueryYmBalAndOffers>d__7
<QueryYmBalAndOffers>d__7
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_1
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass9_1
<>c__DisplayClass9_1
<>c__DisplayClass9_1
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_1
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass10_1
<>c__DisplayClass10_1
<>c__DisplayClass10_1
<<type>>
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Topuping.TopupHelper/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/YemenPostServices
AppTech.MSMS.Domain.Topuping.YemenPostAPI/YemenPostServices
YemenPostServices
YemenPostServices
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<QueryAsync>d__31
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<QueryAsync>d__31
<QueryAsync>d__31
<QueryAsync>d__31
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<MakeBagaAsyn>d__34
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<MakeBagaAsyn>d__34
<MakeBagaAsyn>d__34
<MakeBagaAsyn>d__34
<<type>>
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<PaymentAsync>d__38
AppTech.MSMS.Domain.Topuping.YemenPostAPI/<PaymentAsync>d__38
<PaymentAsync>d__38
<PaymentAsync>d__38
<<type>>
AppTech.MSMS.Domain.Topuping.YemenSaeedApi/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Topuping.YemenSaeedApi/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_1
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c__DisplayClass0_1
<>c__DisplayClass0_1
<>c__DisplayClass0_1
<<type>>
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c
AppTech.MSMS.Domain.Topuping.Helper.TopupClosure/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Sync.EbsAccountService/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c
AppTech.MSMS.Domain.Sync.RemittanceSync/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Sync.SyncJournalManager/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Sync.SyncJournalManager/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass16_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass16_0
<>c__DisplayClass16_0
<>c__DisplayClass16_0
<<type>>
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Sessions.LoginService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_1
AppTech.MSMS.Domain.Sessions.AgentSession/<>c__DisplayClass5_1
<>c__DisplayClass5_1
<>c__DisplayClass5_1
<<type>>
AppTech.MSMS.Domain.Sessions.MerchantSession/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Sessions.MerchantSession/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Rest.GenericApi/<>c__1`1
AppTech.MSMS.Domain.Rest.GenericApi/<>c__1`1
<>c__1`1
<>c__1`1
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<>c
AppTech.MSMS.Domain.Rest.ApiRequest/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<PostAsync>d__16
AppTech.MSMS.Domain.Rest.ApiRequest/<PostAsync>d__16
<PostAsync>d__16
<PostAsync>d__16
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__18
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__18
<GetAysnc>d__18
<GetAysnc>d__18
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__19
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__19
<GetAysnc>d__19
<GetAysnc>d__19
<<type>>
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__25
AppTech.MSMS.Domain.Rest.ApiRequest/<GetAysnc>d__25
<GetAysnc>d__25
<GetAysnc>d__25
<<type>>
AppTech.MSMS.Domain.Reports.AccountReport/<>c
AppTech.MSMS.Domain.Reports.AccountReport/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Reports.AgentsReport/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Reports.AgentsReport/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Reports.PointsBalanceReport/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Reports.PointsBalanceReport/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Reports.WalletReport/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Reports.WalletReport/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Reports.TopupReport/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Reports.TopupReport/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Reports.TopupReport/<>c
AppTech.MSMS.Domain.Reports.TopupReport/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Reports.TransactionReport/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Reports.TransactionReport/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Reports.BalanceSheetReport/BalanceSheet
AppTech.MSMS.Domain.Reports.BalanceSheetReport/BalanceSheet
BalanceSheet
BalanceSheet
jYtAgeIXSoL
<ParentID>k__BackingField
hHUAgxG9U6Q
<AccountID>k__BackingField
mtWAgsrZsAZ
<CurrencyID>k__BackingField
TJtAgOJwmav
<VoucherID>k__BackingField
yR9Agh9ojkA
<Type>k__BackingField
fTXAg6VXwki
<NoPrevBalance>k__BackingField
H2JAg4ljE5W
<GroupbyVoucher>k__BackingField
SLuAgmlsTRl
<BalanceState>k__BackingField
M8GAg7K8mOW
<TotalDain>k__BackingField
YNeAgWDVnyC
<IsCredited>k__BackingField
yP1AgnQOIGv
<Balance>k__BackingField
<<type>>
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService/<>c
AppTech.MSMS.Domain.Remittances.SyncRemittanceOutService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Remittances.Intilization.AccountBindService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Remittances.Intilization.ExchangerTargetService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Notifications.Firebase/<Notify>d__0
AppTech.MSMS.Domain.Notifications.Firebase/<Notify>d__0
<Notify>d__0
<Notify>d__0
<<type>>
AppTech.MSMS.Domain.Notifications.Firebase/<Fire>d__1
AppTech.MSMS.Domain.Notifications.Firebase/<Fire>d__1
<Fire>d__1
<Fire>d__1
<<type>>
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyTopicAsync>d__3
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyTopicAsync>d__3
<NotifyTopicAsync>d__3
<NotifyTopicAsync>d__3
<<type>>
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyAsync>d__4
AppTech.MSMS.Domain.Notifications.FmcProvider/<NotifyAsync>d__4
<NotifyAsync>d__4
<NotifyAsync>d__4
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c
AppTech.MSMS.Domain.Services.DistributorService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_0
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_0
<>c__DisplayClass22_0
<>c__DisplayClass22_0
<<type>>
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_1
AppTech.MSMS.Domain.Services.DistributorService/<>c__DisplayClass22_1
<>c__DisplayClass22_1
<>c__DisplayClass22_1
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c
AppTech.MSMS.Domain.Services.CurrencyRateAccountService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AsyncBagatService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.AsyncBagatService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.AsyncBagatService/<ExecuteAsync>d__0
AppTech.MSMS.Domain.Services.AsyncBagatService/<ExecuteAsync>d__0
<ExecuteAsync>d__0
<ExecuteAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteAsync>d__0
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteAsync>d__0
<ExecuteAsync>d__0
<ExecuteAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ProccessByProviderAsync>d__2
AppTech.MSMS.Domain.Services.AsyncTopupService/<ProccessByProviderAsync>d__2
<ProccessByProviderAsync>d__2
<ProccessByProviderAsync>d__2
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteTopupAsync>d__3
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteTopupAsync>d__3
<ExecuteTopupAsync>d__3
<ExecuteTopupAsync>d__3
<<type>>
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteBagatAsync>d__4
AppTech.MSMS.Domain.Services.AsyncTopupService/<ExecuteBagatAsync>d__4
<ExecuteBagatAsync>d__4
<ExecuteBagatAsync>d__4
<<type>>
AppTech.MSMS.Domain.Services.BankDepositService/<>c
AppTech.MSMS.Domain.Services.BankDepositService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ConsumeInvoiceService/<>c
AppTech.MSMS.Domain.Services.ConsumeInvoiceService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.DeviceService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.DeviceService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.DeviceService/<>c
AppTech.MSMS.Domain.Services.DeviceService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ExchangerCommissionService/<>c
AppTech.MSMS.Domain.Services.ExchangerCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService
AppTech.MSMS.Domain.Services.GroupService/GroupItemService
GroupItemService
GroupItemService
<<type>>
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.GroupService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.PartyService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.PartyService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.ProviderCommissionService/<>c
AppTech.MSMS.Domain.Services.ProviderCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.SyncRemittanceInService/<>c
AppTech.MSMS.Domain.Services.SyncRemittanceInService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_1
AppTech.MSMS.Domain.Services.CommissionReceiptService/<>c__DisplayClass5_1
<>c__DisplayClass5_1
<>c__DisplayClass5_1
<<type>>
AppTech.MSMS.Domain.Services.GsmService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.GsmService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c
AppTech.MSMS.Domain.Services.TransferCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TransferOutService/<>c
AppTech.MSMS.Domain.Services.TransferOutService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TransportOrderService/<>c
AppTech.MSMS.Domain.Services.TransportOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.UserDeviceService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.UserDeviceService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_1
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass6_1
<>c__DisplayClass6_1
<>c__DisplayClass6_1
<<type>>
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.WifiCardService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_1
AppTech.MSMS.Domain.Services.WifiFactionService/<>c__DisplayClass12_1
<>c__DisplayClass12_1
<>c__DisplayClass12_1
<<type>>
AppTech.MSMS.Domain.Services.WifiFactionService/<>c
AppTech.MSMS.Domain.Services.WifiFactionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.BuyCurrencyService/<>c
AppTech.MSMS.Domain.Services.BuyCurrencyService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.LoanOrderService/<>c
AppTech.MSMS.Domain.Services.LoanOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.LoanOrderService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.LoanOrderService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass16_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass16_0
<>c__DisplayClass16_0
<>c__DisplayClass16_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_0
<>c__DisplayClass18_0
<>c__DisplayClass18_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_1
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass18_1
<>c__DisplayClass18_1
<>c__DisplayClass18_1
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass23_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass23_0
<>c__DisplayClass23_0
<>c__DisplayClass23_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass24_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass24_0
<>c__DisplayClass24_0
<>c__DisplayClass24_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass32_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass32_0
<>c__DisplayClass32_0
<>c__DisplayClass32_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c
AppTech.MSMS.Domain.Services.AccountService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass39_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass39_0
<>c__DisplayClass39_0
<>c__DisplayClass39_0
<<type>>
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass42_0
AppTech.MSMS.Domain.Services.AccountService/<>c__DisplayClass42_0
<>c__DisplayClass42_0
<>c__DisplayClass42_0
<<type>>
AppTech.MSMS.Domain.Services.AccountSlatingService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.AccountSlatingService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c
AppTech.MSMS.Domain.Services.AgentService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass22_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass22_0
<>c__DisplayClass22_0
<>c__DisplayClass22_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_0
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_0
<>c__DisplayClass23_0
<>c__DisplayClass23_0
<<type>>
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_1
AppTech.MSMS.Domain.Services.AgentService/<>c__DisplayClass23_1
<>c__DisplayClass23_1
<>c__DisplayClass23_1
<<type>>
AppTech.MSMS.Domain.Services.AgentPointUserService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.AgentPointUserService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_0
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_0
<>c__DisplayClass16_0
<>c__DisplayClass16_0
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_1
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_1
<>c__DisplayClass16_1
<>c__DisplayClass16_1
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_2
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_2
<>c__DisplayClass16_2
<>c__DisplayClass16_2
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_3
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass16_3
<>c__DisplayClass16_3
<>c__DisplayClass16_3
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_1
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_1
<>c__DisplayClass17_1
<>c__DisplayClass17_1
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_2
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_2
<>c__DisplayClass17_2
<>c__DisplayClass17_2
<<type>>
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_3
AppTech.MSMS.Domain.Services.ExternalBranchService/<>c__DisplayClass17_3
<>c__DisplayClass17_3
<>c__DisplayClass17_3
<<type>>
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.FundUserService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c
AppTech.MSMS.Domain.Services.ClientService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_0
<>c__DisplayClass37_0
<>c__DisplayClass37_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_1
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass37_1
<>c__DisplayClass37_1
<>c__DisplayClass37_1
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_0
<>c__DisplayClass38_0
<>c__DisplayClass38_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_1
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass38_1
<>c__DisplayClass38_1
<>c__DisplayClass38_1
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_0
<>c__DisplayClass39_0
<>c__DisplayClass39_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_1
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass39_1
<>c__DisplayClass39_1
<>c__DisplayClass39_1
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass41_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass41_0
<>c__DisplayClass41_0
<>c__DisplayClass41_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass42_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass42_0
<>c__DisplayClass42_0
<>c__DisplayClass42_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass43_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass43_0
<>c__DisplayClass43_0
<>c__DisplayClass43_0
<<type>>
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass44_0
AppTech.MSMS.Domain.Services.ClientService/<>c__DisplayClass44_0
<>c__DisplayClass44_0
<>c__DisplayClass44_0
<<type>>
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.Notification/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.OfferOrderService/<>c
AppTech.MSMS.Domain.Services.OfferOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.SaleCurrencyService/<>c
AppTech.MSMS.Domain.Services.SaleCurrencyService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_1
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass8_1
<>c__DisplayClass8_1
<>c__DisplayClass8_1
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.ServiceClaimService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.CurrencyExchangeService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.CurrencyExchangeService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.CurrencyRateService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.CurrencyRateService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.DepositOrderService/<>c
AppTech.MSMS.Domain.Services.DepositOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.JournalService/<>c
AppTech.MSMS.Domain.Services.JournalService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.JournalService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.JournalEntryService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.JournalEntryService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.BasicFactionService/<>c
AppTech.MSMS.Domain.Services.BasicFactionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.FactionService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass13_0
AppTech.MSMS.Domain.Services.FundService/<>c__DisplayClass13_0
<>c__DisplayClass13_0
<>c__DisplayClass13_0
<<type>>
AppTech.MSMS.Domain.Services.MerchantService/<>c
AppTech.MSMS.Domain.Services.MerchantService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.MerchantPaymentService/MerPayResult
AppTech.MSMS.Domain.Services.MerchantPaymentService/MerPayResult
MerPayResult
MerPayResult
BKwAgz3xDHx
<ID>k__BackingField
dEnAcGCEd9i
<Number>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.TopupNetworkService/Networks
AppTech.MSMS.Domain.Services.TopupNetworkService/Networks
Networks
Networks
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass19_0
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass19_0
<>c__DisplayClass19_0
<>c__DisplayClass19_0
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass36_0
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass36_0
<>c__DisplayClass36_0
<>c__DisplayClass36_0
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_0
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_0
<>c__DisplayClass40_0
<>c__DisplayClass40_0
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_1
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_1
<>c__DisplayClass40_1
<>c__DisplayClass40_1
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_2
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_2
<>c__DisplayClass40_2
<>c__DisplayClass40_2
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_3
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_3
<>c__DisplayClass40_3
<>c__DisplayClass40_3
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_4
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_4
<>c__DisplayClass40_4
<>c__DisplayClass40_4
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_5
AppTech.MSMS.Domain.Services.OrderInfoService/<>c__DisplayClass40_5
<>c__DisplayClass40_5
<>c__DisplayClass40_5
<<type>>
AppTech.MSMS.Domain.Services.OrderInfoService/<>c
AppTech.MSMS.Domain.Services.OrderInfoService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c
AppTech.MSMS.Domain.Services.PageService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_1
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_1
<>c__DisplayClass2_1
<>c__DisplayClass2_1
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_2
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass2_2
<>c__DisplayClass2_2
<>c__DisplayClass2_2
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_1
AppTech.MSMS.Domain.Services.PageService/<>c__DisplayClass3_1
<>c__DisplayClass3_1
<>c__DisplayClass3_1
<<type>>
AppTech.MSMS.Domain.Services.PersonalInfoService/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Services.PersonalInfoService/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.SimInvoiceService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.TopupCommissionService/<>c
AppTech.MSMS.Domain.Services.TopupCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupService/<>c
AppTech.MSMS.Domain.Services.TopupService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupOrderService/<>c
AppTech.MSMS.Domain.Services.TopupOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Registeration/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.Registeration/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/CommissionRequest
AppTech.MSMS.Domain.Services.RemittanceCommissionService/CommissionRequest
CommissionRequest
CommissionRequest
uBTAcAEJ5XW
<IsSending>k__BackingField
LaJAcU2YIto
<CurrencyID>k__BackingField
lQKAcpKkUb5
<ExchangerID>k__BackingField
lgkAc9RLn0f
<AccountID>k__BackingField
viKAci34CXR
<Amount>k__BackingField
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_1
AppTech.MSMS.Domain.Services.RemittanceCommissionService/<>c__DisplayClass8_1
<>c__DisplayClass8_1
<>c__DisplayClass8_1
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass19_0
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass19_0
<>c__DisplayClass19_0
<>c__DisplayClass19_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass20_0
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass20_0
<>c__DisplayClass20_0
<>c__DisplayClass20_0
<<type>>
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.RemittanceOutService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c
AppTech.MSMS.Domain.Services.PaymentCommissionService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_1
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass11_1
<>c__DisplayClass11_1
<>c__DisplayClass11_1
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c
AppTech.MSMS.Domain.Services.TopupProviderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.TopupProviderService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c
AppTech.MSMS.Domain.Services.LiveTopupService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.LiveTopupService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.BagatService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.BagatService/<>c
AppTech.MSMS.Domain.Services.BagatService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c__DisplayClass12_0
AppTech.MSMS.Domain.Services.ServiceInfoService/<>c__DisplayClass12_0
<>c__DisplayClass12_0
<>c__DisplayClass12_0
<<type>>
AppTech.MSMS.Domain.Services.SimCardOrderService/<>c
AppTech.MSMS.Domain.Services.SimCardOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService/<ExecuteAsyn>d__0
AppTech.MSMS.Domain.Services.TrailToupOrderService/<ExecuteAsyn>d__0
<ExecuteAsyn>d__0
<ExecuteAsyn>d__0
<<type>>
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c
AppTech.MSMS.Domain.Services.TrailToupOrderService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass14_0
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass14_0
<>c__DisplayClass14_0
<>c__DisplayClass14_0
<<type>>
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.TransferOrderService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c
AppTech.MSMS.Domain.Services.UserService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass17_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass17_0
<>c__DisplayClass17_0
<>c__DisplayClass17_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass21_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass21_0
<>c__DisplayClass21_0
<>c__DisplayClass21_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass25_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass25_0
<>c__DisplayClass25_0
<>c__DisplayClass25_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass26_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass26_0
<>c__DisplayClass26_0
<>c__DisplayClass26_0
<<type>>
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass27_0
AppTech.MSMS.Domain.Services.UserService/<>c__DisplayClass27_0
<>c__DisplayClass27_0
<>c__DisplayClass27_0
<<type>>
AppTech.MSMS.Domain.Services.PermissionManager/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.PermissionManager/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.UserPermissionService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService/<>c
AppTech.MSMS.Domain.Services.AccountUserService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.AccountUserService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.VoucherService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.VoucherService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.WithdrawOrderService/<>c__DisplayClass8_0
AppTech.MSMS.Domain.Services.WithdrawOrderService/<>c__DisplayClass8_0
<>c__DisplayClass8_0
<>c__DisplayClass8_0
<<type>>
AppTech.MSMS.Domain.Services.BagatPaymentService/<>c
AppTech.MSMS.Domain.Services.BagatPaymentService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_1
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c__DisplayClass9_1
<>c__DisplayClass9_1
<>c__DisplayClass9_1
<<type>>
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c
AppTech.MSMS.Domain.Services.WifiCards.WifiInventoryService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass7_0
AppTech.MSMS.Domain.Services.TopupPayments.BundleService/<>c__DisplayClass7_0
<>c__DisplayClass7_0
<>c__DisplayClass7_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<ExecuteAsync>d__1
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<ExecuteAsync>d__1
<ExecuteAsync>d__1
<ExecuteAsync>d__1
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<PushToProvider>d__3
AppTech.MSMS.Domain.Services.TopupPayments.GomalaTopupService/<PushToProvider>d__3
<PushToProvider>d__3
<PushToProvider>d__3
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.TopupPayments.ItemCostService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.ItemService/<>c__DisplayClass11_0
AppTech.MSMS.Domain.Services.TopupPayments.ItemService/<>c__DisplayClass11_0
<>c__DisplayClass11_0
<>c__DisplayClass11_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.TopupPayments.OperatorService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c
AppTech.MSMS.Domain.Services.TopupPayments.QuotationService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService/<TopupAsync>d__0
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileBagatService/<TopupAsync>d__0
<TopupAsync>d__0
<TopupAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService/<>c
AppTech.MSMS.Domain.Services.TopupPayments.RiyalMobileService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.TopupPayments.TopupCallback/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c__DisplayClass15_0
AppTech.MSMS.Domain.Services.Sims.SimPurchaseService/<>c__DisplayClass15_0
<>c__DisplayClass15_0
<>c__DisplayClass15_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass6_0
AppTech.MSMS.Domain.Services.Sims.SimSaleService/<>c__DisplayClass6_0
<>c__DisplayClass6_0
<>c__DisplayClass6_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.Sims.SimTransferService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.Security.AccountApiService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.Security.AccountApiService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteAsync>d__8
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteAsync>d__8
<ExecuteAsync>d__8
<ExecuteAsync>d__8
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteBagat>d__9
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ExecuteBagat>d__9
<ExecuteBagat>d__9
<ExecuteBagat>d__9
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ProccessByProviderAsync>d__11
AppTech.MSMS.Domain.Services.Payment.YmBagatService/<ProccessByProviderAsync>d__11
<ProccessByProviderAsync>d__11
<ProccessByProviderAsync>d__11
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<>c__DisplayClass0_0
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<>c__DisplayClass0_0
<>c__DisplayClass0_0
<>c__DisplayClass0_0
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<ExecuteAsync>d__0
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<ExecuteAsync>d__0
<ExecuteAsync>d__0
<ExecuteAsync>d__0
<<type>>
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<TopupAsync>d__1
AppTech.MSMS.Domain.Services.Payment.YmTopupBagatService/<TopupAsync>d__1
<TopupAsync>d__1
<TopupAsync>d__1
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.GeneralLedger.AccountFrozenService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService/<>c
AppTech.MSMS.Domain.Services.GeneralLedger.DoubleEntryBondService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Cards.CardService/<>c__DisplayClass9_0
AppTech.MSMS.Domain.Services.Cards.CardService/<>c__DisplayClass9_0
<>c__DisplayClass9_0
<>c__DisplayClass9_0
<<type>>
AppTech.MSMS.Domain.Services.Branching.BranchService/<>c__DisplayClass5_0
AppTech.MSMS.Domain.Services.Branching.BranchService/<>c__DisplayClass5_0
<>c__DisplayClass5_0
<>c__DisplayClass5_0
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PersonService`1/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.Accounting.PersonService`1/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass4_0
AppTech.MSMS.Domain.Services.Accounting.PartiesService/<>c__DisplayClass4_0
<>c__DisplayClass4_0
<>c__DisplayClass4_0
<<type>>
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService/<>c
AppTech.MSMS.Domain.Services.Remittances.BaseRemittanceInService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService/<>c
AppTech.MSMS.Domain.Services.Remittances.DirectRemittanceInService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService/<>c__DisplayClass10_0
AppTech.MSMS.Domain.Services.Satellite.SatelliteFactionService/<>c__DisplayClass10_0
<>c__DisplayClass10_0
<>c__DisplayClass10_0
<<type>>
AppTech.MSMS.Domain.Security.IpAddressManager/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Security.IpAddressManager/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Helpers.ExcelHelper/<>c
AppTech.MSMS.Domain.Helpers.ExcelHelper/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<PushToExchangerAsync>d__0
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<PushToExchangerAsync>d__0
<PushToExchangerAsync>d__0
<PushToExchangerAsync>d__0
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass1_0
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass1_0
<>c__DisplayClass1_0
<>c__DisplayClass1_0
<<type>>
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Helpers.ExpressRemittanceProxy/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_0
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_0
<>c__DisplayClass2_0
<>c__DisplayClass2_0
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_1
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass2_1
<>c__DisplayClass2_1
<>c__DisplayClass2_1
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c
<>c
<>c
<<type>>
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass3_0
AppTech.MSMS.Domain.Services.GroupService/GroupItemService/<>c__DisplayClass3_0
<>c__DisplayClass3_0
<>c__DisplayClass3_0
<<type>>
<Module>{39DC1B84-DDD5-441B-B8C5-2FBCFC8EE993}
<Module>{39DC1B84-DDD5-441B-B8C5-2FBCFC8EE993}
<Module>{39DC1B84-DDD5-441B-B8C5-2FBCFC8EE993}
<Module>{39DC1B84-DDD5-441B-B8C5-2FBCFC8EE993}
<<type>>
MXARKnA5YLicH073Ow.AB2QveaMNUOTg0nDdq
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc
AB2QveaMNUOTg0nDdq
CDCWSn7SaPjUwoq2Cc
DAJAcIDvj5D
TWp4PNnQc
<<type>>
MXARKnA5YLicH073Ow.AB2QveaMNUOTg0nDdq/SFU4mbT3GMret7THonf
SOj3wtG2Ob7xEudvw7.CDCWSn7SaPjUwoq2Cc/SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
SFU4mbT3GMret7THonf
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il
eWsIXYWywlxILZjpNO
DyyVDbaRvM1YfIq9il
lebAcHeQ2wa
creoiNvd7
NIrAcuO2URq
jZiU8kt7k
dmrAcNVfsYX
yIEeUuogE
vRDAcDXR436
HNMMnrD0K
OO6Actk6apY
U6ZIpjiMV
l1yAcduiu2R
TYIaeXNeW
Mt6AcSolGDP
rI3lmZ9FL
d9EAckIXMY2
SuhhReBcy
OB3AcPr39tc
QWOOk18h0
HCyAcj58oYv
BjkXsyRir
AGkAcalEo08
mCC9ZT9yx
r94AcY32PbC
b82VQ34LR
dwbAcw7VbCC
P4kZBQ8Uk
oqJAc0cH6IN
KX0HrYNeb
nZTAcqGrAqF
pvQ2Nvbv9
RHwAcMo8E2h
KqVWF2r0M
QQfAcX6SuNl
SR2f8Si0X
J20AcovwT12
LXFsnj021
ewpAcLFm2cu
jMyYFyWuy
YdMAcZAGBw5
NvQ34uZt895nxEhi2FIr
dooAcK9YO8E
gVU0QeojF
rurAcyWphZP
HK2JaffxR
H97AcvD2nqo
ubITRqgdO
js0Ac2CNaAB
vEB6drODu
MVyAc8RT1gg
vZF7RiFiF
xSGAc346rgM
puGi6bKKk
idZAcb2T1LT
ROhFJh1RB
D4QAcEnyqll
T7LBbJ4ta
lnJAc5twnBY
fMdPu7i25
YClAcBmreQw
yMayDYsjD
zXqAclZPwn0
Kxm8CyXvJ
TkgAcTvl6CG
JkHjxJCFT
gihAcCAj2Hy
eM2t2dfoT
ctvAcgkfURF
vDfq2bW1V
Sv2Accb3tTW
B3XRfqih9
yG5AcJTcUv4
sVk5WFvVV
SFBAc1bwR66
E3GryunuI
wCsAcroNlf6
yxOcIGI9u
ljEAcQ5qfRk
Oihu8LNHm
l5jAcFoIO8c
ifqQyNVWS
A85AcVcRfCl
hcDmskCdX
CZtAcRucGwE
mKgSOTjDj
qijAcfhEyi4
aYTwtN0c5
SaFAce1Z11Z
udfDaXdkp
OqRAcxyp8P5
NrL10qsNW
ma3AcsDDu1o
j8hgmZJ7n
ssFAcO7g2TN
M6EKmwjSJ
HqQAchsJVy4
PVVpfAGtG
vW7Ac6NW7xa
cQCd71PIW
jBuAc4Wxrt9
lodECQQVs
w1KAcmhVE7i
VvPxdPh3O
WrLAc7eQ2Bs
hIsn23p8h
jUNAcWExy40
dKMLoMpMs
W6vAcnvDfnW
ghLACNa05
bjMAczuk8iI
c9FNce5cf
K7DAJGYkLWM
diL3t0peo
rvbAJAMdjHU
sMgC0o5PW
jsKAJUe6hRv
S0FvrGWpN
gwsAJpFKU59
hSjGubHK9
THOAJ9FmxSS
d1uknJpcW
rk4AJiQnPe4
uS9zmJ6WC
ILKAJI2IbPq
i244bikuos
mVjAJHAxJal
bFB44BUGlg
RWrAJu6Vqff
x3c4o2PyTx
LIyAJN0aV2N
phV4Uu6SUx
wBIAJDiatud
Qwp4ejR7FG
gu6AJtnl1ja
TWn4MujlZv
EfrAJdUpjgZ
NFL4IGyoc7
g7FAJSQbuJ1
WS94a0Vnlv
GpVAJkRPkBO
XtL4lyIIgx
SfYAJPZoK5L
firstrundone
AwfAJjpFdDR
IBe4hEip2A
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/wgo8g4RvXiAHxRI7AY
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9
wgo8g4RvXiAHxRI7AY
AXBrnIFfMAfABnJrF9
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/wgo8g4RvXiAHxRI7AY/uWfOPs3kUDM7a9or4y`1
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/AXBrnIFfMAfABnJrF9/z0oyxsqySXMDuI4ZyY`1
uWfOPs3kUDM7a9or4y`1
z0oyxsqySXMDuI4ZyY`1
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/Y51vQpdKfmsaX87NZj
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/ay67rn8SHAWRagidNL
Y51vQpdKfmsaX87NZj
ay67rn8SHAWRagidNL
q91AJa5CZ1T
D4r4O0AxSI
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/DWJTS2cmWegRgR55TW
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/rL2N9N6wh7IWY3IC3G
DWJTS2cmWegRgR55TW
rL2N9N6wh7IWY3IC3G
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/URi04pEQwrEq77tfyT
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/LhmiV9AUoOr1v5yhIs
URi04pEQwrEq77tfyT
LhmiV9AUoOr1v5yhIs
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/J8OISrCDTdBhBI9t49
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/Lk7BwHKFmNJY32ZC3n
J8OISrCDTdBhBI9t49
Lk7BwHKFmNJY32ZC3n
rGNAJYStQxl
bV44XU8KQo
REvAJwNddrm
Uu349Vtr47
<<type>>
ir19WvjWtnCmJAqpOD.eWsIXYWywlxILZjpNO/mp7mTDyioHjBuW72TR
vJiGl01UUJfXfNWas3.DyyVDbaRvM1YfIq9il/WDRJe2H6E4HVV6PGZs
mp7mTDyioHjBuW72TR
WDRJe2H6E4HVV6PGZs
<<type>>
c1MvVxeZfIpepF12Xt.t9DMo2vH16pt2kFnaC
cH8IXcwQY4Peh2qpAn.xrUtBVoaXtCT6B0w6a
t9DMo2vH16pt2kFnaC
xrUtBVoaXtCT6B0w6a
RTBAJ07QLeJ
ywq4VEynyU
<<type>>
heh1UnlmFGRKn7eyym.IJHYe6x3srxLmEW0cp
c5uHW3cSW8ou55rAF3.KKr6hZkjvwWjdm9A4Z
IJHYe6x3srxLmEW0cp
KKr6hZkjvwWjdm9A4Z
ARjAJqMJ7mC
Uur4ZuAaiM
<<type>>
jtsP2V0nP5K7iq1Vfm.FexOkq4nnWIghTRBr0
CvXDWVQi7y2eO3gBAC.OsyMlHJSvCHNZySQs6
FexOkq4nnWIghTRBr0
OsyMlHJSvCHNZySQs6
<<type>>
ITK6DEmmQfCdUYkx2G.S82JyFSmbbNsELXsX4
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q
S82JyFSmbbNsELXsX4
R2mIapWar4cwoqqx6Q
jH0AJMrRgRg
HNM4YkXJs5
RLqAJXYDisr
pfJ40gjxwv
wVtAJo1XeOJ
eBxqprrF8
JT8AJLZbW2E
Ypf4J7ba8u
zIRAJZ5Ta7Z
CCw4Tb9h3V
OveAJKmGClW
n3x46T2MQ2
GSFAJyZTT7L
WP947UZNwy
LMxAJvWwPr1
Fko4i7KTuh
<<type>>
ITK6DEmmQfCdUYkx2G.S82JyFSmbbNsELXsX4/CNFguhqF72I7egbiSU
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/dde9wksVEKdElHkEKH
CNFguhqF72I7egbiSU
dde9wksVEKdElHkEKH
<<type>>
ITK6DEmmQfCdUYkx2G.S82JyFSmbbNsELXsX4/LYJi6nL9jXn3bxAtA4
zsd5DaYg9lOJGmlIO4.R2mIapWar4cwoqqx6Q/T9eZG8XLTT9vNo3j18
LYJi6nL9jXn3bxAtA4
T9eZG8XLTT9vNo3j18
WX5AJ2SUmL7
IWZ4FNxMCV
FXlAJ8eaHkV
X4o4BaXNNW
iiuAJ3CvEny
ReR4PkWY9i
K7DAJbQPlEH
XZO4yOqtpA
m9tAJEPvCSk
pcT48wm9UY
pUKAJ5ZRTvJ
Y9l4jroko9
vyQAJBddsri
OY84tBcMwd
ROAAJlfhZX9
JrQ4qkE5mX
JwwAJThs5ML
iRM4R10ean
fsbAJCMO0X2
AGe45CEX5X
yZSAJgFh0cy
Goe4rkO7Su
CysAJcovLNI
Tt04cJf5Ud
HYCAJJSvJZS
wDU4ucXGpO
aPeAJ168HGY
HGp4Q5R9ww
xvgAJrxN8AR
FvC4mE2qIR
mmuAJQ1JAaq
iv04SsOrFF
QtpAJFspldX
zBi4wdjAN2
LF5AJVK4b0r
PN14D93Kyx
BpOAJRSkUij
ulr41vALu8
qJKAJfi1FMx
lQp4gbkEqU
utnAJeFWPlg
IRA4KTlYfd
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=256
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
__StaticArrayInitTypeSize=256
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=40
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
__StaticArrayInitTypeSize=40
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=30
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
__StaticArrayInitTypeSize=30
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=32
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
__StaticArrayInitTypeSize=32
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=16
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
__StaticArrayInitTypeSize=16
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=64
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
__StaticArrayInitTypeSize=64
<<type>>
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=18
<PrivateImplementationDetails>{CEDA8D79-7F5C-46EE-9AA7-85B8D99399FE}/__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
__StaticArrayInitTypeSize=18
