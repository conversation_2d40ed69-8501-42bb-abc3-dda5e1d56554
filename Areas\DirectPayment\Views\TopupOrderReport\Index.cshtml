﻿@model AppTech.MSMS.Domain.Reports.Models.TopupOrderModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


    <div class="form-horizontal">
        @{
            Html.RenderPartial("_DateControl");
        }

        <span class="lbl">اسم الحساب  </span>
        @Html.DropDownListFor(model => model.AccountID, (SelectList)ViewBag.Accounts)
        <div class="space-6"></div>

        <span class="lbl">الخدمة</span>
        @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services)
        <div class="space-6"></div>
       
        <span class="lbl"> نوع التقرير</span>
        @Html.EnumDropDownListFor(m => m.ReportType)


    </div>

