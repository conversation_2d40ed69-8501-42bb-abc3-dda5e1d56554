﻿
@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Security
@{
    ViewBag.Title = "لوحة المراقبة";
}
@*<div class="alert alert-block alert-success">
    <button type="button" class="close" data-dismiss="alert">
        <i class="ace-icon fa fa-times"></i>
    </button>
    <i class="ace-icon fa fa-check green"></i>
     Welcome  <h4>Welcome : @User.FirstName</h4>  to
    <strong class="green">
        Frenchi Financial System
        <small>(v1.0)</small>
    </strong>,
    Frenchi is a remittances and payment system which helps you managing  all your financial transactions from one place.
</div>*@

@if (CurrentUser.Type == UserType.Admin)
{
    <div class="small-boxes">
        <div class="partialContents" data-url="/home/<USER>">
            @Html.Partial("_Indicator")
        </div>
    </div>

    <div class="hr hr32 hr-dotted"></div>

    <div class="client-balances">
        <div class="partialContents" data-url="/home/<USER>">
            @Html.Partial("_Indicator")
        </div>
    </div>
}
else
{
    <div class="party-info">
        <div class="partialContents" data-url="/home/<USER>">
            @Html.Partial("_Indicator")
        </div>
    </div>
}


<script>

    setPageTitle('لوحة المراقبة');
    var site = site || {};
    site.baseUrl = site.baseUrl || "";
    //i('site.baseUrl:' + site.baseUrl);
    $(document).ready(function(e) {


        $(".partialContents").each(function(index, item) {
            var url = site.baseUrl + $(item).data("url");
            if (url && url.length > 0) {
                i('on load Dashboard url ' + url);
                $(item).load(url);
            }
        });
    });

</script>

@*<script type="text/javascript">

    // Load the Visualization API and the corechart package.
    google.charts.load('current', {'packages':['corechart']});

    // Set a callback to run when the Google Visualization API is loaded.
    google.charts.setOnLoadCallback(drawChart);

    // Callback that creates and populates a data table,
    // instantiates the pie chart, passes in the data and
    // draws it.


    function getDatatableAsJson() {
        return '{\r\n  "cols": [\r\n        {"id":"","label":"Topping","pattern":"","type":"string"},\r\n        {"id":"","label":"Slices","pattern":"","type":"number"}\r\n      ],\r\n  "rows": [\r\n        {"c":[{"v":"Mushrooms","f":null},{"v":3,"f":null}]},\r\n        {"c":[{"v":"Onions","f":null},{"v":1,"f":null}]},\r\n        {"c":[{"v":"Olives","f":null},{"v":1,"f":null}]},\r\n        {"c":[{"v":"Zucchini","f":null},{"v":1,"f":null}]},\r\n        {"c":[{"v":"Pepperoni","f":null},{"v":2,"f":null}]}\r\n      ]\r\n}';
    }
    function drawChartAjax() {
        var jsonData = $.ajax({
            url: "getData.php",
            dataType: "json",
            async: false
        }).responseText;
          
        // Create our data table out of JSON data loaded from server.
        var data = new google.visualization.DataTable(jsonData);

        // Instantiate and draw our chart, passing in some options.
        var chart = new google.visualization.PieChart(document.getElementById('chart_div'));
        chart.draw(data, {width: 400, height: 240});
    }

    function drawChart() {

        // Create the data table.
        var data = new google.visualization.DataTable();
        data.addColumn('string', 'Topping');
        data.addColumn('number', 'Slices');
        //data.addRows([
        //    ['Mushrooms', 3],
        //    ['Onions', 0.5],
        //    ['Olives', 0.5],
        //    ['Zucchini', 1],
        //    ['Pepperoni', 2]
        //]);

        data.addRows([
            ['أرسال حوالة', 0.07],
            ['أيداع نقدي', 1.70],
            ['سحب نقدي', 2.70],
            ['تحويل الى حساب', 0.14],
            ['تسديدات مباشرة', 4.49],
            ['سحب حوالة', 0.04],
            [' حوالة', 0.04],
            [' مدفوعات التاجر', 1.04]
            
        ]);

        // Set chart options
        var options = {
            'title': 'العمليات'
            ,vAxis: {
                minValue: 0,
                maxValue: 100,
                format: '#\'%\''
            } ,
            //,
            is3D: true,
            //  'width':400,
            //'height': 300
        };

        // Instantiate and draw our chart, passing in some options.
        var chart = new google.visualization.PieChart(document.getElementById('piechart'));
        chart.draw(data, options);
    }
</script>*@