﻿@using AppTech.MSMS.Web.Code.HtmlHelpers
<style>
    .borderless td, .borderless th {
        border: none;
    }
</style>
@model AppTech.MSMS.Web.Models.BalanceSheetModel
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
<div class="form-horizontal">
    @{
        Html.RenderPartial("_DateControl");
    }

    <table id="search-filter-table" class="table table-responsive borderless">
        <tr>
            <td>        <span class="lbl"> نوع التقرير</span></td>
            <td>  @Html.EnumRadioButton(m => m.Type)</td>
        </tr>
        <tr>
            <td>  <span class="lbl"> العملة</span></td>
            <td>  @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)</td>
        </tr>

        <tr>
            <td>  <span class="lbl"> المستند</span></td>
            <td>@Html.DropDownListFor(model => model.VoucherID, (SelectList)ViewBag.Vouchers, new { htmlAttributes = new { @class = "form-control" } })</td>
        </tr>

        <tr>
            <td><span class="lbl"> الحالة   </span></td>
            <td> @Html.EnumDropDownListFor(m => m.BalanceState)</td>
        </tr>
        <tr>
            <td> <span class="lbl">بدون رصيد سابق</span></td>
            <td> @Html.EditorFor(m => m.NoPrevBalance)</td>
        </tr>
        <tr id="row-gross">
            <td> <span class="lbl">التجميع بالمستند</span></td>
            <td> @Html.EditorFor(m => m.GroupbyVoucher)</td>
        </tr>
    </table>

</div>

<script>
    $("select#CurrencyID").prop('selectedIndex', 1);
    $('input[type=radio][name=Type]').change(function () {

        if (this.value === 'تفصيلي') {
            $("#row-gross").hide();
        }
        else {
            $("#row-gross").show();
        }
    });
</script>