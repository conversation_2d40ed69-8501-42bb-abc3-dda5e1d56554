﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.SimInvoice

@{
    Layout = "~/Views/Shared/_MultiForm.cshtml";
}

<h2>فاتورة بيع شرائح</h2>

<div class="hr hr-dotted hr-24"></div>
@Html.HiddenFor(model => model.ID)
<div class="form-group">
    <label class="col-sm-2 control-label no-padding-right" for="DebitorAccountID">اسم الحساب</label>
    <div class="col-sm-10">
        @Html.Obout(new ComboBox("DebitorAccountID")
        {
            Width = 300,
            SelectedValue = Model.DebitorAccountID == 0 ? null : Model.DebitorAccountID.ToString(),
            FilterType = ComboBoxFilterType.Contains
        })
        @Html.ValidationMessageFor(model => model.DebitorAccountID)
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.NetworkID, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        <div class="col-md-10">
            @Html.DropDownListFor(model => model.NetworkID, new[]
            {
                new SelectListItem {Text = "يمن موبايل", Value = "1"},
                new SelectListItem {Text = "MTN", Value = "2"},
                new SelectListItem {Text = "سبأفون", Value = "3"}
            })
        </div>
        @Html.ValidationMessageFor(model => model.NetworkID, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group">
    @Html.Label("تصدير من ملف اكسل", new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.ViaExcel, new { htmlAttributes = new { onClick="toggle(this)" } })
    </div>
</div>
<div class="form-group excel-browser">

    <label class="col-sm-2 control-label">أختر ملف الأكسل</label>
    <div style="position: relative;">
        <input type="file" name="excelfile" size="40">
    </div>

</div>


<div class="form-group sim-row">
    @Html.LabelFor(model => model.StartNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.StartNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.StartNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group sim-row">
    @Html.LabelFor(model => model.EndNumber, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.EndNumber, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.EndNumber, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group form-inline">
    @Html.LabelFor(model => model.UnitPrice, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.UnitPrice, new { htmlAttributes = new { @class = "form-control" } })
        @*<input type="button" class="btn btn-white" value="احتساب المبلغ الأجمالي" id="calc"/>*@
        @Html.ValidationMessageFor(model => model.UnitPrice, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group sim-row">
    @Html.LabelFor(model => model.TotalUnits, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.TotalUnits, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.TotalUnits, "", new { @class = "text-danger" })
    </div>
</div>

<div class="form-group form-inline sim-row">
    @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
    <div class="col-md-10 form-inline">
        @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
        <label id="words" class="red"></label>
        @Html.ValidationMessageFor(model => model.Amount)
    </div>
</div>




<div class="form-group">
    @Html.LabelFor(model => model.Date, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "date-picker" } })
        @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
    <div class="col-md-10">
        @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control" } })
        @Html.ValidationMessageFor(model => model.Note, "", new { @class = "text-danger" })
    </div>
</div>


<script>

    $(function () {
        var typingTimer;                //timer identifier
        var doneTypingInterval = 5000;  //time in ms (5 seconds)

        //on keyup, start the countdown
        $('#EndNumber').keyup(function () {
            clearTimeout(typingTimer);
            if ($('#EndNumber').val()) {
                typingTimer = setTimeout(doneTyping, doneTypingInterval);
            }
        });

          $('#StartNumber').keyup(function () {
            clearTimeout(typingTimer);
            if ($('#StartNumber').val()) {
                typingTimer = setTimeout(doneTyping, doneTypingInterval);
            }
          });

        function doneTyping() {
            calcRemote();
        }
    });
     $(".excel-browser").hide();
    $('#TotalUnits').prop('readonly', true);
    $('#Amount').prop('readonly', true);

    // initForm();
    function toggle(source) {
      
        if (source.checked) {

            $(".excel-browser").show();
            $(".sim-row").hide();
        }
        else {

            $(".excel-browser").hide();
            $(".sim-row").show();
        }
    }

    
    //$("#ViaExcel").on('click', function () {
    //    initForm();
    //});
    //function initForm() {
    //      i('initForm');
    //    var source = $("#ViaExcel");
    //    if (source.checked) {

    //        $(".excel-browser").show();
    //        $(".sim-row").hide();
    //    }
    //    else {

    //        $(".excel-browser").hide();
    //        $(".sim-row").show();
    //    }
    //}




    //$("#StartNumber").on('change paste input', function () {
    //    calc();
    //});

    //$("#EndNumber").on('change paste input', function () {
    //    calc();
    //});

    //setup before functions

    $("#UnitPrice").on('change paste input', function () {
        calcTotal();
    });

    function calc() {
        calcRemote();
        //var startNo = Number($('#StartNumber').val());
        //var endNo = Number($('#EndNumber').val());
        //i('sno' + startNo);
        //i('eno' + endNo);

        //if (startNo > endNo) {
        //    i('sno<eno');
        //    return;
        //}
        //i('get total sim');
        //var counter = 0;
        //for (var x = startNo; x <= endNo; x++) {
        //    counter++;
        //}
        //$('#TotalUnits').val(counter);
        //$('#Amount').val($('#UnitPrice').val() * counter);

        //     calcTotal();
    }
    function calcTotal() {
        i('calcTotal');
        var price = Number($('#UnitPrice').val());
        var counter = Number($('#TotalUnits').val());

        i('calcTotal price' + price + ' total ' + counter);
        if (price > 0 && counter > 0) {
            $('#Amount').val(price * counter);
        }

    }

    function calcButton() {
        var startNo = $('#StartNumber').val();
        var endNo = $('#EndNumber').val();
        var price = Number($('#UnitPrice').val());
        i('sno' + startNo);
        i('eno' + endNo);
        if (price <= 0) {
            ar('قم بأخال سعر الشريحة');
            return;
        }
        var counter = 0;
        for (var x = startNo; x <= endNo; x++) {
            counter++;
        }
        $('#TotalUnits').val(counter);
        $('#Amount').val($('#UnitPrice').val() * counter);
    }

    function calcRemote() {
        var startNo = $('#StartNumber').val();
        var endNo = $('#EndNumber').val();
       // var price = Number($('#UnitPrice').val());
        i('sno' + startNo);
        i('eno' + endNo);

        if (startNo.length === 0 || endNo.length === 0)
            {
            i('start or end is empty');
            return;
        }
        //if (price <= 0) {
        //    ar('قم بأخال سعر الشريحة');
        //    return;
        //}
        AjaxCall('/DirectPayment/SimInvoice/CalcTotal?start=' + startNo+'&end='+endNo )
            .done(function (response) {
                i('response ' + response);
                var counter = Number(response);
                i('counter ' + counter);

                $('#TotalUnits').val(counter);
                $('#Amount').val($('#UnitPrice').val() * counter);
            }).fail(function (xhr, textStatus, errorThrown) {
                parseAndShowError(xhr, textStatus, errorThrown)
            });
    }
</script>

