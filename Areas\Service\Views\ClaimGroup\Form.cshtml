﻿@model AppTech.MSMS.Domain.Models.ClaimGroup
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.LabelFor(model => model.Name, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.Name, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.Name, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.SelectedServices, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.ListBoxFor(model => model.SelectedServices, (SelectList) ViewBag.Services, new {htmlAttributes = new {@class = "chosen-select"}})
        @Html.ValidationMessageFor(model => model.SelectedServices, "", new {@class = "text-danger"})
    </div>
</div>