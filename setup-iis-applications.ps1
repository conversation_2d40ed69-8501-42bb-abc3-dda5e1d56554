# Script to setup IIS applications for AppTech MSMS

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Setup IIS Applications" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Check if running as administrator
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "✓ Running as Administrator" -ForegroundColor Green

Write-Host "`n1. Checking IIS installation..." -ForegroundColor Yellow

# Check if IIS is installed
try {
    $iisFeature = Get-WindowsOptionalFeature -Online -FeatureName "IIS-WebServerRole"
    
    if ($iisFeature.State -eq "Enabled") {
        Write-Host "✓ IIS is already installed" -ForegroundColor Green
    } else {
        Write-Host "Installing IIS..." -ForegroundColor Yellow
        
        # Install IIS with ASP.NET support
        $features = @(
            "IIS-WebServerRole",
            "IIS-WebServer",
            "IIS-CommonHttpFeatures",
            "IIS-HttpErrors",
            "IIS-HttpLogging",
            "IIS-RequestFiltering",
            "IIS-StaticContent",
            "IIS-DefaultDocument",
            "IIS-DirectoryBrowsing",
            "IIS-ASPNET45",
            "IIS-NetFxExtensibility45",
            "IIS-ISAPIExtensions",
            "IIS-ISAPIFilter",
            "IIS-IISManagementConsole"
        )
        
        foreach ($feature in $features) {
            try {
                Enable-WindowsOptionalFeature -Online -FeatureName $feature -All -NoRestart
                Write-Host "  ✓ Enabled: $feature" -ForegroundColor Green
            } catch {
                Write-Host "  ⚠ Failed to enable: $feature" -ForegroundColor Yellow
            }
        }
        
        Write-Host "✓ IIS installation completed" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Error checking/installing IIS: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n2. Importing IIS management module..." -ForegroundColor Yellow

try {
    Import-Module WebAdministration -ErrorAction Stop
    Write-Host "✓ IIS Management module imported" -ForegroundColor Green
} catch {
    Write-Host "✗ Failed to import IIS Management module" -ForegroundColor Red
    Write-Host "Please install IIS Management Console" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n3. Configuring Default Web Site..." -ForegroundColor Yellow

try {
    # Set default website path to E:\inetpub
    $defaultSite = Get-Website -Name "Default Web Site" -ErrorAction SilentlyContinue
    
    if ($defaultSite) {
        Set-ItemProperty -Path "IIS:\Sites\Default Web Site" -Name physicalPath -Value "E:\inetpub"
        Write-Host "✓ Default Web Site path set to E:\inetpub" -ForegroundColor Green
        
        # Start the website if it's stopped
        if ($defaultSite.State -ne "Started") {
            Start-Website -Name "Default Web Site"
            Write-Host "✓ Default Web Site started" -ForegroundColor Green
        }
    } else {
        # Create default website
        New-Website -Name "Default Web Site" -PhysicalPath "E:\inetpub" -Port 80
        Write-Host "✓ Default Web Site created" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Error configuring Default Web Site: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n4. Creating virtual directories..." -ForegroundColor Yellow

$virtualDirs = @(
    @{Name="api"; Path="E:\inetpub\wwwroot\api"},
    @{Name="apiTEST"; Path="E:\inetpub\wwwroot\apiTEST"},
    @{Name="apinewAN"; Path="E:\inetpub\wwwroot\apinewAN"},
    @{Name="client"; Path="E:\inetpub\wwwroot\client"},
    @{Name="portal"; Path="E:\inetpub\wwwroot\portal"},
    @{Name="TopupInspector"; Path="E:\inetpub\wwwroot\TopupInspector"}
)

foreach ($vdir in $virtualDirs) {
    if (Test-Path $vdir.Path) {
        try {
            # Remove existing virtual directory if it exists
            $existing = Get-WebVirtualDirectory -Site "Default Web Site" -Name $vdir.Name -ErrorAction SilentlyContinue
            if ($existing) {
                Remove-WebVirtualDirectory -Site "Default Web Site" -Name $vdir.Name
            }
            
            # Create new virtual directory
            New-WebVirtualDirectory -Site "Default Web Site" -Name $vdir.Name -PhysicalPath $vdir.Path
            Write-Host "  ✓ Created virtual directory: $($vdir.Name)" -ForegroundColor Green
            
        } catch {
            Write-Host "  ✗ Failed to create virtual directory: $($vdir.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠ Path not found: $($vdir.Path)" -ForegroundColor Yellow
    }
}

Write-Host "`n5. Configuring application pools..." -ForegroundColor Yellow

$appPools = @(
    @{Name="AppTechAPI"; Framework="v4.0"; Identity="ApplicationPoolIdentity"},
    @{Name="AppTechClient"; Framework="v4.0"; Identity="ApplicationPoolIdentity"}
)

foreach ($pool in $appPools) {
    try {
        # Remove existing app pool if it exists
        $existing = Get-IISAppPool -Name $pool.Name -ErrorAction SilentlyContinue
        if ($existing) {
            Remove-WebAppPool -Name $pool.Name
        }
        
        # Create new app pool
        New-WebAppPool -Name $pool.Name
        Set-ItemProperty -Path "IIS:\AppPools\$($pool.Name)" -Name processModel.identityType -Value $pool.Identity
        Set-ItemProperty -Path "IIS:\AppPools\$($pool.Name)" -Name managedRuntimeVersion -Value $pool.Framework
        
        Write-Host "  ✓ Created application pool: $($pool.Name)" -ForegroundColor Green
        
    } catch {
        Write-Host "  ✗ Failed to create application pool: $($pool.Name) - $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n6. Converting virtual directories to applications..." -ForegroundColor Yellow

$applications = @(
    @{Name="api"; AppPool="AppTechAPI"},
    @{Name="client"; AppPool="AppTechClient"}
)

foreach ($app in $applications) {
    try {
        # Convert virtual directory to application
        ConvertTo-WebApplication -Site "Default Web Site" -PSPath "IIS:\Sites\Default Web Site\$($app.Name)" -ApplicationPool $app.AppPool
        Write-Host "  ✓ Converted to application: $($app.Name)" -ForegroundColor Green
        
    } catch {
        Write-Host "  ⚠ Failed to convert to application: $($app.Name) - $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host "`n7. Setting permissions..." -ForegroundColor Yellow

$paths = @("E:\inetpub", "E:\inetpub\wwwroot")

foreach ($path in $paths) {
    if (Test-Path $path) {
        try {
            # Give IIS_IUSRS read and execute permissions
            $acl = Get-Acl $path
            $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule)
            
            # Give IUSR read permissions
            $accessRule2 = New-Object System.Security.AccessControl.FileSystemAccessRule("IUSR", "Read", "ContainerInherit,ObjectInherit", "None", "Allow")
            $acl.SetAccessRule($accessRule2)
            
            Set-Acl -Path $path -AclObject $acl
            Write-Host "  ✓ Permissions set for: $path" -ForegroundColor Green
            
        } catch {
            Write-Host "  ⚠ Failed to set permissions for: $path" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n8. Testing configuration..." -ForegroundColor Yellow

try {
    # Test if IIS is responding
    $response = Invoke-WebRequest -Uri "http://localhost" -UseBasicParsing -TimeoutSec 10 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ IIS is responding on http://localhost" -ForegroundColor Green
    } else {
        Write-Host "⚠ IIS response code: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Unable to test IIS response: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "`n" + "="*60 -ForegroundColor Green
Write-Host "IIS Setup Completed!" -ForegroundColor Green
Write-Host "="*60 -ForegroundColor Green

Write-Host "`nApplications should now be accessible at:" -ForegroundColor Cyan
Write-Host "  Main Site: http://localhost/" -ForegroundColor White
Write-Host "  API: http://localhost/api/" -ForegroundColor White
Write-Host "  API Test: http://localhost/apiTEST/" -ForegroundColor White
Write-Host "  Client: http://localhost/client/" -ForegroundColor White
Write-Host "  Portal: http://localhost/portal/" -ForegroundColor White
Write-Host "  TopupInspector: http://localhost/TopupInspector/" -ForegroundColor White

Write-Host "`nTest pages:" -ForegroundColor Cyan
Write-Host "  http://localhost/api/test.html" -ForegroundColor White
Write-Host "  http://localhost/client/test.html" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Run fix-web-applications.ps1 to configure connection strings" -ForegroundColor Cyan
Write-Host "2. Setup database" -ForegroundColor Cyan
Write-Host "3. Test applications" -ForegroundColor Cyan

pause
