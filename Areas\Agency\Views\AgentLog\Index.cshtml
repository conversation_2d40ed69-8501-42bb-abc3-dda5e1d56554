﻿@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Reports.AgentTransModel
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
@{
    Html.RenderPartial("_DateControl");
}

<span class="lbl">اسم الوكيل </span>
<div class="form-group">
    <div class="col-md-10">
        @Html.Obout(new ComboBox("AgentID")
        {
            Width = 230,
            FilterType = ComboBoxFilterType.Contains
        })

    </div>
</div>

<div class="hr hr-dotted hr-24"></div>