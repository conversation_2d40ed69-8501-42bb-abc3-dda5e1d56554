USE [2022]
GO

INSERT INTO [dbo].[jobs]
           ([queue]
           ,[payload]
           ,[attempts]
           ,[reserved_at]
           ,[available_at]
           ,[created_at])
     VALUES
           (	"check-topup"	,{"uuid":"6e606968-6df9-4a21-9576-17fb62b83bae","displayName":"App\\Jobs\\CheckTopup","job":"Illuminate\\Queue\\CallQueuedHandler@call","maxTries":null,"maxExceptions":null,"failOnTimeout":false,"backoff":null,"timeout":null,"retryUntil":null,"data":{"commandName":"App\\Jobs\\CheckTopup","command":"O:19:\"App\\Jobs\\CheckTopup\":6:{s:26:\"\u0000App\\Jobs\\CheckTopup\u0000topup\";O:45:\"Illuminate\\Contracts\\Database\\ModelIdentifier\":5:{s:5:\"class\";s:16:\"App\\Models\\Topup\";s:2:\"id\";i:6572313;s:9:\"relations\";a:0:{}s:10:\"connection\";s:6:\"sqlsrv\";s:15:\"collectionClass\";N;}s:28:\"\u0000App\\Jobs\\CheckTopup\u0000journal\";O:45:\"Illuminate\\Contracts\\Database\\ModelIdentifier\":5:{s:5:\"class\";s:18:\"App\\Models\\Journal\";s:2:\"id\";i:6829576;s:9:\"relations\";a:0:{}s:10:\"connection\";s:6:\"sqlsrv\";s:15:\"collectionClass\";N;}s:25:\"\u0000App\\Jobs\\CheckTopup\u0000type\";s:5:\"TOPUP\";s:10:\"connection\";s:8:\"database\";s:5:\"queue\";s:11:\"check-topup\";s:5:\"delay\";O:25:\"Illuminate\\Support\\Carbon\":3:{s:4:\"date\";s:26:\"2024-10-23 10:35:27.885512\";s:13:\"timezone_type\";i:3;s:8:\"timezone\";s:9:\"Asia\/Aden\";}}"}}	0	NULL	1729668927	1729668918
GO


