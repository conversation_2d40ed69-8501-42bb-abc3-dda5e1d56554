﻿@using AppTech.MSMS.Web.Security
@*<button class="btn btn-link fa fa-check check">&nbsp; التحقق &nbsp;</button>*@

@*@Html.Partial("_RecordAction")*@
@if (Model.Row["الحالة"].ToString().Equals("تم قبول الطلب") || Model.Row["الحالة"].ToString().Equals("تم رفض الطلب"))
{
    <button class="btn btn-link fa fa-eye show">&nbsp; مشاهدة &nbsp;</button>
}
else
{
    @*@Html.ActionButton((string)ViewBag.PageName, "WifiProvider", "الفئات", "eye", "faction", "", false)*@

    @*@Html.ActionButton((string)ViewBag.PageName, "PROCCESS", " معالجة الطلب ", "cog", "proccess", "")*@
    @Html.ActionButton((string)ViewBag.PageName, "EDIT", " معالجة ", "pencil", "edit-record", "تعديل السجل")

}