﻿@model AppTech.MSMS.Web.Areas.Remittance.Models.RemittanceReceiptModel
<html>
<head>
    <meta charset="UTF-8">
    <title>Bootstrap 3</title>


</head>

<body style="overflow: auto;">

<div class="container text-right holder  " style="direction: rtl; margin-top: 50px; border: 1px solid #000000">
    <div class="row ">
        <div class="col-sm-3    ">رقم الحوالة: @Html.DisplayFor(model => model.RemittanceNumber)</div>
        <div class="col-sm-4 text-center   ">سند صرف حوالة</div>
        <div class="col-sm-4 text-center  ">التاريخ: @Html.DisplayFor(model => model.Date)</div>
    </div>
    <hr>
    <div class="row ">
        <div class="col-sm-9  ">
            <div class="row">
                <div class="col-sm-2" style="">
                    <label>انا الموقع ادناة:</label>
                </div>
                <div class="col-sm"> @Html.DisplayFor(model => model.BenficiaryName)</div>
            </div>
            <div class="row text-right">
                <div class="col-sm-3 ">
                    <label>استلمت مبلغا وقدرة:</label>
                </div>
                <div class="col-sm">
                    <div class="sum"> @Html.DisplayFor(model => model.AmountInText)</div>
                </div>
            </div>
            <div class="row  ">
                <div class="col-sm-3">
                    <label>المحولة لي من المرسل:</label>
                </div>
                <div class="col-sm">@Html.DisplayFor(model => model.SenderName)</div>
            </div>
            <div class="row ">
                <div class="col-sm-2" style="">
                    <label>عن طريق:</label>
                </div>
                <div class="col-sm-5 ">@Html.DisplayFor(model => model.TargetName)</div>
                <div class="col-sm-4">تاريخ الحوالة:@Html.DisplayFor(model => model.Date)</div>
            </div>
            <div class="row ">
                <div class="col-sm-2">
                    <label>ملاحظات:</label>
                </div>
                <div class="col-sm">--------------------------------------------------------------</div>

            </div>
            <hr>
            <div class="bold">
                <div class="row bold">
                    <div class="col-sm-2" style="">
                        <label>اسم المستلم:</label>
                    </div>
                    <div class="col-sm">---------------------------------------</div>
                </div>
                <div class="row bold">
                    <div class="col-sm-2" style="">
                        <label>هاتف المستلم:</label>
                    </div>
                    <div class="col-sm">-----------------------------</div>
                </div>

                <div class="row text-right ">
                    <div class="col-sm-7">
                        <div>
                            <label>التوقيع:----------------------------------</label>
                        </div>
                        <div>استلمت المبلغ المذكور اعلاه كاملا وفي حالة جيدة.:</div>
                    </div>

                    <div class="col-sm text-center">
                        <h5>البصمة</h5>
                        <div class="print"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-sm-3">
            <div class="img">
                <img class="" src="images/c5.jpg">
            </div>

        </div>
    </div>


</div>

<script src="js/jquery-3.2.1.min.js"></script>
<script src="js/bootstrap.js"></script>
<script src="js/main.js"></script>

</body>
</html>