﻿<configuration>
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <requiredRuntime version="v4.0.30319" safemode="true" />
  </startup>
  <runtime>
    <NetFx40_LegacySecurityPolicy enabled="true"/>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="microsoft.analysisservices.timedimgenerator.dll" publicKeyToken="97189f74fdbb8935" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-65535.65535.65535.65535" newVersion="9.00.242.00" />
        <publisherPolicy apply="no" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="EventSource" publicKeyToken="31bf3856ad364e35" />
        <codeBase version="*******" href="PQEventSource\EventSource.dll"/>
      </dependentAssembly>
      <probing privatePath="MDataEngine"/>
    </assemblyBinding>
  </runtime>
  <system.data>
    <DbProviderFactories>
      <add name="Microsoft Mashup Data Provider"
        invariant="Microsoft.Data.Mashup"
        description="Ado.NET Provider for Power Query Mashups"
        type="Microsoft.Data.Mashup.MashupProviderFactory, Microsoft.Data.Mashup"/>
    </DbProviderFactories>
  </system.data>
</configuration>
