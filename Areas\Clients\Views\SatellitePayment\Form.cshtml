﻿@model AppTech.MSMS.Domain.Models.SatellitePayment

@{
    Layout = "~/Views/Shared/_FormLayout.cshtml";
}
<div class="form-group">
    <label class="col-md-2" style="text-align: left;">المحافظة</label>
    <div class="col-md-10">
        <select id="Provinces" name="" Class="select2" style="width: 110px;" placeholder="اختر محافظة" required></select>
    </div>
</div>
<div class="form-group">
    <label class="col-md-2" style="text-align: left;">المديرية</label>
    <div class="col-md-10">
        <select id="RegionID" name="RegionID" class="select2" style="width: 110px;" placeholder="اختر منطقة" required></select>
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">القناة </label>
    <div class="col-md-10">
        @Html.DropDownListFor(m => m.ProviderID, (SelectList)ViewBag.Providers, new { })
        @Html.ValidationMessageFor(model => model.ProviderID)
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">الفئة </label>
    <div class="col-md-10">
        <select id="FactionID" name="FactionID" class="select2" style="width: 110px;" placeholder="اختر فئه" required></select>
        @Html.ValidationMessageFor(model => model.FactionID)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label"> مدة الإشتراك بالاشهر</label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriptionTerm, new { htmlAttributes = new { min = 1, max = 20 } })
        @Html.ValidationMessageFor(model => model.SubscriptionTerm)
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">رقم الإشتراك </label>
    <div class="col-md-10">
        @Html.EditorFor(model => model.SubscriptionNumber)
        @Html.ValidationMessageFor(model => model.SubscriptionNumber)
    </div>
</div>
<input type="number" id="Cost" hidden />
<input type="number" id="ProviderSubscriptionTerm" hidden />

<script>
     function loadProvincesList() {
        i('loadDataList');
        fillDataList('Provinces', '/Clients/SatellitePayment/GetProvinces');
    }
    function loadRegionList(id) {
        i('loadDataList');
        fillDataList('RegionID', '/Clients/SatellitePayment/GetRegions?ID=' + id, false, "اختر مديرية");
        loadProviderList(id);
    }
    loadProvincesList();
    
    function loadProviderList(id) {
        i('loadDataList');
        fillDataList('ProviderID', '/Clients/SatellitePayment/GetSatelliteProviders?id=' + id, false, "اختر قناة");
        loadFactionList(id);
        // $.post("Clients/SatellitePayment/GetProviderSubscriptionTerm", { id: id }, function (data) {
        //    $("#ProviderSubscriptionTerm").val(data);
        //});
    };

    function loadFactionList(id) {
        i('loadDataList');
        fillDataList('FactionID', '/Clients/SatellitePayment/GetSatelliteFactions?id=' + id, false, "اختر فئــه");
    };
    loadRegionList(1);
    loadProviderList(1);
    loadFactionList(1);

     $("#Provinces").on('change',function () {
        var id = $("#Provinces option:selected").val();
        i('id: ' + id);
         loadRegionList(id);
           $.post("Clients/SatellitePayment/GetProviderSubscriptionTerm", { id: id }, function (data) {
               $("#ProviderSubscriptionTerm").val(data);
                i('ProviderSubscriptionTerm: ' + data);
        });
     });
    
    $("#RegionID").on("change", function () {
        var id = $("#RegionID").val();
        loadProviderList(id);
    });

    $("#ProviderID").on("change", function () {
        var id = $("#ProviderID").val();
        loadFactionList(id);
            $.post("Clients/SatellitePayment/GetProviderSubscriptionTerm", { id: id }, function (data) {
               $("#ProviderSubscriptionTerm").val(data);
                i('ProviderSubscriptionTerm: ' + data);
        });
     });

    $("#FactionID").on("change", function () {
        var faction = $("#FactionID").val();
        var subscriptionTerm = $("#SubscriptionTerm").val();

        $.post("Clients/SatellitePayment/GetCost", { id: faction }, function (data) {
            $("#Cost").val(data);
        });
    });
    //$("#SubscriptionTerm").on("change", function () {
    //    var subscriptionTerm = $("#SubscriptionTerm").val();
    //    var cost = subscriptionTerm * $("#Cost").val();
    //        $("#Cost").val(cost);
    //});

    $(function () {
        $("#cancel-button").hide();
        $("#submit-button").text('طلب الكرت');

        $("#submit-button").on('click', function () {
            var subscriptionTerm = $("#SubscriptionTerm").val();
            var providerSubscriptionTerm = $("#ProviderSubscriptionTerm").val();
            i('subscriptionTerm' + subscriptionTerm);

            if (subscriptionTerm == 0) {
                i('subscriptionTerm' + subscriptionTerm);
                alert("الرجاء تحديد مدة الاشتراك");
                history.go(0);
            }
            if (providerSubscriptionTerm > subscriptionTerm) {
                alert(" اقل مدة للاشتراك" + providerSubscriptionTerm +" اشهر");
                history.go(0);
            }
            else {
                var total = ($("#Cost").val()) * subscriptionTerm;

                var msg = "سوف يتم طلب اشتراك  " +
                    $("#ProviderID option:selected").text()
                    + ' فئة : ' + $("#FactionID option:selected").text()
                    + ' بمبلغ ' + total + ' ريال يمني  \n'
                    + ' هل انت متأكد؟';

                if (!confirm(msg)) {
                    i('not confirmed');
                    history.go(0);
                    return false;
                } else {
                    i('confirmed');
                    return true;
                }
            }
        })
    });
</script>

