﻿@*@model IEnumerable<AppTech.MSMS.Domain.Models.JournalEntry>*@
@model AppTech.MSMS.Domain.Models.Journal

@Styles.Render("~/Content/print")


<div class="" style="padding: 20px 20px;">
    <div class="col-sm-6 pull-right" style="margin-top: 10px">
        التاريخ : @Html.DisplayFor(model => model.Date.Date)
    </div>

    <div class="col-sm-6 align-left" style="margin-top: 10px">
        رقم السند : @Html.DisplayFor(model => model.Number)
    </div>

    <div class="col-sm-12 align-center" style="border-bottom: 1px solid black; margin-top: 1px; margin-bottom: 20px; width: 100%; padding: 8px 0px; height: 30px; font: bold 18px Helvetica, Sans-Serif;">
        سند قيد مزدوج
    </div>

    <div class="form-inline">
        <label class="bolder">العملة: </label>
        @if (Model.CurrencyID == 1)
        {
    <label class="bolder">ريال يمني</label>
        }
        else if (Model.CurrencyID == 2)
        {
    <label class="bolder">دولار امريكي</label>
        }
        else if (Model.CurrencyID == 3)
        {
            <label class="bolder">ريال سعودي</label>
        }
    </div>

    <div class="table-responsive">
        <table class="table table-sm">
            <tr>
                <th>
                    @Html.DisplayName("اسم الحساب")
                </th>
                <th>
                    @Html.DisplayName("المبلغ")
                </th>
                <th>
                    @Html.DisplayName("العملة")
                </th>
                <th>
                    @Html.DisplayName("نوع القيد")
                </th>
                <th>
                    @Html.DisplayName("البيان")
                </th>
            </tr>

            @foreach (var item in Model.JournalEntries)
            {
                <tr>

                    <td>
                        @Html.DisplayFor(modelItem => item.Account.Name)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Amount)
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Currency.Name)
                    </td>

                    <td>
                        @if (item.CostCenterID == 0)
                        {
                            <span>من حــ/</span>
                        }
                        else
                        {
                            <span>الى حــ/</span>
                        }
                    </td>
                    <td>
                        @Html.DisplayFor(modelItem => item.Note)
                    </td>

                </tr>
            }

        </table>
    </div>
</div>

<div class="col-sm-12">
    <div class="col-sm-6 pull-right align-center forDivStayle">
        <span class="">
            المحاسب
        </span>
        ............................
    </div>
    <div class="col-sm-6 pull-left align-center forDivStayle">
        <span class="">
            المدير العام
        </span>
        ............................
    </div><br />
</div>