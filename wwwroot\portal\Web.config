<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="elmah">
      <section name="security" requirePermission="false" type="Elmah.SecuritySection<PERSON><PERSON><PERSON>, Elmah" />
      <section name="errorLog" requirePermission="false" type="Elmah.ErrorLogSectionHand<PERSON>, Elmah" />
      <section name="errorMail" requirePermission="false" type="Elmah.ErrorMailSectionHandler, Elmah" />
      <section name="errorFilter" requirePermission="false" type="Elmah.ErrorFilterSectionHand<PERSON>, Elmah" />
    </sectionGroup>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  <connectionStrings></connectionStrings>
  <elmah>
    <errorLog type="AppTech.MSMS.Web.Code.ElmahErrorLog, AppTech.MSMS.Web" connectionStringName="elmah-sql"></errorLog>
    <!--<security allowRemoteAccess="0" />
		<errorLog type="Elmah.SqlErrorLog, Elmah" connectionStringName="elmah-sql">
		</errorLog>
-->
    <!--<errorLog type="Elmah.XmlFileErrorLog, Elmah" logPath="~/App_Data" />-->
    <!--
				See http://code.google.com/p/elmah/wiki/SecuringErrorLogPages for 
				more information on remote access and securing ELMAH.
		-->
    <security allowRemoteAccess="false" />
  </elmah>
  <appSettings>
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <add key="vs:EnableBrowserLink" value="true" />
    <!--roles settings-->
    <add key="RolesConfigKey" value="Admin" />
    <add key="UsersConfigKey" value="Client" />
    <!--basic settings-->
    <add key="ApplicationType" value="web" />
    <add key="ConnectionStringSource" value="EncrptedFile" />
    <add key="Async" value="false" />
	
		<!--modules settings-->		
		<add key="ConfigLicense" value="1" />
		<add key="ConfigModules" value="Admin,GeneralLedger,Client,DirectPayment,SMS,Agency,Remittance,Branch" />
		<add key="CustomerName" value="نوافذ" />
    <!--security setting-->
    
	<add key="Recaptcha" value="0" />
    <add key="recaptchaPublickey" value="6LeGJqwUAAAAAHzyxHQnZn8SuKQnFwDCliM-RrLG" />
    <add key="recaptchaPrivatekey" value="6LeGJqwUAAAAADVYtcackLrCMn_5VbwqTIZ8lyRk" />
    <!--web api settings-->
    <add key="IsWebApi" value="1" />
    <!--sms settings-->
    
    <!--portal settings-->
    <add key="CheckSecureToken" value="1" />
    <add key="party_partal" value="0" />
    <add key="AdminPort" value="5700" />
    <add key="RoleMode" value="2" />
    <add key="SupportBranches" value="0" />
    <!--General settings-->
    <add key="OwnerName" value="نوافذ" />
    <add key="Flavor" value="abuosama" />
	
		<!--branch settings-->  
	<add key="SupportMultiBranching" value="1" />
    <add key="IsBranch" value="1" />
	
    <!--remittance settings-->
    <add key="trans_num_prefix" value="12" />
    <add key="trans_num_len" value="9" />
    <!--express settings-->
    <add key="DirectRemittance" value="0" />
    <!--topup settings-->
    
    <add key="AyncExecuting" value="1" />
        <add key="BundleByProvider" value="1" />
        <add key="AutoTopupInspect" value="1" />
        <add key="DailyTopupClosure" value="1" />
        <add key="None_Clients_Types" value="0" />
        <add key="Show_Agents_Clients" value="1" />
        <add key="DatestambReport" value="1" />
        <add key="DatestambTopup" value="1" />
  </appSettings>
  <system.serviceModel>
       <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_ICSDServiceLinkLib" />
        <binding name="BasicHttpBinding_ICSDServiceLinkLib1" />
        <binding name="BasicHttpBinding_IService" />
        <binding name="BasicHttpBinding_Ialbayanmtnclientservice" />
        <binding name="BasicHttpsBinding_IService">
          <security mode="Transport" />
        </binding>
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://***************:9999/albayanmtnclientservice/services/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_Ialbayanmtnclientservice" contract="ServiceReference1.Ialbayanmtnclientservice" name="BasicHttpBinding_Ialbayanmtnclientservice" />
      <endpoint address="http://*************:8444/ForMeFast/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib" contract="ForMeServiceReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib" />
      <endpoint address="http://*************:8020/CSDWebServiceLinkLibrary/CSDServiceLinkLib/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib" contract="AbsiReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib" />
      <endpoint address="http://tadawul.dyndns.org:8448/Tadawul/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib1" contract="TadawulReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib1" />
      <endpoint address="https://mobile.mutarrebvon.com/AgentsService2/AgentsService.svc" binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_IService" contract="AlmutarebReference.IService" name="BasicHttpsBinding_IService" />
        <endpoint address="http://**************:8444/DerhimApiWebService/MainService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICSDServiceLinkLib1" contract="DerhimApiReference.ICSDServiceLinkLib" name="BasicHttpBinding_ICSDServiceLinkLib1" />
    </client>
  </system.serviceModel>
  
  
  <system.web>
    <!--Force secure connections for all Cookies & Prevent client script from reading Cookies -->
    <!--<httpCookies httpOnlyCookies="true" requireSSL="true" />-->
    <machineKey validationKey="FBD17EE6B7786E3CC14F8C97E716322ADD454F11DF9DC02A7D2050FD0C8445070C6A685106371980E09FC8EE81FF07C8264314820AE1AF0A7045A7D5B9C24805" decryptionKey="2F8EE5D282A6D0366CAD230214DE93B57E394AE4398F2D5F53924BA8AF4569E8" validation="SHA1" decryption="AES" />
    <httpModules>
      <add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" />
      <add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" />
      <add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" />
    </httpModules>
    <httpHandlers>
      <add verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
    </httpHandlers>
    <authentication mode="Windows">
      <!--<forms requireSSL="true" />-->
      <!--<forms loginUrl="Account/Login" timeout="2000"  protection="All" slidingExpiration="true" enableCrossAppRedirects="false"/>-->
    </authentication>
    <sessionState timeout="300" />
    <!--<authentication mode="Forms">
 <forms loginUrl="~/Account/login"
 protection="All"
 timeout="30"
 name=".ASPXAUTH" 
 path="/"
 requireSSL="false"
 slidingExpiration="true"
 defaultUrl="~/Home/Index"
 cookieless="UseDeviceProfile"
 enableCrossAppRedirects="false" />
 </authentication>-->
    <roleManager enabled="true" />
    <compilation targetFramework="4.6.1" />
    <!--<customErrors mode="On" defaultRedirect="~/Error" />-->
    <customErrors mode="Off" />
    <pages>
      <namespaces>
        <add namespace="System.Web.Helpers" />
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Optimization" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.WebPages" />
      </namespaces>
    </pages>
    <httpRuntime targetFramework="4.5" />
  </system.web>
  <location path="~/Scripts">
    <system.web>
      <authorization>
        <allow users="*" />
      </authorization>
    </system.web>
  </location>
  <system.webServer>
    <httpErrors errorMode="Detailed" />
    <asp scriptErrorSentToBrowser="true" />
    <httpCompression directory="%SystemDrive%\inetpub\temp\IIS Temporary Compressed Files">
      <scheme name="gzip" dll="%Windir%\system32\inetsrv\gzip.dll" staticCompressionLevel="9" />
      <dynamicTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/x-javascript" enabled="true" />
        <add mimeType="application/json" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </dynamicTypes>
      <staticTypes>
        <add mimeType="text/*" enabled="true" />
        <add mimeType="message/*" enabled="true" />
        <add mimeType="application/x-javascript" enabled="true" />
        <add mimeType="application/atom+xml" enabled="true" />
        <add mimeType="application/xaml+xml" enabled="true" />
        <add mimeType="*/*" enabled="false" />
      </staticTypes>
    </httpCompression>
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true">
      <add name="ErrorLog" type="Elmah.ErrorLogModule, Elmah" preCondition="managedHandler" />
      <add name="ErrorMail" type="Elmah.ErrorMailModule, Elmah" preCondition="managedHandler" />
      <add name="ErrorFilter" type="Elmah.ErrorFilterModule, Elmah" preCondition="managedHandler" />
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" />
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" />
      <remove name="ExtensionlessUrlHandler-Integrated-4.0" />
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0" />
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0" />
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
      <add name="Elmah" verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
    </handlers>
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By" />
      </customHeaders>
    </httpProtocol>
        <security>
            <requestFiltering>
                <denyUrlSequences>
                    <remove sequence="account/" />
                </denyUrlSequences>
            </requestFiltering>
        </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Spatial" publicKeyToken="31BF3856AD364E35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.4.0" newVersion="5.8.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="AutoMapper" publicKeyToken="be96cd2c38ef1005" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.2.1.0" newVersion="2.2.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.0" newVersion="4.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.OData" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.4.0" newVersion="5.8.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Data.Edm" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.4.0" newVersion="5.8.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Spatial" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.8.4.0" newVersion="5.8.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.1.3" newVersion="4.1.1.3" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="v13.0" />
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
    </providers>
  </entityFramework>
  <location path="elmah.axd" inheritInChildApplications="false">
    <system.web>
      <httpHandlers>
        <add verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" />
      </httpHandlers>
      <!-- 
				See http://code.google.com/p/elmah/wiki/SecuringErrorLogPages for 
				more information on using ASP.NET authorization securing ELMAH.

			<authorization>
				<allow roles="admin" />
				<deny users="*" />  
			</authorization>
			-->
    </system.web>
    <system.webServer>
      <handlers>
        <add name="ELMAH" verb="POST,GET,HEAD" path="elmah.axd" type="Elmah.ErrorLogPageFactory, Elmah" preCondition="integratedMode" />
      </handlers>
    </system.webServer>
  </location>
</configuration>
<!--ProjectGuid: 7E9139D6-85B4-4A64-ACE9-FFDC1602A091-->