﻿@model AppTech.MSMS.Domain.Reports.Models.OrderModel

@{
    Layout = "~/Views/Shared/_Report.cshtml";
}


<div class="form-horizontal">


    @{

        Html.RenderPartial("_DateControl");
    }

    <span class="lbl">الحالة</span>
    @Html.DropDownListFor(model => model.OrderState, new[]
    {
        new SelectListItem {Text = "جاهز", Value = "جاهز"},
        new SelectListItem {Text = "غير جاهز", Value = "غير جاهز"},
        new SelectListItem {Text = "مرفوض", Value = "مرفوض"}
    })

</div>

<script>
    $("#export-pdf").hide();
</script>