# سكريبت اختبار التطبيقات لنظام AppTech MSMS

Write-Host "اختبار التطبيقات..." -ForegroundColor Green

# اختبار حالة IIS
Write-Host "`n1. اختبار حالة IIS..." -ForegroundColor Yellow
try {
    $iisService = Get-Service W3SVC -ErrorAction Stop
    if ($iisService.Status -eq "Running") {
        Write-Host "✓ خدمة IIS تعمل بنجاح" -ForegroundColor Green
    } else {
        Write-Host "✗ خدمة IIS متوقفة - محاولة تشغيلها..." -ForegroundColor Red
        Start-Service W3SVC
        Write-Host "✓ تم تشغيل خدمة IIS" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ خدمة IIS غير متاحة: $($_.Exception.Message)" -ForegroundColor Red
}

# اختبار Application Pools
Write-Host "`n2. اختبار Application Pools..." -ForegroundColor Yellow
$appPools = @("AppTechAPI", "AppTechClient", "AppTechPortal")
foreach ($pool in $appPools) {
    try {
        $poolState = Get-IISAppPool -Name $pool -ErrorAction Stop
        if ($poolState.State -eq "Started") {
            Write-Host "✓ Application Pool '$pool' يعمل" -ForegroundColor Green
        } else {
            Write-Host "✗ Application Pool '$pool' متوقف - محاولة تشغيله..." -ForegroundColor Red
            Start-WebAppPool -Name $pool
            Write-Host "✓ تم تشغيل Application Pool '$pool'" -ForegroundColor Green
        }
    } catch {
        Write-Host "✗ Application Pool '$pool' غير موجود" -ForegroundColor Red
    }
}

# اختبار المواقع
Write-Host "`n3. اختبار المواقع..." -ForegroundColor Yellow
$websites = @(
    @{Name="AppTechAPI"; Port=80; Path="wwwroot\api"},
    @{Name="AppTechClient"; Port=8080; Path="wwwroot\client"},
    @{Name="AppTechPortal"; Port=8081; Path="wwwroot\portal"}
)

foreach ($site in $websites) {
    try {
        $website = Get-Website -Name $site.Name -ErrorAction Stop
        if ($website.State -eq "Started") {
            Write-Host "✓ الموقع '$($site.Name)' يعمل على المنفذ $($site.Port)" -ForegroundColor Green
        } else {
            Write-Host "✗ الموقع '$($site.Name)' متوقف - محاولة تشغيله..." -ForegroundColor Red
            Start-Website -Name $site.Name
            Write-Host "✓ تم تشغيل الموقع '$($site.Name)'" -ForegroundColor Green
        }
        
        # اختبار وجود الملفات الأساسية
        if (Test-Path $site.Path) {
            Write-Host "  ✓ مجلد الموقع موجود: $($site.Path)" -ForegroundColor Gray
            
            $essentialFiles = @("Global.asax", "Web.config", "bin")
            foreach ($file in $essentialFiles) {
                $filePath = Join-Path $site.Path $file
                if (Test-Path $filePath) {
                    Write-Host "    ✓ $file موجود" -ForegroundColor Gray
                } else {
                    Write-Host "    ✗ $file مفقود" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "  ✗ مجلد الموقع مفقود: $($site.Path)" -ForegroundColor Red
        }
        
    } catch {
        Write-Host "✗ الموقع '$($site.Name)' غير موجود" -ForegroundColor Red
    }
}

# اختبار المنافذ
Write-Host "`n4. اختبار المنافذ..." -ForegroundColor Yellow
$ports = @(80, 8080, 8081)
foreach ($port in $ports) {
    try {
        $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
        if ($connection.TcpTestSucceeded) {
            Write-Host "✓ المنفذ $port مفتوح ومتاح" -ForegroundColor Green
        } else {
            Write-Host "✗ المنفذ $port مغلق أو غير متاح" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ خطأ في اختبار المنفذ $port" -ForegroundColor Red
    }
}

# اختبار HTTP
Write-Host "`n5. اختبار HTTP..." -ForegroundColor Yellow
$urls = @(
    "http://localhost/",
    "http://localhost:8080/", 
    "http://localhost:8081/"
)

foreach ($url in $urls) {
    try {
        Write-Host "اختبار: $url" -ForegroundColor Gray
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 10 -UseBasicParsing -ErrorAction Stop
        Write-Host "✓ استجابة HTTP: $($response.StatusCode) $($response.StatusDescription)" -ForegroundColor Green
    } catch {
        $errorMessage = $_.Exception.Message
        if ($errorMessage -like "*500*") {
            Write-Host "⚠ خطأ خادم داخلي (500) - قد يكون مشكلة في قاعدة البيانات أو التكوين" -ForegroundColor Yellow
        } elseif ($errorMessage -like "*404*") {
            Write-Host "⚠ الصفحة غير موجودة (404) - قد تحتاج إعداد Default Document" -ForegroundColor Yellow
        } else {
            Write-Host "✗ خطأ في الاتصال: $errorMessage" -ForegroundColor Red
        }
    }
}

# اختبار قاعدة البيانات
Write-Host "`n6. اختبار قاعدة البيانات..." -ForegroundColor Yellow
$sqlInstances = @("localhost", ".\SQLEXPRESS", "localhost\SQLEXPRESS")
$dbConnected = $false

foreach ($instance in $sqlInstances) {
    try {
        $result = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT @@VERSION" -ErrorAction Stop
        Write-Host "✓ اتصال قاعدة البيانات ناجح: $instance" -ForegroundColor Green
        
        # اختبار وجود قاعدة البيانات
        $dbExists = Invoke-Sqlcmd -ServerInstance $instance -Query "SELECT name FROM sys.databases WHERE name = 'AppTechMSMS'"
        if ($dbExists) {
            Write-Host "✓ قاعدة البيانات AppTechMSMS موجودة" -ForegroundColor Green
        } else {
            Write-Host "⚠ قاعدة البيانات AppTechMSMS غير موجودة" -ForegroundColor Yellow
        }
        
        $dbConnected = $true
        break
    } catch {
        Write-Host "✗ فشل الاتصال بـ $instance" -ForegroundColor Red
    }
}

if (-not $dbConnected) {
    Write-Host "✗ لم يتم العثور على SQL Server" -ForegroundColor Red
}

# ملخص النتائج
Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "ملخص اختبار النظام" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

Write-Host "`nروابط الوصول للتطبيقات:" -ForegroundColor White
Write-Host "API: http://localhost/" -ForegroundColor Yellow
Write-Host "Client: http://localhost:8080/" -ForegroundColor Yellow  
Write-Host "Portal: http://localhost:8081/" -ForegroundColor Yellow

Write-Host "`nملاحظات مهمة:" -ForegroundColor White
Write-Host "- إذا ظهرت أخطاء HTTP 500، تحقق من سلاسل الاتصال في web.config" -ForegroundColor Yellow
Write-Host "- إذا ظهرت أخطاء HTTP 404، تحقق من Default Documents" -ForegroundColor Yellow
Write-Host "- تأكد من تشغيل SQL Server وإنشاء قاعدة البيانات" -ForegroundColor Yellow
Write-Host "- قد تحتاج إلى تشغيل Entity Framework Migrations" -ForegroundColor Yellow

Write-Host "`nانتهى اختبار التطبيقات" -ForegroundColor Green
