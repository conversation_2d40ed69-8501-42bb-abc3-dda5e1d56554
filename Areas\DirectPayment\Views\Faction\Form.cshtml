﻿@model AppTech.MSMS.Domain.Models.Faction
@{
    Layout = "~/Views/Shared/_Form.cshtml";
}
<div class="form-horizontal">



    <div class="form-group">
        @Html.LabelFor(model => model.OrderNo, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.OrderNo)
            @Html.ValidationMessageFor(model => model.OrderNo)
        </div>
    </div>


    <div class="form-group">
        @Html.LabelFor(model => model.Name, new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Name)
            @Html.ValidationMessageFor(model => model.Name)
        </div>
    </div>

    <div class="form-group">
        @Html.Label("الرصيد/ الكمية", new { @class = "control-label col-md-2" })
        <div class="col-md-10">
            @Html.EditorFor(model => model.Price)
            @Html.ValidationMessageFor(model => model.Price)
        </div>
    </div>

    

</div>
