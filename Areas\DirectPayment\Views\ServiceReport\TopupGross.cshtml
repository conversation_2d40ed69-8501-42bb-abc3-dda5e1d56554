﻿@model AppTech.MSMS.Domain.Reports.Models.TopupModel
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@using AppTech.MSMS.Web.Code.HtmlHelpers
@{
    Layout = "~/Views/Shared/_Report.cshtml";
}
    <style>
        .borderless td, .borderless th {
            border: none;
        }
    </style>
    <div class="">
        @{
            Html.RenderPartial("_DateControl");
        }

        <span class="lbl">الحساب </span>
        <select id="AccountID" name="AccountID" class="select2" placeholder="كافة الحسابات"></select>
        
        <div class="space-10"></div>
        <table id="search-filter-table" class="table table-responsive borderless">
           

            <tr>
                <td> <span class="lbl">المزود</span></td>
                <td> @Html.DropDownListFor(model => model.ProviderID, (SelectList)ViewBag.Providers) </td>
            </tr>


            <tr>
                <td> <span class="lbl">الخدمة</span></td>
                <td>   @Html.DropDownListFor(model => model.ServiceID, (SelectList)ViewBag.Services)</td>
            </tr>


            <tr>
                <td> <span class="lbl">الحالة</span></td>
                <td> @Html.EnumDropDownListFor(model => model.Status) </td>
            </tr>

            
            <tr>
                <td> <span class="lbl">التجميع بالحسابات</span></td>
                <td>  @Html.EditorFor(model => model.GroupByAccounts) </td>
            </tr>

            <tr>
                <td> <span class="lbl">التجميع بالخدمات</span></td>
                <td>  @Html.EditorFor(model => model.GroupByServices) </td>
            </tr>

            <tr>
                <td> <span class="lbl">التجميع بالمزودين</span></td>
                <td>  @Html.EditorFor(model => model.GroupByProviders) </td>
            </tr>


            <tr>
                <td> <span class="lbl">التجميع بالحالة</span></td>
                <td>  @Html.EditorFor(model => model.GroupByStatus) </td>
            </tr>

            <tr id="row-gross">
                <td> <span class="lbl">عدم تضمين تحصيلات النقاط</span></td>
                <td> @Html.EditorFor(m => m.WithoutPointsTopup)</td>
            </tr>
            @*<tr>
                <td> <span class="lbl"></span></td>
                <td> </td>
            </tr>*@

        </table>


    </div>
<script>
    $(function () {
        $("select#Status").prop('selectedIndex', 2);  
        AjaxCall('/Print/GetParties').done(function (response) {
            console.log('get parties');
            if (response.length > 0) {
                $('#AccountID').html('');
                var options = '<option value="0">كافة الحسابات</option>';
                for (var i = 0; i < response.length; i++) {
                    options += '<option value="' + response[i].ID + '">' + response[i].Name + '</option>';
                }
                $('#AccountID').append(options);

            }
        }).fail(function (xhr, textStatus, errorThrown) {
            parseAndShowError(xhr, textStatus, errorThrown);
        });

        //select2
        $('.select2').css('width', '200px').select2({ allowClear: true });
        $('#select2-multiple-style .btn').on('click',
            function (e) {
                var target = $(this).find('input[type=radio]');
                var which = parseInt(target.val());
                if (which == 2) $('.select2').addClass('tag-input-style');
                else $('.select2').removeClass('tag-input-style');
            });
    });

</script>