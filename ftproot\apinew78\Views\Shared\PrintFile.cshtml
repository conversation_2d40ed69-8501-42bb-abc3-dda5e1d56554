﻿@using AppTech.MSMS.Web.Security
@Styles.Render("~/Content/print")
@*<style>
    .table-wrapper-scroll-y {
        display: block;
        max-height: 500px;
        overflow-y: auto;
        -ms-overflow-style: -ms-autohiding-scrollbar;
    }

    .table > thead > tr:first-child > td {
        border: none;
    }

    .table-bordered td {
        border: none !important;
        border-right: solid 0px #ccc !important;
    }


    th {
        background-color: white;
        color: steelblue;
        border-right: none !important;
        border-left: none !important;
    }

</style>*@
<body class="no-skin rtl ">
<div class="main-content">
    <div class="main-content-inner">
        <div class="page-content">

            <div id="page-wrap">


                <div id="calisha" style="margin-top: 20px" class="align-center">

                    @*<div id="address">
                            BC Mobile Center
                            <br />
                            Mukla,Sayaoon
                            <br />
                            Hadramot
                            <br />
                            Phone: 770926229
                        </div>*@


                    <img id="receipt_logo" class="align-center" src="@Url.Content("~/Photos/calisha.jpeg")" alt="logo" style="margin-left: 90px"/>


                    @*<div id="addressRight" class="align-right">
                            مركز باسلامة
                            <br />
                            سيئون المكلاء
                            <br />
                            حضرموت
                            <br />
                            تلفون: 770926229
                        </div>*@


                </div>


                @*<div id="report_title" class="reportheader col-sm-6 col-xs-6 f-15 p-t-50" style="text-align: center">
                    
                </div>*@
                <div id="divReport" style="margin-left: 5px; margin-right: 5px;">

                </div>


                <div id="terms">
                    <span>المستخدم: @CurrentUser.CurrentSession.User.Name</span>
                    <span> @DateTime.Now</span>
                    
                    @*<button onclick="printReport()" id="print-report">طباعة</button>*@
                </div>

            </div>
        </div>
    </div>
</div>

</body>
<script type="text/javascript">


    
    window.onload = function() {


        try {
            var reportHTML = localStorage['REPORT'];
            var divReport = document.getElementById('divReport');
            //    title = localStorage['REPORT_title'],
            //   divtitle = document.getElementById('report_title');
            divReport.innerHTML = '';

            if (reportHTML) {

                divReport.innerHTML = reportHTML;
                //if (title) {
                //    divtitle.innerHTML = title;
                //    document.title = title;
                //} else {
                //    divtitle.hide();
                //}

            }

            try {
                document.getElementsByClassName('none-print-element').style.display = 'none';
            } catch (e) {

            } 

            window.print();

        } catch (e) {

            console.log('print error '+e);
        } 

    }

    function printReport() {
        document.getElementById("print-report").style.display = "none";

        window.print();
        
        i('hide print btn3');

    }
</script>