﻿@model AppTech.MSMS.Domain.Models.WifiCard

@{
    Layout = "~/Views/Shared/_Form.cshtml";
}

<input type="hidden" name="ID" value="@Model.ID" />
<input type="hidden" name="FactionID" value="@Model.FactionID" />
<input type="hidden" name="ProviderID" value="@Model.ProviderID" />

@*@if (Model.ID == 0)
{*@
    <div>
        <div class="form-group">
            @Html.LabelFor(model => model.SerialNo, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.SerialNo)
                @Html.ValidationMessageFor(model => model.SerialNo)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Username, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Username)
                @Html.ValidationMessageFor(model => model.Username)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Password, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Password)
                @Html.ValidationMessageFor(model => model.Password)
            </div>
        </div>
        <div class="form-group">
            @Html.LabelFor(model => model.Description, new { @class = "control-label col-md-2" })
            <div class="col-md-10">
                @Html.EditorFor(model => model.Description)
                @Html.ValidationMessageFor(model => model.Description)
            </div>
        </div>
    </div>
@*}
else
{
    <p> else</p>
}*@



<script>
    hideLoading();
</script>
<script>

    $(function () {
        try {
            formHelper.onSuccess = function (data) {
                log('user party form');
                i('data> ' + data);
                history.go(0);
            }
            formHelper.onBegin = function (context) {
                return true;
            };
        } catch (e) {
        }
    });
    $(function () {
        i('on load');
        $("#SyncAccountID").on('change',
            function () {
                i('on change sync account select');
                var name = $("#SyncAccountID option:selected").text();
                $("#OwnerName").val(name);
            });
    });

</script>