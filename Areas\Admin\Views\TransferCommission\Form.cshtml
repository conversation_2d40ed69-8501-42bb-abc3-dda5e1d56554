﻿@model AppTech.MSMS.Domain.Models.RemittanceCommission
@{
    Layout = "/Views/Shared/_Form.cshtml";
}
<div class="form-group">
    @Html.Label("نوع الحوالة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.RemittanceType, new[]
        {
            new SelectListItem {Text = "صرف حوالة", Value = bool.FalseString},
            new SelectListItem {Text = "قبض حوالة", Value = bool.TrueString}
        })
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.CurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CurrencyID, (SelectList) ViewBag.Currencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CurrencyID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.AccountState, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @*@Html.DropDownListFor(model => model.AccountState, (SelectList)ViewBag.States, new { htmlAttributes = new { @class = "form-control" } })*@

        @Html.DropDownListFor(model => model.AccountState, new[]
        {
            new SelectListItem {Text = "كافة الحسابات", Value = "1"},
            new SelectListItem {Text = "حساب محدد", Value = "2"},
            new SelectListItem {Text = "مجموعة", Value = "3"}
        })
        @Html.ValidationMessageFor(model => model.AccountState, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group" id="specifc">
    @Html.LabelFor(model => model.AccountID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountID, (SelectList) ViewBag.Accounts, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.AccountID, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group" id="group">
    @Html.LabelFor(model => model.AccountGroupID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.AccountGroupID, (SelectList) ViewBag.Groups, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.AccountGroupID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.StartAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.StartAmount, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.StartAmount, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.EndAmount, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.EndAmount, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.EndAmount, "", new {@class = "text-danger"})
    </div>
</div>

<div class="form-group">
    @Html.Label("نوع العمولة", new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommmissionType, new[]
        {
            new SelectListItem {Text = "بالمبلغ", Value = "0"},
            new SelectListItem {Text = "بالنسبة", Value = "1"}
        })
    </div>
</div>

<div class="form-group">
    @Html.LabelFor(model => model.CommissionCurrencyID, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.DropDownListFor(model => model.CommissionCurrencyID, (SelectList) ViewBag.CommissionCurrencies, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CommissionCurrencyID, "", new {@class = "text-danger"})
    </div>
</div>


<div class="form-group">
    @Html.LabelFor(model => model.CenterCommission, new {@class = "control-label col-md-2"})
    <div class="col-md-10">
        @Html.EditorFor(model => model.CenterCommission, new {htmlAttributes = new {@class = "form-control"}})
        @Html.ValidationMessageFor(model => model.CenterCommission, "", new {@class = "text-danger"})
    </div>
</div>


<script>

    $(function() {
        $('#specifc').hide();
        $('#group').hide();

        $('#AccountState').on('change',
            function() {
                i('onchange');
                var num = Number($("#AccountState").children("option:selected").val());
                i('selected state: ' + num);
                if (num === 1) {
                    $('#specifc').hide();
                    $('#group').hide();

                } else if (num === 3) {
                    $('#specifc').show();
                    $('#group').hide();

                } else if (num === 2) {
                    $('#specifc').hide();
                    $('#group').show();

                }


            });
    })
</script>