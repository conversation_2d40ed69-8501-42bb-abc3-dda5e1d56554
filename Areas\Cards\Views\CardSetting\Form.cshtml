﻿@model AppTech.MSMS.Domain.Settings.CardSetting

<div class="row">
    <div class="col-xs-12 col-sm-6">
        @using (Ajax.BeginForm(new AjaxOptions
        {
            OnBegin = "return OnFormBegin()",
            OnSuccess = "onCrudSuccess",
            OnFailure = "onCrudFailure",
            LoadingElementId = "formloader"
        }))
        {
            foreach (var property in ViewData.ModelMetadata.Properties)
            {
                if (property.PropertyName.Contains("PrafitAccount"))
                {
                    <div class="form-group col-md-12">
                        @Html.Label(property.DisplayName, new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.DropDownList(property.PropertyName, (SelectList)ViewBag.CreditorAccounts , new { @class = "select2" })
                        </div>
                    </div>
                }
                else if (property.PropertyName.Contains("DisableGames"))
                {
                    <div class="form-group col-md-12">
                        @Html.Label(property.DisplayName, new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.Editor(property.PropertyName)
                        </div>
                    </div>
                }
                else if (property.PropertyName.Contains("DisableCards"))
                {
                    <div class="form-group col-md-12">
                        @Html.Label(property.DisplayName, new { @class = "control-label col-md-3" })
                        <div class="col-md-9">
                            @Html.Editor(property.PropertyName)
                        </div>
                    </div>
                }
            }

            <div class="space-10"></div>

            <div class="space-32"></div>
            <div class="space-32"></div>
            <div class="hr hr32 hr-dotted"></div>
            @Html.Partial("_FormAction")
        }
    </div>
</div>

<script>
      $(function () {
    $('.select2').css('width', '200px').select2({ allowClear: false });
    $('#select2-multiple-style .btn').on('click',
        function (e) {
            var target = $(this).find('input[type=radio]');
            var which = parseInt(target.val());
            if (which == 2) $('.select2').addClass('tag-input-style');
            else $('.select2').removeClass('tag-input-style');
        });
    });
    function loadFirstList() {
        i('loadDataList');
        fillDataList('PropertyName', '/Cards/CardSetting/GetAccounts');
    }
        //loadFirstList();
</script>