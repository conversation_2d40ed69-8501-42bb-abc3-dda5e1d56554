﻿@{
    ViewBag.Title = "Chat";
}

<hgroup>

    <h2>@ViewBag.Title.</h2>

    <h3>@ViewBag.Message</h3>

</hgroup>

<div class="container">

    <input type="text" id="TxtMessage"/>

    <input type="button" id="BtnSend" value="Send"/>

    <input type="hidden" id="UserName"/>

    <ul id="Chats"></ul>

</div>


<script src="~/signalr/hubs"></script>


<script>

    $(function() {


        i('load signal');
        try {
            var chat = $.connection.chatHub;
            i('load connect');
            chat.client.NewMessage = function(Cl_Name, Cl_Message) {

                $('#Chats').append('<li><strong>' +
                    htmlEncode(Cl_Name) +
                    '</strong>: ' +
                    htmlEncode(Cl_Message) +
                    '</li>');

            };

            $('#UserName').val(prompt('Please Enter Your Name:', ''));

            $('#TxtMessage').focus();

            $.connection.hub.start().done(function() {

                $('#BtnSend').click(function() {

                    chat.server.Notify($('#UserName').val(), $('#TxtMessage').val());

                    $('#TxtMessage').val('').focus();

                });

            });
        } catch (e) {
            ar(e);
        }

    });

    function htmlEncode(value) {
        var encodedValue = $('<div />').text(value).html();
        return encodedValue;
    }

</script>