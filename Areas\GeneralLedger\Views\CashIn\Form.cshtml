﻿
@using Obout.Mvc.ComboBox
@model AppTech.MSMS.Domain.Models.CashIn
@{
    Layout = "/Views/Shared/_Form.cshtml";
}


    <div class="row">


        <div class="col-xs-12 col-sm-6">
            <div class="form-group">
                <label class="col-md-2 control-label">طريقة القبض</label>

                <div class="col-md-10">
                    @Html.DropDownListFor(model => model.Method, new[] { new SelectListItem { Text = "نقد", Value = "نقد", Selected = true }, new SelectListItem { Text = "بنك", Value = "بنك" }, new SelectListItem { Text = "صراف", Value = "صراف" }, })
                </div>
                @Html.ValidationMessageFor(model => model.Method)
            </div>


            <div class="form-group form-inline">
                @Html.LabelFor(model => model.Amount, new { @class = "control-label col-md-2" })
                <div class="col-md-10 form-inline">
                    @Html.EditorFor(model => model.Amount, new { @class = "col-md-8" })
                    <label id="words" class="red"></label>
                    @Html.ValidationMessageFor(model => model.Amount)
                </div>
            </div>


            <div class="form-group">
                @Html.LabelFor(model => model.CreditorAccountID, new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    @Html.Obout(new ComboBox("CreditorAccountID") { Width = 300, SelectedValue = Model.CreditorAccountID == 0 ? null : Model.CreditorAccountID.ToString(), FilterType = ComboBoxFilterType.Contains, LoadingText = "Loading" })

                    @Html.ValidationMessageFor(model => model.CreditorAccountID)
                </div>
            </div>



            <div class="form-group">
                @Html.LabelFor(model => model.Note, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Note, new { htmlAttributes = new { @class = "form-control", id = "form-field-note" } })
                    @Html.ValidationMessageFor(model => model.Note)
                </div>
            </div>

        </div>



        <div class="col-xs-12 col-sm-6">




            <div class="form-group">
                @Html.Label("الصندوق", new { @id = "exchange_name", @class = "control-label col-md-2" })
                <div class="col-md-10">
                    <select id="DebitorAccountID" name="DebitorAccountID"></select>

                </div>
            </div>


            <div class="form-group">
                <div class="col-md-12">
                    @Html.LabelFor(model => model.CurrencyID, new { @class = "control-label col-md-2" })
                    @Html.DropDownListFor(m => m.CurrencyID, (SelectList)ViewBag.Currencies)
                </div>
            </div>


            <div class="form-group">

                @Html.LabelFor(model => model.Date, new { @class = "control-label col-md-2" })
                <div class="col-md-10">
                    @Html.EditorFor(model => model.Date, new { htmlAttributes = new { @class = "date-picker" } })
                    @Html.ValidationMessageFor(model => model.Date, "", new { @class = "text-danger" })
                </div>
            </div>

            <div class="form-group">
                @Html.LabelFor(model => model.RefNumber, new { @class = "control-label col-md-2" })
                <div class="col-md-10">

                    @Html.EditorFor(model => model.RefNumber)
                    @Html.ValidationMessageFor(model => model.RefNumber)
                </div>
            </div>



        </div>

    </div>

        <script>

    $(function() {

        loadDataList();
        $('#Method').on("change",
            function() {
                i('change');
                loadDataList();
            });

        function loadDataList() {
            var method = $('#Method').val();
            i('Method>' + method);
            var path = "GetFunds";
            var title = "اختر الصندوق";

            if (method === 'بنك') {
                title = "اختر البنك";
                $("#exchange_name").text("اسم البنك")
                path = "GetBanks";

            }
            else if (method === 'صراف') {
                title = "اختر صراف";
                $("#exchange_name").text("اسم صراف")
                path = "GetExchangers";

            }
            else {
                $("#exchange_name").text("اسم الصندوق")
            }
            fillListWithSelected('DebitorAccountID', @Model.DebitorAccountID, '/GeneralLedger/Account/' + path, true, title);
        }

    });

        </script>
