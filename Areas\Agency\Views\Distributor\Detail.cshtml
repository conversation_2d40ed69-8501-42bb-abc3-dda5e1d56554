﻿@using AppTech.Common.Extensions
@using AppTech.MSMS.Web.Security
@using AppTech.MSMS.Domain
@model AppTech.MSMS.Domain.Models.Distributor

<div>
    <div class="hr dotted"></div>

    <div>
        <div id="user-profile-1" class="user-profile row">
            <div class="col-xs-12 col-sm-3 center">
                <div>
                    <span class="profile-picture">
        
                    </span>

                    <div class="space-4"></div>

                    <div class="width-80 label label-info label-xlg arrowed-in arrowed-in-right">
                        <div class="inline position-relative">
                            <a href="#" class="user-title-label dropdown-toggle" data-toggle="dropdown">
                                @if (Model.Status.AsBool())
                                {
                                    <i class="ace-icon fa fa-circle light-green"></i>
                                }
                                else
                                {
                                    <i class="ace-icon fa fa-circle red"></i>
                                }


                                &nbsp;
                                <span class="white">@Model.Name</span>
                            </a>

                            <ul class="align-left dropdown-menu dropdown-caret dropdown-lighter">
                                <li class="dropdown-header"> الحالة </li>

                                @if (Model.Status.AsBool())
                                {
                                    <li>
                                        <a href="#">
                                            <i class="ace-icon fa fa-circle green"></i>
                                            &nbsp;
                                            <span class="green">مفعل</span>
                                        </a>
                                    </li>
                                }
                                else
                                {
                                    <li>
                                        <a href="#">
                                            <i class="ace-icon fa fa-circle red"></i>
                                            &nbsp;
                                            <span class="red">موقف</span>
                                        </a>
                                    </li>
                                }


                            </ul>
                        </div>
                    </div>
                </div>

                <div class="space-6"></div>

                <div class="profile-contact-info">
                    <input type="hidden" id="id" value="@Model.ID" />
                    <div class="profile-contact-links align-right">

                        @if (DomainManager.SupportAgent)
                        {
                            <button class="btn btn-link " id="upgrade">
                                <i class="ace-icon fa fa-user bigger-120 green"></i>
                                الترقيةالى وكيل

                            </button>
                        }
                    </div>

                    <div class="space-6"></div>
                  
                </div>

            </div>

            <div class="col-xs-12 col-sm-9">
              
                <div class="space-12"></div>
                <div class="profile-user-info profile-user-info-striped">

                    <div class="profile-info-row">
                        <div class="profile-info-name"> رقم الموزع </div>

                        <div class="profile-info-value">
                            <span class="editable" id="age"> @Html.DisplayFor(model => model.Number) </span>
                        </div>
                    </div>


                    <div class="profile-info-row">
                        <div class="profile-info-name"> اسم الموزع </div>

                        <div class="profile-info-value">
                            <span class="editable" id="username"> @Html.DisplayFor(model => model.Name)</span>
                        </div>
                    </div>

                    <div class="profile-info-row">
                        <div class="profile-info-name"> العنوان </div>

                        <div class="profile-info-value">
                            <i class="fa fa-map-marker light-orange bigger-110"></i>
                            <span class="editable" id="country">اليمن</span>
                            <span class="editable" id="city"> @Html.DisplayFor(model => model.Address)</span>
                        </div>
                    </div>


                    <div class="profile-info-row">
                        <div class="profile-info-name"> رقم الهاتف </div>

                        <div class="profile-info-value">
                            <span class="editable" id="login"> @Html.DisplayFor(model => model.PhoneNumber)</span>
                        </div>
                    </div>


                    <div class="profile-info-row">
                        <div class="profile-info-name"> أرقام تواصل أخرى </div>

                        <div class="profile-info-value">
                            <span class="editable" id="login"> @Html.DisplayFor(model => model.ContactNumber)</span>
                        </div>
                    </div>


                    @if (Model.Agent != null)
                    {
                        <div class="profile-info-row">
                            <div class="profile-info-name"> مسجل  لدى وكيل </div>

                            <div class="profile-info-value">
                                <span class="editable" id="login"> @Html.DisplayFor(model => model.Agent.Name)</span>
                            </div>
                        </div>
                    }

                    @if (DomainManager.SupportMultiBranching)
                    {
                        <div class="profile-info-row">
                            <div class="profile-info-name">الفرع </div>

                            <div class="profile-info-value">
                                <span class="editable" id="login"> @Html.DisplayFor(model => model.Branch.Name)</span>
                            </div>
                        </div>
                    }

                </div>

                <div class="space-20"></div>


                <div class="hr hr2 hr-double"></div>

                <div class="space-6"></div>
                
            </div>
        </div>
    </div>


</div>

@*<script src="~/Scripts/bootbox.js"></script>*@
<script>

    $(function () {
        $("#upgrade").on(ace.click_event,
            function () {
                var id = @Model.ID;
                bootbox.confirm("سوف يتم ترقية الموزع الى وكيل , هل انت متأكد?",
                    function (result) {
                        if (result) {
                            showLoading();
                            try {
                                $.ajax({
                                    url: '/Agency/Distributor/UpgradeToAgent',
                                    data: { id: id },
                                    success: function (data) {
                                        hideLoading();
                                        alert(data);
                                        //  showSuccess(data);
                                    //    $("#upgrade").hide();
                                        window.location.href = '/#!/route/Agency/Agent';
                                    },
                                    error: function (xhr, ajaxOptions, thrownError) {
                                        hideLoading();
                                        handleXhr(xhr);
                                    }
                                });
                            } catch (e) {
                                alert(e);
                            }
                        }
                    });
            });

    });

</script>